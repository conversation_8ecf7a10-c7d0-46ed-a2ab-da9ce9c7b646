# 📱 Telegram Message Templates Configuration Guide

## 📍 **Where Message Templates Are Located**

The Telegram message templates in Pluto Trading Bot are defined in multiple locations depending on the type of notification:

---

## 🎯 **Backend Templates** (`backend/services/telegram_service.py`)

### **1. Trade Notification Template - Lines 69-79**
**Location:** `backend/services/telegram_service.py` → `send_trade_notification()` method

```python
# Format the message
message = f"{emoji} <b>{trade_data.get('orderType', '')} order executed</b>\n\n"
message += f"Pair: {crypto1}/{crypto2}\n"
message += f"Amount: {amount:.6f} {crypto1}\n"
message += f"Price: {price:.6f} {crypto2}\n"
message += f"Value: {value:.2f} {crypto2}\n"

if pnl is not None:
    profit_emoji = "✅" if pnl > 0 else "❌"
    message += f"PnL: {profit_emoji} {pnl:.2f} {crypto2}\n"
```

**Example Output:**
```
🟢 BUY order executed

Pair: BTC/USDT
Amount: 0.001000 BTC
Price: 45000.000000 USDT
Value: 45.00 USDT
PnL: ✅ 2.50 USDT
```

### **2. Bot Status Notification Template - Lines 98-108**
**Location:** `backend/services/telegram_service.py` → `send_bot_status_notification()` method

```python
message = f"🤖 <b>{status_data.get('status', 'Bot Status Update')}</b>\n\n"

if 'config_name' in status_data:
    message += f"Configuration: {status_data.get('config_name')}\n"
    
if 'message' in status_data:
    message += f"{status_data.get('message')}\n"
    
if 'details' in status_data and status_data['details']:
    message += f"\nDetails: {status_data.get('details')}\n"
```

**Example Output:**
```
🤖 Bot Started

Configuration: BTC/USDT SimpleSpot
Bot is now running and monitoring market conditions.

Details: Session ID: abc123
```

---

## 🎯 **Frontend Templates** (`frontend/src/contexts/TradingContext.tsx`)

### **3. Error Notification Template - Lines 621-633**
**Location:** `frontend/src/contexts/TradingContext.tsx` → `sendTelegramErrorNotification()` function

```typescript
// Format error message with emoji and structure
let message = `⚠️ <b>Error Alert</b>\n\n`;
message += `<b>Type:</b> ${errorType}\n`;
message += `<b>Error:</b> ${errorMessage}\n`;

if (context) {
  message += `<b>Context:</b> ${context}\n`;
}

if (state.config.crypto1 && state.config.crypto2) {
  message += `<b>Trading Pair:</b> ${state.config.crypto1}/${state.config.crypto2}\n`;
}

message += `<b>Time:</b> ${new Date().toLocaleString()}\n`;
```

**Example Output:**
```
⚠️ Error Alert

Type: Price Fetch Error
Error: Failed to fetch market price: Network timeout
Context: Trading pair: BTC/USDT
Trading Pair: BTC/USDT
Time: 1/15/2024, 2:30:25 PM
```

---

## 🎯 **Admin Panel Templates** (`frontend/src/app/admin/page.tsx`)

### **4. Test Message Template - Line 82**
**Location:** `frontend/src/app/admin/page.tsx` → `handleTestTelegram()` function

```typescript
text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'
```

**Example Output:**
```
🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.
```

---

## 🛠️ **How to Customize Message Templates**

### **Step 1: Identify the Template Type**
- **Trade Notifications**: Backend (`telegram_service.py`)
- **Bot Status**: Backend (`telegram_service.py`)
- **Error Alerts**: Frontend (`TradingContext.tsx`)
- **Test Messages**: Frontend (`admin/page.tsx`)

### **Step 2: Edit the Template**

#### **For Trade Notifications:**
Edit `backend/services/telegram_service.py` lines 69-79:

```python
# Custom trade notification template
message = f"💰 <b>{trade_data.get('orderType')} Trade Completed</b>\n\n"
message += f"📊 Pair: {crypto1}/{crypto2}\n"
message += f"💎 Amount: {amount:.6f} {crypto1}\n"
message += f"💵 Price: ${price:.2f}\n"
message += f"💸 Total Value: ${value:.2f}\n"
message += f"⏰ Time: {datetime.now().strftime('%H:%M:%S')}\n"

if pnl is not None:
    profit_emoji = "🚀" if pnl > 0 else "📉"
    message += f"📈 P&L: {profit_emoji} ${pnl:.2f}\n"
```

#### **For Error Notifications:**
Edit `frontend/src/contexts/TradingContext.tsx` lines 621-633:

```typescript
// Custom error notification template
let message = `🚨 <b>ALERT: ${errorType}</b>\n\n`;
message += `❌ <b>Issue:</b> ${errorMessage}\n`;

if (context) {
  message += `🔍 <b>Details:</b> ${context}\n`;
}

if (state.config.crypto1 && state.config.crypto2) {
  message += `📈 <b>Pair:</b> ${state.config.crypto1}/${state.config.crypto2}\n`;
}

message += `🕐 <b>Timestamp:</b> ${new Date().toISOString()}\n`;
```

#### **For Bot Status Notifications:**
Edit `backend/services/telegram_service.py` lines 98-108:

```python
# Custom bot status template
message = f"🔔 <b>Bot Update: {status_data.get('status', 'Status Change')}</b>\n\n"

if 'config_name' in status_data:
    message += f"⚙️ Config: {status_data.get('config_name')}\n"
    
if 'message' in status_data:
    message += f"📝 Info: {status_data.get('message')}\n"
    
if 'details' in status_data and status_data['details']:
    message += f"\n🔎 Additional Details:\n{status_data.get('details')}\n"
```

### **Step 3: Restart the Application**
After making changes:
1. **Backend changes**: Restart the Python backend server
2. **Frontend changes**: Restart the Next.js frontend application

---

## 🎨 **Template Customization Options**

### **Emojis and Formatting**
- **HTML Formatting**: Use `<b>bold</b>`, `<i>italic</i>`, `<code>code</code>`
- **Emojis**: 🟢🔴⚠️🤖💰📊💎💵💸⏰📈🚀📉🚨❌🔍📈🕐🔔⚙️📝🔎
- **Line Breaks**: Use `\n` for new lines, `\n\n` for paragraph breaks

### **Dynamic Data Fields**
Available data fields you can use in templates:

#### **Trade Data:**
- `orderType` - "BUY" or "SELL"
- `crypto1`, `crypto2` - Trading pair symbols
- `amountCrypto1` - Amount of base currency
- `avgPrice` - Execution price
- `valueCrypto2` - Total value in quote currency
- `realizedProfitLossCrypto2` - Profit/Loss amount

#### **Status Data:**
- `status` - Bot status message
- `config_name` - Configuration name
- `message` - Status message
- `details` - Additional details

#### **Error Data:**
- `errorType` - Type of error
- `errorMessage` - Error description
- `context` - Additional context
- `crypto1`, `crypto2` - Current trading pair

### **Advanced Customization**
For more complex templates, you can:
1. Add conditional formatting based on values
2. Include additional data fields
3. Create multi-language templates
4. Add custom emoji logic
5. Format numbers with different precision

---

## 📋 **Template Examples**

### **Minimal Trade Template:**
```python
message = f"{emoji} {trade_data.get('orderType')} {amount:.4f} {crypto1} @ ${price:.2f}"
```

### **Detailed Trade Template:**
```python
message = f"""
🎯 <b>TRADE EXECUTED</b>

📊 <b>Details:</b>
• Type: {trade_data.get('orderType')}
• Pair: {crypto1}/{crypto2}
• Amount: {amount:.6f} {crypto1}
• Price: {price:.6f} {crypto2}
• Value: {value:.2f} {crypto2}
• Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💰 <b>P&L:</b> {profit_emoji} {pnl:.2f} {crypto2}
"""
```

### **Custom Error Template:**
```typescript
let message = `
🔥 <b>SYSTEM ALERT</b>

⚠️ <b>Error Type:</b> ${errorType}
📝 <b>Description:</b> ${errorMessage}
🔧 <b>Action Required:</b> Check bot configuration
⏰ <b>Time:</b> ${new Date().toLocaleString()}
`;
```

---

## ✅ **Testing Your Templates**

1. **Make your changes** to the template files
2. **Restart the application** (backend and/or frontend)
3. **Use the Test Telegram button** in Admin Panel
4. **Trigger actual notifications** by starting/stopping the bot
5. **Check the message formatting** in your Telegram chat

---

## 🚨 **Important Notes**

- **HTML Parsing**: All messages use HTML parse mode
- **Character Limits**: Telegram messages have a 4096 character limit
- **Special Characters**: Escape HTML special characters (`<`, `>`, `&`)
- **Backup**: Always backup original templates before customizing
- **Testing**: Test thoroughly with different scenarios before production use
