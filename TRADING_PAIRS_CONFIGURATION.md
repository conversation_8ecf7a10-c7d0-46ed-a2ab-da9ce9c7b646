# 🔧 Trading Pairs Configuration Guide

## 📍 **Where to Configure Trading Pairs**

The trading pairs in Pluto Trading Bot are configured in the following locations:

### **Primary Configuration File**
**File:** `frontend/src/lib/types.ts`

**Key Sections:**

#### **1. Allowed Crypto 1 (Base Currencies) - Lines 557-561**
```typescript
export const ALLOWED_CRYPTO1 = [
  "BTC", "ETH", "BNB", "SOL", "LINK", "AVAX", "DOT", "UNI", "NEAR", "AAVE",
  "ATOM", "VET", "RENDER", "POL", "ALGO", "ARB", "FET", "PAXG", "GALA",
  "CRV", "COMP", "ENJ"
];
```

#### **2. Allowed Crypto 2 (Quote/Stablecoin Currencies) - Lines 564-566**
```typescript
export const ALLOWED_CRYPTO2 = [
  "USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR"
];
```

#### **3. Available Stablecoins - Line 551**
```typescript
export const AVAILABLE_STABLECOINS = ["USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR"];
```

#### **4. Default Quote Currencies - Line 554**
```typescript
export const DEFAULT_QUOTE_CURRENCIES = ["USDT", "USDC", "BTC"];
```

---

## 🚀 **How to Add New Cryptocurrencies**

### **Step 1: Add Base Cryptocurrency (Crypto 1)**
Edit `frontend/src/lib/types.ts` at **line 557-561**:

```typescript
export const ALLOWED_CRYPTO1 = [
  "BTC", "ETH", "BNB", "SOL", "LINK", "AVAX", "DOT", "UNI", "NEAR", "AAVE",
  "ATOM", "VET", "RENDER", "POL", "ALGO", "ARB", "FET", "PAXG", "GALA",
  "CRV", "COMP", "ENJ",
  // Add your new cryptocurrencies here:
  "ADA", "XRP", "DOGE", "MATIC", "LTC"  // Example additions
];
```

### **Step 2: Add Quote/Stablecoin (Crypto 2)**
Edit `frontend/src/lib/types.ts` at **line 564-566**:

```typescript
export const ALLOWED_CRYPTO2 = [
  "USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR",
  // Add your new quote currencies here:
  "BUSD", "USDD"  // Example additions
];
```

### **Step 3: Update Stablecoins List (if adding stablecoins)**
Edit `frontend/src/lib/types.ts` at **line 551**:

```typescript
export const AVAILABLE_STABLECOINS = [
  "USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR",
  // Add new stablecoins here:
  "BUSD", "USDD"  // Example additions
];
```

---

## 📋 **Current Available Trading Pairs**

### **Base Cryptocurrencies (Crypto 1):**
```
BTC, ETH, BNB, SOL, LINK, AVAX, DOT, UNI, NEAR, AAVE,
ATOM, VET, RENDER, POL, ALGO, ARB, FET, PAXG, GALA,
CRV, COMP, ENJ
```

### **Quote/Stablecoin Currencies (Crypto 2):**
```
USDC, DAI, TUSD, FDUSD, USDT, EUR
```

### **Supported Trading Modes:**
1. **SimpleSpot**: Crypto1/Stablecoin (e.g., BTC/USDT)
2. **StablecoinSwap**: Crypto1/Crypto2 (e.g., BTC/ETH)

---

## ⚠️ **Important Notes**

### **1. Exchange Compatibility**
- Ensure the cryptocurrency is available on your configured exchange (default: Binance)
- The bot will validate trading pairs against the exchange's available markets

### **2. Symbol Format**
- Use the exact symbol format as used by the exchange
- Symbols are case-sensitive (use uppercase: "BTC", not "btc")

### **3. Testing New Pairs**
After adding new cryptocurrencies:
1. Restart the frontend application
2. Test the new crypto selection in the Trading Config sidebar
3. Verify price fetching works correctly
4. Test with small amounts first

### **4. Backend Integration**
The backend automatically supports any cryptocurrencies added to the frontend configuration. No backend changes are required for adding new trading pairs.

---

## 🔍 **Advanced Configuration**

### **Dynamic Trading Pairs (Advanced)**
For advanced users, the backend provides endpoints to fetch available trading pairs directly from the exchange:

- **GET** `/api/trading/exchange/trading-pairs` - Get all available trading pairs
- **GET** `/api/trading/exchange/cryptocurrencies` - Get all available cryptocurrencies

These can be used to build dynamic cryptocurrency selection if needed.

### **Custom Quote Currency Mapping**
The system also supports a comprehensive mapping of cryptocurrencies to their available quote currencies in the `AVAILABLE_QUOTES_SIMPLE` object (lines 60-549 in types.ts), though this is primarily used for reference and advanced configurations.

---

## 🛠️ **Troubleshooting**

### **Crypto Not Appearing in Selection**
1. Check spelling and case sensitivity
2. Verify the crypto is added to the correct array (`ALLOWED_CRYPTO1` or `ALLOWED_CRYPTO2`)
3. Restart the frontend application
4. Clear browser cache if necessary

### **Price Fetching Issues**
1. Verify the trading pair exists on the configured exchange
2. Check exchange API connectivity
3. Ensure the symbol format matches the exchange's requirements

### **Trading Errors**
1. Confirm the trading pair has sufficient liquidity
2. Check minimum order sizes for the specific pair
3. Verify account permissions for the trading pair

---

## 📞 **Support**

If you encounter issues after adding new trading pairs:
1. Check the browser console for error messages
2. Verify the cryptocurrency symbols are correct
3. Test with well-known pairs first (e.g., BTC/USDT)
4. Ensure your exchange account supports the new trading pairs
