(()=>{var e={};e.id=698,e.ids=[698],e.modules={13:(e,r,s)=>{"use strict";s.d(r,{J:()=>l});var t=s(687),a=s(3210),n=s(4780);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("label",{ref:s,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...r}));l.displayName="Label"},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1003:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2614).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},1132:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx","default")},1662:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3056:(e,r,s)=>{Promise.resolve().then(s.bind(s,7598))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3503:(e,r,s)=>{"use strict";s.d(r,{Cf:()=>m,Es:()=>h,HM:()=>c,L3:()=>p,c7:()=>x,lG:()=>o,rr:()=>f});var t=s(687),a=s(3210),n=s(8726),l=s(4780);let i=a.createContext({open:!1,onOpenChange:()=>{}}),o=({children:e,open:r=!1,onOpenChange:s})=>{let[n,l]=a.useState(r);return a.useEffect(()=>{l(r)},[r]),(0,t.jsx)(i.Provider,{value:{open:n,onOpenChange:e=>{l(e),s?.(e)}},children:e})};a.forwardRef(({className:e,children:r,asChild:s=!1,...n},l)=>{let{onOpenChange:o}=a.useContext(i);return(0,t.jsx)("button",{ref:l,className:e,onClick:()=>o(!0),...n,children:r})}).displayName="DialogTrigger";let d=({children:e})=>(0,t.jsx)(t.Fragment,{children:e}),c=a.forwardRef(({className:e,children:r,asChild:s=!1,...n},l)=>{let{onOpenChange:o}=a.useContext(i);return(0,t.jsx)("button",{ref:l,className:e,onClick:()=>o(!1),...n,children:r})});c.displayName="DialogClose";let u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80",e),...r}));u.displayName="DialogOverlay";let m=a.forwardRef(({className:e,children:r,...s},o)=>{let{open:c,onOpenChange:m}=a.useContext(i);return c?(0,t.jsxs)(d,{children:[(0,t.jsx)(u,{onClick:()=>m(!1)}),(0,t.jsxs)("div",{ref:o,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg",e),...s,children:[r,(0,t.jsxs)("button",{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",onClick:()=>m(!1),children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}):null});m.displayName="DialogContent";let x=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...r}));x.displayName="DialogHeader";let h=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...r}));h.displayName="DialogFooter";let p=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h2",{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...r}));p.displayName="DialogTitle";let f=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...r}));f.displayName="DialogDescription"},3873:e=>{"use strict";e.exports=require("path")},4026:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2614).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var t=s(687),a=s(3210),n=s(4780);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...r}));l.displayName="Card";let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...r}));i.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-4 md:p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",e),...r})).displayName="CardFooter"},4912:(e,r,s)=>{Promise.resolve().then(s.bind(s,1132))},5036:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5079:(e,r,s)=>{"use strict";s.d(r,{bq:()=>d,eb:()=>u,gC:()=>c,l6:()=>o,yv:()=>m});var t=s(687),a=s(3210),n=s(1662),l=s(4780);let i=a.createContext({value:"",onValueChange:()=>{},open:!1,setOpen:()=>{}}),o=a.forwardRef(({children:e,value:r,onValueChange:s,defaultValue:n,...l},o)=>{let[d,c]=a.useState(r||n||""),[u,m]=a.useState(!1);return a.useEffect(()=>{void 0!==r&&c(r)},[r]),(0,t.jsx)(i.Provider,{value:{value:d,onValueChange:e=>{void 0===r&&c(e),s?.(e),m(!1)},open:u,setOpen:m},children:(0,t.jsx)("div",{ref:o,className:"relative",...l,children:e})})});o.displayName="Select";let d=a.forwardRef(({className:e,children:r,...s},o)=>{let{open:d,setOpen:c}=a.useContext(i);return(0,t.jsxs)("button",{ref:o,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),onClick:()=>c(!d),...s,children:[r,(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})]})});d.displayName="SelectTrigger";let c=a.forwardRef(({className:e,children:r,...s},n)=>{let{open:o}=a.useContext(i);return o?(0,t.jsx)("div",{ref:n,className:(0,l.cn)("absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md",e),...s,children:r}):null});c.displayName="SelectContent";let u=a.forwardRef(({className:e,children:r,value:s,...n},o)=>{let{onValueChange:d}=a.useContext(i);return(0,t.jsx)("button",{ref:o,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",e),onClick:()=>d(s),...n,children:r})});u.displayName="SelectItem";let m=a.forwardRef(({placeholder:e,...r},s)=>{let{value:n}=a.useContext(i);return(0,t.jsx)("span",{ref:s,...r,children:n||e})});m.displayName="SelectValue"},5105:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(5239),a=s(8088),n=s(8170),l=s.n(n),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5511:e=>{"use strict";e.exports=require("crypto")},5763:(e,r,s)=>{"use strict";s.d(r,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var t=s(687),a=s(3210),n=s(4780);let l=a.createContext({activeTab:"",setActiveTab:()=>{}}),i=a.forwardRef(({defaultValue:e="",value:r,onValueChange:s,children:n,className:i,...o},d)=>{let[c,u]=a.useState(r||e);return a.useEffect(()=>{void 0!==r&&u(r)},[r]),(0,t.jsx)(l.Provider,{value:{activeTab:c,setActiveTab:e=>{void 0===r&&u(e),s?.(e)}},children:(0,t.jsx)("div",{ref:d,className:i,...o,children:n})})});i.displayName="Tabs";let o=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s,children:r}));o.displayName="TabsList";let d=a.forwardRef(({className:e,value:r,children:s,onClick:i,...o},d)=>{let{activeTab:c,setActiveTab:u}=a.useContext(l);return(0,t.jsx)("button",{ref:d,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",c===r&&"bg-background text-foreground shadow-sm",e),onClick:()=>{u(r),i?.()},...o,children:s})});d.displayName="TabsTrigger";let c=a.forwardRef(({className:e,value:r,children:s,...i},o)=>{let{activeTab:d}=a.useContext(l);return d!==r?null:(0,t.jsx)("div",{ref:o,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...i,children:s})});c.displayName="TabsContent"},6311:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2614).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6834:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(687),a=s(3210),n=s(4780);let l={variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},i=a.forwardRef(({className:e,variant:r="default",...s},a)=>{let i=l.variant[r]||l.variant.default;return(0,t.jsx)("div",{ref:a,className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",i,e),...s})});i.displayName="Badge"},7207:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},7598:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>U});var t=s(687),a=s(3210),n=s(6189),l=s(5763),i=s(9523),o=s(9667),d=s(13),c=s(4493),u=s(4780);let m=a.forwardRef(({className:e,checked:r,onCheckedChange:s,onChange:a,...n},l)=>(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",ref:l,className:"sr-only",checked:r,onChange:e=>{let r=e.target.checked;s?.(r),a?.(e)},...n}),(0,t.jsx)("div",{className:(0,u.cn)("relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600",r&&"bg-primary",e)})]}));m.displayName="Switch";var x=s(8895),h=s(2614);let p=(0,h.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),f=(0,h.A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),g=(0,h.A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),v=(0,h.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var b=s(4026),j=s(1003),y=s(6311),N=s(6834);let w=(0,h.A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),k=(0,h.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),C=(0,h.A)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),S=(0,h.A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]),E=(0,h.A)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),A=(0,h.A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);var O=s(5036),T=s(7207),R=s(5551),_=s(3503),D=s(5079);let M={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/ringtones/cheer.wav",soundError:"/ringtones/G_hades_curse.wav"},B="custom_sound_execution",$="custom_sound_error",P=[{value:"/ringtones/cheer.wav",label:"Cheer"},{value:"/ringtones/chest1.wav",label:"Chest"},{value:"/ringtones/chime2.wav",label:"Chime"},{value:"/ringtones/bells.wav",label:"Bells"},{value:"/ringtones/bird1.wav",label:"Bird 1"},{value:"/ringtones/bird7.wav",label:"Bird 2"},{value:"/ringtones/sparrow1.wav",label:"Sparrow"},{value:"/ringtones/space_bells4a.wav",label:"Space Bells"},{value:"/ringtones/sanctuary1.wav",label:"Sanctuary"},{value:"/ringtones/marble1.wav",label:"Marble"},{value:"/ringtones/foundry2.wav",label:"Foundry"},{value:B,label:"Upload Custom..."}],z=[{value:"/ringtones/G_hades_curse.wav",label:"Hades Curse"},{value:"/ringtones/G_hades_demat.wav",label:"Hades Demat"},{value:"/ringtones/G_hades_sanctify.wav",label:"Hades Sanctify"},{value:"/ringtones/dark2.wav",label:"Dark"},{value:"/ringtones/Satyr_atk4.wav",label:"Satyr Attack"},{value:"/ringtones/S_mon1.mp3",label:"Monster 1"},{value:"/ringtones/S_mon2.mp3",label:"Monster 2"},{value:"/ringtones/wolf4.wav",label:"Wolf"},{value:"/ringtones/goatherd1.wav",label:"Goatherd"},{value:"/ringtones/tax3.wav",label:"Tax Alert"},{value:"/ringtones/G_hades_mat.wav",label:"Hades Mat"},{value:$,label:"Upload Custom..."}];function I({isOpen:e,onClose:r,sessionId:s,sessionName:n}){let[l,c]=(0,a.useState)({...M,customSoundOrderExecutionDataUri:void 0,customSoundErrorDataUri:void 0}),x=(0,a.useRef)(null),h=(e,r)=>{c(s=>({...s,[e]:r}))},p=(e,r)=>{c(s=>({...s,[e]:r}))},f=(e,r)=>{let s=e.target.files?.[0];if(s){let e=new FileReader;e.onload=e=>{let s=e.target?.result;"orderExecution"===r?c(e=>({...e,customSoundOrderExecutionDataUri:s,soundOrderExecution:B})):c(e=>({...e,customSoundErrorDataUri:s,soundError:$}))},e.readAsDataURL(s)}},g=e=>{let r=l[e];if("soundOrderExecution"===e&&l.soundOrderExecution===B?r=l.customSoundOrderExecutionDataUri||"":"soundError"===e&&l.soundError===$&&(r=l.customSoundErrorDataUri||""),x.current&&r){let e=r;r.startsWith("/sounds/")&&(e=r.replace("/sounds/","/ringtones/")),x.current.src=e,x.current.currentTime=0,x.current.play().then(()=>{setTimeout(()=>{x.current&&(x.current.pause(),x.current.currentTime=0)},2e3)}).catch(e=>{})}};return(0,t.jsx)(_.lG,{open:e,onOpenChange:r,children:(0,t.jsxs)(_.Cf,{className:"sm:max-w-md bg-card border-2 border-border",children:[(0,t.jsxs)(_.c7,{children:[(0,t.jsx)(_.L3,{className:"text-primary",children:"Session Alarm Configuration"}),(0,t.jsxs)(_.rr,{children:['Configure sound alerts for "',n,'". These settings apply only to this session.']})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"soundAlertsEnabled",className:"text-base",children:"Enable Sound Alerts"}),(0,t.jsx)(m,{id:"soundAlertsEnabled",checked:!!l.soundAlertsEnabled,onCheckedChange:e=>h("soundAlertsEnabled",e)})]}),l.soundAlertsEnabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"alertOnOrderExecution",children:"Alert on Successful Order Execution"}),(0,t.jsx)(m,{id:"alertOnOrderExecution",checked:!!l.alertOnOrderExecution,onCheckedChange:e=>h("alertOnOrderExecution",e)})]}),l.alertOnOrderExecution&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(D.l6,{value:l.soundOrderExecution,onValueChange:e=>p("soundOrderExecution",e),children:[(0,t.jsx)(D.bq,{className:"flex-grow",children:(0,t.jsx)(D.yv,{placeholder:"Select sound"})}),(0,t.jsx)(D.gC,{children:P.map(e=>(0,t.jsx)(D.eb,{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>g("soundOrderExecution"),className:"btn-outline-neo p-2",children:(0,t.jsx)(S,{className:"h-4 w-4"})})]}),l.soundOrderExecution===B&&(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"customSoundExecutionFile",className:"text-xs",children:"Upload Execution Sound (.mp3, .wav, etc.)"}),(0,t.jsx)(o.p,{id:"customSoundExecutionFile",type:"file",accept:"audio/*",onChange:e=>f(e,"orderExecution"),className:(0,u.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),l.customSoundOrderExecutionDataUri&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"alertOnError",children:"Alert on Errors/Failures"}),(0,t.jsx)(m,{id:"alertOnError",checked:!!l.alertOnError,onCheckedChange:e=>h("alertOnError",e)})]}),l.alertOnError&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(D.l6,{value:l.soundError,onValueChange:e=>p("soundError",e),children:[(0,t.jsx)(D.bq,{className:"flex-grow",children:(0,t.jsx)(D.yv,{placeholder:"Select sound"})}),(0,t.jsx)(D.gC,{children:z.map(e=>(0,t.jsx)(D.eb,{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>g("soundError"),className:"btn-outline-neo p-2",children:(0,t.jsx)(S,{className:"h-4 w-4"})})]}),l.soundError===$&&(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"customSoundErrorFile",className:"text-xs",children:"Upload Error Sound (.mp3, .wav, etc.)"}),(0,t.jsx)(o.p,{id:"customSoundErrorFile",type:"file",accept:"audio/*",onChange:e=>f(e,"error"),className:(0,u.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),l.customSoundErrorDataUri&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]})]})]}),(0,t.jsxs)(_.Es,{children:[(0,t.jsx)(_.HM,{asChild:!0,children:(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,t.jsx)(i.$,{onClick:()=>{let e={...l};l.soundOrderExecution===B&&l.customSoundOrderExecutionDataUri?e.soundOrderExecution=l.customSoundOrderExecutionDataUri:l.soundOrderExecution!==B||l.customSoundOrderExecutionDataUri||(e.soundOrderExecution=M.soundOrderExecution),l.soundError===$&&l.customSoundErrorDataUri?e.soundError=l.customSoundErrorDataUri:l.soundError!==$||l.customSoundErrorDataUri||(e.soundError=M.soundError);let{customSoundOrderExecutionDataUri:t,customSoundErrorDataUri:a,...n}=e;R.SessionManager.getInstance().updateSessionAlarmSettings(s,n)&&r()},className:"btn-primary-neo",children:"Save Settings"})]})]})})}function F(){let{config:e,targetPriceRows:r,orderHistory:s,currentMarketPrice:n,crypto1Balance:l,crypto2Balance:d,stablecoinBalance:u,botSystemStatus:m,dispatch:h}=(0,x.U)(),[p,f]=(0,a.useState)([]),[g,b]=(0,a.useState)(null),[j,y]=(0,a.useState)(null),[_,D]=(0,a.useState)(""),[M,B]=(0,a.useState)(0),[$,P]=(0,a.useState)(!1),[z,F]=(0,a.useState)(""),[U,H]=(0,a.useState)(""),L=R.SessionManager.getInstance(),V=()=>{f(L.getAllSessions().sort((e,r)=>r.lastModified-e.lastModified))},q=async()=>{if(g)try{let t,a;let i=L.loadSession(g);if(!i)return;let o=L.getAllSessions(),c=i.name.replace(/ \((Saved|AutoSaved).*\)$/,""),m=o.find(e=>e.id!==g&&e.name.startsWith(c)&&e.name.includes("(Saved")&&!e.isActive);if(m){t=m.id;let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a=`${c} (Saved ${e})`}else{let r=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a=`${c} (Saved ${r})`,t=await L.createNewSession(a,e,{crypto1:l,crypto2:d,stablecoin:u})}let x=L.getCurrentRuntime(g);m&&L.renameSession(t,a),L.saveSession(t,e,r,s,n,l,d,u,!1,x)&&V()}catch(e){}},K=e=>{let r=L.loadSession(e);r&&(h({type:"SET_CONFIG",payload:r.config}),h({type:"SET_TARGET_PRICE_ROWS",payload:r.targetPriceRows}),h({type:"CLEAR_ORDER_HISTORY"}),r.orderHistory.forEach(e=>{h({type:"ADD_ORDER_HISTORY_ENTRY",payload:e})}),h({type:"SET_MARKET_PRICE",payload:r.currentMarketPrice}),h({type:"UPDATE_BALANCES",payload:{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}}),h({type:"SYSTEM_STOP_BOT"}),L.setCurrentSession(e),b(e),V(),setTimeout(()=>{window.location.href="/dashboard"},1e3))},Z=e=>{L.deleteSession(e)&&(g===e&&b(null),V())},G=e=>{_.trim()&&L.renameSession(e,_.trim())&&(y(null),D(""),V())},J=(e,r)=>{F(e),H(r),P(!0)},W=e=>{let r=L.exportSessionToCSV(e);if(!r)return;let s=L.loadSession(e),t=new Blob([r],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),n=URL.createObjectURL(t);a.setAttribute("href",n),a.setAttribute("download",`${s?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a)},Y=e=>{if(!e||e<0)return"0s";let r=Math.floor(e/1e3),s=Math.floor(r/3600),t=Math.floor(r%3600/60),a=r%60;return s>0?`${s}h ${t}m ${a}s`:t>0?`${t}m ${a}s`:`${a}s`},X=()=>p.filter(e=>e.isActive),Q=()=>p.filter(e=>!e.isActive&&L.getCurrentRuntime(e.id)>5e3);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(w,{className:"h-5 w-5"}),"Running Sessions"]})}),(0,t.jsx)(c.Wu,{children:X().length>0?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,t.jsx)("div",{children:"Session Name"}),(0,t.jsx)("div",{children:"Active Status"}),(0,t.jsx)("div",{children:"Runtime"}),(0,t.jsx)("div",{children:"Actions"})]}),(0,t.jsx)("div",{className:"space-y-2",children:X().map(e=>(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:j===e.id?(0,t.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,t.jsx)(o.p,{value:_,onChange:e=>D(e.target.value),onKeyPress:r=>"Enter"===r.key&&G(e.id),className:"text-sm"}),(0,t.jsx)(i.$,{size:"sm",onClick:()=>G(e.id),children:(0,t.jsx)(k,{className:"h-3 w-3"})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>{y(e.id),D(e.name)},children:(0,t.jsx)(C,{className:"h-3 w-3"})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(N.E,{variant:"default",children:"Active"})}),(0,t.jsx)("div",{className:"text-sm",children:e.id===g?Y(M):Y(e.runtime)}),(0,t.jsx)("div",{children:e.id===g?(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsxs)(i.$,{onClick:q,size:"sm",className:"btn-neo",children:[(0,t.jsx)(k,{className:"mr-2 h-3 w-3"}),"Save"]}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>J(e.id,e.name),title:"Configure Alarms",children:(0,t.jsx)(S,{className:"h-3 w-3"})})]}):(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>K(e.id),title:"Load Session",children:(0,t.jsx)(E,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>W(e.id),title:"Export Session",children:(0,t.jsx)(A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>J(e.id,e.name),title:"Configure Alarms",children:(0,t.jsx)(S,{className:"h-3 w-3"})})]})})]},e.id))})]}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)(w,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{children:"No running sessions"}),(0,t.jsx)("p",{className:"text-xs",children:"Start the bot to see running sessions here"})]})})]}),(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(v,{className:"h-5 w-5"}),"Past Sessions (",Q().length,")"]}),(0,t.jsxs)(c.BT,{children:["Auto-saved: ",Q().length," | Manual: 0"]})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"h-[400px] overflow-y-auto",children:0===Q().length?(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)(O.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{children:"No saved sessions yet."}),(0,t.jsx)("p",{className:"text-xs",children:"Save your current session to get started."})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,t.jsx)("div",{children:"Session Name"}),(0,t.jsx)("div",{children:"Active Status"}),(0,t.jsx)("div",{children:"Total Runtime"}),(0,t.jsx)("div",{children:"Actions"})]}),(0,t.jsx)("div",{className:"space-y-2",children:Q().map(e=>(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:j===e.id?(0,t.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,t.jsx)(o.p,{value:_,onChange:e=>D(e.target.value),onKeyPress:r=>"Enter"===r.key&&G(e.id),className:"text-sm"}),(0,t.jsx)(i.$,{size:"sm",onClick:()=>G(e.id),children:(0,t.jsx)(k,{className:"h-3 w-3"})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>{y(e.id),D(e.name)},children:(0,t.jsx)(C,{className:"h-3 w-3"})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(N.E,{variant:"secondary",children:"Inactive"})}),(0,t.jsx)("div",{className:"text-sm",children:Y(L.getCurrentRuntime(e.id))}),(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>K(e.id),title:"Load Session",children:(0,t.jsx)(E,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>W(e.id),title:"Export Session",children:(0,t.jsx)(A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>J(e.id,e.name),title:"Configure Alarms",children:(0,t.jsx)(S,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>Z(e.id),title:"Delete Session",children:(0,t.jsx)(T.A,{className:"h-3 w-3"})})]})]},e.id))})]})})})]}),(0,t.jsx)(I,{isOpen:$,onClose:()=>{P(!1),F(""),H("")},sessionId:z,sessionName:U})]})}function U(){let{botSystemStatus:e}=(0,x.U)(),[r,s]=(0,a.useState)("iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA"),[u,h]=(0,a.useState)("jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp"),[N,w]=(0,a.useState)(!1),[k,C]=(0,a.useState)(!1),[S,E]=(0,a.useState)(""),[A,O]=(0,a.useState)(""),T=(0,n.useRouter)(),R=async()=>{try{localStorage.setItem("binance_api_key",r),localStorage.setItem("binance_api_secret",u)}catch(e){}},_=async()=>{try{(await fetch("https://api.binance.com/api/v3/ping")).ok}catch(e){}},D=async()=>{if(S&&A)try{(await fetch(`https://api.telegram.org/bot${S}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:A,text:"\uD83E\uDD16 Test message from Pluto Trading Bot! Your Telegram integration is working correctly."})})).ok}catch(e){}},M=[{value:"systemTools",label:"System Tools",icon:(0,t.jsx)(p,{className:"mr-2 h-4 w-4"})},{value:"apiKeys",label:"Exchange API Keys",icon:(0,t.jsx)(f,{className:"mr-2 h-4 w-4"})},{value:"telegram",label:"Telegram Integration",icon:(0,t.jsx)(g,{className:"mr-2 h-4 w-4"})},{value:"sessionManager",label:"Session Manager",icon:(0,t.jsx)(v,{className:"mr-2 h-4 w-4"})}];return(0,t.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,t.jsxs)(c.Zp,{className:"border-2 border-border",children:[(0,t.jsxs)(c.aR,{className:"flex flex-row justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.ZB,{className:"text-3xl font-bold text-primary",children:"Admin Panel"}),(0,t.jsx)(c.BT,{children:"Manage global settings and tools for Pluto Trading Bot."})]}),(0,t.jsxs)(i.$,{variant:"outline",onClick:()=>T.push("/dashboard"),className:"btn-outline-neo",children:[(0,t.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Return to Dashboard"]})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(l.tU,{defaultValue:"systemTools",className:"w-full",children:[(0,t.jsx)("div",{className:"pb-2 overflow-x-auto",children:(0,t.jsx)(l.j7,{className:"bg-card border-border border-2 p-1",children:M.map(e=>(0,t.jsx)(l.Xi,{value:e.value,className:"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:(0,t.jsxs)("div",{className:"flex items-center",children:[e.icon," ",e.label]})},e.value))})}),(0,t.jsx)(l.av,{value:"systemTools",className:"mt-6",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"System Tools"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation)."}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("DB Editor Clicked"),children:"View Database (Read-Only)"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Export Orders Clicked"),children:"Export Orders to Excel"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Export History Clicked"),children:"Export History to Excel"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Backup DB Clicked"),children:"Backup Database"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Restore DB Clicked"),disabled:!0,children:"Restore Database"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Diagnostics Clicked"),children:"Run System Diagnostics"})]})]})]})})}),(0,t.jsx)(l.av,{value:"apiKeys",className:"mt-6",children:(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Exchange API Keys (Binance)"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure your Binance API keys for real trading. Keys are stored securely in browser storage."}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"apiKey",children:"API Key"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{id:"apiKey",type:N?"text":"password",value:r,onChange:e=>s(e.target.value),placeholder:"Enter your Binance API key",className:"pr-10"}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!N),children:N?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"apiSecret",children:"API Secret"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{id:"apiSecret",type:k?"text":"password",value:u,onChange:e=>h(e.target.value),placeholder:"Enter your Binance API secret",className:"pr-10"}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C(!k),children:k?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.$,{onClick:R,className:"btn-neo",children:"Save API Keys"}),(0,t.jsx)(i.$,{onClick:_,variant:"outline",className:"btn-outline-neo",children:"Test Connection"})]})]})]})}),(0,t.jsx)(l.av,{value:"telegram",className:"mt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Telegram Configuration"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure Telegram bot for real-time trading notifications."}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"telegramToken",children:"Telegram Bot Token"}),(0,t.jsx)(o.p,{id:"telegramToken",type:"password",value:S,onChange:e=>E(e.target.value),placeholder:"Enter your Telegram bot token"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"telegramChatId",children:"Telegram Chat ID"}),(0,t.jsx)(o.p,{id:"telegramChatId",value:A,onChange:e=>O(e.target.value),placeholder:"Enter your Telegram chat ID"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m,{id:"notifyOnOrder"}),(0,t.jsx)(d.J,{htmlFor:"notifyOnOrder",children:"Notify on Order Execution"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m,{id:"notifyOnErrors"}),(0,t.jsx)(d.J,{htmlFor:"notifyOnErrors",children:"Notify on Errors"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.$,{onClick:()=>{try{localStorage.setItem("telegram_bot_token",S),localStorage.setItem("telegram_chat_id",A)}catch(e){}},className:"btn-neo",children:"Save Telegram Config"}),(0,t.jsx)(i.$,{onClick:D,variant:"outline",className:"btn-outline-neo",children:"Test Telegram"})]})]})]}),(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Setup Guide"})}),(0,t.jsx)(c.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 1: Create a Telegram Bot"}),(0,t.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,t.jsxs)("li",{children:["Open Telegram and search for ",(0,t.jsx)("code",{className:"bg-muted px-1 rounded",children:"@BotFather"})]}),(0,t.jsxs)("li",{children:["Send ",(0,t.jsx)("code",{className:"bg-muted px-1 rounded",children:"/newbot"})," command"]}),(0,t.jsx)("li",{children:'Choose a name for your bot (e.g., "My Trading Bot")'}),(0,t.jsx)("li",{children:'Choose a username ending with "bot" (e.g., "mytradingbot")'}),(0,t.jsx)("li",{children:"Copy the bot token provided by BotFather"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 2: Get Your Chat ID"}),(0,t.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,t.jsx)("li",{children:"Start a chat with your new bot"}),(0,t.jsx)("li",{children:"Send any message to the bot"}),(0,t.jsxs)("li",{children:["Visit: ",(0,t.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates"})]}),(0,t.jsx)("li",{children:'Look for "chat" and "id" fields in the response'}),(0,t.jsx)("li",{children:"Copy the chat ID number"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 3: Configure Bot"}),(0,t.jsxs)("ul",{className:"text-sm space-y-1 list-disc list-inside text-muted-foreground",children:[(0,t.jsx)("li",{children:'Paste the bot token in the "Telegram Bot Token" field'}),(0,t.jsx)("li",{children:'Paste the chat ID in the "Telegram Chat ID" field'}),(0,t.jsx)("li",{children:"Choose your notification preferences"}),(0,t.jsx)("li",{children:'Click "Save Telegram Config"'}),(0,t.jsx)("li",{children:'Test the connection with "Test Telegram"'})]})]}),(0,t.jsx)("div",{className:"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-yellow-600",children:"\uD83D\uDCA1"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("p",{className:"font-medium text-yellow-600 mb-1",children:"Pro Tip:"}),(0,t.jsx)("p",{className:"text-yellow-700",children:"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed."})]})]})})]})})]})]})}),(0,t.jsx)(l.av,{value:"sessionManager",className:"mt-6",children:(0,t.jsx)(F,{})})]})})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,r,s)=>{"use strict";s.d(r,{$:()=>i});var t=s(687),a=s(3210),n=s(4780);let l={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},i=a.forwardRef(({className:e,variant:r="default",size:s="default",asChild:a=!1,...i},o)=>{let d=l.variant[r]||l.variant.default,c=l.size[s]||l.size.default;return(0,t.jsx)(a?"span":"button",{className:(0,n.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",d,c,e),ref:o,...i})});i.displayName="Button"},9667:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var t=s(687),a=s(3210),n=s(4780);let l=a.forwardRef(({className:e,type:r,...s},a)=>(0,t.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));l.displayName="Input"}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[641,271],()=>s(5105));module.exports=t})();