(()=>{var e={};e.id=754,e.ids=[754],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4080:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\analytics\\page.tsx","default")},4492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(687),a=r(3210),i=r(7079),o=r(428),l=r(4493),n=r(5079),d=r(6834),c=r(5950),p=r(8895),x=r(5551),m=r(8751),u=r(2614);let f=(0,u.A)("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),h=(0,u.A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),y=(0,u.A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var g=r(5036),b=r(3010);let v=(e,t)=>{if(!e||0===e.length)return{totalProfitLossCrypto1:0,totalProfitLossCrypto2:0,winRate:0,totalTradesExecuted:0,buyTrades:0,sellTrades:0,avgProfitPerTradeCrypto2:0,avgProfitPerTradeCrypto1:0};let r=e.filter(e=>void 0!==e.realizedProfitLossCrypto2&&null!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2),s=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),a=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto1||0),0),i=r.filter(e=>(e.realizedProfitLossCrypto2||0)>0).length,o=r.length>0?i/r.length*100:0,l=e.length;"StablecoinSwap"===t.tradingMode&&(l=Math.max(2*r.length,e.length));let n=e.filter(e=>"BUY"===e.orderType).length,d=e.filter(e=>"SELL"===e.orderType).length,c=r.length>0?s/r.length:0,p=r.length>0?a/r.length:0;return{totalProfitLossCrypto1:parseFloat(a.toFixed(t.numDigits)),totalProfitLossCrypto2:parseFloat(s.toFixed(t.numDigits)),winRate:parseFloat(o.toFixed(2)),totalTradesExecuted:l,buyTrades:n,sellTrades:d,avgProfitPerTradeCrypto2:parseFloat(c.toFixed(t.numDigits)),avgProfitPerTradeCrypto1:parseFloat(p.toFixed(t.numDigits))}},j=(e,t)=>{let r=e.filter(e=>void 0!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2),s=0;return r.map((e,t)=>(s+=e.realizedProfitLossCrypto2||0,{date:(0,b.GP)(new Date(e.timestamp),"MMM dd HH:mm"),pnl:parseFloat(s.toFixed(4)),trade:t+1}))};function P(){let{orderHistory:e,config:t,getDisplayOrders:r}=(0,p.U)(),[i,o]=(0,a.useState)([]),[u,b]=(0,a.useState)("current"),[P,C]=(0,a.useState)([]),[N,w]=(0,a.useState)(t);x.SessionManager.getInstance();let T=(0,a.useMemo)(()=>v(P,N),[P,N]),L=(0,a.useMemo)(()=>j(P,N.crypto2),[P,N.crypto2]),k=(0,a.useMemo)(()=>"current"!==u?"0.0000":r().reduce((e,t)=>"Full"===t.status&&void 0!==t.incomeCrypto2?e+t.incomeCrypto2:e,0).toFixed(N.numDigits),[r,N.numDigits,u]),A="current"===u?{name:"Current Session",pair:t.crypto1&&t.crypto2?`${t.crypto1}/${t.crypto2}`:"Crypto 1/Crypto 2",isActive:!0}:i.find(e=>e.id===u),_=(0,a.useMemo)(()=>[{title:`Total Realized P/L (${N.crypto1||"Crypto 1"})`,value:T.totalProfitLossCrypto1,icon:(0,s.jsx)(m.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto1",isProfit:T.totalProfitLossCrypto1>=0},{title:`Total Realized P/L (${N.crypto2||"Crypto 2"})`,value:T.totalProfitLossCrypto2,icon:(0,s.jsx)(m.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto2",isProfit:T.totalProfitLossCrypto2>=0},{title:"Win Rate",value:`${T.winRate}%`,icon:(0,s.jsx)(f,{className:"h-6 w-6 text-primary"}),description:"Profitable sell trades / Total sell trades",isProfit:T.winRate>=50},{title:"Total Trades",value:T.totalTradesExecuted,icon:(0,s.jsx)(h,{className:"h-6 w-6 text-primary"}),description:`${T.buyTrades} buys, ${T.sellTrades} sells`,isProfit:!0},{title:`Avg Profit/Trade (${N.crypto2||"Crypto 2"})`,value:T.avgProfitPerTradeCrypto2,icon:(0,s.jsx)(y,{className:"h-6 w-6 text-primary"}),description:"Average profit per sell trade",isProfit:T.avgProfitPerTradeCrypto2>=0},{title:`Current Unrealized P/L (${N.crypto2||"Crypto 2"})`,value:k,icon:(0,s.jsx)(g.A,{className:"h-6 w-6 text-primary"}),description:"Unrealized profit/loss from active positions",isProfit:parseFloat(k)>=0,isCurrentOnly:!0}],[T,N,k]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{className:"text-xl font-bold text-primary",children:"Session Analytics"}),(0,s.jsx)(l.BT,{children:"View trading analytics for current and past sessions."})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,s.jsxs)(n.l6,{value:u,onValueChange:b,children:[(0,s.jsx)(n.bq,{className:"w-full sm:w-[300px]",children:(0,s.jsx)(n.yv,{placeholder:"Select a session"})}),(0,s.jsxs)(n.gC,{children:[(0,s.jsx)(n.eb,{value:"current",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.E,{variant:"default",className:"text-xs",children:"Current"}),(0,s.jsxs)("span",{children:["Current Session (",t.crypto1&&t.crypto2?`${t.crypto1}/${t.crypto2}`:"Crypto 1/Crypto 2 = 0",")"]})]})}),i.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.w,{className:"my-1"}),i.map(e=>(0,s.jsx)(n.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,s.jsx)("span",{children:e.name})]})},e.id))]})]})]})]})}),A&&(0,s.jsx)("div",{className:"bg-muted/50 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:A.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:A.pair})]}),A.isActive&&(0,s.jsx)(d.E,{variant:"default",className:"text-xs",children:"Active"})]})})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:_.map((e,t)=>e.isCurrentOnly&&"current"!==u?null:(0,s.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:e.title}),e.icon]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("div",{className:`text-2xl font-bold ${"number"==typeof e.value?e.isProfit?"text-green-600":"text-red-600":"text-foreground"}`,children:"number"==typeof e.value?e.value.toFixed(N.numDigits):e.value}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))}),(0,s.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{className:"text-xl font-bold text-primary",children:["Cumulative Profit/Loss Over Time (",N.crypto2,")"]}),(0,s.jsxs)(l.BT,{children:["Chart visualization of trading performance for ",A?.name||"selected session","."]})]}),(0,s.jsx)(l.Wu,{className:"h-80",children:L.length>0?(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg border-2 border-dashed border-border",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(h,{className:"h-16 w-16 mx-auto mb-4 text-primary opacity-60"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Chart Temporarily Disabled"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Chart functionality is being optimized for better performance"}),(0,s.jsxs)("div",{className:"bg-background/80 rounded-lg p-4 border border-border",children:[(0,s.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:"Quick Stats:"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,s.jsxs)("div",{children:["Data Points: ",(0,s.jsx)("span",{className:"font-medium text-primary",children:L.length})]}),(0,s.jsxs)("div",{children:["Latest P/L: ",(0,s.jsxs)("span",{className:`font-medium ${L[L.length-1]?.pnl>=0?"text-green-500":"text-red-500"}`,children:["$",L[L.length-1]?.pnl?.toFixed(2)||"0.00"]})]})]})]})]})}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(h,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No sell trades recorded yet for this session."}),(0,s.jsx)("p",{className:"text-xs",children:"Chart will appear after first profitable trade."})]})})})]})]})}function C(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)(o.A,{}),(0,s.jsx)(P,{})]})}},5331:(e,t,r)=>{Promise.resolve().then(r.bind(r,4492))},5511:e=>{"use strict";e.exports=require("crypto")},5659:(e,t,r)=>{Promise.resolve().then(r.bind(r,4080))},5797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),o=r.n(i),l=r(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d={children:["",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4080)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\analytics\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/analytics/page",pathname:"/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8751:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2614).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[641,606,45,271,12],()=>r(5797));module.exports=s})();