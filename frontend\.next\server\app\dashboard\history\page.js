(()=>{var e={};e.id=610,e.ids=[610],e.modules={540:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2267:(e,t,r)=>{Promise.resolve().then(r.bind(r,540))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5273:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(5239),i=r(8088),o=r(8170),a=r.n(o),n=r(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,540)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/history/page",pathname:"/dashboard/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5511:e=>{"use strict";e.exports=require("crypto")},7207:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(687),i=r(3210),o=r(7079),a=r(428),n=r(4493),l=r(9523),d=r(5079),c=r(6834),p=r(5950),x=r(8895),m=r(5551),y=r(7207);let h=(0,r(2614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var u=r(5036),f=r(3010);function b(){let{dispatch:e,orderHistory:t,config:r}=(0,x.U)(),[o,a]=(0,i.useState)([]),[u,b]=(0,i.useState)("current"),[v,g]=(0,i.useState)([]),P=m.SessionManager.getInstance(),C=(()=>{if("current"===u){let e=t.filter(e=>void 0!==e.realizedProfitLossCrypto2&&null!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0);return{name:"Current Session",pair:`${r.crypto1}/${r.crypto2}`,totalTrades:t.length,totalProfitLoss:e,lastModified:Date.now(),isActive:!0}}return o.find(e=>e.id===u)})();return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,s.jsx)(n.BT,{children:"View trading history for current and past sessions."})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,s.jsxs)(d.l6,{value:u,onValueChange:b,children:[(0,s.jsx)(d.bq,{className:"w-full sm:w-[300px]",children:(0,s.jsx)(d.yv,{placeholder:"Select a session"})}),(0,s.jsxs)(d.gC,{children:[(0,s.jsx)(d.eb,{value:"current",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.E,{variant:"default",className:"text-xs",children:"Current"}),(0,s.jsxs)("span",{children:["Current Session (",r.crypto1&&r.crypto2?`${r.crypto1}/${r.crypto2}`:"Crypto 1/Crypto 2 = 0",")"]})]})}),o.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.w,{className:"my-1"}),o.map(e=>(0,s.jsx)(d.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,s.jsx)("span",{children:e.name})]})},e.id))]})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{"current"===u&&e({type:"CLEAR_ORDER_HISTORY"})},className:"btn-outline-neo",disabled:"current"!==u,children:[(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===v.length)return;if("current"===u)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...v.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(r.numDigits)||"",e.avgPrice?.toFixed(r.numDigits)||"",e.valueCrypto2?.toFixed(r.numDigits)||"",e.price1?.toFixed(r.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(r.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(r.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(r.numDigits)||""].join(","))].join("\n"),t=`current_session_history_${new Date().toISOString().split("T")[0]}.csv`;else{let r=P.exportSessionToCSV(u);if(!r)return;e=r;let s=P.loadSession(u);t=`${s?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`}if(!e)return;let s=new Blob([e],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),o=URL.createObjectURL(s);i.setAttribute("href",o),i.setAttribute("download",t),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)},className:"btn-outline-neo",children:[(0,s.jsx)(h,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),C&&(0,s.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,s.jsx)("div",{className:"font-medium",children:C.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,s.jsx)("div",{className:"font-medium",children:C.pair})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,s.jsx)("div",{className:"font-medium",children:C.totalTrades})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,s.jsx)("div",{className:`font-medium ${C.totalProfitLoss>=0?"text-green-600":"text-red-600"}`,children:C.totalProfitLoss.toFixed(4)})]})]}),"current"!==u&&(0,s.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",(0,f.GP)(new Date(C.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,s.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",C?.name||"Unknown Session"]}),(0,s.jsx)(n.BT,{children:0===v.length?"No trades recorded for this session yet.":`Showing ${v.length} trades for the selected session.`})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)(j,{history:v,config:r})})]})]})}function j({history:e,config:t}){let r=e=>e?.toFixed(t.numDigits)??"-",i=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:`Crypto (${t.crypto1})`},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:`Value (${t.crypto2})`},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:`Crypto (${t.crypto1})`},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:`Crypto (${t.crypto2})`},{key:"profitCrypto1",label:`Profit/Loss (${t.crypto1})`},{key:"profitCrypto2",label:`Profit/Loss (${t.crypto2})`}];return 0===e.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No trading history for this session yet."})]}):(0,s.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:i.map(e=>(0,s.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,f.GP)(new Date(e.timestamp),"yyyy-MM-dd")}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,f.GP)(new Date(e.timestamp),"HH:mm:ss")}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,s.jsx)("td",{className:`px-3 py-2 text-xs font-semibold ${"BUY"===e.orderType?"text-green-400":"text-destructive"}`,children:e.orderType}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.amountCrypto1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.avgPrice)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.valueCrypto2)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.price1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.price2)??"-"}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,s.jsx)("td",{className:`px-3 py-2 text-xs ${e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1>0?"text-green-400":e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1<0?"text-destructive":""}`,children:void 0!==e.realizedProfitLossCrypto1&&null!==e.realizedProfitLossCrypto1&&0!==e.realizedProfitLossCrypto1?r(e.realizedProfitLossCrypto1):"-"}),(0,s.jsx)("td",{className:`px-3 py-2 text-xs ${e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2>0?"text-green-400":e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2<0?"text-destructive":""}`,children:void 0!==e.realizedProfitLossCrypto2&&null!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2?r(e.realizedProfitLossCrypto2):"-"})]},e.id))})]})})})}function v(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(o.A,{}),(0,s.jsx)(a.A,{}),(0,s.jsx)(b,{})]})}},8291:(e,t,r)=>{Promise.resolve().then(r.bind(r,8061))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[641,606,45,271,12],()=>r(5273));module.exports=s})();