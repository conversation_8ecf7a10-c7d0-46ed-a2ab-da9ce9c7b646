(()=>{var e={};e.id=105,e.ids=[105],e.modules={196:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1277:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("CircleDollarSign",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 18V6",key:"zqpxq5"}]])},1305:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},1662:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},1840:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},2375:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},3014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var a=r(687),s=r(3210),o=r(4493),l=r(8895);r(5551);var d=r(4780);let n=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,d.cn)("w-full caption-bottom text-sm",e),...t})}));n.displayName="Table";let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("thead",{ref:r,className:(0,d.cn)("[&_tr]:border-b",e),...t}));i.displayName="TableHeader";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tbody",{ref:r,className:(0,d.cn)("[&_tr:last-child]:border-0",e),...t}));c.displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tfoot",{ref:r,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let p=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tr",{ref:r,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));p.displayName="TableRow";let x=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("th",{ref:r,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));x.displayName="TableHead";let y=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("td",{ref:r,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));y.displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("caption",{ref:r,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";var h=r(6834);function m(){let{getDisplayOrders:e,config:t,currentMarketPrice:r}=(0,l.U)(),s=e(),o=(e,r=!1)=>{if(null==e||isNaN(e))return"-";let a=e.toFixed(t.numDigits);return r&&e>0?`+${a}`:a},m=e=>null==e||isNaN(e)?"-":`${e.toFixed(2)}%`,u=[{key:"#",label:"#"},{key:"status",label:"Status"},{key:"orderLevel",label:"Level"},{key:"valueLevel",label:"Value"},{key:"crypto2Var",label:`${t.crypto2||"Crypto 2"} Var.`},{key:"crypto1Var",label:`${t.crypto1||"Crypto 1"} Var.`},{key:"targetPrice",label:"Target Price"},{key:"percentFromActualPrice",label:"% from Actual"},{key:"incomeCrypto1",label:`Income ${t.crypto1||"Crypto 1"}`},{key:"incomeCrypto2",label:`Income ${t.crypto2||"Crypto 2"}`},{key:"originalCostCrypto2",label:`Original Cost ${t.crypto2||"Crypto 2"}`}];return(0,a.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,a.jsx)("div",{className:"w-full overflow-x-auto whitespace-nowrap",children:(0,a.jsxs)(n,{className:"min-w-full",children:[(0,a.jsx)(i,{children:(0,a.jsx)(p,{className:"bg-card hover:bg-card",children:u.map(e=>(0,a.jsx)(x,{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm",children:e.label},e.key))})}),(0,a.jsx)(c,{children:0===s.length?(0,a.jsx)(p,{children:(0,a.jsx)(y,{colSpan:u.length,className:"h-24 text-center text-muted-foreground",children:'No target prices set. Use "Set Target Prices" in the sidebar.'})}):s.map(e=>(0,a.jsxs)(p,{className:"hover:bg-card/80",children:[(0,a.jsx)(y,{className:"px-3 py-2 text-xs",children:e.counter}),(0,a.jsx)(y,{className:"px-3 py-2 text-xs",children:(0,a.jsx)(h.E,{variant:"Full"===e.status?"default":"secondary",className:(0,d.cn)("Full"===e.status?"bg-green-600 text-white":"bg-yellow-500 text-black","font-bold"),children:e.status})}),(0,a.jsx)(y,{className:"px-3 py-2 text-xs",children:e.orderLevel}),(0,a.jsx)(y,{className:"px-3 py-2 text-xs",children:o(e.valueLevel)}),(0,a.jsx)(y,{className:(0,d.cn)("px-3 py-2 text-xs",e.crypto2Var&&e.crypto2Var<0?"text-destructive":"text-green-400"),children:o(e.crypto2Var,!0)}),(0,a.jsx)(y,{className:(0,d.cn)("px-3 py-2 text-xs",e.crypto1Var&&e.crypto1Var<0?"text-destructive":"text-green-400"),children:o(e.crypto1Var,!0)}),(0,a.jsx)(y,{className:"px-3 py-2 text-xs font-semibold text-primary",children:o(e.targetPrice)}),(0,a.jsx)(y,{className:(0,d.cn)("px-3 py-2 text-xs",e.percentFromActualPrice<0?"text-destructive":"text-green-400"),children:m(e.percentFromActualPrice)}),(0,a.jsx)(y,{className:(0,d.cn)("px-3 py-2 text-xs",e.incomeCrypto1&&e.incomeCrypto1<0?"text-destructive":"text-green-400"),children:o(e.incomeCrypto1)}),(0,a.jsx)(y,{className:(0,d.cn)("px-3 py-2 text-xs",e.incomeCrypto2&&e.incomeCrypto2<0?"text-destructive":"text-green-400"),children:o(e.incomeCrypto2)}),(0,a.jsx)(y,{className:"px-3 py-2 text-xs",children:o(e.originalCostCrypto2)})]},e.id))})]})})})}var u=r(7079),b=r(428),f=r(8751);function g(){let{config:e,currentMarketPrice:t}=(0,l.U)(),r=t=>t.toFixed(e.numDigits),s=e.crypto1&&e.crypto2,o="Crypto 1/Crypto 2",d="0",n="$";return s&&t>0&&("StablecoinSwap"===e.tradingMode?(o=`${e.crypto1}/${e.crypto2}`,d=r(t),n=""):(o=`${e.crypto1}/${e.crypto2}`,d=r(t),n="$")),(0,a.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-green-500"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Current Market Price",(0,a.jsxs)("span",{className:"ml-1 text-xs",children:["(","StablecoinSwap"===e.tradingMode?"Stablecoin Swap":"Simple Spot",")"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-foreground",children:[o,":"]}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-primary",children:[n,d]})]})]})})}function k(){let{config:e,saveCurrentSession:t,targetPriceRows:r,orderHistory:d}=(0,l.U)(),[n,i]=(0,s.useState)(""),[c,p]=(0,s.useState)(null),x=n||(e.crypto1&&e.crypto2?`${e.crypto1}/${e.crypto2} ${e.tradingMode||"SimpleSpot"}`:"Crypto 1/Crypto 2 = 0");return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(u.A,{}),(0,a.jsx)(b.A,{}),(0,a.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"text-2xl font-bold text-primary",children:["Active Orders (",x,")"]}),(0,a.jsx)(o.BT,{children:"Current state of your target price levels. Prices update in real-time."})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)(g,{}),(0,a.jsx)(m,{})]})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3341:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},3873:e=>{"use strict";e.exports=require("path")},3886:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},4026:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4610:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},5036:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5371:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},5511:e=>{"use strict";e.exports=require("crypto")},6287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>i});var a=r(5239),s=r(8088),o=r(8170),l=r.n(o),d=r(893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(t,n);let i={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,559)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,3144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},7457:(e,t,r)=>{Promise.resolve().then(r.bind(r,559))},7729:(e,t,r)=>{Promise.resolve().then(r.bind(r,3014))},8450:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},8751:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9272:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9497:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},9812:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]])},9892:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Bitcoin",[["path",{d:"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727",key:"yr8idg"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[641,606,271,12],()=>r(6287));module.exports=a})();