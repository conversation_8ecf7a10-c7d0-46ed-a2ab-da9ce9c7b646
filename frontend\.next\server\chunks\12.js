exports.id=12,exports.ids=[12],exports.modules={13:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var s=t(687),a=t(3210),l=t(4780);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("label",{ref:t,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...r}));n.displayName="Label"},226:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>X});var s=t(687),a=t(3210),l=t(5814),n=t.n(l),i=t(6189),o=t(8417),c=t(9523),d=t(3213),p=t(4026),u=t(3886),m=t(196),x=t(9497),f=t(6834),h=t(4493),g=t(7857),b=t(5777),v=t(5036);function y({className:e=""}){let[r,t]=(0,a.useState)(!0),[l,n]=(0,a.useState)(new Date),[i,o]=(0,a.useState)(!1);return(0,s.jsxs)("div",{className:`flex items-center gap-2 ${e}`,children:[(0,s.jsxs)(f.E,{variant:r?"default":"destructive",className:`flex items-center gap-1 ${r?"bg-green-600 hover:bg-green-600/90 text-white":""}`,children:[r?(0,s.jsx)(g.A,{className:"h-3 w-3"}):(0,s.jsx)(b.A,{className:"h-3 w-3"}),r?"Online":"Offline"]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,s.jsx)(v.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:l.toLocaleTimeString()})]})]})}function j(){let{logout:e}=(0,d.A)();(0,i.useRouter)();let r=(0,i.usePathname)(),t=[{href:"/dashboard",label:"Home",icon:(0,s.jsx)(p.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,s.jsx)(u.A,{})}];return(0,s.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(o.A,{useFullName:!1}),(0,s.jsx)(y,{})]}),(0,s.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[t.map(e=>(0,s.jsx)(c.$,{variant:r===e.href?"default":"ghost",size:"sm",asChild:!0,className:`${r===e.href?"btn-neo":"hover:bg-accent/50"}`,children:(0,s.jsxs)(n(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,s.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,s.jsx)(m.A,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,s.jsx)(x.A,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var N=t(8895),S=t(9667),C=t(13),w=t(8450),A=t(4780);let T=a.forwardRef(({className:e,checked:r,onCheckedChange:t,onChange:a,...l},n)=>(0,s.jsxs)("div",{className:"relative inline-flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",ref:n,className:"sr-only",checked:r,onChange:e=>{let r=e.target.checked;t?.(r),a?.(e)},...l}),(0,s.jsx)("div",{className:(0,A.cn)("h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer",r&&"bg-primary text-primary-foreground",e),onClick:()=>{if(!l.disabled){let e=!r;t?.(e)}},children:r&&(0,s.jsx)("div",{className:"flex items-center justify-center text-current",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})})]}));T.displayName="Checkbox";var E=t(5079),R=t(5950),D=t(3503);let O=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("textarea",{className:(0,A.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...r}));O.displayName="Textarea";var P=t(5763);function k({isOpen:e,onClose:r,onSetTargetPrices:t}){let l;let[n,i]=(0,a.useState)("manual"),[o,d]=(0,a.useState)("");try{l=(0,N.U)()}catch(e){l=null}let[p,u]=(0,a.useState)("8"),[m,x]=(0,a.useState)("5"),[f,h]=(0,a.useState)("even"),g=l?.currentMarketPrice||1e5,b=l?.config?.slippagePercent||.2,v=()=>{let e=parseInt(p),r=parseFloat(m);if(!e||e<2||e>20||!r||r<=0)return[];let t=[],s=g*(1-r/100),a=g*(1+r/100);if("even"===f)for(let r=0;r<e;r++){let l=s+r/(e-1)*(a-s);t.push(Math.round(l))}else if("fibonacci"===f){let r=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let l=0;l<e;l++){let n=s+(a-s)*(r[Math.min(l,r.length-1)]||l/(e-1));t.push(Math.round(n))}}else if("exponential"===f)for(let r=0;r<e;r++){let l=s+(a-s)*Math.pow(r/(e-1),1.5);t.push(Math.round(l))}let l=3*b/100*g,n=t.sort((e,r)=>e-r),i=[];for(let e=0;e<n.length;e++){let r=n[e];if(i.length>0){let e=i[i.length-1];r-e<l&&(r=e+l)}i.push(Math.round(r))}return i},y=()=>{let e=o.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,r)=>e-r);if(e.length<2)return{hasOverlap:!1,message:""};let r=b/100*g;for(let t=0;t<e.length-1;t++)if(e[t]+r>=e[t+1]-r){let s=2*r,a=e[t+1]-e[t];return{hasOverlap:!0,message:`Overlap detected between ${e[t]} and ${e[t+1]}. Minimum gap needed: ${s.toFixed(0)}, actual gap: ${a.toFixed(0)}`}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},j=y();return(0,s.jsx)(D.lG,{open:e,onOpenChange:r,children:(0,s.jsxs)(D.Cf,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,s.jsxs)(D.c7,{children:[(0,s.jsx)(D.L3,{className:"text-primary",children:"Set Target Prices"}),(0,s.jsx)(D.rr,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,s.jsxs)(P.tU,{value:n,onValueChange:i,className:"w-full",children:[(0,s.jsxs)(P.j7,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(P.Xi,{value:"manual",children:"Manual Entry"}),(0,s.jsx)(P.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,s.jsx)(P.av,{value:"manual",className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(C.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,s.jsx)(O,{id:"target-prices-input",value:o,onChange:e=>d(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),j.message&&(0,s.jsx)("p",{className:`text-sm ${j.hasOverlap?"text-red-500":"text-green-500"}`,children:j.message})]})}),(0,s.jsxs)(P.av,{value:"automatic",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(C.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,s.jsxs)(E.l6,{value:p,onValueChange:u,children:[(0,s.jsx)(E.bq,{children:(0,s.jsx)(E.yv,{})}),(0,s.jsx)(E.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,s.jsxs)(E.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(C.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,s.jsxs)(E.l6,{value:m,onValueChange:x,children:[(0,s.jsx)(E.bq,{children:(0,s.jsx)(E.yv,{})}),(0,s.jsx)(E.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,s.jsxs)(E.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(C.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,s.jsxs)(E.l6,{value:f,onValueChange:h,children:[(0,s.jsx)(E.bq,{children:(0,s.jsx)(E.yv,{})}),(0,s.jsxs)(E.gC,{children:[(0,s.jsx)(E.eb,{value:"even",children:"Even Distribution"}),(0,s.jsx)(E.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,s.jsx)(E.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,s.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Current Market Price:"})," $",g.toLocaleString(),(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Slippage:"})," \xb1",b,"% ($",(g*b/100).toFixed(0),")",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Range:"})," $",(g*(1-parseFloat(m)/100)).toLocaleString()," - $",(g*(1+parseFloat(m)/100)).toLocaleString()]})}),(0,s.jsxs)(c.$,{onClick:()=>{d(v().join("\n"))},className:"w-full btn-neo",children:["Generate ",p," Target Prices"]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(C.J,{children:"Generated Prices (Preview)"}),(0,s.jsx)(O,{value:o,onChange:e=>d(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),j.message&&(0,s.jsx)("p",{className:`text-sm ${j.hasOverlap?"text-red-500":"text-green-500"}`,children:j.message})]})]})]}),(0,s.jsxs)(D.Es,{children:[(0,s.jsx)(D.HM,{asChild:!0,children:(0,s.jsx)(c.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,s.jsx)(c.$,{type:"button",onClick:()=>{let e=o.split("\n").map(e=>e.trim()).filter(e=>""!==e),s=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);(0!==s.length||!(e.length>0))&&!y().hasOverlap&&(t(s),d(""),r())},disabled:j.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var B=t(5280),U=t(8726),F=t(2963);function L({label:e,value:r,allowedCryptos:t,onValidCrypto:l,placeholder:n="Enter crypto symbol",description:i,className:o}){let[d,p]=(0,a.useState)(""),[u,m]=(0,a.useState)("idle"),[x,f]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),b=()=>{let e=d.toUpperCase().trim();if(!e){m("invalid"),f("Please enter a crypto symbol");return}if(!t||!Array.isArray(t)){m("invalid"),f("No allowed cryptocurrencies configured");return}t.includes(e)?(m("valid"),f(""),g(!0),l(e)):(m("invalid"),f(`${e} is not available. Allowed: ${t.join(", ")}`))};return(0,s.jsxs)("div",{className:(0,A.cn)("space-y-2",o),children:[(0,s.jsx)(C.J,{htmlFor:`crypto-input-${e}`,children:e}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(S.p,{id:`crypto-input-${e}`,value:d||r,onChange:e=>{p(e.target.value),m("idle"),f("")},onKeyPress:e=>{"Enter"===e.key&&b()},placeholder:n,className:(0,A.cn)("pr-8",(()=>{switch(u){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==u&&(0,s.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(u){case"valid":return(0,s.jsx)(w.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,s.jsx)(U.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,s.jsx)(c.$,{onClick:b,variant:"outline",className:"btn-neo",disabled:!d.trim(),children:"Check"})]}),r&&h&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 text-green-500"}),(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",r]})]}),"invalid"===u&&x&&(0,s.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,s.jsx)(F.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,s.jsx)("span",{children:x})]}),i&&(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:i}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t&&Array.isArray(t)?t.length:0," cryptocurrencies available"]})]})}var M=t(2375),I=t(1516),$=t(5371),G=t(1305);let V=["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],_=["USDC","DAI","TUSD","FDUSD","USDT","EUR"],z=["USDT","USDC","BTC"],J=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function H(){let e;try{e=(0,N.U)()}catch(e){return(0,s.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:r,dispatch:t,botSystemStatus:l,appSettings:n,setTargetPrices:i}=e,o="Running"===l,d="WarmingUp"===l,[p,u]=(0,a.useState)(!1),m=e=>{let r;let{name:s,value:a,type:l,checked:n}=e.target;if("checkbox"===l)r=n;else if("number"===l){if(""===a||null==a)r=0;else{let e=parseFloat(a);r=isNaN(e)?0:e}}else r=a;t({type:"SET_CONFIG",payload:{[s]:r}})},x=(e,s)=>{if(t({type:"SET_CONFIG",payload:{[e]:s}}),"crypto1"===e){let e=B.vA[s]||z||["USDT","USDC","BTC"];r.crypto2&&Array.isArray(e)&&e.includes(r.crypto2)||t({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},f=(e,r)=>{let s=parseFloat(r);isNaN(s)&&(s=0),s<0&&(s=0),s>100&&(s=100),"incomeSplitCrypto1Percent"===e?t({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:s,incomeSplitCrypto2Percent:100-s}}):t({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:s,incomeSplitCrypto1Percent:100-s}})};return B.hg,"SimpleSpot"===r.tradingMode?B.vA[r.crypto1]:(B.hg||[]).filter(e=>e!==r.crypto1),(0,s.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"})}),(0,s.jsx)("div",{className:"flex-1 pr-2 overflow-y-auto",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(h.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,s.jsxs)(h.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(T,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===r.tradingMode,onCheckedChange:e=>{let s;let a=e?"StablecoinSwap":"SimpleSpot";s="StablecoinSwap"===a?(V||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==r.crypto1)[0]:(_||["USDC","DAI","TUSD","FDUSD","USDT","EUR"])[0],t({type:"SET_CONFIG",payload:{tradingMode:a,crypto2:s}})}}),(0,s.jsx)(C.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===r.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===r.tradingMode&&(0,s.jsxs)("div",{children:[(0,s.jsx)(C.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,s.jsxs)(E.l6,{value:r.preferredStablecoin,onValueChange:e=>x("preferredStablecoin",e),children:[(0,s.jsx)(E.bq,{id:"preferredStablecoin",children:(0,s.jsx)(E.yv,{placeholder:"Select stablecoin"})}),(0,s.jsx)(E.gC,{children:J.map(e=>(0,s.jsx)(E.eb,{value:e,children:e},e))})]})]})]})]}),(0,s.jsxs)(h.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsx)(L,{label:"Crypto 1 (Base)",value:r.crypto1,allowedCryptos:V||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],onValidCrypto:e=>{if(t({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===r.tradingMode){let s=(V||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(r=>r!==e);s.includes(r.crypto2)&&r.crypto2!==e||t({type:"SET_CONFIG",payload:{crypto2:s[0]}})}else{let e=_||["USDC","DAI","TUSD","FDUSD","USDT","EUR"];e.includes(r.crypto2)||t({type:"SET_CONFIG",payload:{crypto2:e[0]}})}},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,s.jsx)(L,{label:"StablecoinSwap"===r.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:r.crypto2,allowedCryptos:"StablecoinSwap"===r.tradingMode?(V||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==r.crypto1):_||["USDC","DAI","TUSD","FDUSD","USDT","EUR"],onValidCrypto:e=>{("StablecoinSwap"!==r.tradingMode||e!==r.crypto1)&&t({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===r.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===r.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,s.jsxs)(h.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,s.jsxs)(h.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,s.jsxs)("div",{children:[(0,s.jsx)(C.J,{htmlFor:e.name,children:e.label}),(0,s.jsx)(S.p,{id:e.name,name:e.name,type:e.type,value:r[e.name],onChange:m,step:e.step,min:"0"})]},e.name)),(0,s.jsxs)("div",{children:[(0,s.jsx)(C.J,{children:"Couple Income % Split (must sum to 100)"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(C.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[r.crypto1||"Crypto 1","%"]}),(0,s.jsx)(S.p,{id:"incomeSplitCrypto1Percent",type:"number",value:r.incomeSplitCrypto1Percent,onChange:e=>f("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(C.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[r.crypto2||"Crypto 2","%"]}),(0,s.jsx)(S.p,{id:"incomeSplitCrypto2Percent",type:"number",value:r.incomeSplitCrypto2Percent,onChange:e=>f("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,s.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,s.jsx)(R.w,{className:"mb-4 bg-sidebar-border"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:(0,A.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",o?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[o?(0,s.jsx)(M.A,{className:"h-4 w-4"}):d?(0,s.jsx)(I.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)($.A,{className:"h-4 w-4"}),"Bot Status: ",o?"Running":d?"Warming Up":"Stopped"]}),(0,s.jsx)(c.$,{onClick:()=>u(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,s.jsxs)(c.$,{onClick:()=>{o?t({type:"SYSTEM_STOP_BOT"}):t({type:"SYSTEM_START_BOT_INITIATE"})},className:(0,A.cn)("w-full btn-neo",o||d?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:d,children:[o?(0,s.jsx)(M.A,{className:"h-4 w-4"}):d?(0,s.jsx)(I.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)($.A,{className:"h-4 w-4"}),o?"Stop Bot":d?"Warming Up...":"Start Bot"]}),(0,s.jsxs)(c.$,{onClick:()=>{t({type:"SYSTEM_RESET_BOT"})},variant:"outline",className:"w-full btn-outline-neo",disabled:d,children:[(0,s.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,s.jsx)(k,{isOpen:p,onClose:()=>u(!1),onSetTargetPrices:i})]})}function X({children:e}){let{isAuthenticated:r,isLoading:t}=(0,d.A)();return((0,i.useRouter)(),t)?(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,s.jsx)(I.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Loading..."})]}):r?(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,s.jsx)(j,{}),(0,s.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,s.jsx)(H,{}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:e})]})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,s.jsx)(I.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting..."})]})}},428:(e,r,t)=>{"use strict";t.d(r,{A:()=>f});var s=t(687),a=t(3210),l=t(8895),n=t(4493),i=t(9667),o=t(9523),c=t(8450),d=t(8726),p=t(1840),u=t(9892),m=t(1277),x=t(9812);function f(){let{crypto1Balance:e,crypto2Balance:r,stablecoinBalance:t,config:f,dispatch:h}=(0,l.U)(),[g,b]=(0,a.useState)(null),[v,y]=(0,a.useState)({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()}),j=e=>e.toFixed(f.numDigits),N=s=>{b(s),y({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()})},S=t=>{let s=parseFloat(v[t]);!isNaN(s)&&s>=0&&("crypto1"===t?h({type:"UPDATE_BALANCES",payload:{crypto1:s,crypto2:r}}):"crypto2"===t?h({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:s}}):"stablecoin"===t&&h({type:"UPDATE_STABLECOIN_BALANCE",payload:s})),b(null)},C=()=>{b(null),y({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()})},w=(e,r,t,a,l)=>(0,s.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),a]}),(0,s.jsx)(n.Wu,{children:g===t?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.p,{type:"number",value:v[t],onChange:e=>y(r=>({...r,[t]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(o.$,{size:"sm",onClick:()=>S(t),className:"flex-1",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:C,className:"flex-1",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:j(r)}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",l]})]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>N(t),className:"ml-2",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})})]});return(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-3 mb-6",children:[w(`${f.crypto1||"Crypto 1"} Balance`,e,"crypto1",(0,s.jsx)(u.A,{className:"h-5 w-5 text-primary"}),f.crypto1||"Crypto 1"),w(`${f.crypto2||"Crypto 2"} Balance`,r,"crypto2",(0,s.jsx)(m.A,{className:"h-5 w-5 text-primary"}),f.crypto2||"Crypto 2"),w(`Stablecoin Balance (${f.preferredStablecoin||"N/A"})`,t,"stablecoin",(0,s.jsx)(x.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})}},674:(e,r,t)=>{Promise.resolve().then(t.bind(t,3144))},3144:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx","default")},3503:(e,r,t)=>{"use strict";t.d(r,{Cf:()=>u,Es:()=>x,HM:()=>d,L3:()=>f,c7:()=>m,lG:()=>o,rr:()=>h});var s=t(687),a=t(3210),l=t(8726),n=t(4780);let i=a.createContext({open:!1,onOpenChange:()=>{}}),o=({children:e,open:r=!1,onOpenChange:t})=>{let[l,n]=a.useState(r);return a.useEffect(()=>{n(r)},[r]),(0,s.jsx)(i.Provider,{value:{open:l,onOpenChange:e=>{n(e),t?.(e)}},children:e})};a.forwardRef(({className:e,children:r,asChild:t=!1,...l},n)=>{let{onOpenChange:o}=a.useContext(i);return(0,s.jsx)("button",{ref:n,className:e,onClick:()=>o(!0),...l,children:r})}).displayName="DialogTrigger";let c=({children:e})=>(0,s.jsx)(s.Fragment,{children:e}),d=a.forwardRef(({className:e,children:r,asChild:t=!1,...l},n)=>{let{onOpenChange:o}=a.useContext(i);return(0,s.jsx)("button",{ref:n,className:e,onClick:()=>o(!1),...l,children:r})});d.displayName="DialogClose";let p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80",e),...r}));p.displayName="DialogOverlay";let u=a.forwardRef(({className:e,children:r,...t},o)=>{let{open:d,onOpenChange:u}=a.useContext(i);return d?(0,s.jsxs)(c,{children:[(0,s.jsx)(p,{onClick:()=>u(!1)}),(0,s.jsxs)("div",{ref:o,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg",e),...t,children:[r,(0,s.jsxs)("button",{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",onClick:()=>u(!1),children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}):null});u.displayName="DialogContent";let m=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...r}));m.displayName="DialogHeader";let x=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...r}));x.displayName="DialogFooter";let f=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h2",{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...r}));f.displayName="DialogTitle";let h=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));h.displayName="DialogDescription"},4493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=t(687),a=t(3210),l=t(4780);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...r}));n.displayName="Card";let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...r}));i.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-4 md:p-6 pt-0",e),...r}));d.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-4 md:p-6 pt-0",e),...r})).displayName="CardFooter"},5079:(e,r,t)=>{"use strict";t.d(r,{bq:()=>c,eb:()=>p,gC:()=>d,l6:()=>o,yv:()=>u});var s=t(687),a=t(3210),l=t(1662),n=t(4780);let i=a.createContext({value:"",onValueChange:()=>{},open:!1,setOpen:()=>{}}),o=a.forwardRef(({children:e,value:r,onValueChange:t,defaultValue:l,...n},o)=>{let[c,d]=a.useState(r||l||""),[p,u]=a.useState(!1);return a.useEffect(()=>{void 0!==r&&d(r)},[r]),(0,s.jsx)(i.Provider,{value:{value:c,onValueChange:e=>{void 0===r&&d(e),t?.(e),u(!1)},open:p,setOpen:u},children:(0,s.jsx)("div",{ref:o,className:"relative",...n,children:e})})});o.displayName="Select";let c=a.forwardRef(({className:e,children:r,...t},o)=>{let{open:c,setOpen:d}=a.useContext(i);return(0,s.jsxs)("button",{ref:o,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),onClick:()=>d(!c),...t,children:[r,(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})]})});c.displayName="SelectTrigger";let d=a.forwardRef(({className:e,children:r,...t},l)=>{let{open:o}=a.useContext(i);return o?(0,s.jsx)("div",{ref:l,className:(0,n.cn)("absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md",e),...t,children:r}):null});d.displayName="SelectContent";let p=a.forwardRef(({className:e,children:r,value:t,...l},o)=>{let{onValueChange:c}=a.useContext(i);return(0,s.jsx)("button",{ref:o,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",e),onClick:()=>c(t),...l,children:r})});p.displayName="SelectItem";let u=a.forwardRef(({placeholder:e,...r},t)=>{let{value:l}=a.useContext(i);return(0,s.jsx)("span",{ref:t,...r,children:l||e})});u.displayName="SelectValue"},5763:(e,r,t)=>{"use strict";t.d(r,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>i});var s=t(687),a=t(3210),l=t(4780);let n=a.createContext({activeTab:"",setActiveTab:()=>{}}),i=a.forwardRef(({defaultValue:e="",value:r,onValueChange:t,children:l,className:i,...o},c)=>{let[d,p]=a.useState(r||e);return a.useEffect(()=>{void 0!==r&&p(r)},[r]),(0,s.jsx)(n.Provider,{value:{activeTab:d,setActiveTab:e=>{void 0===r&&p(e),t?.(e)}},children:(0,s.jsx)("div",{ref:c,className:i,...o,children:l})})});i.displayName="Tabs";let o=a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t,children:r}));o.displayName="TabsList";let c=a.forwardRef(({className:e,value:r,children:t,onClick:i,...o},c)=>{let{activeTab:d,setActiveTab:p}=a.useContext(n);return(0,s.jsx)("button",{ref:c,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",d===r&&"bg-background text-foreground shadow-sm",e),onClick:()=>{p(r),i?.()},...o,children:t})});c.displayName="TabsTrigger";let d=a.forwardRef(({className:e,value:r,children:t,...i},o)=>{let{activeTab:c}=a.useContext(n);return c!==r?null:(0,s.jsx)("div",{ref:o,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...i,children:t})});d.displayName="TabsContent"},5950:(e,r,t)=>{"use strict";t.d(r,{w:()=>n});var s=t(687),a=t(3210),l=t(4780);let n=a.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...a},n)=>(0,s.jsx)("div",{ref:n,role:t?"none":"separator","aria-orientation":r,className:(0,l.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...a}));n.displayName="Separator"},6834:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(687),a=t(3210),l=t(4780);let n={variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},i=a.forwardRef(({className:e,variant:r="default",...t},a)=>{let i=n.variant[r]||n.variant.default;return(0,s.jsx)("div",{ref:a,className:(0,l.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",i,e),...t})});i.displayName="Badge"},7079:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var s=t(687);t(3210);var a=t(5763),l=t(6189),n=t(9272),i=t(4610),o=t(3341);let c=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,s.jsx)(n.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,s.jsx)(i.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,s.jsx)(o.A,{})}];function d(){let e=(0,l.useRouter)(),r=(0,l.usePathname)(),t="orders";return"/dashboard/history"===r?t="history":"/dashboard/analytics"===r&&(t="analytics"),(0,s.jsx)(a.tU,{value:t,onValueChange:r=>{let t=c.find(e=>e.value===r);t&&e.push(t.href)},className:"w-full mb-6",children:(0,s.jsx)(a.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:c.map(e=>(0,s.jsx)(a.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},8417:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(687);t(3210);var a=t(474);let l=({className:e,useFullName:r=!0})=>(0,s.jsxs)("div",{className:`flex items-center text-2xl font-bold text-primary ${e}`,children:[(0,s.jsx)(a.default,{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",width:28,height:28,className:"mr-2 rounded-sm"}),(0,s.jsxs)("span",{children:["Pluto",r?" Trading Bot":""]})]})},8826:(e,r,t)=>{Promise.resolve().then(t.bind(t,226))},9523:(e,r,t)=>{"use strict";t.d(r,{$:()=>i});var s=t(687),a=t(3210),l=t(4780);let n={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},i=a.forwardRef(({className:e,variant:r="default",size:t="default",asChild:a=!1,...i},o)=>{let c=n.variant[r]||n.variant.default,d=n.size[t]||n.size.default;return(0,s.jsx)(a?"span":"button",{className:(0,l.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",c,d,e),ref:o,...i})});i.displayName="Button"},9667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(687),a=t(3210),l=t(4780);let n=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"}};