exports.id=271,exports.ids=[271],exports.modules={1129:(e,t,r)=>{"use strict";r.d(t,{ClientProviders:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call ClientProviders() from the server but ClientProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\components\\ClientProviders.tsx","ClientProviders")},1135:()=>{},2185:(e,t,r)=>{"use strict";async function s(e,t={}){let r=`http://localhost:5000${e}`,o=localStorage.getItem("plutoAuthToken"),n={"Content-Type":"application/json",...o?{Authorization:`Bearer ${o}`}:{},...t.headers};try{let e;let s=new AbortController,o=setTimeout(()=>s.abort(),1e4),a=await fetch(r,{...t,headers:n,signal:s.signal}).finally(()=>clearTimeout(o));if(401===a.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),Error("Authentication expired. Please login again.");let i=a.headers.get("content-type");if(i&&i.includes("application/json"))e=await a.json();else{let t=await a.text();try{e=JSON.parse(t)}catch(r){e={message:t}}}if(!a.ok){let t=e?.error||e?.message||`HTTP ${a.status}: ${a.statusText}`;if(401===a.status||422===a.status)return null;throw Error(t)}return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw Error("Request timed out. Server may be unavailable.");throw e}}r.d(t,{Rk:()=>a,ZQ:()=>o,oc:()=>n});let o={login:async(e,t)=>{try{let r=await i(async()=>await s("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(r&&r.access_token)return localStorage.setItem("plutoAuthToken",r.access_token),localStorage.setItem("plutoAuth","true"),r.user&&localStorage.setItem("plutoUser",JSON.stringify(r.user)),!0;return!1}catch(e){return!1}},register:async(e,t,r)=>i(async()=>await s("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:r})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},n={getConfig:async e=>s(e?`/trading/config/${e}`:"/trading/config"),saveConfig:async e=>s("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>s(`/trading/config/${e}`,{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>s(`/trading/bot/start/${e}`,{method:"POST"}),stopBot:async e=>s(`/trading/bot/stop/${e}`,{method:"POST"}),getBotStatus:async e=>s(`/trading/bot/status/${e}`),getTradeHistory:async e=>{let t=e?`?configId=${e}`:"";return s(`/trading/history${t}`)},getBalances:async()=>s("/trading/balances"),getMarketPrice:async e=>s(`/trading/market-data/${e}`),getTradingPairs:async(e="binance")=>s(`/trading/exchange/trading-pairs?exchange=${e}`),getCryptocurrencies:async(e="binance")=>s(`/trading/exchange/cryptocurrencies?exchange=${e}`)},a={getAllSessions:async(e=!0)=>s(`/sessions/?include_inactive=${e}`),createSession:async e=>s("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>s(`/sessions/${e}`),updateSession:async(e,t)=>s(`/sessions/${e}`,{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>s(`/sessions/${e}`,{method:"DELETE"}),activateSession:async e=>s(`/sessions/${e}/activate`,{method:"POST"}),getSessionHistory:async e=>s(`/sessions/${e}/history`),getActiveSession:async()=>s("/sessions/active")},i=async(e,t=3)=>{let r=0,s=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&r<t){let e=500*Math.pow(2,r);return r++,await new Promise(t=>setTimeout(t,e)),s()}throw e}};return s()}},2694:(e,t,r)=>{"use strict";r.d(t,{d:()=>p,t:()=>d});var s=r(687),o=r(3210),n=r(3662),a=r(2963),i=r(9196),c=r(8726),l=r(4780);let u=(0,o.createContext)(void 0);function d({children:e}){let[t,r]=(0,o.useState)([]),n=(0,o.useCallback)(e=>{let t=Math.random().toString(36).substr(2,9),s={...e,id:t,duration:e.duration??2e3};r(e=>[...e,s]),setTimeout(()=>{a(t)},s.duration)},[]),a=(0,o.useCallback)(e=>{r(t=>t.filter(t=>t.id!==e))},[]);return(0,s.jsxs)(u.Provider,{value:{toasts:t,toast:n,dismiss:a},children:[e,(0,s.jsx)(y,{})]})}function p(){let e=(0,o.useContext)(u);if(!e)throw Error("useToast must be used within a ToastProvider");return e}function y(){let{toasts:e,dismiss:t}=p();return(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(e=>(0,s.jsx)(h,{toast:e,onDismiss:t},e.id))})}function h({toast:e,onDismiss:t}){let[r,u]=(0,o.useState)(!1);return(0,s.jsxs)("div",{className:(0,l.cn)("flex items-start gap-3 p-4 bg-white border-2 rounded-lg shadow-lg transition-all duration-150 ease-out min-w-[300px] max-w-[400px]",(()=>{switch(e.type){case"success":return"border-green-200";case"error":return"border-red-200";case"warning":return"border-yellow-200";default:return"border-blue-200"}})(),r?"translate-x-0 opacity-100":"translate-x-full opacity-0"),children:[(()=>{switch(e.type){case"success":return(0,s.jsx)(n.A,{className:"h-4 w-4 text-green-500"});case"error":return(0,s.jsx)(a.A,{className:"h-4 w-4 text-red-500"});case"warning":return(0,s.jsx)(a.A,{className:"h-4 w-4 text-yellow-500"});default:return(0,s.jsx)(i.A,{className:"h-4 w-4 text-blue-500"})}})(),(0,s.jsxs)("div",{className:"flex-1 space-y-1",children:[e.title&&(0,s.jsx)("div",{className:"font-semibold text-sm text-gray-900",children:e.title}),e.description&&(0,s.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,s.jsx)("button",{onClick:()=>{u(!1),setTimeout(()=>t(e.id),150)},className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})]})}},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,O:()=>l});var s=r(687),o=r(3210),n=r(6189),a=r(2185),i=r(5551);let c=(0,o.createContext)(void 0),l=({children:e})=>{let[t,l]=(0,o.useState)(!1),[u,d]=(0,o.useState)(!0),[p,y]=(0,o.useState)(!1),h=(0,n.useRouter)(),g=(0,n.usePathname)();(0,o.useEffect)(()=>{(async()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");if("true"===e&&t){l(!0),d(!1),y(!0);try{i.SessionManager.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}return}try{await new Promise(e=>setTimeout(e,1e3));let e=await fetch("http://localhost:5000/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"testuser",password:"password123"})});if(e.ok){let t=await e.json();localStorage.setItem("plutoAuth","true"),localStorage.setItem("plutoAuthToken",t.access_token),localStorage.setItem("plutoRefreshToken",t.refresh_token),localStorage.setItem("plutoUser",JSON.stringify(t.user)),l(!0);try{let{SessionManager:e}=await Promise.resolve().then(r.bind(r,5551));e.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}}}catch(e){}d(!1),y(!0)})();let e=setTimeout(()=>{d(!1),y(!0)},1e4);return()=>clearTimeout(e)},[]),(0,o.useEffect)(()=>{t&&("/login"===g||"/"===g)?h.replace("/dashboard"):t||"/login"===g||"/"===g||h.replace("/login")},[t,g,h]);let S=async(e,t)=>{d(!0);try{if(await a.ZQ.login(e,t))return l(!0),h.push("/dashboard"),!0;return l(!1),!1}catch(e){return l(!1),!1}finally{d(!1)}},m=async()=>{try{await a.ZQ.logout()}catch(e){}finally{l(!1),h.push("/login")}};return(0,s.jsx)(c.Provider,{value:{isAuthenticated:t,login:S,logout:m,isLoading:u},children:e})},u=()=>{let e=(0,o.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>i});var s=r(7413),o=r(7653),n=r.n(o);r(1135);var a=r(1129);let i={title:"Pluto Trading Bot",description:"Simulated cryptocurrency trading bot with Neo Brutalist UI."};function c({children:e}){return(0,s.jsxs)("html",{lang:"en",className:n().variable,suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"preload",href:"/ringtones/cheer.wav",as:"audio"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://api.coingecko.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://i.imgur.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"http://localhost:5000"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"//api.coingecko.com"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"//i.imgur.com"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"//localhost"}),(0,s.jsx)("link",{rel:"prefetch",href:"/dashboard"}),(0,s.jsx)("link",{rel:"prefetch",href:"/login"}),(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                if (typeof window === 'undefined') return;

                // Chunk loading retry mechanism
                const originalOnError = window.onerror;
                window.onerror = function(message, source, lineno, colno, error) {
                  if (
                    error?.name === 'ChunkLoadError' ||
                    (typeof message === 'string' && message.includes('Loading chunk'))
                  ) {
                    console.warn('Chunk loading error detected, reloading page...');
                    setTimeout(() => window.location.reload(), 1000);
                    return true;
                  }
                  if (originalOnError) return originalOnError.call(window, message, source, lineno, colno, error);
                  return false;
                };

                // Handle unhandled promise rejections
                window.addEventListener('unhandledrejection', function(event) {
                  const error = event.reason;
                  if (
                    error?.name === 'ChunkLoadError' ||
                    (error?.message && error.message.includes('Loading chunk'))
                  ) {
                    console.warn('Chunk loading promise rejection, reloading page...');
                    event.preventDefault();
                    setTimeout(() => window.location.reload(), 1000);
                  }
                });
              })();
            `}})]}),(0,s.jsxs)("body",{className:"font-sans antialiased",suppressHydrationWarning:!0,children:[" ",(0,s.jsx)(a.ClientProviders,{children:e})]})]})}},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(9384),o=r(2348);function n(...e){return(0,o.QP)((0,s.$)(e))}},5019:(e,t,r)=>{"use strict";r.d(t,{ClientProviders:()=>i});var s=r(687),o=r(3213),n=r(8895),a=r(2694);function i({children:e}){return(0,s.jsx)(o.O,{children:(0,s.jsx)(n.r,{children:(0,s.jsx)(a.t,{children:e})})})}},5280:(e,t,r)=>{"use strict";r.d(t,{Oh:()=>s,Ql:()=>a,hg:()=>o,vA:()=>n});let s={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},o=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],n={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},a=["USDT","USDC","FDUSD","DAI"]},5551:(e,t,r)=>{"use strict";r.d(t,{SessionManager:()=>c});var s=r(4112),o=r(2185);let n={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/trade_success.mp3",soundError:"/sounds/trade_error.mp3"},a="pluto_current_session",i=()=>"server";class c{constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.sessionStartTimes=new Map,this.heartbeatInterval=null,this.windowId=i(),this.sessionStartTimes.clear(),this.useBackend=!1,this.loadSessionsFromStorage(),this.setupStorageListener(),this.setupBeforeUnloadHandler(),this.startHeartbeat()}static getInstance(){return c.instance||(c.instance=new c),c.instance}generateSessionName(e){let t=e.crypto1||"Crypto1",r=e.crypto2||"Crypto2",s=e.tradingMode||"SimpleSpot",o=`${t}/${r} ${s}`,n=Array.from(this.sessions.values()).filter(e=>e.name.startsWith(o));if(0===n.length)return o;let a=0;return n.forEach(e=>{let t=e.name.match(RegExp(`^${o.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")} Session (\\d+)$`));if(t){let e=parseInt(t[1],10);e>a&&(a=e)}else e.name===o&&(a=Math.max(a,1))}),`${o} Session ${a+1}`}async checkBackendConnectionWhenReady(){await this.checkBackendConnection()}async checkBackendConnection(){try{let e=localStorage.getItem("plutoAuthToken");if(!e){this.useBackend=!1,this.loadSessionsFromStorage();return}let t=new AbortController,r=setTimeout(()=>t.abort(),3e3),s=await fetch("http://localhost:5000/",{method:"GET",signal:t.signal,headers:{Authorization:`Bearer ${e}`}});if(clearTimeout(r),200===s.status)this.useBackend=!0,localStorage.getItem("plutoAuthToken")?await this.loadSessionsFromBackend():this.loadSessionsFromStorage();else if(401===s.status||422===s.status){this.useBackend=!1,this.loadSessionsFromStorage();return}else throw Error(`Backend returned error: ${s.status}`)}catch(e){this.useBackend=!1,"AbortError"!==e.name&&(e.message.includes("Authentication required")||e.message.includes("422")||e.message.includes("401")),this.loadSessionsFromStorage()}}getWindowSpecificKey(e){return`${e}_${this.windowId}`}setupStorageListener(){}setupBeforeUnloadHandler(){}startHeartbeat(){}cleanupStaleSessions(){let e=Date.now();this.sessions.forEach((t,r)=>{if(t.isActive){let s=[];for(let e=0;e<localStorage.length;e++){let t=localStorage.key(e);t&&t.startsWith(`pluto_heartbeat_${r}_`)&&s.push(t)}let o=!1;for(let t of s)if(e-parseInt(localStorage.getItem(t)||"0")<3e4){o=!0;break}o||(t.isActive=!1,t.lastModified=e,this.sessions.set(r,t),s.forEach(e=>localStorage.removeItem(e)))}}),this.saveSessionsToStorage()}async loadSessionsFromBackend(){if(!localStorage.getItem("plutoAuthToken")){this.loadSessionsFromStorage();return}try{let e=await o.Rk.getAllSessions();if(!e){this.loadSessionsFromStorage();return}e&&e.sessions&&(this.sessions.clear(),e.sessions.forEach(e=>{let t={id:e.session_uuid,name:e.name,config:e.config_snapshot,targetPriceRows:e.target_price_rows||[],orderHistory:e.order_history||[],currentMarketPrice:e.current_market_price||0,crypto1Balance:e.crypto1_balance||10,crypto2Balance:e.crypto2_balance||1e5,stablecoinBalance:e.stablecoin_balance||0,createdAt:new Date(e.created_at).getTime(),lastModified:new Date(e.last_modified||e.created_at).getTime(),isActive:e.is_active||!1,runtime:e.runtime_seconds?1e3*e.runtime_seconds:0,alarmSettings:e.alarm_settings||{...n}};this.sessions.set(e.session_uuid,t)}))}catch(e){e.message&&(e.message.includes("422")||e.message.includes("401")||e.message.includes("UNPROCESSABLE ENTITY")),this.loadSessionsFromStorage()}}loadSessionsFromStorage(){try{return}catch(e){}}async saveSessionToBackend(e,t){try{let r={name:t.name||"Untitled Session",config:t.config,targetPriceRows:t.targetPriceRows||[],currentMarketPrice:Number(t.currentMarketPrice)||0,crypto1Balance:Number(t.crypto1Balance)||0,crypto2Balance:Number(t.crypto2Balance)||0,stablecoinBalance:Number(t.stablecoinBalance)||0,isActive:!!t.isActive,additionalRuntime:Math.floor(Number(t.runtime||0)/1e3)};try{await o.Rk.updateSession(e,r)}catch(t){let e=t instanceof Error?t.message:String(t);if(e.includes("404")||e.includes("not found"))await o.Rk.createSession(r);else throw t}}catch(t){let e=t instanceof Error?t.message:String(t);throw t?.status,t?.response,e.includes("422")||e.includes("401")||e.includes("UNPROCESSABLE ENTITY"),t}}saveSessionsToStorage(){try{return}catch(e){}}async createNewSessionWithAutoName(e,t,r){let s=t||this.generateSessionName(e);return this.createNewSession(s,e,r)}async createNewSession(e,t,r){let a=r||{crypto1:10,crypto2:1e5,stablecoin:0};if(this.useBackend)try{let r=await o.Rk.createSession({name:e,config:t,targetPriceRows:[],currentMarketPrice:0,crypto1Balance:a.crypto1,crypto2Balance:a.crypto2,stablecoinBalance:a.stablecoin});if(!r||!r.session)throw Error("Invalid response from server: missing session data");let s=r.session.id,i=Date.now(),c={id:s,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:a.crypto1,crypto2Balance:a.crypto2,stablecoinBalance:a.stablecoin,createdAt:i,lastModified:i,isActive:!1,runtime:0,alarmSettings:{...n}};return this.sessions.set(s,c),s}catch(e){this.useBackend=!1}let i=(0,s.A)(),c=Date.now(),l={id:i,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:a.crypto1,crypto2Balance:a.crypto2,stablecoinBalance:a.stablecoin,createdAt:c,lastModified:c,isActive:!1,runtime:0,alarmSettings:{...n}};return this.sessions.set(i,l),this.saveSessionsToStorage(),i}saveSession(e,t,r,s,o,n,a,i,c=!1,l){try{let u;let d=this.sessions.get(e);if(!d)return!1;if(void 0!==l)u=l;else{u=d.runtime||0;let t=this.sessionStartTimes.get(e);t&&c?(u=(d.runtime||0)+(Date.now()-t),this.sessionStartTimes.set(e,Date.now())):!c&&t?(u=(d.runtime||0)+(Date.now()-t),this.sessionStartTimes.delete(e)):c&&!t&&this.sessionStartTimes.set(e,Date.now())}let p={...d,config:t,targetPriceRows:[...r],orderHistory:[...s],currentMarketPrice:o,crypto1Balance:n,crypto2Balance:a,stablecoinBalance:i,isActive:c,lastModified:Date.now(),runtime:u};return this.sessions.set(e,p),this.useBackend?this.saveSessionToBackend(e,p).catch(e=>{(e.message.includes("422")||e.message.includes("401")||e.message.includes("UNPROCESSABLE ENTITY"))&&(this.useBackend=!1),this.saveSessionsToStorage()}):this.saveSessionsToStorage(),!0}catch(e){return!1}}loadSession(e){return this.sessions.get(e)||null}deleteSession(e){let t=this.sessions.delete(e);if(t){if(this.currentSessionId===e){this.currentSessionId=null;let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}this.saveSessionsToStorage()}return t}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:`${e.config.crypto1}/${e.config.crypto2}`,createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)}))}setCurrentSession(e){if(this.sessions.has(e)){this.currentSessionId=e;let t=this.getWindowSpecificKey(a);localStorage.setItem(t,e);let r=this.sessions.get(e);r&&!r.isActive&&(r.isActive=!0,r.lastModified=Date.now(),this.sessions.set(e,r),this.saveSessionsToStorage())}}getCurrentSessionId(){return this.currentSessionId}clearCurrentSession(){if(this.currentSessionId){let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage())}this.currentSessionId=null}startSessionRuntime(e){this.sessionStartTimes.set(e,Date.now())}stopSessionRuntime(e){let t=this.sessionStartTimes.get(e);if(t){let r=this.sessions.get(e);if(r){let s=Date.now()-t;r.runtime=(r.runtime||0)+s,r.lastModified=Date.now(),this.sessions.set(e,r),this.saveSessionsToStorage()}this.sessionStartTimes.delete(e)}}deactivateSession(e){let t=this.sessions.get(e);t&&t.isActive&&(t.isActive=!1,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage())}getCurrentRuntime(e){let t=this.sessions.get(e);if(!t)return 0;let r=this.sessionStartTimes.get(e);return r?(t.runtime||0)+(Date.now()-r):t.runtime||0}exportSessionToJSON(e){let t=this.sessions.get(e);return t?JSON.stringify(t,null,2):null}importSessionFromJSON(e){try{let t=JSON.parse(e),r=(0,s.A)(),o={...t,id:r,isActive:!1,lastModified:Date.now()};return this.sessions.set(r,o),this.saveSessionsToStorage(),r}catch(e){return null}}updateSessionAlarmSettings(e,t){let r=this.sessions.get(e);return!!r&&(r.alarmSettings={...t},r.lastModified=Date.now(),this.sessions.set(e,r),this.useBackend?this.saveSessionToBackend(e,r).catch(e=>{(e.message.includes("422")||e.message.includes("401")||e.message.includes("UNPROCESSABLE ENTITY"))&&(this.useBackend=!1),this.saveSessionsToStorage()}):this.saveSessionsToStorage(),!0)}renameSession(e,t){let r=this.sessions.get(e);return!!r&&(r.name=t,r.lastModified=Date.now(),this.sessions.set(e,r),this.saveSessionsToStorage(),!0)}getSessionHistory(e){let t=this.sessions.get(e);return t?[...t.orderHistory]:[]}exportSessionToCSV(e){let t=this.sessions.get(e);return t?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...t.orderHistory.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(t.config.numDigits)||"",e.avgPrice?.toFixed(t.config.numDigits)||"",e.valueCrypto2?.toFixed(t.config.numDigits)||"",e.price1?.toFixed(t.config.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(t.config.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(t.config.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(t.config.numDigits)||""].join(","))].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem("pluto_trading_sessions");let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}destroy(){if(this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null),this.clearCurrentSession(),this.currentSessionId){let e=`pluto_heartbeat_${this.currentSessionId}_${this.windowId}`;localStorage.removeItem(e)}}enableAutoSave(e,t,r=3e4){let s=setInterval(()=>{let r=t();this.saveSession(e,r.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,r.isActive)},r);return()=>clearInterval(s)}}},6949:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8687:(e,t,r)=>{Promise.resolve().then(r.bind(r,1129))},8805:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8895:(e,t,r)=>{"use strict";r.d(t,{r:()=>_,U:()=>D});var s=r(687),o=r(3210),n=r(5280),a=r(4112),i=r(2185),c=r(5551);class l{constructor(){this.isOnline="undefined"==typeof navigator||navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.hasInitialized=!1,this.setupEventListeners(),this.startPeriodicCheck(),setTimeout(()=>{this.hasInitialized=!0},1e3)}static getInstance(){return l.instance||(l.instance=new l),l.instance}setupEventListeners(){"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0,!this.hasInitialized)}handleOffline(){this.isOnline=!1,this.notifyListeners(!1,!this.hasInitialized)}async checkConnection(){let e="undefined"==typeof navigator||navigator.onLine;return e!==this.isOnline&&(this.isOnline=e,this.notifyListeners(e,!this.hasInitialized),e&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),e}startPeriodicCheck(){let e=setInterval(()=>{this.checkConnection()},6e4);this.periodicInterval=e}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(e,t=!1){this.listeners.forEach(r=>{try{r(e,t)}catch(e){}})}addListener(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return!1;this.reconnectAttempts++;let e=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);await new Promise(t=>setTimeout(t,e));let t=await this.checkConnection();return!t&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),t}}class u{constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=l.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}static getInstance(){return u.instance||(u.instance=new u),u.instance}setupNetworkListener(){this.networkMonitor.addListener(e=>{e&&this.saveFunction&&(this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(this.saveFunction(),this.lastSaveTime=Date.now())})}enable(e,t=3e4){this.saveFunction=e,this.intervalMs=t,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs)}disable(){this.isEnabled=!1,this.stop()}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}}class d{constructor(){this.checkInterval=null,this.warningThreshold=262144e3,this.criticalThreshold=0x19000000,this.listeners=new Set,this.startMonitoring()}static getInstance(){return d.instance||(d.instance=new d),d.instance}startMonitoring(){}checkMemoryUsage(){if("undefined"!=typeof performance&&"memory"in performance){let e=performance.memory,t=e.usedJSHeapSize;this.notifyListeners(e),t>this.criticalThreshold||this.warningThreshold}}notifyListeners(e){this.listeners.forEach(t=>{try{t(e)}catch(e){}})}addListener(e){return this.listeners.add(e),()=>this.listeners.delete(e)}getMemoryUsage(){return"undefined"!=typeof performance&&"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}}var p=r(2694);let y=e=>e.crypto1&&e.crypto2?f(e):0,h=async e=>{try{if(!e.crypto1||!e.crypto2)return 0;if("StablecoinSwap"===e.tradingMode&&e.preferredStablecoin)try{let t=await S(e.crypto1,e.preferredStablecoin),r=await S(e.crypto2,e.preferredStablecoin);if(t>0&&r>0)return t/r}catch(e){}let t=`${e.crypto1}${e.crypto2}`.toUpperCase();try{let e=await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${t}`);if(e.ok){let t=await e.json(),r=parseFloat(t.price);if(r>0)return r}}catch(e){}try{let t=g(e.crypto1),r=g(e.crypto2);if(t&&r){let e=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${t}&vs_currencies=${r}`);if(e.ok){let s=await e.json(),o=s[t]?.[r];if(o>0)return o}}}catch(e){}return f(e)}catch(t){return f(e)}},g=e=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[e.toUpperCase()]||null,S=async(e,t)=>{try{if(e.toUpperCase()===t.toUpperCase())return 1;let r=`${e.toUpperCase()}${t.toUpperCase()}`;try{let e=await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${r}`);if(e.ok){let t=await e.json(),r=parseFloat(t.price);if(r>0)return r}}catch(e){}let s=g(e),o=g(t);if(s&&o){let e=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${s}&vs_currencies=${o}`);if(e.ok){let t=await e.json(),r=t[s]?.[o];if(r>0)return r}}let n=m(e),a=m(t);return n/a}catch(r){return m(e)/m(t)}},m=e=>({BTC:108e3,ETH:2100,SOL:240,ADA:1.2,DOGE:.4,LINK:25,MATIC:.5,DOT:8,AVAX:45,SHIB:3e-5,XRP:2.5,LTC:110,BCH:500,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[e.toUpperCase()]||100,f=e=>{let t=m(e.crypto1),r=m(e.crypto2);return e.tradingMode,t/r*(1+(Math.random()-.5)*.02)},v={tradingMode:"SimpleSpot",crypto1:"",crypto2:"",baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:n.Ql[0]},T={config:v,targetPriceRows:[],orderHistory:[],appSettings:n.Oh,currentMarketPrice:y(v),botSystemStatus:"Stopped",crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,backendStatus:"unknown"},b=new Map,E=e=>{},w=()=>null,C=(e,t,r)=>{},P=()=>({crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0}),A=e=>{},B=()=>"Stopped",R=()=>{let e=P();return{config:v,targetPriceRows:[],orderHistory:[],appSettings:n.Oh,currentMarketPrice:y(v),botSystemStatus:"Stopped",crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,backendStatus:"unknown"}},I=(e,t)=>{switch(t.type){case"SET_CONFIG":let r={...e.config,...t.payload};if(t.payload.crypto1||t.payload.crypto2)return{...e,config:r,currentMarketPrice:y(r)};return{...e,config:r};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload.sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}))};case"ADD_TARGET_PRICE_ROW":{let r=[...e.targetPriceRows,t.payload].sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:r}}case"UPDATE_TARGET_PRICE_ROW":{let r=e.targetPriceRows.map(e=>e.id===t.payload.id?t.payload:e).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:r}}case"REMOVE_TARGET_PRICE_ROW":{let r=e.targetPriceRows.filter(e=>e.id!==t.payload).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:r}}case"ADD_ORDER_HISTORY_ENTRY":return{...e,orderHistory:[t.payload,...e.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...e,orderHistory:[]};case"SET_APP_SETTINGS":return{...e,appSettings:{...e.appSettings,...t.payload}};case"SET_MARKET_PRICE":return{...e,currentMarketPrice:t.payload};case"FLUCTUATE_MARKET_PRICE":{if(e.currentMarketPrice<=0)return e;let t=(Math.random()-.5)*.006,r=e.currentMarketPrice*(1+t);return{...e,currentMarketPrice:r>0?r:e.currentMarketPrice}}case"SET_BALANCES":return{...e,crypto1Balance:t.payload.crypto1,crypto2Balance:t.payload.crypto2};case"UPDATE_BALANCES":return{...e,crypto1Balance:void 0!==t.payload.crypto1?t.payload.crypto1:e.crypto1Balance,crypto2Balance:void 0!==t.payload.crypto2?t.payload.crypto2:e.crypto2Balance,stablecoinBalance:void 0!==t.payload.stablecoin?t.payload.stablecoin:e.stablecoinBalance};case"UPDATE_STABLECOIN_BALANCE":return{...e,stablecoinBalance:t.payload};case"RESET_SESSION":let s={...e.config};return{...T,config:s,appSettings:{...e.appSettings},currentMarketPrice:y(s)};case"SET_BACKEND_STATUS":return{...e,backendStatus:t.payload};case"SYSTEM_START_BOT_INITIATE":return A("WarmingUp"),{...e,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return A("Running"),{...e,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return A("Stopped"),{...e,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":return b.clear(),c.SessionManager.getInstance().clearCurrentSession(),A("Stopped"),{...e,botSystemStatus:"Stopped",targetPriceRows:[],orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload};case"RESET_FOR_NEW_CRYPTO":return A("Stopped"),{...T,config:e.config,backendStatus:e.backendStatus,botSystemStatus:"Stopped",currentMarketPrice:0};default:return e}},k=(0,o.createContext)(void 0),_=({children:e})=>{let{toast:t}=(0,p.d)(),[r,n]=(0,o.useReducer)(I,(()=>{let e,t;try{t=(e=c.SessionManager.getInstance()).getCurrentSessionId()}catch(r){e=null,t=null}if(!t)return R();if(e&&t)try{let r=e.loadSession(t);if(r){let e=B();return{...T,config:r.config,targetPriceRows:r.targetPriceRows,orderHistory:r.orderHistory,currentMarketPrice:r.currentMarketPrice,crypto1Balance:r.crypto1Balance,crypto2Balance:r.crypto2Balance,stablecoinBalance:r.stablecoinBalance,botSystemStatus:e}}}catch(e){}let r=w();if(r){let e=B(),t=P();return{...R(),...r,crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance,botSystemStatus:e}}return R()})()),y=(0,o.useRef)(null),g=(0,o.useCallback)(async(e,t,s)=>{try{let o=localStorage.getItem("telegram_bot_token"),n=localStorage.getItem("telegram_chat_id");if(!o||!n)return;let a=`⚠️ <b>Error Alert</b>

`;a+=`<b>Type:</b> ${e}
<b>Error:</b> ${t}
`,s&&(a+=`<b>Context:</b> ${s}
`),r.config.crypto1&&r.config.crypto2&&(a+=`<b>Trading Pair:</b> ${r.config.crypto1}/${r.config.crypto2}
`),a+=`<b>Time:</b> ${new Date().toLocaleString()}
`,(await fetch(`https://api.telegram.org/bot${o}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:n,text:a,parse_mode:"HTML"})})).ok}catch(e){}},[r.config.crypto1,r.config.crypto2]),S=(0,o.useCallback)(async()=>{try{if(!r.config.crypto1||!r.config.crypto2){n({type:"SET_MARKET_PRICE",payload:0});return}let e=await h(r.config);n({type:"SET_MARKET_PRICE",payload:e})}catch(e){g("Price Fetch Error",`Failed to fetch market price: ${e instanceof Error?e.message:"Unknown error"}`,`Trading pair: ${r.config.crypto1}/${r.config.crypto2}`)}},[r.config,n,g]);(0,o.useEffect)(()=>{S();let e=setInterval(()=>{l.getInstance().getStatus().isOnline&&n({type:"FLUCTUATE_MARKET_PRICE"})},2e3);return()=>{clearInterval(e)}},[S,n]),(0,o.useEffect)(()=>{},[]);let f=(0,o.useRef)([]),v=(0,o.useRef)(!1),b=(0,o.useCallback)(async()=>{if(v.current||0===f.current.length||!y.current)return;v.current=!0;let{soundKey:e,sessionId:t}=f.current.shift();try{let s;let o=c.SessionManager.getInstance(),n=t||o.getCurrentSessionId(),a=n?o.loadSession(n):null,i=a?.alarmSettings||r.appSettings;if(!i.soundAlertsEnabled)return;if("soundOrderExecution"===e&&i.alertOnOrderExecution?s=i.soundOrderExecution:"soundError"===e&&i.alertOnError&&(s=i.soundError),s){let e=s;if(s.startsWith("/sounds/")&&(e=s.replace("/sounds/","/ringtones/")),e.startsWith("/ringtones/")&&!e.includes("data:audio")){let t=e.split("/").pop();t&&!["cheer.wav","chest1.wav","chime2.wav","bells.wav","bird1.wav","bird7.wav","sparrow1.wav","space_bells4a.wav","sanctuary1.wav","marble1.wav","foundry2.wav","G_hades_curse.wav","G_hades_demat.wav","G_hades_sanctify.wav","dark2.wav","Satyr_atk4.wav","S_mon1.mp3","S_mon2.mp3","wolf4.wav","goatherd1.wav","tax3.wav","G_hades_mat.wav"].includes(t)&&(e="/ringtones/cheer.wav")}y.current.pause(),y.current.currentTime=0,y.current.src=e,await new Promise((e,t)=>{let r=()=>{y.current?.removeEventListener("canplaythrough",r),y.current?.removeEventListener("error",s),e()},s=e=>{y.current?.removeEventListener("canplaythrough",r),y.current?.removeEventListener("error",s),t(e)};y.current?.addEventListener("canplaythrough",r,{once:!0}),y.current?.addEventListener("error",s,{once:!0}),y.current?.load()}),await y.current.play(),setTimeout(()=>{y.current&&(y.current.pause(),y.current.currentTime=0)},2e3)}}catch(e){if(e instanceof Error&&("AbortError"===e.name||e.message.includes("interrupted")||e.message.includes("play() request")));else if(y.current&&"/ringtones/cheer.wav"!==y.current.src)try{y.current.src="/ringtones/cheer.wav",await y.current.play(),setTimeout(()=>{y.current&&(y.current.pause(),y.current.currentTime=0)},2e3)}catch(e){}}finally{v.current=!1,setTimeout(()=>b(),150)}},[r.appSettings]),A=(0,o.useCallback)((e,t)=>{f.current.push({soundKey:e,sessionId:t}),b()},[b]),_=(0,o.useCallback)(async e=>{try{let t=localStorage.getItem("telegram_bot_token"),r=localStorage.getItem("telegram_chat_id");if(!t||!r)return;(await fetch(`https://api.telegram.org/bot${t}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:r,text:e,parse_mode:"HTML"})})).ok}catch(e){}},[]);(0,o.useEffect)(()=>{},[r.config.crypto1,r.config.crypto2]);let D=(0,o.useCallback)(e=>{e&&Array.isArray(e)&&n({type:"SET_TARGET_PRICE_ROWS",payload:[...e].filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t).map((e,t)=>{let s=r.targetPriceRows.find(t=>t.targetPrice===e);return s?{...s,counter:t+1}:{id:(0,a.A)(),counter:t+1,status:"Free",orderLevel:0,valueLevel:r.config.baseBid,targetPrice:e}})})},[r.targetPriceRows,r.config.baseBid,n]);(0,o.useEffect)(()=>{let e=l.getInstance().getStatus().isOnline;if("Running"!==r.botSystemStatus||0===r.targetPriceRows.length||r.currentMarketPrice<=0||!e){e||r.botSystemStatus;return}let{config:s,currentMarketPrice:o,targetPriceRows:i,crypto1Balance:c,crypto2Balance:u}=r,d=[...i].sort((e,t)=>e.targetPrice-t.targetPrice),p=c,y=u,h=0;d.filter(e=>Math.abs(o-e.targetPrice)/o*100<=s.slippagePercent).length;for(let e=0;e<d.length;e++){let r=d[e];if(Math.abs(o-r.targetPrice)/o*100<=s.slippagePercent){if("SimpleSpot"===s.tradingMode){if("Free"===r.status){let e=r.valueLevel;if(y>=e){let i=e/o;n({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Full",orderLevel:r.orderLevel+1,valueLevel:s.baseBid*Math.pow(s.multiplier,r.orderLevel+1),crypto1AmountHeld:i,originalCostCrypto2:e,crypto1Var:i,crypto2Var:-e}}),n({type:"UPDATE_BALANCES",payload:{crypto1:p+i,crypto2:y-e}}),n({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,a.A)(),timestamp:Date.now(),pair:`${s.crypto1}/${s.crypto2}`,crypto1:s.crypto1,orderType:"BUY",amountCrypto1:i,avgPrice:o,valueCrypto2:e,price1:o,crypto1Symbol:s.crypto1||"",crypto2Symbol:s.crypto2||""}}),A("soundOrderExecution"),t({type:"success",title:"\uD83D\uDFE2 BUY EXECUTED",description:`Bought ${i.toFixed(6)} ${s.crypto1} at $${o.toFixed(2)}`,duration:2e3}),_(`🟢 <b>BUY EXECUTED</b>
📊 Counter: ${r.counter}
💰 Amount: ${i.toFixed(6)} ${s.crypto1}
💵 Price: $${o.toFixed(2)}
💸 Cost: $${e.toFixed(2)} ${s.crypto2}
📈 Mode: Simple Spot`),h++,y-=e,p+=i}else g("Insufficient Balance",`Cannot execute BUY order - insufficient ${s.crypto2} balance`,`Required: ${e.toFixed(2)} ${s.crypto2}, Available: ${y.toFixed(2)} ${s.crypto2}`)}let e=r.counter,i=d.find(t=>t.counter===e-1);if(i&&"Full"===i.status&&i.crypto1AmountHeld&&i.originalCostCrypto2){let r=i.crypto1AmountHeld,c=r*o,l=c-i.originalCostCrypto2,u=o>0?l/o:0;n({type:"UPDATE_TARGET_PRICE_ROW",payload:{...i,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:s.baseBid*Math.pow(s.multiplier,i.orderLevel),crypto1Var:-r,crypto2Var:c}}),n({type:"UPDATE_BALANCES",payload:{crypto1:p-r,crypto2:y+c}}),n({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,a.A)(),timestamp:Date.now(),pair:`${s.crypto1}/${s.crypto2}`,crypto1:s.crypto1,orderType:"SELL",amountCrypto1:r,avgPrice:o,valueCrypto2:c,price1:o,crypto1Symbol:s.crypto1||"",crypto2Symbol:s.crypto2||"",realizedProfitLossCrypto2:l,realizedProfitLossCrypto1:u}}),A("soundOrderExecution");let d=l>0?"\uD83D\uDCC8":l<0?"\uD83D\uDCC9":"➖";t({type:l>0?"success":l<0?"warning":"info",title:"\uD83D\uDD34 SELL EXECUTED",description:`Sold ${r.toFixed(6)} ${s.crypto1} | ${d} Profit: $${l.toFixed(2)}`,duration:2e3}),_(`🔴 <b>SELL EXECUTED</b>
📊 Counter: ${e-1}
💰 Amount: ${r.toFixed(6)} ${s.crypto1}
💵 Price: $${o.toFixed(2)}
💸 Received: $${c.toFixed(2)} ${s.crypto2}
${d} Profit: $${l.toFixed(2)} ${s.crypto2}
📈 Mode: Simple Spot`),h++,p-=r,y+=c}}else if("StablecoinSwap"===s.tradingMode){if("Free"===r.status){let e=r.valueLevel;if(y>=e){let o=m(s.crypto2||"USDT")/m(s.preferredStablecoin||"USDT"),i=e*o,c=m(s.crypto1||"BTC")/m(s.preferredStablecoin||"USDT"),l=i/c,u=r.orderLevel+1,d=s.baseBid*Math.pow(s.multiplier,u);n({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Full",orderLevel:u,valueLevel:d,crypto1AmountHeld:l,originalCostCrypto2:e,crypto1Var:l,crypto2Var:-e}}),n({type:"UPDATE_BALANCES",payload:{crypto1:p+l,crypto2:y-e}}),n({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,a.A)(),timestamp:Date.now(),pair:`${s.crypto2}/${s.preferredStablecoin}`,crypto1:s.crypto2,orderType:"SELL",amountCrypto1:e,avgPrice:o,valueCrypto2:i,price1:o,crypto1Symbol:s.crypto2||"",crypto2Symbol:s.preferredStablecoin||"",realizedProfitLossCrypto2:-e,realizedProfitLossCrypto1:c>0?-e/c:0}}),n({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,a.A)(),timestamp:Date.now(),pair:`${s.crypto1}/${s.preferredStablecoin}`,crypto1:s.crypto1,orderType:"BUY",amountCrypto1:l,avgPrice:c,valueCrypto2:i,price1:c,crypto1Symbol:s.crypto1||"",crypto2Symbol:s.preferredStablecoin||"",realizedProfitLossCrypto2:e,realizedProfitLossCrypto1:l}}),A("soundOrderExecution"),t({type:"success",title:"\uD83D\uDFE2 BUY EXECUTED (Stablecoin)",description:`Bought ${l.toFixed(6)} ${s.crypto1} via ${s.preferredStablecoin}`,duration:2e3}),_(`🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${r.counter}
🔄 Step 1: Sold ${e.toFixed(2)} ${s.crypto2} → ${i.toFixed(2)} ${s.preferredStablecoin}
🔄 Step 2: Bought ${l.toFixed(6)} ${s.crypto1}
📊 Level: ${r.orderLevel} → ${u}
📈 Mode: Stablecoin Swap`),h++,y-=e,p+=l}}let e=r.counter,o=d.find(t=>t.counter===e-1);if(o&&"Full"===o.status&&o.crypto1AmountHeld&&o.originalCostCrypto2){let r=o.crypto1AmountHeld,i=m(s.crypto1||"BTC")/m(s.preferredStablecoin||"USDT"),c=r*i,l=m(s.crypto2||"USDT")/m(s.preferredStablecoin||"USDT"),u=c/l,d=u-o.originalCostCrypto2,g=i>0?d/i:0;n({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:s.baseBid*Math.pow(s.multiplier,o.orderLevel),crypto1Var:0,crypto2Var:0}}),n({type:"UPDATE_BALANCES",payload:{crypto1:p-r,crypto2:y+u}}),n({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,a.A)(),timestamp:Date.now(),pair:`${s.crypto1}/${s.preferredStablecoin}`,crypto1:s.crypto1,orderType:"SELL",amountCrypto1:r,avgPrice:i,valueCrypto2:c,price1:i,crypto1Symbol:s.crypto1||"",crypto2Symbol:s.preferredStablecoin||"",realizedProfitLossCrypto2:c/l,realizedProfitLossCrypto1:r}}),n({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,a.A)(),timestamp:Date.now(),pair:`${s.crypto2}/${s.preferredStablecoin}`,crypto1:s.crypto2,orderType:"BUY",amountCrypto1:u,avgPrice:l,valueCrypto2:c,price1:l,crypto1Symbol:s.crypto2||"",crypto2Symbol:s.preferredStablecoin||"",realizedProfitLossCrypto2:d,realizedProfitLossCrypto1:g}}),A("soundOrderExecution");let S=d>0?"\uD83D\uDCC8":d<0?"\uD83D\uDCC9":"➖";t({type:d>0?"success":d<0?"warning":"info",title:"\uD83D\uDD34 SELL EXECUTED (Stablecoin)",description:`Sold ${r.toFixed(6)} ${s.crypto1} | ${S} Profit: $${d.toFixed(2)}`,duration:2e3}),_(`🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${e-1}
🔄 Step A: Sold ${r.toFixed(6)} ${s.crypto1} → ${c.toFixed(2)} ${s.preferredStablecoin}
🔄 Step B: Bought ${u.toFixed(2)} ${s.crypto2}
${S} Profit: ${d.toFixed(2)} ${s.crypto2}
📊 Level: ${o.orderLevel} (unchanged)
📈 Mode: Stablecoin Swap`),h++,p-=r,y+=u}}}}},[r.botSystemStatus,r.currentMarketPrice,r.targetPriceRows,r.config,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,n,A,_]);let O=(0,o.useCallback)(()=>r.targetPriceRows&&Array.isArray(r.targetPriceRows)?r.targetPriceRows.map(e=>{let t,s;let o=r.currentMarketPrice||0,n=e.targetPrice||0;if("Full"===e.status&&e.crypto1AmountHeld&&e.originalCostCrypto2){if("StablecoinSwap"===r.config.tradingMode){let r=e.targetPrice||0,n=e.crypto1AmountHeld*(o-r);s=n,o>0&&(t=n/o)}else{let r=o*e.crypto1AmountHeld-e.originalCostCrypto2;s=r,o>0&&(t=r/o)}}return{...e,currentPrice:o,priceDifference:n-o,priceDifferencePercent:o>0?(n-o)/o*100:0,potentialProfitCrypto1:r.config.incomeSplitCrypto1Percent/100*e.valueLevel/(n||1),potentialProfitCrypto2:r.config.incomeSplitCrypto2Percent/100*e.valueLevel,percentFromActualPrice:o&&n?(o/n-1)*100:0,incomeCrypto1:t,incomeCrypto2:s}}).sort((e,t)=>t.targetPrice-e.targetPrice):[],[r.targetPriceRows,r.currentMarketPrice,r.config.incomeSplitCrypto1Percent,r.config.incomeSplitCrypto2Percent,r.config.baseBid,r.config.multiplier]),$=(0,o.useCallback)(async e=>{try{let t={name:`${e.crypto1}/${e.crypto2} ${e.tradingMode}`,tradingMode:e.tradingMode,crypto1:e.crypto1,crypto2:e.crypto2,baseBid:e.baseBid,multiplier:e.multiplier,numDigits:e.numDigits,slippagePercent:e.slippagePercent,incomeSplitCrypto1Percent:e.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:e.incomeSplitCrypto2Percent,preferredStablecoin:e.preferredStablecoin,targetPrices:r.targetPriceRows.map(e=>e.targetPrice)},s=await i.oc.saveConfig(t);return s.config?.id||null}catch(e){return null}},[r.targetPriceRows]),L=(0,o.useCallback)(async e=>{try{return await i.oc.startBot(e),!0}catch(e){return!1}},[]),M=(0,o.useCallback)(async e=>{try{return await i.oc.stopBot(e),!0}catch(e){return!1}},[]),x=(0,o.useCallback)(async()=>{let e="http://localhost:5000";if(!e){n({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let t=await fetch(`${e}/health/`);t.ok||await t.text().catch(()=>"Could not read response text."),n({type:"SET_BACKEND_STATUS",payload:t.ok?"online":"offline"})}catch(e){n({type:"SET_BACKEND_STATUS",payload:"offline"}),e.cause}},[n]);(0,o.useEffect)(()=>{x()},[x]),(0,o.useEffect)(()=>{E(r)},[r]),(0,o.useEffect)(()=>{"WarmingUp"===r.botSystemStatus&&n({type:"SYSTEM_COMPLETE_WARMUP"})},[r.botSystemStatus,n]),(0,o.useEffect)(()=>{let e=c.SessionManager.getInstance(),t=e.getCurrentSessionId();t&&("Running"===r.botSystemStatus?e.startSessionRuntime(t):"Stopped"===r.botSystemStatus&&e.stopSessionRuntime(t))},[r.botSystemStatus]),(0,o.useEffect)(()=>{let e=c.SessionManager.getInstance();"WarmingUp"===r.botSystemStatus&&!e.getCurrentSessionId()&&r.config.crypto1&&r.config.crypto2&&r.targetPriceRows.length>0&&e.createNewSessionWithAutoName(r.config,void 0,{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}).then(t=>{e.setCurrentSession(t)}).catch(e=>{});let t=e.getCurrentSessionId();t&&("Running"===r.botSystemStatus?e.startSessionRuntime(t):"Stopped"===r.botSystemStatus&&e.stopSessionRuntime(t))},[r.botSystemStatus,r.config.crypto1,r.config.crypto2]),(0,o.useEffect)(()=>{let e=c.SessionManager.getInstance(),t=e.getCurrentSessionId();if(t){let s=e.loadSession(t);if(s&&(s.config.crypto1!==r.config.crypto1||s.config.crypto2!==r.config.crypto2)){if("Running"===r.botSystemStatus||r.targetPriceRows.length>0||r.orderHistory.length>0){let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),o=`${s.name} (AutoSaved ${t})`;e.createNewSession(o,s.config,{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}).then(t=>{e.saveSession(t,s.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,!1)}).catch(e=>{})}n({type:"RESET_FOR_NEW_CRYPTO"})}}},[r.config.crypto1,r.config.crypto2]),(0,o.useEffect)(()=>{let e=l.getInstance(),t=u.getInstance(),s=d.getInstance(),o=c.SessionManager.getInstance(),a=e.addListener((e,t)=>{if(e||t);else if("Running"===r.botSystemStatus){n({type:"SYSTEM_STOP_BOT"});let e=c.SessionManager.getInstance(),t=e.getCurrentSessionId();if(t){let s=e.loadSession(t);if(s){let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),o=`${s.name} (Offline Backup ${t})`;e.createNewSession(o,s.config,{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}).then(t=>{e.saveSession(t,r.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,!1)}).catch(e=>{})}}}}),i=s.addListener(e=>{e.usedJSHeapSize}),p=()=>{try{let e=o.getCurrentSessionId();e&&o.saveSession(e,r.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,!0),E(r)}catch(e){}};t.enable(p,3e4);let y=e=>{if(p(),"Running"===r.botSystemStatus){let t="Trading bot is currently running. Are you sure you want to leave?";return e.returnValue=t,t}};return window.addEventListener("beforeunload",y),()=>{a(),i(),t.disable(),window.removeEventListener("beforeunload",y)}},[r]),(0,o.useEffect)(()=>{u.getInstance().saveNow()},[r.botSystemStatus]),(0,o.useEffect)(()=>{C(r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance)},[r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance]),(0,o.useEffect)(()=>{if(10===r.crypto1Balance&&1e5===r.crypto2Balance&&0===r.stablecoinBalance){let e=P();(10!==e.crypto1Balance||1e5!==e.crypto2Balance||0!==e.stablecoinBalance)&&n({type:"UPDATE_BALANCES",payload:{crypto1:e.crypto1Balance,crypto2:e.crypto2Balance,stablecoin:e.stablecoinBalance}})}},[]);let N=(0,o.useCallback)(()=>{try{let e=c.SessionManager.getInstance(),t=e.getCurrentSessionId();if(!t){if(r.config.crypto1&&r.config.crypto2)return e.createNewSessionWithAutoName(r.config,void 0,{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}).then(t=>{e.setCurrentSession(t),e.saveSession(t,r.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,!0)}).catch(e=>{g("Session Creation Error","Failed to create new trading session",`Error: ${e instanceof Error?e.message:"Unknown error"}`)}),!0;return g("Session Save Error","Cannot save session - no trading pair selected","Please select both crypto1 and crypto2 before saving"),!1}return e.saveSession(t,r.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,!0)}catch(e){return g("Session Save Error","Unexpected error while saving session",`Error: ${e instanceof Error?e.message:"Unknown error"}`),!1}},[r,g]),U={...r,dispatch:n,setTargetPrices:D,getDisplayOrders:O,checkBackendStatus:x,fetchMarketPrice:S,startBackendBot:L,stopBackendBot:M,saveConfigToBackend:$,saveCurrentSession:N,backendStatus:r.backendStatus,botSystemStatus:r.botSystemStatus,isBotActive:"Running"===r.botSystemStatus};return(0,s.jsx)(k.Provider,{value:U,children:e})},D=()=>{let e=(0,o.useContext)(k);if(void 0===e)throw Error("useTradingContext must be used within a TradingProvider");return e}},9212:(e,t,r)=>{Promise.resolve().then(r.bind(r,5019))}};