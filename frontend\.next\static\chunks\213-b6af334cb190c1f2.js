"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[213],{6982:(t,e,r)=>{r.d(e,{d:()=>d,t:()=>u});var a=r(5155),n=r(2115),o=r(8376),c=r(1133),i=r(594),s=r(5318),l=r(9434);let p=(0,n.createContext)(void 0);function u(t){let{children:e}=t,[r,o]=(0,n.useState)([]),c=(0,n.useCallback)(t=>{var e;let r=Math.random().toString(36).substr(2,9),a={...t,id:r,duration:null!==(e=t.duration)&&void 0!==e?e:2e3};o(t=>[...t,a]),setTimeout(()=>{i(r)},a.duration)},[]),i=(0,n.useCallback)(t=>{o(e=>e.filter(e=>e.id!==t))},[]);return(0,a.jsxs)(p.Provider,{value:{toasts:r,toast:c,dismiss:i},children:[e,(0,a.jsx)(y,{})]})}function d(){let t=(0,n.useContext)(p);if(!t)throw Error("useToast must be used within a ToastProvider");return t}function y(){let{toasts:t,dismiss:e}=d();return(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.map(t=>(0,a.jsx)(S,{toast:t,onDismiss:e},t.id))})}function S(t){let{toast:e,onDismiss:r}=t,[p,u]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let t=setTimeout(()=>u(!0),10);return()=>clearTimeout(t)},[]),(0,a.jsxs)("div",{className:(0,l.cn)("flex items-start gap-3 p-4 bg-white border-2 rounded-lg shadow-lg transition-all duration-150 ease-out min-w-[300px] max-w-[400px]",(()=>{switch(e.type){case"success":return"border-green-200";case"error":return"border-red-200";case"warning":return"border-yellow-200";default:return"border-blue-200"}})(),p?"translate-x-0 opacity-100":"translate-x-full opacity-0"),children:[(()=>{switch(e.type){case"success":return(0,a.jsx)(o.A,{className:"h-4 w-4 text-green-500"});case"error":return(0,a.jsx)(c.A,{className:"h-4 w-4 text-red-500"});case"warning":return(0,a.jsx)(c.A,{className:"h-4 w-4 text-yellow-500"});default:return(0,a.jsx)(i.A,{className:"h-4 w-4 text-blue-500"})}})(),(0,a.jsxs)("div",{className:"flex-1 space-y-1",children:[e.title&&(0,a.jsx)("div",{className:"font-semibold text-sm text-gray-900",children:e.title}),e.description&&(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),(0,a.jsx)("button",{onClick:()=>{u(!1),setTimeout(()=>r(e.id),150)},className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(s.A,{className:"h-4 w-4"})})]})}},7213:(t,e,r)=>{r.d(e,{r:()=>M,U:()=>U});var a=r(5155),n=r(2115),o=r(9348),c=r(9737),i=r(5731),s=r(4553);class l{static getInstance(){return l.instance||(l.instance=new l),l.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0,!this.hasInitialized)}handleOffline(){this.isOnline=!1,this.notifyListeners(!1,!this.hasInitialized)}async checkConnection(){let t="undefined"==typeof navigator||navigator.onLine;return t!==this.isOnline&&(this.isOnline=t,this.notifyListeners(t,!this.hasInitialized),t&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),t}startPeriodicCheck(){let t=setInterval(()=>{this.checkConnection()},6e4);this.periodicInterval=t}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.listeners.forEach(r=>{try{r(t,e)}catch(t){}})}addListener(t){return this.listeners.add(t),()=>{this.listeners.delete(t)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return!1;this.reconnectAttempts++;let t=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);await new Promise(e=>setTimeout(e,t));let e=await this.checkConnection();return!e&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),e}constructor(){this.isOnline="undefined"==typeof navigator||navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.hasInitialized=!1,this.setupEventListeners(),this.startPeriodicCheck(),setTimeout(()=>{this.hasInitialized=!0},1e3)}}class p{static getInstance(){return p.instance||(p.instance=new p),p.instance}setupNetworkListener(){this.networkMonitor.addListener(t=>{t&&this.saveFunction&&(this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&this.saveFunction()}),"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(this.saveFunction(),this.lastSaveTime=Date.now())})}enable(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4;this.saveFunction=t,this.intervalMs=e,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs)}disable(){this.isEnabled=!1,this.stop()}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=l.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}}class u{static getInstance(){return u.instance||(u.instance=new u),u.instance}startMonitoring(){}checkMemoryUsage(){if("undefined"!=typeof performance&&"memory"in performance){let t=performance.memory,e=t.usedJSHeapSize;this.notifyListeners(t),e>this.criticalThreshold?"gc"in window&&window.gc():this.warningThreshold}}notifyListeners(t){this.listeners.forEach(e=>{try{e(t)}catch(t){}})}addListener(t){return this.listeners.add(t),()=>this.listeners.delete(t)}getMemoryUsage(){return"undefined"!=typeof performance&&"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}constructor(){this.checkInterval=null,this.warningThreshold=262144e3,this.criticalThreshold=0x19000000,this.listeners=new Set,this.startMonitoring()}}var d=r(6982);let y=t=>t.crypto1&&t.crypto2?m(t):0,S=async t=>{try{if(!t.crypto1||!t.crypto2)return 0;if("StablecoinSwap"===t.tradingMode&&t.preferredStablecoin)try{let e=await h(t.crypto1,t.preferredStablecoin),r=await h(t.crypto2,t.preferredStablecoin);if(e>0&&r>0)return e/r}catch(t){}let r="".concat(t.crypto1).concat(t.crypto2).toUpperCase();try{let t=await fetch("https://api.binance.com/api/v3/ticker/price?symbol=".concat(r));if(t.ok){let e=await t.json(),r=parseFloat(e.price);if(r>0)return r}}catch(t){}try{let r=g(t.crypto1),a=g(t.crypto2);if(r&&a){let t=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(r,"&vs_currencies=").concat(a));if(t.ok){var e;let n=await t.json(),o=null===(e=n[r])||void 0===e?void 0:e[a];if(o>0)return o}}}catch(t){}return m(t)}catch(e){return m(t)}},g=t=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[t.toUpperCase()]||null,h=async(t,e)=>{try{if(t.toUpperCase()===e.toUpperCase())return 1;let a="".concat(t.toUpperCase()).concat(e.toUpperCase());try{let t=await fetch("https://api.binance.com/api/v3/ticker/price?symbol=".concat(a));if(t.ok){let e=await t.json(),r=parseFloat(e.price);if(r>0)return r}}catch(t){}let n=g(t),o=g(e);if(n&&o){let t=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(n,"&vs_currencies=").concat(o));if(t.ok){var r;let e=await t.json(),a=null===(r=e[n])||void 0===r?void 0:r[o];if(a>0)return a}}let c=f(t),i=f(e);return c/i}catch(r){return f(t)/f(e)}},f=t=>({BTC:108e3,ETH:2100,SOL:240,ADA:1.2,DOGE:.4,LINK:25,MATIC:.5,DOT:8,AVAX:45,SHIB:3e-5,XRP:2.5,LTC:110,BCH:500,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[t.toUpperCase()]||100,m=t=>{let e=f(t.crypto1),r=f(t.crypto2);return t.tradingMode,e/r*(1+(Math.random()-.5)*.02)},E={tradingMode:"SimpleSpot",crypto1:"",crypto2:"",baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:o.Ql[0]},C={config:E,targetPriceRows:[],orderHistory:[],appSettings:o.Oh,currentMarketPrice:y(E),botSystemStatus:"Stopped",crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,backendStatus:"unknown"},b=new Map,T="pluto_trading_state",v="pluto_global_balances",D="pluto_bot_status",w=t=>{try{{let e={config:t.config,targetPriceRows:t.targetPriceRows,orderHistory:t.orderHistory,appSettings:t.appSettings,currentMarketPrice:t.currentMarketPrice,crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance,botSystemStatus:t.botSystemStatus,timestamp:Date.now()};localStorage.setItem(T,JSON.stringify(e))}}catch(t){}},P=()=>{try{{let t=localStorage.getItem(T);if(t){let e=JSON.parse(t);if(e.timestamp&&Date.now()-e.timestamp<864e5)return e}}}catch(t){}return null},A=(t,e,r)=>{try{{let a={crypto1Balance:t,crypto2Balance:e,stablecoinBalance:r,timestamp:Date.now()};localStorage.setItem(v,JSON.stringify(a))}}catch(t){}},B=()=>{try{{let t=localStorage.getItem(v);if(t){let e=JSON.parse(t);return{crypto1Balance:e.crypto1Balance||10,crypto2Balance:e.crypto2Balance||1e5,stablecoinBalance:e.stablecoinBalance||0}}}}catch(t){}return{crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0}},R=t=>{try{localStorage.setItem(D,t)}catch(t){}},_=()=>{try{let t=localStorage.getItem(D);if(t&&("Stopped"===t||"WarmingUp"===t||"Running"===t))return t}catch(t){}return"Stopped"},I=()=>{let t=B();return{config:E,targetPriceRows:[],orderHistory:[],appSettings:o.Oh,currentMarketPrice:y(E),botSystemStatus:"Stopped",crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance,backendStatus:"unknown"}},L=(t,e)=>{switch(e.type){case"SET_CONFIG":let r={...t.config,...e.payload};if(e.payload.crypto1||e.payload.crypto2)return{...t,config:r,currentMarketPrice:y(r)};return{...t,config:r};case"SET_TARGET_PRICE_ROWS":return{...t,targetPriceRows:e.payload.sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}))};case"ADD_TARGET_PRICE_ROW":{let r=[...t.targetPriceRows,e.payload].sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:r}}case"UPDATE_TARGET_PRICE_ROW":{let r=t.targetPriceRows.map(t=>t.id===e.payload.id?e.payload:t).sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:r}}case"REMOVE_TARGET_PRICE_ROW":{let r=t.targetPriceRows.filter(t=>t.id!==e.payload).sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:r}}case"ADD_ORDER_HISTORY_ENTRY":return{...t,orderHistory:[e.payload,...t.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...t,orderHistory:[]};case"SET_APP_SETTINGS":return{...t,appSettings:{...t.appSettings,...e.payload}};case"SET_MARKET_PRICE":return{...t,currentMarketPrice:e.payload};case"FLUCTUATE_MARKET_PRICE":{if(t.currentMarketPrice<=0)return t;let e=(Math.random()-.5)*.006,r=t.currentMarketPrice*(1+e);return{...t,currentMarketPrice:r>0?r:t.currentMarketPrice}}case"SET_BALANCES":return{...t,crypto1Balance:e.payload.crypto1,crypto2Balance:e.payload.crypto2};case"UPDATE_BALANCES":return{...t,crypto1Balance:void 0!==e.payload.crypto1?e.payload.crypto1:t.crypto1Balance,crypto2Balance:void 0!==e.payload.crypto2?e.payload.crypto2:t.crypto2Balance,stablecoinBalance:void 0!==e.payload.stablecoin?e.payload.stablecoin:t.stablecoinBalance};case"UPDATE_STABLECOIN_BALANCE":return{...t,stablecoinBalance:e.payload};case"RESET_SESSION":let a={...t.config};return{...C,config:a,appSettings:{...t.appSettings},currentMarketPrice:y(a)};case"SET_BACKEND_STATUS":return{...t,backendStatus:e.payload};case"SYSTEM_START_BOT_INITIATE":return R("WarmingUp"),{...t,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return R("Running"),{...t,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return R("Stopped"),{...t,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":return b.clear(),s.SessionManager.getInstance().clearCurrentSession(),R("Stopped"),{...t,botSystemStatus:"Stopped",targetPriceRows:[],orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...t,targetPriceRows:e.payload};case"RESET_FOR_NEW_CRYPTO":return R("Stopped"),{...C,config:t.config,backendStatus:t.backendStatus,botSystemStatus:"Stopped",currentMarketPrice:0};default:return t}},O=(0,n.createContext)(void 0),M=t=>{let{children:e}=t,{toast:r}=(0,d.d)(),[o,y]=(0,n.useReducer)(L,(()=>{let t,e;if("true"===new URLSearchParams(window.location.search).get("newSession"))return setTimeout(()=>{let t=window.location.pathname;window.history.replaceState({},"",t)},0),I();try{e=(t=s.SessionManager.getInstance()).getCurrentSessionId()}catch(r){t=null,e=null}if(!e)return I();if(t&&e)try{let r=t.loadSession(e);if(r){let t=_();return{...C,config:r.config,targetPriceRows:r.targetPriceRows,orderHistory:r.orderHistory,currentMarketPrice:r.currentMarketPrice,crypto1Balance:r.crypto1Balance,crypto2Balance:r.crypto2Balance,stablecoinBalance:r.stablecoinBalance,botSystemStatus:t}}}catch(t){}let r=P();if(r){let t=_(),e=B();return{...I(),...r,crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,botSystemStatus:t}}return I()})()),g=(0,n.useRef)(null),h=(0,n.useCallback)(async(t,e,r)=>{try{let a=localStorage.getItem("telegram_bot_token"),n=localStorage.getItem("telegram_chat_id");if(!a||!n)return;let c="⚠️ <b>Error Alert</b>\n\n";c+="<b>Type:</b> ".concat(t,"\n"),c+="<b>Error:</b> ".concat(e,"\n"),r&&(c+="<b>Context:</b> ".concat(r,"\n")),o.config.crypto1&&o.config.crypto2&&(c+="<b>Trading Pair:</b> ".concat(o.config.crypto1,"/").concat(o.config.crypto2,"\n")),c+="<b>Time:</b> ".concat(new Date().toLocaleString(),"\n"),(await fetch("https://api.telegram.org/bot".concat(a,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:n,text:c,parse_mode:"HTML"})})).ok}catch(t){}},[o.config.crypto1,o.config.crypto2]),m=(0,n.useCallback)(async()=>{try{if(!o.config.crypto1||!o.config.crypto2){y({type:"SET_MARKET_PRICE",payload:0});return}let t=await S(o.config);y({type:"SET_MARKET_PRICE",payload:t})}catch(t){h("Price Fetch Error","Failed to fetch market price: ".concat(t instanceof Error?t.message:"Unknown error"),"Trading pair: ".concat(o.config.crypto1,"/").concat(o.config.crypto2))}},[o.config,y,h]);(0,n.useEffect)(()=>{m();let t=setInterval(()=>{l.getInstance().getStatus().isOnline&&y({type:"FLUCTUATE_MARKET_PRICE"})},2e3);return()=>{clearInterval(t)}},[m,y]),(0,n.useEffect)(()=>{g.current=new Audio},[]);let E=(0,n.useRef)([]),b=(0,n.useRef)(!1),T=(0,n.useCallback)(async()=>{if(b.current||0===E.current.length||!g.current)return;b.current=!0;let{soundKey:t,sessionId:e}=E.current.shift();try{let r;let a=s.SessionManager.getInstance(),n=e||a.getCurrentSessionId(),c=n?a.loadSession(n):null,i=(null==c?void 0:c.alarmSettings)||o.appSettings;if(!i.soundAlertsEnabled)return;if("soundOrderExecution"===t&&i.alertOnOrderExecution?r=i.soundOrderExecution:"soundError"===t&&i.alertOnError&&(r=i.soundError),r){let t=r;if(r.startsWith("/sounds/")&&(t=r.replace("/sounds/","/ringtones/")),t.startsWith("/ringtones/")&&!t.includes("data:audio")){let e=t.split("/").pop();e&&!["cheer.wav","chest1.wav","chime2.wav","bells.wav","bird1.wav","bird7.wav","sparrow1.wav","space_bells4a.wav","sanctuary1.wav","marble1.wav","foundry2.wav","G_hades_curse.wav","G_hades_demat.wav","G_hades_sanctify.wav","dark2.wav","Satyr_atk4.wav","S_mon1.mp3","S_mon2.mp3","wolf4.wav","goatherd1.wav","tax3.wav","G_hades_mat.wav"].includes(e)&&(t="/ringtones/cheer.wav")}g.current.pause(),g.current.currentTime=0,g.current.src=t,await new Promise((t,e)=>{var r,a,n;let o=()=>{var e,r;null===(e=g.current)||void 0===e||e.removeEventListener("canplaythrough",o),null===(r=g.current)||void 0===r||r.removeEventListener("error",c),t()},c=t=>{var r,a;null===(r=g.current)||void 0===r||r.removeEventListener("canplaythrough",o),null===(a=g.current)||void 0===a||a.removeEventListener("error",c),e(t)};null===(r=g.current)||void 0===r||r.addEventListener("canplaythrough",o,{once:!0}),null===(a=g.current)||void 0===a||a.addEventListener("error",c,{once:!0}),null===(n=g.current)||void 0===n||n.load()}),await g.current.play(),setTimeout(()=>{g.current&&(g.current.pause(),g.current.currentTime=0)},2e3)}}catch(t){if(t instanceof Error&&("AbortError"===t.name||t.message.includes("interrupted")||t.message.includes("play() request")));else if(g.current&&"/ringtones/cheer.wav"!==g.current.src)try{g.current.src="/ringtones/cheer.wav",await g.current.play(),setTimeout(()=>{g.current&&(g.current.pause(),g.current.currentTime=0)},2e3)}catch(t){}}finally{b.current=!1,setTimeout(()=>T(),150)}},[o.appSettings]),v=(0,n.useCallback)((t,e)=>{E.current.push({soundKey:t,sessionId:e}),T()},[T]),D=(0,n.useCallback)(async t=>{try{let e=localStorage.getItem("telegram_bot_token"),r=localStorage.getItem("telegram_chat_id");if(!e||!r)return;(await fetch("https://api.telegram.org/bot".concat(e,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:r,text:t,parse_mode:"HTML"})})).ok}catch(t){}},[]);(0,n.useEffect)(()=>{},[o.config.crypto1,o.config.crypto2]);let R=(0,n.useCallback)(t=>{t&&Array.isArray(t)&&y({type:"SET_TARGET_PRICE_ROWS",payload:[...t].filter(t=>!isNaN(t)&&t>0).sort((t,e)=>t-e).map((t,e)=>{let r=o.targetPriceRows.find(e=>e.targetPrice===t);return r?{...r,counter:e+1}:{id:(0,c.A)(),counter:e+1,status:"Free",orderLevel:0,valueLevel:o.config.baseBid,targetPrice:t}})})},[o.targetPriceRows,o.config.baseBid,y]);(0,n.useEffect)(()=>{let t=l.getInstance().getStatus().isOnline;if("Running"!==o.botSystemStatus||0===o.targetPriceRows.length||o.currentMarketPrice<=0||!t){t||o.botSystemStatus;return}let{config:e,currentMarketPrice:a,targetPriceRows:n,crypto1Balance:i,crypto2Balance:s}=o,p=[...n].sort((t,e)=>t.targetPrice-e.targetPrice),u=i,d=s,S=0;p.filter(t=>Math.abs(a-t.targetPrice)/a*100<=e.slippagePercent).length;for(let t=0;t<p.length;t++){let n=p[t];if(Math.abs(a-n.targetPrice)/a*100<=e.slippagePercent){if("SimpleSpot"===e.tradingMode){if("Free"===n.status){let t=n.valueLevel;if(d>=t){let o=t/a;y({type:"UPDATE_TARGET_PRICE_ROW",payload:{...n,status:"Full",orderLevel:n.orderLevel+1,valueLevel:e.baseBid*Math.pow(e.multiplier,n.orderLevel+1),crypto1AmountHeld:o,originalCostCrypto2:t,crypto1Var:o,crypto2Var:-t}}),y({type:"UPDATE_BALANCES",payload:{crypto1:u+o,crypto2:d-t}}),y({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(e.crypto2),crypto1:e.crypto1,orderType:"BUY",amountCrypto1:o,avgPrice:a,valueCrypto2:t,price1:a,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.crypto2||""}}),v("soundOrderExecution"),r({type:"success",title:"\uD83D\uDFE2 BUY EXECUTED",description:"Bought ".concat(o.toFixed(6)," ").concat(e.crypto1," at $").concat(a.toFixed(2)),duration:2e3}),D("\uD83D\uDFE2 <b>BUY EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(n.counter,"\n")+"\uD83D\uDCB0 Amount: ".concat(o.toFixed(6)," ").concat(e.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(a.toFixed(2),"\n")+"\uD83D\uDCB8 Cost: $".concat(t.toFixed(2)," ").concat(e.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),S++,d-=t,u+=o}else h("Insufficient Balance","Cannot execute BUY order - insufficient ".concat(e.crypto2," balance"),"Required: ".concat(t.toFixed(2)," ").concat(e.crypto2,", Available: ").concat(d.toFixed(2)," ").concat(e.crypto2))}let t=n.counter,o=p.find(e=>e.counter===t-1);if(o&&"Full"===o.status&&o.crypto1AmountHeld&&o.originalCostCrypto2){let n=o.crypto1AmountHeld,i=n*a,s=i-o.originalCostCrypto2,l=a>0?s/a:0;y({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:e.baseBid*Math.pow(e.multiplier,o.orderLevel),crypto1Var:-n,crypto2Var:i}}),y({type:"UPDATE_BALANCES",payload:{crypto1:u-n,crypto2:d+i}}),y({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(e.crypto2),crypto1:e.crypto1,orderType:"SELL",amountCrypto1:n,avgPrice:a,valueCrypto2:i,price1:a,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.crypto2||"",realizedProfitLossCrypto2:s,realizedProfitLossCrypto1:l}}),v("soundOrderExecution");let p=s>0?"\uD83D\uDCC8":s<0?"\uD83D\uDCC9":"➖";r({type:s>0?"success":s<0?"warning":"info",title:"\uD83D\uDD34 SELL EXECUTED",description:"Sold ".concat(n.toFixed(6)," ").concat(e.crypto1," | ").concat(p," Profit: $").concat(s.toFixed(2)),duration:2e3}),D("\uD83D\uDD34 <b>SELL EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(t-1,"\n")+"\uD83D\uDCB0 Amount: ".concat(n.toFixed(6)," ").concat(e.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(a.toFixed(2),"\n")+"\uD83D\uDCB8 Received: $".concat(i.toFixed(2)," ").concat(e.crypto2,"\n")+"".concat(p," Profit: $").concat(s.toFixed(2)," ").concat(e.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),S++,u-=n,d+=i}}else if("StablecoinSwap"===e.tradingMode){if("Free"===n.status){let t=n.valueLevel;if(d>=t){let a=f(e.crypto2||"USDT")/f(e.preferredStablecoin||"USDT"),o=t*a,i=f(e.crypto1||"BTC")/f(e.preferredStablecoin||"USDT"),s=o/i,l=n.orderLevel+1,p=e.baseBid*Math.pow(e.multiplier,l);y({type:"UPDATE_TARGET_PRICE_ROW",payload:{...n,status:"Full",orderLevel:l,valueLevel:p,crypto1AmountHeld:s,originalCostCrypto2:t,crypto1Var:s,crypto2Var:-t}}),y({type:"UPDATE_BALANCES",payload:{crypto1:u+s,crypto2:d-t}}),y({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto2,"/").concat(e.preferredStablecoin),crypto1:e.crypto2,orderType:"SELL",amountCrypto1:t,avgPrice:a,valueCrypto2:o,price1:a,crypto1Symbol:e.crypto2||"",crypto2Symbol:e.preferredStablecoin||"",realizedProfitLossCrypto2:-t,realizedProfitLossCrypto1:i>0?-t/i:0}}),y({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(e.preferredStablecoin),crypto1:e.crypto1,orderType:"BUY",amountCrypto1:s,avgPrice:i,valueCrypto2:o,price1:i,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.preferredStablecoin||"",realizedProfitLossCrypto2:t,realizedProfitLossCrypto1:s}}),v("soundOrderExecution"),r({type:"success",title:"\uD83D\uDFE2 BUY EXECUTED (Stablecoin)",description:"Bought ".concat(s.toFixed(6)," ").concat(e.crypto1," via ").concat(e.preferredStablecoin),duration:2e3}),D("\uD83D\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(n.counter,"\n")+"\uD83D\uDD04 Step 1: Sold ".concat(t.toFixed(2)," ").concat(e.crypto2," → ").concat(o.toFixed(2)," ").concat(e.preferredStablecoin,"\n")+"\uD83D\uDD04 Step 2: Bought ".concat(s.toFixed(6)," ").concat(e.crypto1,"\n")+"\uD83D\uDCCA Level: ".concat(n.orderLevel," → ").concat(l,"\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),S++,d-=t,u+=s}}let t=n.counter,a=p.find(e=>e.counter===t-1);if(a&&"Full"===a.status&&a.crypto1AmountHeld&&a.originalCostCrypto2){let n=a.crypto1AmountHeld,o=f(e.crypto1||"BTC")/f(e.preferredStablecoin||"USDT"),i=n*o,s=f(e.crypto2||"USDT")/f(e.preferredStablecoin||"USDT"),l=i/s,p=l-a.originalCostCrypto2,g=o>0?p/o:0;y({type:"UPDATE_TARGET_PRICE_ROW",payload:{...a,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:e.baseBid*Math.pow(e.multiplier,a.orderLevel),crypto1Var:0,crypto2Var:0}}),y({type:"UPDATE_BALANCES",payload:{crypto1:u-n,crypto2:d+l}}),y({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(e.preferredStablecoin),crypto1:e.crypto1,orderType:"SELL",amountCrypto1:n,avgPrice:o,valueCrypto2:i,price1:o,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.preferredStablecoin||"",realizedProfitLossCrypto2:i/s,realizedProfitLossCrypto1:n}}),y({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto2,"/").concat(e.preferredStablecoin),crypto1:e.crypto2,orderType:"BUY",amountCrypto1:l,avgPrice:s,valueCrypto2:i,price1:s,crypto1Symbol:e.crypto2||"",crypto2Symbol:e.preferredStablecoin||"",realizedProfitLossCrypto2:p,realizedProfitLossCrypto1:g}}),v("soundOrderExecution");let h=p>0?"\uD83D\uDCC8":p<0?"\uD83D\uDCC9":"➖";r({type:p>0?"success":p<0?"warning":"info",title:"\uD83D\uDD34 SELL EXECUTED (Stablecoin)",description:"Sold ".concat(n.toFixed(6)," ").concat(e.crypto1," | ").concat(h," Profit: $").concat(p.toFixed(2)),duration:2e3}),D("\uD83D\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(t-1,"\n")+"\uD83D\uDD04 Step A: Sold ".concat(n.toFixed(6)," ").concat(e.crypto1," → ").concat(i.toFixed(2)," ").concat(e.preferredStablecoin,"\n")+"\uD83D\uDD04 Step B: Bought ".concat(l.toFixed(2)," ").concat(e.crypto2,"\n")+"".concat(h," Profit: ").concat(p.toFixed(2)," ").concat(e.crypto2,"\n")+"\uD83D\uDCCA Level: ".concat(a.orderLevel," (unchanged)\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),S++,u-=n,d+=l}}}}},[o.botSystemStatus,o.currentMarketPrice,o.targetPriceRows,o.config,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,y,v,D]);let M=(0,n.useCallback)(()=>o.targetPriceRows&&Array.isArray(o.targetPriceRows)?o.targetPriceRows.map(t=>{let e,r;let a=o.currentMarketPrice||0,n=t.targetPrice||0;if("Full"===t.status&&t.crypto1AmountHeld&&t.originalCostCrypto2){if("StablecoinSwap"===o.config.tradingMode){let n=t.targetPrice||0,o=t.crypto1AmountHeld*(a-n);r=o,a>0&&(e=o/a)}else{let n=a*t.crypto1AmountHeld-t.originalCostCrypto2;r=n,a>0&&(e=n/a)}}return{...t,currentPrice:a,priceDifference:n-a,priceDifferencePercent:a>0?(n-a)/a*100:0,potentialProfitCrypto1:o.config.incomeSplitCrypto1Percent/100*t.valueLevel/(n||1),potentialProfitCrypto2:o.config.incomeSplitCrypto2Percent/100*t.valueLevel,percentFromActualPrice:a&&n?(a/n-1)*100:0,incomeCrypto1:e,incomeCrypto2:r}}).sort((t,e)=>e.targetPrice-t.targetPrice):[],[o.targetPriceRows,o.currentMarketPrice,o.config.incomeSplitCrypto1Percent,o.config.incomeSplitCrypto2Percent,o.config.baseBid,o.config.multiplier]),U=(0,n.useCallback)(async t=>{try{var e;let r={name:"".concat(t.crypto1,"/").concat(t.crypto2," ").concat(t.tradingMode),tradingMode:t.tradingMode,crypto1:t.crypto1,crypto2:t.crypto2,baseBid:t.baseBid,multiplier:t.multiplier,numDigits:t.numDigits,slippagePercent:t.slippagePercent,incomeSplitCrypto1Percent:t.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:t.incomeSplitCrypto2Percent,preferredStablecoin:t.preferredStablecoin,targetPrices:o.targetPriceRows.map(t=>t.targetPrice)},a=await i.oc.saveConfig(r);return(null===(e=a.config)||void 0===e?void 0:e.id)||null}catch(t){return null}},[o.targetPriceRows]),k=(0,n.useCallback)(async t=>{try{return await i.oc.startBot(t),!0}catch(t){return!1}},[]),N=(0,n.useCallback)(async t=>{try{return await i.oc.stopBot(t),!0}catch(t){return!1}},[]),x=(0,n.useCallback)(async()=>{let t="http://localhost:5000";if(!t){y({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let e=await fetch("".concat(t,"/health/"));e.ok||await e.text().catch(()=>"Could not read response text."),y({type:"SET_BACKEND_STATUS",payload:e.ok?"online":"offline"})}catch(t){y({type:"SET_BACKEND_STATUS",payload:"offline"}),t.cause}},[y]);(0,n.useEffect)(()=>{x()},[x]),(0,n.useEffect)(()=>{w(o)},[o]),(0,n.useEffect)(()=>{"WarmingUp"===o.botSystemStatus&&y({type:"SYSTEM_COMPLETE_WARMUP"})},[o.botSystemStatus,y]),(0,n.useEffect)(()=>{let t=s.SessionManager.getInstance(),e=t.getCurrentSessionId();e&&("Running"===o.botSystemStatus?t.startSessionRuntime(e):"Stopped"===o.botSystemStatus&&t.stopSessionRuntime(e))},[o.botSystemStatus]),(0,n.useEffect)(()=>{let t=s.SessionManager.getInstance();"WarmingUp"===o.botSystemStatus&&!t.getCurrentSessionId()&&o.config.crypto1&&o.config.crypto2&&o.targetPriceRows.length>0&&t.createNewSessionWithAutoName(o.config,void 0,{crypto1:o.crypto1Balance,crypto2:o.crypto2Balance,stablecoin:o.stablecoinBalance}).then(e=>{t.setCurrentSession(e)}).catch(t=>{});let e=t.getCurrentSessionId();e&&("Running"===o.botSystemStatus?t.startSessionRuntime(e):"Stopped"===o.botSystemStatus&&t.stopSessionRuntime(e))},[o.botSystemStatus,o.config.crypto1,o.config.crypto2]),(0,n.useEffect)(()=>{let t=s.SessionManager.getInstance(),e=t.getCurrentSessionId();if(e){let r=t.loadSession(e);if(r&&(r.config.crypto1!==o.config.crypto1||r.config.crypto2!==o.config.crypto2)){if("Running"===o.botSystemStatus||o.targetPriceRows.length>0||o.orderHistory.length>0){let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),a="".concat(r.name," (AutoSaved ").concat(e,")");t.createNewSession(a,r.config,{crypto1:o.crypto1Balance,crypto2:o.crypto2Balance,stablecoin:o.stablecoinBalance}).then(e=>{t.saveSession(e,r.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1)}).catch(t=>{})}y({type:"RESET_FOR_NEW_CRYPTO"})}}},[o.config.crypto1,o.config.crypto2]),(0,n.useEffect)(()=>{let t=l.getInstance(),e=p.getInstance(),r=u.getInstance(),a=s.SessionManager.getInstance(),n=t.addListener((t,e)=>{if(t||e);else if("Running"===o.botSystemStatus){y({type:"SYSTEM_STOP_BOT"});let t=s.SessionManager.getInstance(),e=t.getCurrentSessionId();if(e){let r=t.loadSession(e);if(r){let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),a="".concat(r.name," (Offline Backup ").concat(e,")");t.createNewSession(a,r.config,{crypto1:o.crypto1Balance,crypto2:o.crypto2Balance,stablecoin:o.stablecoinBalance}).then(e=>{t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1)}).catch(t=>{})}}}}),c=r.addListener(t=>{t.usedJSHeapSize}),i=()=>{try{let t=a.getCurrentSessionId();t&&a.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!0),w(o)}catch(t){}};e.enable(i,3e4);let d=t=>{if(i(),"Running"===o.botSystemStatus){let e="Trading bot is currently running. Are you sure you want to leave?";return t.returnValue=e,e}};return window.addEventListener("beforeunload",d),()=>{n(),c(),e.disable(),window.removeEventListener("beforeunload",d)}},[o]),(0,n.useEffect)(()=>{p.getInstance().saveNow()},[o.botSystemStatus]),(0,n.useEffect)(()=>{A(o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance)},[o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance]),(0,n.useEffect)(()=>{if(10===o.crypto1Balance&&1e5===o.crypto2Balance&&0===o.stablecoinBalance){let t=B();(10!==t.crypto1Balance||1e5!==t.crypto2Balance||0!==t.stablecoinBalance)&&y({type:"UPDATE_BALANCES",payload:{crypto1:t.crypto1Balance,crypto2:t.crypto2Balance,stablecoin:t.stablecoinBalance}})}},[]);let F=(0,n.useCallback)(()=>{try{let t=s.SessionManager.getInstance(),e=t.getCurrentSessionId();if(!e){if(o.config.crypto1&&o.config.crypto2)return t.createNewSessionWithAutoName(o.config,void 0,{crypto1:o.crypto1Balance,crypto2:o.crypto2Balance,stablecoin:o.stablecoinBalance}).then(e=>{t.setCurrentSession(e),t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!0)}).catch(t=>{h("Session Creation Error","Failed to create new trading session","Error: ".concat(t instanceof Error?t.message:"Unknown error"))}),!0;return h("Session Save Error","Cannot save session - no trading pair selected","Please select both crypto1 and crypto2 before saving"),!1}return t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!0)}catch(t){return h("Session Save Error","Unexpected error while saving session","Error: ".concat(t instanceof Error?t.message:"Unknown error")),!1}},[o,h]),H={...o,dispatch:y,setTargetPrices:R,getDisplayOrders:M,checkBackendStatus:x,fetchMarketPrice:m,startBackendBot:k,stopBackendBot:N,saveConfigToBackend:U,saveCurrentSession:F,backendStatus:o.backendStatus,botSystemStatus:o.botSystemStatus,isBotActive:"Running"===o.botSystemStatus};return(0,a.jsx)(O.Provider,{value:H,children:e})},U=()=>{let t=(0,n.useContext)(O);if(void 0===t)throw Error("useTradingContext must be used within a TradingProvider");return t}},9348:(t,e,r)=>{r.d(e,{Oh:()=>a,Ql:()=>c,hg:()=>n,vA:()=>o});let a={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},n=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],o={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},c=["USDT","USDC","FDUSD","DAI"]},9434:(t,e,r)=>{r.d(e,{cn:()=>o});var a=r(2596),n=r(9688);function o(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return(0,n.QP)((0,a.$)(e))}}}]);