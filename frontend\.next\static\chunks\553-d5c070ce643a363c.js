"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[553],{4553:(e,t,s)=>{s.d(t,{SessionManager:()=>d});var i=s(9737),o=s(5731);let n={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/trade_success.mp3",soundError:"/sounds/trade_error.mp3"},r="pluto_trading_sessions",a="pluto_current_session",c=()=>"window_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),l=()=>{let e=sessionStorage.getItem("pluto_window_id");return e||(e=c(),sessionStorage.setItem("pluto_window_id",e)),e};class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}generateSessionName(e){let t=e.crypto1||"Crypto1",s=e.crypto2||"Crypto2",i=e.tradingMode||"SimpleSpot",o="".concat(t,"/").concat(s," ").concat(i),n=Array.from(this.sessions.values()).filter(e=>e.name.startsWith(o));if(0===n.length)return o;let r=0;return n.forEach(e=>{let t=e.name.match(new RegExp("^".concat(o.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")," Session (\\d+)$")));if(t){let e=parseInt(t[1],10);e>r&&(r=e)}else e.name===o&&(r=Math.max(r,1))}),"".concat(o," Session ").concat(r+1)}async checkBackendConnectionWhenReady(){await this.checkBackendConnection()}async checkBackendConnection(){try{let e=localStorage.getItem("plutoAuthToken");if(!e){this.useBackend=!1,this.loadSessionsFromStorage();return}let t=new AbortController,s=setTimeout(()=>t.abort(),3e3),i=await fetch("http://localhost:5000/",{method:"GET",signal:t.signal,headers:{Authorization:"Bearer ".concat(e)}});if(clearTimeout(s),200===i.status)this.useBackend=!0,localStorage.getItem("plutoAuthToken")?await this.loadSessionsFromBackend():this.loadSessionsFromStorage();else if(401===i.status||422===i.status){this.useBackend=!1,this.loadSessionsFromStorage();return}else throw Error("Backend returned error: ".concat(i.status))}catch(e){this.useBackend=!1,"AbortError"!==e.name&&(e.message.includes("Authentication required")||e.message.includes("422")||e.message.includes("401")),this.loadSessionsFromStorage()}}getWindowSpecificKey(e){return"".concat(e,"_").concat(this.windowId)}setupStorageListener(){window.addEventListener("storage",e=>{if(e.key===r&&e.newValue)try{let t=JSON.parse(e.newValue);this.sessions=new Map(Object.entries(t))}catch(e){}})}setupBeforeUnloadHandler(){window.addEventListener("beforeunload",()=>{if(this.currentSessionId){let e=this.sessions.get(this.currentSessionId);if(e&&e.isActive){e.isActive=!1,e.lastModified=Date.now(),this.stopSessionRuntime(this.currentSessionId);let t="pluto_heartbeat_".concat(this.currentSessionId,"_").concat(this.windowId);localStorage.removeItem(t),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage()}}}),document.addEventListener("visibilitychange",()=>{if(document.hidden&&this.currentSessionId){let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.stopSessionRuntime(this.currentSessionId),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage())}else if(!document.hidden&&this.currentSessionId){let e=this.sessions.get(this.currentSessionId);e&&!e.isActive&&(e.isActive=!0,e.lastModified=Date.now(),this.startSessionRuntime(this.currentSessionId),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage())}})}startHeartbeat(){this.heartbeatInterval=setInterval(()=>{if(this.currentSessionId){let e=this.sessions.get(this.currentSessionId);if(e&&e.isActive){e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e);let t="pluto_heartbeat_".concat(this.currentSessionId,"_").concat(this.windowId);localStorage.setItem(t,Date.now().toString())}}this.cleanupStaleSessions()},1e4)}cleanupStaleSessions(){let e=Date.now();this.sessions.forEach((t,s)=>{if(t.isActive){let i=[];for(let e=0;e<localStorage.length;e++){let t=localStorage.key(e);t&&t.startsWith("pluto_heartbeat_".concat(s,"_"))&&i.push(t)}let o=!1;for(let t of i)if(e-parseInt(localStorage.getItem(t)||"0")<3e4){o=!0;break}o||(t.isActive=!1,t.lastModified=e,this.sessions.set(s,t),i.forEach(e=>localStorage.removeItem(e)))}}),this.saveSessionsToStorage()}async loadSessionsFromBackend(){if(!localStorage.getItem("plutoAuthToken")){this.loadSessionsFromStorage();return}try{let e=await o.Rk.getAllSessions();if(!e){this.loadSessionsFromStorage();return}e&&e.sessions&&(this.sessions.clear(),e.sessions.forEach(e=>{let t={id:e.session_uuid,name:e.name,config:e.config_snapshot,targetPriceRows:e.target_price_rows||[],orderHistory:e.order_history||[],currentMarketPrice:e.current_market_price||0,crypto1Balance:e.crypto1_balance||10,crypto2Balance:e.crypto2_balance||1e5,stablecoinBalance:e.stablecoin_balance||0,createdAt:new Date(e.created_at).getTime(),lastModified:new Date(e.last_modified||e.created_at).getTime(),isActive:e.is_active||!1,runtime:e.runtime_seconds?1e3*e.runtime_seconds:0,alarmSettings:e.alarm_settings||{...n}};this.sessions.set(e.session_uuid,t)}))}catch(e){e.message&&(e.message.includes("422")||e.message.includes("401")||e.message.includes("UNPROCESSABLE ENTITY")),this.loadSessionsFromStorage()}}loadSessionsFromStorage(){try{let e=localStorage.getItem(r),t=this.getWindowSpecificKey(a),s=localStorage.getItem(t);if(e){let t=JSON.parse(e);this.sessions=new Map(Object.entries(t))}this.currentSessionId=s}catch(e){}}async saveSessionToBackend(e,t){try{let s={name:t.name||"Untitled Session",config:t.config,targetPriceRows:t.targetPriceRows||[],currentMarketPrice:Number(t.currentMarketPrice)||0,crypto1Balance:Number(t.crypto1Balance)||0,crypto2Balance:Number(t.crypto2Balance)||0,stablecoinBalance:Number(t.stablecoinBalance)||0,isActive:!!t.isActive,additionalRuntime:Math.floor(Number(t.runtime||0)/1e3)};try{await o.Rk.updateSession(e,s)}catch(t){let e=t instanceof Error?t.message:String(t);if(e.includes("404")||e.includes("not found"))await o.Rk.createSession(s);else throw t}}catch(t){let e=t instanceof Error?t.message:String(t);throw null==t||t.status,null==t||t.response,e.includes("422")||e.includes("401")||e.includes("UNPROCESSABLE ENTITY"),t}}saveSessionsToStorage(){try{let e=Object.fromEntries(this.sessions);localStorage.setItem(r,JSON.stringify(e));let t=this.getWindowSpecificKey(a);this.currentSessionId&&localStorage.setItem(t,this.currentSessionId)}catch(e){}}async createNewSessionWithAutoName(e,t,s){let i=t||this.generateSessionName(e);return this.createNewSession(i,e,s)}async createNewSession(e,t,s){let r=s||{crypto1:10,crypto2:1e5,stablecoin:0};if(this.useBackend)try{let s=await o.Rk.createSession({name:e,config:t,targetPriceRows:[],currentMarketPrice:0,crypto1Balance:r.crypto1,crypto2Balance:r.crypto2,stablecoinBalance:r.stablecoin});if(!s||!s.session)throw Error("Invalid response from server: missing session data");let i=s.session.id,a=Date.now(),c={id:i,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:r.crypto1,crypto2Balance:r.crypto2,stablecoinBalance:r.stablecoin,createdAt:a,lastModified:a,isActive:!1,runtime:0,alarmSettings:{...n}};return this.sessions.set(i,c),i}catch(e){this.useBackend=!1}let a=(0,i.A)(),c=Date.now(),l={id:a,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:r.crypto1,crypto2Balance:r.crypto2,stablecoinBalance:r.stablecoin,createdAt:c,lastModified:c,isActive:!1,runtime:0,alarmSettings:{...n}};return this.sessions.set(a,l),this.saveSessionsToStorage(),a}saveSession(e,t,s,i,o,n,r,a){let c=arguments.length>8&&void 0!==arguments[8]&&arguments[8],l=arguments.length>9?arguments[9]:void 0;try{let d;let u=this.sessions.get(e);if(!u)return!1;if(void 0!==l)d=l;else{d=u.runtime||0;let t=this.sessionStartTimes.get(e);t&&c?(d=(u.runtime||0)+(Date.now()-t),this.sessionStartTimes.set(e,Date.now())):!c&&t?(d=(u.runtime||0)+(Date.now()-t),this.sessionStartTimes.delete(e)):c&&!t&&this.sessionStartTimes.set(e,Date.now())}let h={...u,config:t,targetPriceRows:[...s],orderHistory:[...i],currentMarketPrice:o,crypto1Balance:n,crypto2Balance:r,stablecoinBalance:a,isActive:c,lastModified:Date.now(),runtime:d};return this.sessions.set(e,h),this.useBackend?this.saveSessionToBackend(e,h).catch(e=>{(e.message.includes("422")||e.message.includes("401")||e.message.includes("UNPROCESSABLE ENTITY"))&&(this.useBackend=!1),this.saveSessionsToStorage()}):this.saveSessionsToStorage(),!0}catch(e){return!1}}loadSession(e){return this.sessions.get(e)||null}deleteSession(e){let t=this.sessions.delete(e);if(t){if(this.currentSessionId===e){this.currentSessionId=null;let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}this.saveSessionsToStorage()}return t}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:"".concat(e.config.crypto1,"/").concat(e.config.crypto2),createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)}))}setCurrentSession(e){if(this.sessions.has(e)){this.currentSessionId=e;let t=this.getWindowSpecificKey(a);localStorage.setItem(t,e);let s=this.sessions.get(e);s&&!s.isActive&&(s.isActive=!0,s.lastModified=Date.now(),this.sessions.set(e,s),this.saveSessionsToStorage())}}getCurrentSessionId(){return this.currentSessionId}clearCurrentSession(){if(this.currentSessionId){let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage())}this.currentSessionId=null;{let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}}startSessionRuntime(e){this.sessionStartTimes.set(e,Date.now())}stopSessionRuntime(e){let t=this.sessionStartTimes.get(e);if(t){let s=this.sessions.get(e);if(s){let i=Date.now()-t;s.runtime=(s.runtime||0)+i,s.lastModified=Date.now(),this.sessions.set(e,s),this.saveSessionsToStorage()}this.sessionStartTimes.delete(e)}}deactivateSession(e){let t=this.sessions.get(e);t&&t.isActive&&(t.isActive=!1,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage())}getCurrentRuntime(e){let t=this.sessions.get(e);if(!t)return 0;let s=this.sessionStartTimes.get(e);return s?(t.runtime||0)+(Date.now()-s):t.runtime||0}exportSessionToJSON(e){let t=this.sessions.get(e);return t?JSON.stringify(t,null,2):null}importSessionFromJSON(e){try{let t=JSON.parse(e),s=(0,i.A)(),o={...t,id:s,isActive:!1,lastModified:Date.now()};return this.sessions.set(s,o),this.saveSessionsToStorage(),s}catch(e){return null}}updateSessionAlarmSettings(e,t){let s=this.sessions.get(e);return!!s&&(s.alarmSettings={...t},s.lastModified=Date.now(),this.sessions.set(e,s),this.useBackend?this.saveSessionToBackend(e,s).catch(e=>{(e.message.includes("422")||e.message.includes("401")||e.message.includes("UNPROCESSABLE ENTITY"))&&(this.useBackend=!1),this.saveSessionsToStorage()}):this.saveSessionsToStorage(),!0)}renameSession(e,t){let s=this.sessions.get(e);return!!s&&(s.name=t,s.lastModified=Date.now(),this.sessions.set(e,s),this.saveSessionsToStorage(),!0)}getSessionHistory(e){let t=this.sessions.get(e);return t?[...t.orderHistory]:[]}exportSessionToCSV(e){let t=this.sessions.get(e);return t?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...t.orderHistory.map(e=>{var s,i,o,n,r,a,c;return[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,(null===(s=e.amountCrypto1)||void 0===s?void 0:s.toFixed(t.config.numDigits))||"",(null===(i=e.avgPrice)||void 0===i?void 0:i.toFixed(t.config.numDigits))||"",(null===(o=e.valueCrypto2)||void 0===o?void 0:o.toFixed(t.config.numDigits))||"",(null===(n=e.price1)||void 0===n?void 0:n.toFixed(t.config.numDigits))||"",e.crypto1Symbol,(null===(r=e.price2)||void 0===r?void 0:r.toFixed(t.config.numDigits))||"",e.crypto2Symbol,(null===(a=e.realizedProfitLossCrypto1)||void 0===a?void 0:a.toFixed(t.config.numDigits))||"",(null===(c=e.realizedProfitLossCrypto2)||void 0===c?void 0:c.toFixed(t.config.numDigits))||""].join(",")})].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem(r);let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}destroy(){if(this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null),this.clearCurrentSession(),this.currentSessionId){let e="pluto_heartbeat_".concat(this.currentSessionId,"_").concat(this.windowId);localStorage.removeItem(e)}}enableAutoSave(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,i=setInterval(()=>{let s=t();this.saveSession(e,s.config,s.targetPriceRows,s.orderHistory,s.currentMarketPrice,s.crypto1Balance,s.crypto2Balance,s.stablecoinBalance,s.isActive)},s);return()=>clearInterval(i)}constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.sessionStartTimes=new Map,this.heartbeatInterval=null,this.windowId=l(),this.sessionStartTimes.clear(),this.useBackend=!1,this.loadSessionsFromStorage(),this.setupStorageListener(),this.setupBeforeUnloadHandler(),this.startHeartbeat()}}},5731:(e,t,s)=>{async function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s="".concat("http://localhost:5000").concat(e),i=localStorage.getItem("plutoAuthToken"),o={"Content-Type":"application/json",...i?{Authorization:"Bearer ".concat(i)}:{},...t.headers};try{let e;let i=new AbortController,n=setTimeout(()=>i.abort(),1e4),r=await fetch(s,{...t,headers:o,signal:i.signal}).finally(()=>clearTimeout(n));if(401===r.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let a=r.headers.get("content-type");if(a&&a.includes("application/json"))e=await r.json();else{let t=await r.text();try{e=JSON.parse(t)}catch(s){e={message:t}}}if(!r.ok){let t=(null==e?void 0:e.error)||(null==e?void 0:e.message)||"HTTP ".concat(r.status,": ").concat(r.statusText);if(401===r.status||422===r.status)return null;throw Error(t)}return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw Error("Request timed out. Server may be unavailable.");throw e}}s.d(t,{Rk:()=>r,ZQ:()=>o,oc:()=>n});let o={login:async(e,t)=>{try{let s=await a(async()=>await i("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(s&&s.access_token)return localStorage.setItem("plutoAuthToken",s.access_token),localStorage.setItem("plutoAuth","true"),s.user&&localStorage.setItem("plutoUser",JSON.stringify(s.user)),!0;return!1}catch(e){return!1}},register:async(e,t,s)=>a(async()=>await i("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:s})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},n={getConfig:async e=>i(e?"/trading/config/".concat(e):"/trading/config"),saveConfig:async e=>i("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>i("/trading/config/".concat(e),{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>i("/trading/bot/start/".concat(e),{method:"POST"}),stopBot:async e=>i("/trading/bot/stop/".concat(e),{method:"POST"}),getBotStatus:async e=>i("/trading/bot/status/".concat(e)),getTradeHistory:async e=>i("/trading/history".concat(e?"?configId=".concat(e):"")),getBalances:async()=>i("/trading/balances"),getMarketPrice:async e=>i("/trading/market-data/".concat(e)),getTradingPairs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return i("/trading/exchange/trading-pairs?exchange=".concat(e))},getCryptocurrencies:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return i("/trading/exchange/cryptocurrencies?exchange=".concat(e))}},r={getAllSessions:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return i("/sessions/?include_inactive=".concat(e))},createSession:async e=>i("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>i("/sessions/".concat(e)),updateSession:async(e,t)=>i("/sessions/".concat(e),{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>i("/sessions/".concat(e),{method:"DELETE"}),activateSession:async e=>i("/sessions/".concat(e,"/activate"),{method:"POST"}),getSessionHistory:async e=>i("/sessions/".concat(e,"/history")),getActiveSession:async()=>i("/sessions/active")},a=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,s=0,i=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&s<t){let e=500*Math.pow(2,s);return s++,await new Promise(t=>setTimeout(t,e)),i()}throw e}};return i()}}}]);