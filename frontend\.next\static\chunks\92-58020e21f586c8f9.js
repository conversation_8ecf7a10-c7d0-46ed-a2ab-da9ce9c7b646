"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[92],{285:(e,r,t)=>{t.d(r,{$:()=>l});var a=t(5155),s=t(2115),n=t(9434);let o={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},l=s.forwardRef((e,r)=>{let{className:t,variant:s="default",size:l="default",asChild:i=!1,...d}=e,c=o.variant[s]||o.variant.default,u=o.size[l]||o.size.default;return(0,a.jsx)(i?"span":"button",{className:(0,n.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",c,u,t),ref:r,...d})});l.displayName="Button"},2346:(e,r,t)=>{t.d(r,{w:()=>o});var a=t(5155),s=t(2115),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,orientation:s="horizontal",decorative:o=!0,...l}=e;return(0,a.jsx)("div",{ref:r,role:o?"none":"separator","aria-orientation":s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...l})});o.displayName="Separator"},2523:(e,r,t)=>{t.d(r,{p:()=>o});var a=t(5155),s=t(2115),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...o})});o.displayName="Input"},3033:(e,r,t)=>{t.d(r,{A:()=>c});var a=t(5155);t(2115);var s=t(7313),n=t(5695),o=t(1462),l=t(3638),i=t(908);let d=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,a.jsx)(o.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,a.jsx)(l.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,a.jsx)(i.A,{})}];function c(){let e=(0,n.useRouter)(),r=(0,n.usePathname)(),t="orders";return"/dashboard/history"===r?t="history":"/dashboard/analytics"===r&&(t="analytics"),(0,a.jsx)(s.tU,{value:t,onValueChange:r=>{let t=d.find(e=>e.value===r);t&&e.push(t.href)},className:"w-full mb-6",children:(0,a.jsx)(s.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:d.map(e=>(0,a.jsx)(s.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},4530:(e,r,t)=>{t.d(r,{A:()=>b});var a=t(5155),s=t(2115),n=t(7213),o=t(6695),l=t(2523),i=t(285),d=t(518),c=t(5318),u=t(5222),f=t(6392),p=t(5219),m=t(8988);function b(){let{crypto1Balance:e,crypto2Balance:r,stablecoinBalance:t,config:b,dispatch:x}=(0,n.U)(),[g,v]=(0,s.useState)(null),[y,h]=(0,s.useState)({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()}),N=e=>e.toFixed(b.numDigits),j=a=>{v(a),h({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()})},w=t=>{let a=parseFloat(y[t]);!isNaN(a)&&a>=0&&("crypto1"===t?x({type:"UPDATE_BALANCES",payload:{crypto1:a,crypto2:r}}):"crypto2"===t?x({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:a}}):"stablecoin"===t&&x({type:"UPDATE_STABLECOIN_BALANCE",payload:a})),v(null)},C=()=>{v(null),h({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()})},A=(e,r,t,s,n)=>(0,a.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),s]}),(0,a.jsx)(o.Wu,{children:g===t?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.p,{type:"number",value:y[t],onChange:e=>h(r=>({...r,[t]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{size:"sm",onClick:()=>w(t),className:"flex-1",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,a.jsxs)(i.$,{size:"sm",variant:"outline",onClick:C,className:"flex-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground",children:N(r)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",n]})]}),(0,a.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>j(t),className:"ml-2",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})]})})]});return(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3 mb-6",children:[A("".concat(b.crypto1||"Crypto 1"," Balance"),e,"crypto1",(0,a.jsx)(f.A,{className:"h-5 w-5 text-primary"}),b.crypto1||"Crypto 1"),A("".concat(b.crypto2||"Crypto 2"," Balance"),r,"crypto2",(0,a.jsx)(p.A,{className:"h-5 w-5 text-primary"}),b.crypto2||"Crypto 2"),A("Stablecoin Balance (".concat(b.preferredStablecoin||"N/A",")"),t,"stablecoin",(0,a.jsx)(m.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})}},6126:(e,r,t)=>{t.d(r,{E:()=>l});var a=t(5155),s=t(2115),n=t(9434);let o={variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},l=s.forwardRef((e,r)=>{let{className:t,variant:s="default",...l}=e,i=o.variant[s]||o.variant.default;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",i,t),...l})});l.displayName="Badge"},6695:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>l});var a=t(5155),s=t(2115),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...s})});o.displayName="Card";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...s})});l.displayName="CardHeader";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",t),...s})});i.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-4 md:p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",t),...s})}).displayName="CardFooter"},7313:(e,r,t)=>{t.d(r,{Xi:()=>d,av:()=>c,j7:()=>i,tU:()=>l});var a=t(5155),s=t(2115),n=t(9434);let o=s.createContext({activeTab:"",setActiveTab:()=>{}}),l=s.forwardRef((e,r)=>{let{defaultValue:t="",value:n,onValueChange:l,children:i,className:d,...c}=e,[u,f]=s.useState(n||t);return s.useEffect(()=>{void 0!==n&&f(n)},[n]),(0,a.jsx)(o.Provider,{value:{activeTab:u,setActiveTab:e=>{void 0===n&&f(e),null==l||l(e)}},children:(0,a.jsx)("div",{ref:r,className:d,...c,children:i})})});l.displayName="Tabs";let i=s.forwardRef((e,r)=>{let{className:t,children:s,...o}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...o,children:s})});i.displayName="TabsList";let d=s.forwardRef((e,r)=>{let{className:t,value:l,children:i,onClick:d,...c}=e,{activeTab:u,setActiveTab:f}=s.useContext(o);return(0,a.jsx)("button",{ref:r,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",u===l&&"bg-background text-foreground shadow-sm",t),onClick:()=>{f(l),null==d||d()},...c,children:i})});d.displayName="TabsTrigger";let c=s.forwardRef((e,r)=>{let{className:t,value:l,children:i,...d}=e,{activeTab:c}=s.useContext(o);return c!==l?null:(0,a.jsx)("div",{ref:r,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...d,children:i})});c.displayName="TabsContent"},9409:(e,r,t)=>{t.d(r,{bq:()=>d,eb:()=>u,gC:()=>c,l6:()=>i,yv:()=>f});var a=t(5155),s=t(2115),n=t(9556),o=t(9434);let l=s.createContext({value:"",onValueChange:()=>{},open:!1,setOpen:()=>{}}),i=s.forwardRef((e,r)=>{let{children:t,value:n,onValueChange:o,defaultValue:i,...d}=e,[c,u]=s.useState(n||i||""),[f,p]=s.useState(!1);return s.useEffect(()=>{void 0!==n&&u(n)},[n]),(0,a.jsx)(l.Provider,{value:{value:c,onValueChange:e=>{void 0===n&&u(e),null==o||o(e),p(!1)},open:f,setOpen:p},children:(0,a.jsx)("div",{ref:r,className:"relative",...d,children:t})})});i.displayName="Select";let d=s.forwardRef((e,r)=>{let{className:t,children:i,...d}=e,{open:c,setOpen:u}=s.useContext(l);return(0,a.jsxs)("button",{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),onClick:()=>u(!c),...d,children:[i,(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})]})});d.displayName="SelectTrigger";let c=s.forwardRef((e,r)=>{let{className:t,children:n,...i}=e,{open:d}=s.useContext(l);return d?(0,a.jsx)("div",{ref:r,className:(0,o.cn)("absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md",t),...i,children:n}):null});c.displayName="SelectContent";let u=s.forwardRef((e,r)=>{let{className:t,children:n,value:i,...d}=e,{onValueChange:c}=s.useContext(l);return(0,a.jsx)("button",{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",t),onClick:()=>c(i),...d,children:n})});u.displayName="SelectItem";let f=s.forwardRef((e,r)=>{let{placeholder:t,...n}=e,{value:o}=s.useContext(l);return(0,a.jsx)("span",{ref:r,...n,children:o||t})});f.displayName="SelectValue"}}]);