(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{285:(e,r,s)=>{"use strict";s.d(r,{$:()=>i});var t=s(5155),a=s(2115),n=s(9434);let l={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},i=a.forwardRef((e,r)=>{let{className:s,variant:a="default",size:i="default",asChild:o=!1,...d}=e,c=l.variant[a]||l.variant.default,u=l.size[i]||l.size.default;return(0,t.jsx)(o?"span":"button",{className:(0,n.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",c,u,s),ref:r,...d})});i.displayName="Button"},1074:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>z});var t=s(5155),a=s(2115),n=s(5695),l=s(7313),i=s(285),o=s(2523),d=s(5057),c=s(6695),u=s(9434);let m=a.forwardRef((e,r)=>{let{className:s,checked:a,onCheckedChange:n,onChange:l,...i}=e;return(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",ref:r,className:"sr-only",checked:a,onChange:e=>{let r=e.target.checked;null==n||n(r),null==l||l(e)},...i}),(0,t.jsx)("div",{className:(0,u.cn)("relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600",a&&"bg-primary",s)})]})});m.displayName="Switch";var x=s(7213),h=s(8271),f=s(3576),g=s(3503),p=s(5300),v=s(8186),b=s(4607),j=s(7607),y=s(6126),N=s(207),w=s(9119),C=s(3129),S=s(620),E=s(2773),k=s(8718),A=s(7648),O=s(7223),T=s(4553),R=s(4165),D=s(9409);let _={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/ringtones/cheer.wav",soundError:"/ringtones/G_hades_curse.wav"},B="custom_sound_execution",I="custom_sound_error",U=[{value:"/ringtones/cheer.wav",label:"Cheer"},{value:"/ringtones/chest1.wav",label:"Chest"},{value:"/ringtones/chime2.wav",label:"Chime"},{value:"/ringtones/bells.wav",label:"Bells"},{value:"/ringtones/bird1.wav",label:"Bird 1"},{value:"/ringtones/bird7.wav",label:"Bird 2"},{value:"/ringtones/sparrow1.wav",label:"Sparrow"},{value:"/ringtones/space_bells4a.wav",label:"Space Bells"},{value:"/ringtones/sanctuary1.wav",label:"Sanctuary"},{value:"/ringtones/marble1.wav",label:"Marble"},{value:"/ringtones/foundry2.wav",label:"Foundry"},{value:B,label:"Upload Custom..."}],$=[{value:"/ringtones/G_hades_curse.wav",label:"Hades Curse"},{value:"/ringtones/G_hades_demat.wav",label:"Hades Demat"},{value:"/ringtones/G_hades_sanctify.wav",label:"Hades Sanctify"},{value:"/ringtones/dark2.wav",label:"Dark"},{value:"/ringtones/Satyr_atk4.wav",label:"Satyr Attack"},{value:"/ringtones/S_mon1.mp3",label:"Monster 1"},{value:"/ringtones/S_mon2.mp3",label:"Monster 2"},{value:"/ringtones/wolf4.wav",label:"Wolf"},{value:"/ringtones/goatherd1.wav",label:"Goatherd"},{value:"/ringtones/tax3.wav",label:"Tax Alert"},{value:"/ringtones/G_hades_mat.wav",label:"Hades Mat"},{value:I,label:"Upload Custom..."}];function F(e){let{isOpen:r,onClose:s,sessionId:n,sessionName:l}=e,[c,x]=(0,a.useState)({..._,customSoundOrderExecutionDataUri:void 0,customSoundErrorDataUri:void 0}),h=(0,a.useRef)(null);(0,a.useEffect)(()=>{if(r&&n){var e,s;let r=T.SessionManager.getInstance().loadSession(n),t=(null==r?void 0:r.alarmSettings)||_;x({...t,customSoundOrderExecutionDataUri:(null===(e=t.soundOrderExecution)||void 0===e?void 0:e.startsWith("data:audio"))?t.soundOrderExecution:void 0,customSoundErrorDataUri:(null===(s=t.soundError)||void 0===s?void 0:s.startsWith("data:audio"))?t.soundError:void 0})}},[r,n]),(0,a.useEffect)(()=>{h.current=new Audio},[]);let f=(e,r)=>{x(s=>({...s,[e]:r}))},g=(e,r)=>{x(s=>({...s,[e]:r}))},p=(e,r)=>{var s;let t=null===(s=e.target.files)||void 0===s?void 0:s[0];if(t){let e=new FileReader;e.onload=e=>{var s;let t=null===(s=e.target)||void 0===s?void 0:s.result;"orderExecution"===r?x(e=>({...e,customSoundOrderExecutionDataUri:t,soundOrderExecution:B})):x(e=>({...e,customSoundErrorDataUri:t,soundError:I}))},e.readAsDataURL(t)}},v=e=>{let r=c[e];if("soundOrderExecution"===e&&c.soundOrderExecution===B?r=c.customSoundOrderExecutionDataUri||"":"soundError"===e&&c.soundError===I&&(r=c.customSoundErrorDataUri||""),h.current&&r){let e=r;r.startsWith("/sounds/")&&(e=r.replace("/sounds/","/ringtones/")),h.current.src=e,h.current.currentTime=0,h.current.play().then(()=>{setTimeout(()=>{h.current&&(h.current.pause(),h.current.currentTime=0)},2e3)}).catch(e=>{})}};return(0,t.jsx)(R.lG,{open:r,onOpenChange:s,children:(0,t.jsxs)(R.Cf,{className:"sm:max-w-md bg-card border-2 border-border",children:[(0,t.jsxs)(R.c7,{children:[(0,t.jsx)(R.L3,{className:"text-primary",children:"Session Alarm Configuration"}),(0,t.jsxs)(R.rr,{children:['Configure sound alerts for "',l,'". These settings apply only to this session.']})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"soundAlertsEnabled",className:"text-base",children:"Enable Sound Alerts"}),(0,t.jsx)(m,{id:"soundAlertsEnabled",checked:!!c.soundAlertsEnabled,onCheckedChange:e=>f("soundAlertsEnabled",e)})]}),c.soundAlertsEnabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"alertOnOrderExecution",children:"Alert on Successful Order Execution"}),(0,t.jsx)(m,{id:"alertOnOrderExecution",checked:!!c.alertOnOrderExecution,onCheckedChange:e=>f("alertOnOrderExecution",e)})]}),c.alertOnOrderExecution&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(D.l6,{value:c.soundOrderExecution,onValueChange:e=>g("soundOrderExecution",e),children:[(0,t.jsx)(D.bq,{className:"flex-grow",children:(0,t.jsx)(D.yv,{placeholder:"Select sound"})}),(0,t.jsx)(D.gC,{children:U.map(e=>(0,t.jsx)(D.eb,{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>v("soundOrderExecution"),className:"btn-outline-neo p-2",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),c.soundOrderExecution===B&&(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"customSoundExecutionFile",className:"text-xs",children:"Upload Execution Sound (.mp3, .wav, etc.)"}),(0,t.jsx)(o.p,{id:"customSoundExecutionFile",type:"file",accept:"audio/*",onChange:e=>p(e,"orderExecution"),className:(0,u.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),c.customSoundOrderExecutionDataUri&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(d.J,{htmlFor:"alertOnError",children:"Alert on Errors/Failures"}),(0,t.jsx)(m,{id:"alertOnError",checked:!!c.alertOnError,onCheckedChange:e=>f("alertOnError",e)})]}),c.alertOnError&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(D.l6,{value:c.soundError,onValueChange:e=>g("soundError",e),children:[(0,t.jsx)(D.bq,{className:"flex-grow",children:(0,t.jsx)(D.yv,{placeholder:"Select sound"})}),(0,t.jsx)(D.gC,{children:$.map(e=>(0,t.jsx)(D.eb,{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>v("soundError"),className:"btn-outline-neo p-2",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),c.soundError===I&&(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"customSoundErrorFile",className:"text-xs",children:"Upload Error Sound (.mp3, .wav, etc.)"}),(0,t.jsx)(o.p,{id:"customSoundErrorFile",type:"file",accept:"audio/*",onChange:e=>p(e,"error"),className:(0,u.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),c.customSoundErrorDataUri&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]})]})]}),(0,t.jsxs)(R.Es,{children:[(0,t.jsx)(R.HM,{asChild:!0,children:(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,t.jsx)(i.$,{onClick:()=>{let e={...c};c.soundOrderExecution===B&&c.customSoundOrderExecutionDataUri?e.soundOrderExecution=c.customSoundOrderExecutionDataUri:c.soundOrderExecution!==B||c.customSoundOrderExecutionDataUri||(e.soundOrderExecution=_.soundOrderExecution),c.soundError===I&&c.customSoundErrorDataUri?e.soundError=c.customSoundErrorDataUri:c.soundError!==I||c.customSoundErrorDataUri||(e.soundError=_.soundError);let{customSoundOrderExecutionDataUri:r,customSoundErrorDataUri:t,...a}=e;T.SessionManager.getInstance().updateSessionAlarmSettings(n,a)&&s()},className:"btn-primary-neo",children:"Save Settings"})]})]})})}function P(){let{config:e,targetPriceRows:r,orderHistory:s,currentMarketPrice:n,crypto1Balance:l,crypto2Balance:d,stablecoinBalance:u,botSystemStatus:m,dispatch:h}=(0,x.U)(),[f,g]=(0,a.useState)([]),[v,b]=(0,a.useState)(null),[j,R]=(0,a.useState)(null),[D,_]=(0,a.useState)(""),[B,I]=(0,a.useState)(0),[U,$]=(0,a.useState)(!1),[P,z]=(0,a.useState)(""),[M,L]=(0,a.useState)(""),W=T.SessionManager.getInstance();(0,a.useEffect)(()=>{K();let e=W.getCurrentSessionId();b(e),e&&I(W.getCurrentRuntime(e));let r=e=>{"trading_sessions"===e.key&&e.newValue&&K()};return window.addEventListener("storage",r),()=>{window.removeEventListener("storage",r)}},[]),(0,a.useEffect)(()=>{let e=setInterval(()=>{v&&I(W.getCurrentRuntime(v))},5e3);return()=>clearInterval(e)},[v,W]);let K=()=>{g(W.getAllSessions().sort((e,r)=>r.lastModified-e.lastModified))},Z=async()=>{if(v)try{let t,a;let i=W.loadSession(v);if(!i)return;let o=W.getAllSessions(),c=i.name.replace(/ \((Saved|AutoSaved).*\)$/,""),m=o.find(e=>e.id!==v&&e.name.startsWith(c)&&e.name.includes("(Saved")&&!e.isActive);if(m){t=m.id;let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a="".concat(c," (Saved ").concat(e,")")}else{let r=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a="".concat(c," (Saved ").concat(r,")"),t=await W.createNewSession(a,e,{crypto1:l,crypto2:d,stablecoin:u})}let x=W.getCurrentRuntime(v);m&&W.renameSession(t,a),W.saveSession(t,e,r,s,n,l,d,u,!1,x)&&K()}catch(e){}},J=e=>{let r=W.loadSession(e);r&&(h({type:"SET_CONFIG",payload:r.config}),h({type:"SET_TARGET_PRICE_ROWS",payload:r.targetPriceRows}),h({type:"CLEAR_ORDER_HISTORY"}),r.orderHistory.forEach(e=>{h({type:"ADD_ORDER_HISTORY_ENTRY",payload:e})}),h({type:"SET_MARKET_PRICE",payload:r.currentMarketPrice}),h({type:"UPDATE_BALANCES",payload:{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}}),h({type:"SYSTEM_STOP_BOT"}),W.setCurrentSession(e),b(e),K(),setTimeout(()=>{window.location.href="/dashboard"},1e3))},H=e=>{W.deleteSession(e)&&(v===e&&b(null),K())},G=e=>{D.trim()&&W.renameSession(e,D.trim())&&(R(null),_(""),K())},V=(e,r)=>{z(e),L(r),$(!0)},Y=e=>{let r=W.exportSessionToCSV(e);if(!r)return;let s=W.loadSession(e),t=new Blob([r],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),n=URL.createObjectURL(t);a.setAttribute("href",n),a.setAttribute("download","".concat((null==s?void 0:s.name)||"session","_").concat(new Date().toISOString().split("T")[0],".csv")),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a)},X=e=>{if(!e||e<0)return"0s";let r=Math.floor(e/1e3),s=Math.floor(r/3600),t=Math.floor(r%3600/60),a=r%60;return s>0?"".concat(s,"h ").concat(t,"m ").concat(a,"s"):t>0?"".concat(t,"m ").concat(a,"s"):"".concat(a,"s")},Q=()=>f.filter(e=>e.isActive),q=()=>f.filter(e=>!e.isActive&&W.getCurrentRuntime(e.id)>5e3);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Running Sessions"]})}),(0,t.jsx)(c.Wu,{children:Q().length>0?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,t.jsx)("div",{children:"Session Name"}),(0,t.jsx)("div",{children:"Active Status"}),(0,t.jsx)("div",{children:"Runtime"}),(0,t.jsx)("div",{children:"Actions"})]}),(0,t.jsx)("div",{className:"space-y-2",children:Q().map(e=>(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:j===e.id?(0,t.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,t.jsx)(o.p,{value:D,onChange:e=>_(e.target.value),onKeyPress:r=>"Enter"===r.key&&G(e.id),className:"text-sm"}),(0,t.jsx)(i.$,{size:"sm",onClick:()=>G(e.id),children:(0,t.jsx)(w.A,{className:"h-3 w-3"})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>{R(e.id),_(e.name)},children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(y.E,{variant:"default",children:"Active"})}),(0,t.jsx)("div",{className:"text-sm",children:e.id===v?X(B):X(e.runtime)}),(0,t.jsx)("div",{children:e.id===v?(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsxs)(i.$,{onClick:Z,size:"sm",className:"btn-neo",children:[(0,t.jsx)(w.A,{className:"mr-2 h-3 w-3"}),"Save"]}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>V(e.id,e.name),title:"Configure Alarms",children:(0,t.jsx)(S.A,{className:"h-3 w-3"})})]}):(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>J(e.id),title:"Load Session",children:(0,t.jsx)(E.A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>Y(e.id),title:"Export Session",children:(0,t.jsx)(k.A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>V(e.id,e.name),title:"Configure Alarms",children:(0,t.jsx)(S.A,{className:"h-3 w-3"})})]})})]},e.id))})]}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)(N.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{children:"No running sessions"}),(0,t.jsx)("p",{className:"text-xs",children:"Start the bot to see running sessions here"})]})})]}),(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5"}),"Past Sessions (",q().length,")"]}),(0,t.jsxs)(c.BT,{children:["Auto-saved: ",q().length," | Manual: 0"]})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"h-[400px] overflow-y-auto",children:0===q().length?(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{children:"No saved sessions yet."}),(0,t.jsx)("p",{className:"text-xs",children:"Save your current session to get started."})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,t.jsx)("div",{children:"Session Name"}),(0,t.jsx)("div",{children:"Active Status"}),(0,t.jsx)("div",{children:"Total Runtime"}),(0,t.jsx)("div",{children:"Actions"})]}),(0,t.jsx)("div",{className:"space-y-2",children:q().map(e=>(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:j===e.id?(0,t.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,t.jsx)(o.p,{value:D,onChange:e=>_(e.target.value),onKeyPress:r=>"Enter"===r.key&&G(e.id),className:"text-sm"}),(0,t.jsx)(i.$,{size:"sm",onClick:()=>G(e.id),children:(0,t.jsx)(w.A,{className:"h-3 w-3"})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>{R(e.id),_(e.name)},children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(y.E,{variant:"secondary",children:"Inactive"})}),(0,t.jsx)("div",{className:"text-sm",children:X(W.getCurrentRuntime(e.id))}),(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>J(e.id),title:"Load Session",children:(0,t.jsx)(E.A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>Y(e.id),title:"Export Session",children:(0,t.jsx)(k.A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>V(e.id,e.name),title:"Configure Alarms",children:(0,t.jsx)(S.A,{className:"h-3 w-3"})}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>H(e.id),title:"Delete Session",children:(0,t.jsx)(O.A,{className:"h-3 w-3"})})]})]},e.id))})]})})})]}),(0,t.jsx)(F,{isOpen:U,onClose:()=>{$(!1),z(""),L("")},sessionId:P,sessionName:M})]})}function z(){let{botSystemStatus:e}=(0,x.U)(),[r,s]=(0,a.useState)("iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA"),[u,y]=(0,a.useState)("jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp"),[N,w]=(0,a.useState)(!1),[C,S]=(0,a.useState)(!1),[E,k]=(0,a.useState)(""),[A,O]=(0,a.useState)(""),T=(0,n.useRouter)(),R=async()=>{try{localStorage.setItem("binance_api_key",r),localStorage.setItem("binance_api_secret",u)}catch(e){}},D=async()=>{try{(await fetch("https://api.binance.com/api/v3/ping")).ok}catch(e){}},_=async()=>{if(E&&A)try{(await fetch("https://api.telegram.org/bot".concat(E,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:A,text:"\uD83E\uDD16 Test message from Pluto Trading Bot! Your Telegram integration is working correctly."})})).ok}catch(e){}},B=[{value:"systemTools",label:"System Tools",icon:(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"})},{value:"apiKeys",label:"Exchange API Keys",icon:(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"})},{value:"telegram",label:"Telegram Integration",icon:(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4"})},{value:"sessionManager",label:"Session Manager",icon:(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"})}];return(0,t.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,t.jsxs)(c.Zp,{className:"border-2 border-border",children:[(0,t.jsxs)(c.aR,{className:"flex flex-row justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.ZB,{className:"text-3xl font-bold text-primary",children:"Admin Panel"}),(0,t.jsx)(c.BT,{children:"Manage global settings and tools for Pluto Trading Bot."})]}),(0,t.jsxs)(i.$,{variant:"outline",onClick:()=>T.push("/dashboard"),className:"btn-outline-neo",children:[(0,t.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Return to Dashboard"]})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(l.tU,{defaultValue:"systemTools",className:"w-full",children:[(0,t.jsx)("div",{className:"pb-2 overflow-x-auto",children:(0,t.jsx)(l.j7,{className:"bg-card border-border border-2 p-1",children:B.map(e=>(0,t.jsx)(l.Xi,{value:e.value,className:"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:(0,t.jsxs)("div",{className:"flex items-center",children:[e.icon," ",e.label]})},e.value))})}),(0,t.jsx)(l.av,{value:"systemTools",className:"mt-6",children:(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"System Tools"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation)."}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("DB Editor Clicked"),children:"View Database (Read-Only)"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Export Orders Clicked"),children:"Export Orders to Excel"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Export History Clicked"),children:"Export History to Excel"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Backup DB Clicked"),children:"Backup Database"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Restore DB Clicked"),disabled:!0,children:"Restore Database"}),(0,t.jsx)(i.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>console.log("Diagnostics Clicked"),children:"Run System Diagnostics"})]})]})]})})}),(0,t.jsx)(l.av,{value:"apiKeys",className:"mt-6",children:(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Exchange API Keys (Binance)"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure your Binance API keys for real trading. Keys are stored securely in browser storage."}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"apiKey",children:"API Key"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{id:"apiKey",type:N?"text":"password",value:r,onChange:e=>s(e.target.value),placeholder:"Enter your Binance API key",className:"pr-10"}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!N),children:N?(0,t.jsx)(b.A,{className:"h-4 w-4"}):(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"apiSecret",children:"API Secret"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.p,{id:"apiSecret",type:C?"text":"password",value:u,onChange:e=>y(e.target.value),placeholder:"Enter your Binance API secret",className:"pr-10"}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>S(!C),children:C?(0,t.jsx)(b.A,{className:"h-4 w-4"}):(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.$,{onClick:R,className:"btn-neo",children:"Save API Keys"}),(0,t.jsx)(i.$,{onClick:D,variant:"outline",className:"btn-outline-neo",children:"Test Connection"})]})]})]})}),(0,t.jsx)(l.av,{value:"telegram",className:"mt-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Telegram Configuration"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure Telegram bot for real-time trading notifications."}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"telegramToken",children:"Telegram Bot Token"}),(0,t.jsx)(o.p,{id:"telegramToken",type:"password",value:E,onChange:e=>k(e.target.value),placeholder:"Enter your Telegram bot token"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.J,{htmlFor:"telegramChatId",children:"Telegram Chat ID"}),(0,t.jsx)(o.p,{id:"telegramChatId",value:A,onChange:e=>O(e.target.value),placeholder:"Enter your Telegram chat ID"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m,{id:"notifyOnOrder"}),(0,t.jsx)(d.J,{htmlFor:"notifyOnOrder",children:"Notify on Order Execution"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m,{id:"notifyOnErrors"}),(0,t.jsx)(d.J,{htmlFor:"notifyOnErrors",children:"Notify on Errors"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.$,{onClick:()=>{try{localStorage.setItem("telegram_bot_token",E),localStorage.setItem("telegram_chat_id",A)}catch(e){}},className:"btn-neo",children:"Save Telegram Config"}),(0,t.jsx)(i.$,{onClick:_,variant:"outline",className:"btn-outline-neo",children:"Test Telegram"})]})]})]}),(0,t.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Setup Guide"})}),(0,t.jsx)(c.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 1: Create a Telegram Bot"}),(0,t.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,t.jsxs)("li",{children:["Open Telegram and search for ",(0,t.jsx)("code",{className:"bg-muted px-1 rounded",children:"@BotFather"})]}),(0,t.jsxs)("li",{children:["Send ",(0,t.jsx)("code",{className:"bg-muted px-1 rounded",children:"/newbot"})," command"]}),(0,t.jsx)("li",{children:'Choose a name for your bot (e.g., "My Trading Bot")'}),(0,t.jsx)("li",{children:'Choose a username ending with "bot" (e.g., "mytradingbot")'}),(0,t.jsx)("li",{children:"Copy the bot token provided by BotFather"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 2: Get Your Chat ID"}),(0,t.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,t.jsx)("li",{children:"Start a chat with your new bot"}),(0,t.jsx)("li",{children:"Send any message to the bot"}),(0,t.jsxs)("li",{children:["Visit: ",(0,t.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates"})]}),(0,t.jsx)("li",{children:'Look for "chat" and "id" fields in the response'}),(0,t.jsx)("li",{children:"Copy the chat ID number"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 3: Configure Bot"}),(0,t.jsxs)("ul",{className:"text-sm space-y-1 list-disc list-inside text-muted-foreground",children:[(0,t.jsx)("li",{children:'Paste the bot token in the "Telegram Bot Token" field'}),(0,t.jsx)("li",{children:'Paste the chat ID in the "Telegram Chat ID" field'}),(0,t.jsx)("li",{children:"Choose your notification preferences"}),(0,t.jsx)("li",{children:'Click "Save Telegram Config"'}),(0,t.jsx)("li",{children:'Test the connection with "Test Telegram"'})]})]}),(0,t.jsx)("div",{className:"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-yellow-600",children:"\uD83D\uDCA1"}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("p",{className:"font-medium text-yellow-600 mb-1",children:"Pro Tip:"}),(0,t.jsx)("p",{className:"text-yellow-700",children:"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed."})]})]})})]})})]})]})}),(0,t.jsx)(l.av,{value:"sessionManager",className:"mt-6",children:(0,t.jsx)(P,{})})]})})]})})}},2136:(e,r,s)=>{Promise.resolve().then(s.bind(s,1074))},2523:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var t=s(5155),a=s(2115),n=s(9434);let l=a.forwardRef((e,r)=>{let{className:s,type:a,...l}=e;return(0,t.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,...l})});l.displayName="Input"},4165:(e,r,s)=>{"use strict";s.d(r,{Cf:()=>m,Es:()=>h,HM:()=>c,L3:()=>f,c7:()=>x,lG:()=>o,rr:()=>g});var t=s(5155),a=s(2115),n=s(5318),l=s(9434);let i=a.createContext({open:!1,onOpenChange:()=>{}}),o=e=>{let{children:r,open:s=!1,onOpenChange:n}=e,[l,o]=a.useState(s);return a.useEffect(()=>{o(s)},[s]),(0,t.jsx)(i.Provider,{value:{open:l,onOpenChange:e=>{o(e),null==n||n(e)}},children:r})};a.forwardRef((e,r)=>{let{className:s,children:n,asChild:l=!1,...o}=e,{onOpenChange:d}=a.useContext(i);return(0,t.jsx)("button",{ref:r,className:s,onClick:()=>d(!0),...o,children:n})}).displayName="DialogTrigger";let d=e=>{let{children:r}=e;return(0,t.jsx)(t.Fragment,{children:r})},c=a.forwardRef((e,r)=>{let{className:s,children:n,asChild:l=!1,...o}=e,{onOpenChange:d}=a.useContext(i);return(0,t.jsx)("button",{ref:r,className:s,onClick:()=>d(!1),...o,children:n})});c.displayName="DialogClose";let u=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80",s),...a})});u.displayName="DialogOverlay";let m=a.forwardRef((e,r)=>{let{className:s,children:o,...c}=e,{open:m,onOpenChange:x}=a.useContext(i);return m?(0,t.jsxs)(d,{children:[(0,t.jsx)(u,{onClick:()=>x(!1)}),(0,t.jsxs)("div",{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg",s),...c,children:[o,(0,t.jsxs)("button",{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",onClick:()=>x(!1),children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}):null});m.displayName="DialogContent";let x=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})});x.displayName="DialogHeader";let h=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})});h.displayName="DialogFooter";let f=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h2",{ref:r,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",s),...a})});f.displayName="DialogTitle";let g=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});g.displayName="DialogDescription"},5057:(e,r,s)=>{"use strict";s.d(r,{J:()=>l});var t=s(5155),a=s(2115),n=s(9434);let l=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("label",{ref:r,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",s),...a})});l.displayName="Label"},6126:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(5155),a=s(2115),n=s(9434);let l={variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},i=a.forwardRef((e,r)=>{let{className:s,variant:a="default",...i}=e,o=l.variant[a]||l.variant.default;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",o,s),...i})});i.displayName="Badge"},6695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var t=s(5155),a=s(2115),n=s(9434);let l=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",s),...a})});l.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",s),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-4 md:p-6 pt-0",s),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",s),...a})}).displayName="CardFooter"},7313:(e,r,s)=>{"use strict";s.d(r,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var t=s(5155),a=s(2115),n=s(9434);let l=a.createContext({activeTab:"",setActiveTab:()=>{}}),i=a.forwardRef((e,r)=>{let{defaultValue:s="",value:n,onValueChange:i,children:o,className:d,...c}=e,[u,m]=a.useState(n||s);return a.useEffect(()=>{void 0!==n&&m(n)},[n]),(0,t.jsx)(l.Provider,{value:{activeTab:u,setActiveTab:e=>{void 0===n&&m(e),null==i||i(e)}},children:(0,t.jsx)("div",{ref:r,className:d,...c,children:o})})});i.displayName="Tabs";let o=a.forwardRef((e,r)=>{let{className:s,children:a,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...l,children:a})});o.displayName="TabsList";let d=a.forwardRef((e,r)=>{let{className:s,value:i,children:o,onClick:d,...c}=e,{activeTab:u,setActiveTab:m}=a.useContext(l);return(0,t.jsx)("button",{ref:r,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",u===i&&"bg-background text-foreground shadow-sm",s),onClick:()=>{m(i),null==d||d()},...c,children:o})});d.displayName="TabsTrigger";let c=a.forwardRef((e,r)=>{let{className:s,value:i,children:o,...d}=e,{activeTab:c}=a.useContext(l);return c!==i?null:(0,t.jsx)("div",{ref:r,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...d,children:o})});c.displayName="TabsContent"},9409:(e,r,s)=>{"use strict";s.d(r,{bq:()=>d,eb:()=>u,gC:()=>c,l6:()=>o,yv:()=>m});var t=s(5155),a=s(2115),n=s(9556),l=s(9434);let i=a.createContext({value:"",onValueChange:()=>{},open:!1,setOpen:()=>{}}),o=a.forwardRef((e,r)=>{let{children:s,value:n,onValueChange:l,defaultValue:o,...d}=e,[c,u]=a.useState(n||o||""),[m,x]=a.useState(!1);return a.useEffect(()=>{void 0!==n&&u(n)},[n]),(0,t.jsx)(i.Provider,{value:{value:c,onValueChange:e=>{void 0===n&&u(e),null==l||l(e),x(!1)},open:m,setOpen:x},children:(0,t.jsx)("div",{ref:r,className:"relative",...d,children:s})})});o.displayName="Select";let d=a.forwardRef((e,r)=>{let{className:s,children:o,...d}=e,{open:c,setOpen:u}=a.useContext(i);return(0,t.jsxs)("button",{ref:r,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),onClick:()=>u(!c),...d,children:[o,(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})]})});d.displayName="SelectTrigger";let c=a.forwardRef((e,r)=>{let{className:s,children:n,...o}=e,{open:d}=a.useContext(i);return d?(0,t.jsx)("div",{ref:r,className:(0,l.cn)("absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md",s),...o,children:n}):null});c.displayName="SelectContent";let u=a.forwardRef((e,r)=>{let{className:s,children:n,value:o,...d}=e,{onValueChange:c}=a.useContext(i);return(0,t.jsx)("button",{ref:r,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",s),onClick:()=>c(o),...d,children:n})});u.displayName="SelectItem";let m=a.forwardRef((e,r)=>{let{placeholder:s,...n}=e,{value:l}=a.useContext(i);return(0,t.jsx)("span",{ref:r,...n,children:l||s})});m.displayName="SelectValue"}},e=>{var r=r=>e(e.s=r);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,213,358],()=>r(2136)),_N_E=e.O()}]);