/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/admin/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(app-pages-browser)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanelPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/SessionManager */ \"(app-pages-browser)/./src/components/admin/SessionManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Removed ScrollArea import to fix webpack issues\n\n\nfunction AdminPanelPage() {\n    _s();\n    const { botSystemStatus } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_9__.useTradingContext)();\n    const [apiKey, setApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');\n    const [apiSecret, setApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');\n    const [showApiKey, setShowApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApiSecret, setShowApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [telegramToken, setTelegramToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [telegramChatId, setTelegramChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSaveApiKeys = async ()=>{\n        try {\n            // Store API keys securely (in a real implementation, these would be encrypted)\n            localStorage.setItem('binance_api_key', apiKey);\n            localStorage.setItem('binance_api_secret', apiSecret);\n            console.log(\"API Keys Saved:\", {\n                apiKey: apiKey.substring(0, 10) + '...',\n                apiSecret: apiSecret.substring(0, 10) + '...'\n            });\n        } catch (error) {\n            console.error(\"Failed to save API keys:\", error);\n        }\n    };\n    const handleTestApiConnection = async ()=>{\n        try {\n            // Test connection to Binance API\n            const response = await fetch('https://api.binance.com/api/v3/ping');\n            if (response.ok) {\n                console.log(\"Successfully connected to Binance API!\");\n            } else {\n                console.error(\"Unable to connect to Binance API.\");\n            }\n        } catch (error) {\n            console.error(\"Network error while testing API connection:\", error);\n        }\n    };\n    const handleSaveTelegramConfig = ()=>{\n        try {\n            localStorage.setItem('telegram_bot_token', telegramToken);\n            localStorage.setItem('telegram_chat_id', telegramChatId);\n            console.log(\"Telegram Config Saved:\", {\n                telegramToken: telegramToken.substring(0, 10) + '...',\n                telegramChatId\n            });\n        } catch (error) {\n            console.error(\"Failed to save Telegram configuration:\", error);\n        }\n    };\n    const handleTestTelegram = async ()=>{\n        if (!telegramToken || !telegramChatId) {\n            console.error(\"Please enter both Telegram bot token and chat ID.\");\n            return;\n        }\n        try {\n            const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    chat_id: telegramChatId,\n                    text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'\n                })\n            });\n            if (response.ok) {\n                console.log(\"Test message sent successfully!\");\n            } else {\n                console.error(\"Failed to send test message. Check your token and chat ID.\");\n            }\n        } catch (error) {\n            console.error(\"Network error while testing Telegram integration:\", error);\n        }\n    };\n    const adminTabs = [\n        {\n            value: \"systemTools\",\n            label: \"System Tools\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"apiKeys\",\n            label: \"Exchange API Keys\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 59\n            }, this)\n        },\n        {\n            value: \"telegram\",\n            label: \"Telegram Integration\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 63\n            }, this)\n        },\n        {\n            value: \"sessionManager\",\n            label: \"Session Manager\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 64\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                    className: \"flex flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-3xl font-bold text-primary\",\n                                    children: \"Admin Panel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Manage global settings and tools for Pluto Trading Bot.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard'),\n                            className: \"btn-outline-neo\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"systemTools\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pb-2 overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"bg-card border-border border-2 p-1\",\n                                    children: adminTabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: tab.value,\n                                            className: \"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    tab.icon,\n                                                    \" \",\n                                                    tab.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, tab.value, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 20\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"systemTools\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        className: \"bg-card-foreground/5 border-border border-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"System Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 31\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation).\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>console.log(\"DB Editor Clicked\"),\n                                                                children: \"View Database (Read-Only)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>console.log(\"Export Orders Clicked\"),\n                                                                children: \"Export Orders to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>console.log(\"Export History Clicked\"),\n                                                                children: \"Export History to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>console.log(\"Backup DB Clicked\"),\n                                                                children: \"Backup Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>console.log(\"Restore DB Clicked\"),\n                                                                disabled: true,\n                                                                children: \"Restore Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>console.log(\"Diagnostics Clicked\"),\n                                                                children: \"Run System Diagnostics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"apiKeys\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Exchange API Keys (Binance)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Configure your Binance API keys for real trading. Keys are stored securely in browser storage.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiKey\",\n                                                            children: \"API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiKey\",\n                                                                    type: showApiKey ? \"text\" : \"password\",\n                                                                    value: apiKey,\n                                                                    onChange: (e)=>setApiKey(e.target.value),\n                                                                    placeholder: \"Enter your Binance API key\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiKey(!showApiKey),\n                                                                    children: showApiKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiSecret\",\n                                                            children: \"API Secret\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiSecret\",\n                                                                    type: showApiSecret ? \"text\" : \"password\",\n                                                                    value: apiSecret,\n                                                                    onChange: (e)=>setApiSecret(e.target.value),\n                                                                    placeholder: \"Enter your Binance API secret\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiSecret(!showApiSecret),\n                                                                    children: showApiSecret ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 42\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 75\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleSaveApiKeys,\n                                                            className: \"btn-neo\",\n                                                            children: \"Save API Keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleTestApiConnection,\n                                                            variant: \"outline\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: \"Test Connection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"telegram\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Telegram Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Configure Telegram bot for real-time trading notifications.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramToken\",\n                                                                    children: \"Telegram Bot Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramToken\",\n                                                                    type: \"password\",\n                                                                    value: telegramToken,\n                                                                    onChange: (e)=>setTelegramToken(e.target.value),\n                                                                    placeholder: \"Enter your Telegram bot token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramChatId\",\n                                                                    children: \"Telegram Chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramChatId\",\n                                                                    value: telegramChatId,\n                                                                    onChange: (e)=>setTelegramChatId(e.target.value),\n                                                                    placeholder: \"Enter your Telegram chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                                    id: \"notifyOnOrder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnOrder\",\n                                                                    children: \"Notify on Order Execution\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                                    id: \"notifyOnErrors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnErrors\",\n                                                                    children: \"Notify on Errors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleSaveTelegramConfig,\n                                                                    className: \"btn-neo\",\n                                                                    children: \"Save Telegram Config\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleTestTelegram,\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    children: \"Test Telegram\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Setup Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 1: Create a Telegram Bot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                        className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Open Telegram and search for \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded\",\n                                                                                        children: \"@BotFather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 244,\n                                                                                        columnNumber: 60\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Send \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded\",\n                                                                                        children: \"/newbot\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 245,\n                                                                                        columnNumber: 36\n                                                                                    }, this),\n                                                                                    \" command\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Choose a name for your bot (e.g., \"My Trading Bot\")'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 246,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Choose a username ending with \"bot\" (e.g., \"mytradingbot\")'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Copy the bot token provided by BotFather\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 248,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 2: Get Your Chat ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                        className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Start a chat with your new bot\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Send any message to the bot\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 256,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Visit: \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded text-xs\",\n                                                                                        children: \"https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 257,\n                                                                                        columnNumber: 38\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 257,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Look for \"chat\" and \"id\" fields in the response'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Copy the chat ID number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 3: Configure Bot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm space-y-1 list-disc list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Paste the bot token in the \"Telegram Bot Token\" field'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Paste the chat ID in the \"Telegram Chat ID\" field'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Choose your notification preferences\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Click \"Save Telegram Config\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 269,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Test the connection with \"Test Telegram\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-yellow-600\",\n                                                                            children: \"\\uD83D\\uDCA1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-yellow-600 mb-1\",\n                                                                                    children: \"Pro Tip:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 278,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-yellow-700\",\n                                                                                    children: \"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"sessionManager\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_10__.SessionManager, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanelPage, \"NIIPnc6UvlcyBy6GHqOuwEOqpt0=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_9__.useTradingContext,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanelPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPanelPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/modals/SessionAlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [alarmModalSessionId, setAlarmModalSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [alarmModalSessionName, setAlarmModalSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_6__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            console.error(\"No active session to save\");\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                console.error(\"Current session not found\");\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config, {\n                    crypto1: crypto1Balance,\n                    crypto2: crypto2Balance,\n                    stablecoin: stablecoinBalance\n                });\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                console.log(existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\"));\n            } else {\n                console.error(\"Failed to save session\");\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            console.error(\"Failed to load session\");\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'UPDATE_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance,\n                stablecoin: session.stablecoinBalance\n            }\n        });\n        // Always set bot status to Stopped when loading past sessions - manual control required\n        dispatch({\n            type: 'SYSTEM_STOP_BOT'\n        });\n        console.log('🔄 Past session loaded - bot status set to Stopped for manual control');\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        console.log('Session \"'.concat(session.name, '\" has been loaded. Bot is stopped - click Start Trading to begin.'));\n        // Navigate to dashboard after loading session\n        setTimeout(()=>{\n            window.location.href = '/dashboard';\n        }, 1000);\n    };\n    const handleDeleteSession = (sessionId)=>{\n        const success = sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            console.log(\"Session has been deleted successfully\");\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            console.log(\"Session has been renamed successfully\");\n        }\n    };\n    const handleOpenAlarmModal = (sessionId, sessionName)=>{\n        setAlarmModalSessionId(sessionId);\n        setAlarmModalSessionName(sessionName);\n        setAlarmModalOpen(true);\n    };\n    const handleCloseAlarmModal = ()=>{\n        setAlarmModalOpen(false);\n        setAlarmModalSessionId('');\n        setAlarmModalSessionName('');\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            console.error(\"Failed to export session\");\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        console.log(\"Session data has been exported to CSV\");\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        return sessions.filter((s)=>s.isActive);\n    };\n    const getRunningSessions = ()=>{\n        // Show active sessions immediately (no time delay)\n        return sessions.filter((s)=>s.isActive);\n    };\n    const getInactiveSessions = ()=>{\n        // Show inactive sessions that have run for more than 5 seconds\n        return sessions.filter((s)=>!s.isActive && sessionManager.getCurrentRuntime(s.id) > 5000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                \"Running Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getRunningSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getRunningSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: handleSaveCurrentSession,\n                                                                size: \"sm\",\n                                                                className: \"btn-neo\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"mr-2 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Save\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, session.id, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No running sessions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start the bot to see running sessions here\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[400px] overflow-y-auto\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_8__.SessionAlarmConfigModal, {\n                isOpen: alarmModalOpen,\n                onClose: handleCloseAlarmModal,\n                sessionId: alarmModalSessionId,\n                sessionName: alarmModalSessionName\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"tEo+UxH7DF3OwtNU9Yy9NmFLrLU=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx":
/*!***********************************************************!*\
  !*** ./src/components/modals/SessionAlarmConfigModal.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionAlarmConfigModal: () => (/* binding */ SessionAlarmConfigModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _barrel_optimize_names_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SessionAlarmConfigModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Define default session alarm settings locally to avoid import issues\nconst DEFAULT_SESSION_ALARM_SETTINGS = {\n    soundAlertsEnabled: true,\n    alertOnOrderExecution: true,\n    alertOnError: true,\n    soundOrderExecution: \"/ringtones/cheer.wav\",\n    soundError: \"/ringtones/G_hades_curse.wav\"\n};\n\nconst CUSTOM_SOUND_EXECUTION_VALUE = \"custom_sound_execution\";\nconst CUSTOM_SOUND_ERROR_VALUE = \"custom_sound_error\";\nconst executionSoundOptions = [\n    {\n        value: \"/ringtones/cheer.wav\",\n        label: \"Cheer\"\n    },\n    {\n        value: \"/ringtones/chest1.wav\",\n        label: \"Chest\"\n    },\n    {\n        value: \"/ringtones/chime2.wav\",\n        label: \"Chime\"\n    },\n    {\n        value: \"/ringtones/bells.wav\",\n        label: \"Bells\"\n    },\n    {\n        value: \"/ringtones/bird1.wav\",\n        label: \"Bird 1\"\n    },\n    {\n        value: \"/ringtones/bird7.wav\",\n        label: \"Bird 2\"\n    },\n    {\n        value: \"/ringtones/sparrow1.wav\",\n        label: \"Sparrow\"\n    },\n    {\n        value: \"/ringtones/space_bells4a.wav\",\n        label: \"Space Bells\"\n    },\n    {\n        value: \"/ringtones/sanctuary1.wav\",\n        label: \"Sanctuary\"\n    },\n    {\n        value: \"/ringtones/marble1.wav\",\n        label: \"Marble\"\n    },\n    {\n        value: \"/ringtones/foundry2.wav\",\n        label: \"Foundry\"\n    },\n    {\n        value: CUSTOM_SOUND_EXECUTION_VALUE,\n        label: \"Upload Custom...\"\n    }\n];\nconst errorSoundOptions = [\n    {\n        value: \"/ringtones/G_hades_curse.wav\",\n        label: \"Hades Curse\"\n    },\n    {\n        value: \"/ringtones/G_hades_demat.wav\",\n        label: \"Hades Demat\"\n    },\n    {\n        value: \"/ringtones/G_hades_sanctify.wav\",\n        label: \"Hades Sanctify\"\n    },\n    {\n        value: \"/ringtones/dark2.wav\",\n        label: \"Dark\"\n    },\n    {\n        value: \"/ringtones/Satyr_atk4.wav\",\n        label: \"Satyr Attack\"\n    },\n    {\n        value: \"/ringtones/S_mon1.mp3\",\n        label: \"Monster 1\"\n    },\n    {\n        value: \"/ringtones/S_mon2.mp3\",\n        label: \"Monster 2\"\n    },\n    {\n        value: \"/ringtones/wolf4.wav\",\n        label: \"Wolf\"\n    },\n    {\n        value: \"/ringtones/goatherd1.wav\",\n        label: \"Goatherd\"\n    },\n    {\n        value: \"/ringtones/tax3.wav\",\n        label: \"Tax Alert\"\n    },\n    {\n        value: \"/ringtones/G_hades_mat.wav\",\n        label: \"Hades Mat\"\n    },\n    {\n        value: CUSTOM_SOUND_ERROR_VALUE,\n        label: \"Upload Custom...\"\n    }\n];\nfunction SessionAlarmConfigModal(param) {\n    let { isOpen, onClose, sessionId, sessionName } = param;\n    _s();\n    const [localSettings, setLocalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...DEFAULT_SESSION_ALARM_SETTINGS,\n        customSoundOrderExecutionDataUri: undefined,\n        customSoundErrorDataUri: undefined\n    });\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAlarmConfigModal.useEffect\": ()=>{\n            if (isOpen && sessionId) {\n                var _alarmSettings_soundOrderExecution, _alarmSettings_soundError;\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n                const session = sessionManager.loadSession(sessionId);\n                const alarmSettings = (session === null || session === void 0 ? void 0 : session.alarmSettings) || DEFAULT_SESSION_ALARM_SETTINGS;\n                setLocalSettings({\n                    ...alarmSettings,\n                    customSoundOrderExecutionDataUri: ((_alarmSettings_soundOrderExecution = alarmSettings.soundOrderExecution) === null || _alarmSettings_soundOrderExecution === void 0 ? void 0 : _alarmSettings_soundOrderExecution.startsWith('data:audio')) ? alarmSettings.soundOrderExecution : undefined,\n                    customSoundErrorDataUri: ((_alarmSettings_soundError = alarmSettings.soundError) === null || _alarmSettings_soundError === void 0 ? void 0 : _alarmSettings_soundError.startsWith('data:audio')) ? alarmSettings.soundError : undefined\n                });\n            }\n        }\n    }[\"SessionAlarmConfigModal.useEffect\"], [\n        isOpen,\n        sessionId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAlarmConfigModal.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"SessionAlarmConfigModal.useEffect\"], []);\n    const handleSwitchChange = (id, checked)=>{\n        setLocalSettings((prev)=>({\n                ...prev,\n                [id]: checked\n            }));\n    };\n    const handleSelectChange = (id, value)=>{\n        setLocalSettings((prev)=>({\n                ...prev,\n                [id]: value\n            }));\n    };\n    const handleFileChange = (event, type)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const dataUri = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                if (type === 'orderExecution') {\n                    setLocalSettings((prev)=>({\n                            ...prev,\n                            customSoundOrderExecutionDataUri: dataUri,\n                            soundOrderExecution: CUSTOM_SOUND_EXECUTION_VALUE\n                        }));\n                } else {\n                    setLocalSettings((prev)=>({\n                            ...prev,\n                            customSoundErrorDataUri: dataUri,\n                            soundError: CUSTOM_SOUND_ERROR_VALUE\n                        }));\n                }\n                console.log(\"File uploaded: \".concat(file.name, \" ready to be used.\"));\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const handleSave = ()=>{\n        const finalSettings = {\n            ...localSettings\n        };\n        if (localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && localSettings.customSoundOrderExecutionDataUri) {\n            finalSettings.soundOrderExecution = localSettings.customSoundOrderExecutionDataUri;\n        } else if (localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && !localSettings.customSoundOrderExecutionDataUri) {\n            finalSettings.soundOrderExecution = DEFAULT_SESSION_ALARM_SETTINGS.soundOrderExecution;\n            console.log(\"Notice: No custom execution sound uploaded, default used.\");\n        }\n        if (localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && localSettings.customSoundErrorDataUri) {\n            finalSettings.soundError = localSettings.customSoundErrorDataUri;\n        } else if (localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && !localSettings.customSoundErrorDataUri) {\n            finalSettings.soundError = DEFAULT_SESSION_ALARM_SETTINGS.soundError;\n            console.log(\"Notice: No custom error sound uploaded, default used.\");\n        }\n        // Remove custom data URI properties before saving\n        const { customSoundOrderExecutionDataUri, customSoundErrorDataUri, ...settingsToSave } = finalSettings;\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n        const success = sessionManager.updateSessionAlarmSettings(sessionId, settingsToSave);\n        if (success) {\n            console.log('Session Alarm Settings Saved: Sound alerts updated for \"'.concat(sessionName, '\".'));\n            onClose();\n        } else {\n            console.error(\"Error: Failed to save alarm settings.\");\n        }\n    };\n    const playTestSound = (soundSettingKey)=>{\n        let soundToPlay = localSettings[soundSettingKey];\n        if (soundSettingKey === 'soundOrderExecution' && localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE) {\n            soundToPlay = localSettings.customSoundOrderExecutionDataUri || '';\n        } else if (soundSettingKey === 'soundError' && localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE) {\n            soundToPlay = localSettings.customSoundErrorDataUri || '';\n        }\n        if (audioRef.current && soundToPlay) {\n            // Validate and fix sound path if needed\n            let validSoundPath = soundToPlay;\n            // Fix old /sounds/ paths to /ringtones/\n            if (soundToPlay.startsWith('/sounds/')) {\n                validSoundPath = soundToPlay.replace('/sounds/', '/ringtones/');\n                console.warn(\"Fixed deprecated sound path: \".concat(soundToPlay, \" -> \").concat(validSoundPath));\n            }\n            audioRef.current.src = validSoundPath;\n            audioRef.current.currentTime = 0; // Reset to beginning\n            // Play the sound and limit duration to 2 seconds\n            audioRef.current.play().then(()=>{\n                // Set a timeout to pause the audio after 2 seconds\n                setTimeout(()=>{\n                    if (audioRef.current) {\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0; // Reset for next play\n                    }\n                }, 2000); // 2 seconds\n            }).catch((error)=>{\n                console.error(\"Error playing sound:\", error);\n                console.error(\"Could not play test sound. Ensure file is valid or browser permissions are set.\");\n            });\n        } else {\n            console.log(\"No sound file selected or uploaded.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md bg-card border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"text-primary\",\n                            children: \"Session Alarm Configuration\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: [\n                                'Configure sound alerts for \"',\n                                sessionName,\n                                '\". These settings apply only to this session.'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"soundAlertsEnabled\",\n                                    className: \"text-base\",\n                                    children: \"Enable Sound Alerts\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                    id: \"soundAlertsEnabled\",\n                                    checked: !!localSettings.soundAlertsEnabled,\n                                    onCheckedChange: (checked)=>handleSwitchChange('soundAlertsEnabled', checked)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        localSettings.soundAlertsEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 p-4 border-2 border-border rounded-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"alertOnOrderExecution\",\n                                                    children: \"Alert on Successful Order Execution\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                    id: \"alertOnOrderExecution\",\n                                                    checked: !!localSettings.alertOnOrderExecution,\n                                                    onCheckedChange: (checked)=>handleSwitchChange('alertOnOrderExecution', checked)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        localSettings.alertOnOrderExecution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: localSettings.soundOrderExecution,\n                                                            onValueChange: (value)=>handleSelectChange('soundOrderExecution', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    className: \"flex-grow\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select sound\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 62\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: executionSoundOptions.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: s.value,\n                                                                            children: s.label\n                                                                        }, s.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 59\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>playTestSound('soundOrderExecution'),\n                                                            className: \"btn-outline-neo p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this),\n                                                localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"customSoundExecutionFile\",\n                                                            className: \"text-xs\",\n                                                            children: \"Upload Execution Sound (.mp3, .wav, etc.)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"customSoundExecutionFile\",\n                                                            type: \"file\",\n                                                            accept: \"audio/*\",\n                                                            onChange: (e)=>handleFileChange(e, 'orderExecution'),\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"text-xs mt-1\", \"focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary\")\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        localSettings.customSoundOrderExecutionDataUri && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground truncate mt-1\",\n                                                            children: \"Current: Custom sound uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 76\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 p-4 border-2 border-border rounded-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"alertOnError\",\n                                                    children: \"Alert on Errors/Failures\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                                    id: \"alertOnError\",\n                                                    checked: !!localSettings.alertOnError,\n                                                    onCheckedChange: (checked)=>handleSwitchChange('alertOnError', checked)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        localSettings.alertOnError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: localSettings.soundError,\n                                                            onValueChange: (value)=>handleSelectChange('soundError', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    className: \"flex-grow\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select sound\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 62\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: errorSoundOptions.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: s.value,\n                                                                            children: s.label\n                                                                        }, s.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 55\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>playTestSound('soundError'),\n                                                            className: \"btn-outline-neo p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 22\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 21\n                                                }, this),\n                                                localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"customSoundErrorFile\",\n                                                            className: \"text-xs\",\n                                                            children: \"Upload Error Sound (.mp3, .wav, etc.)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            id: \"customSoundErrorFile\",\n                                                            type: \"file\",\n                                                            accept: \"audio/*\",\n                                                            onChange: (e)=>handleFileChange(e, 'error'),\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"text-xs mt-1\", \"focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary\")\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        localSettings.customSoundErrorDataUri && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground truncate mt-1\",\n                                                            children: \"Current: Custom sound uploaded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 67\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogClose, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                className: \"btn-outline-neo\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSave,\n                            className: \"btn-primary-neo\",\n                            children: \"Save Settings\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmConfigModal.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAlarmConfigModal, \"AJdpRvAurCLuwhVDn3EhsWG2kg4=\");\n_c = SessionAlarmConfigModal;\nvar _c;\n$RefreshReg$(_c, \"SessionAlarmConfigModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst DialogContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    open: false,\n    onOpenChange: ()=>{}\n});\nconst Dialog = (param)=>{\n    let { children, open = false, onOpenChange } = param;\n    _s();\n    const [internalOpen, setInternalOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(open);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Dialog.useEffect\": ()=>{\n            setInternalOpen(open);\n        }\n    }[\"Dialog.useEffect\"], [\n        open\n    ]);\n    const handleOpenChange = (newOpen)=>{\n        setInternalOpen(newOpen);\n        onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(newOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContext.Provider, {\n        value: {\n            open: internalOpen,\n            onOpenChange: handleOpenChange\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dialog, \"H7R/imfsAt8ZOKR9FmCf+hkSf6o=\");\n_c = Dialog;\nconst DialogTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = _s1((param, ref)=>{\n    let { className, children, asChild = false, ...props } = param;\n    _s1();\n    const { onOpenChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(DialogContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: className,\n        onClick: ()=>onOpenChange(true),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 63,\n        columnNumber: 7\n    }, undefined);\n}, \"ntPtLpKBO4sLQI001ClAIvJHiws=\")), \"ntPtLpKBO4sLQI001ClAIvJHiws=\");\n_c2 = DialogTrigger;\nDialogTrigger.displayName = \"DialogTrigger\";\nconst DialogPortal = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_c3 = DialogPortal;\nconst DialogClose = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s2((param, ref)=>{\n    let { className, children, asChild = false, ...props } = param;\n    _s2();\n    const { onOpenChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(DialogContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: className,\n        onClick: ()=>onOpenChange(false),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 85,\n        columnNumber: 7\n    }, undefined);\n}, \"ntPtLpKBO4sLQI001ClAIvJHiws=\")), \"ntPtLpKBO4sLQI001ClAIvJHiws=\");\n_c5 = DialogClose;\nDialogClose.displayName = \"DialogClose\";\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n});\n_c6 = DialogOverlay;\nDialogOverlay.displayName = \"DialogOverlay\";\nconst DialogContent = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c7 = _s3((param, ref)=>{\n    let { className, children, ...props } = param;\n    _s3();\n    const { open, onOpenChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(DialogContext);\n    if (!open) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {\n                onClick: ()=>onOpenChange(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n                        onClick: ()=>onOpenChange(false),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 119,\n        columnNumber: 7\n    }, undefined);\n}, \"8AKSzg5XJRYR1b/tpX+fcz88l5I=\")), \"8AKSzg5XJRYR1b/tpX+fcz88l5I=\");\n_c8 = DialogContent;\nDialogContent.displayName = \"DialogContent\";\nconst DialogHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c9 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, undefined);\n});\n_c10 = DialogHeader;\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c11 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, undefined);\n});\n_c12 = DialogFooter;\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c13 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n});\n_c14 = DialogTitle;\nDialogTitle.displayName = \"DialogTitle\";\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c15 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n});\n_c16 = DialogDescription;\nDialogDescription.displayName = \"DialogDescription\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c1, \"DialogTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogTrigger\");\n$RefreshReg$(_c3, \"DialogPortal\");\n$RefreshReg$(_c4, \"DialogClose$React.forwardRef\");\n$RefreshReg$(_c5, \"DialogClose\");\n$RefreshReg$(_c6, \"DialogOverlay\");\n$RefreshReg$(_c7, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogContent\");\n$RefreshReg$(_c9, \"DialogHeader$React.forwardRef\");\n$RefreshReg$(_c10, \"DialogHeader\");\n$RefreshReg$(_c11, \"DialogFooter$React.forwardRef\");\n$RefreshReg$(_c12, \"DialogFooter\");\n$RefreshReg$(_c13, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c14, \"DialogTitle\");\n$RefreshReg$(_c15, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c16, \"DialogDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = \"Label\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFDRTtBQU1oQyxNQUFNRSxzQkFBUUYsNkNBQWdCLE1BQzVCLFFBQTBCSTtRQUF6QixFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBTzt5QkFDdEIsOERBQUNDO1FBQ0NILEtBQUtBO1FBQ0xDLFdBQVdKLDhDQUFFQSxDQUNYLDhGQUNBSTtRQUVELEdBQUdDLEtBQUs7Ozs7Ozs7O0FBSWZKLE1BQU1NLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmludGVyZmFjZSBMYWJlbFByb3BzIGV4dGVuZHMgUmVhY3QuTGFiZWxIVE1MQXR0cmlidXRlczxIVE1MTGFiZWxFbGVtZW50PiB7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxMYWJlbEVsZW1lbnQsIExhYmVsUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxsYWJlbFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxuKTtcbkxhYmVsLmRpc3BsYXlOYW1lID0gXCJMYWJlbFwiO1xuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwibGFiZWwiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/switch.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/switch.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Switch: () => (/* binding */ Switch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Switch auto */ \n\n\nconst Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, checked, onCheckedChange, onChange, ...props } = param;\n    const handleChange = (event)=>{\n        const newChecked = event.target.checked;\n        onCheckedChange === null || onCheckedChange === void 0 ? void 0 : onCheckedChange(newChecked);\n        onChange === null || onChange === void 0 ? void 0 : onChange(event);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: \"relative inline-flex items-center cursor-pointer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"checkbox\",\n                ref: ref,\n                className: \"sr-only\",\n                checked: checked,\n                onChange: handleChange,\n                ...props\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\switch.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\", checked && \"bg-primary\", className)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\switch.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\switch.tsx\",\n        lineNumber: 20,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Switch;\nSwitch.displayName = \"Switch\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Switch$React.forwardRef\");\n$RefreshReg$(_c1, \"Switch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/switch.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_b","vendors-node_modules_date-fns__","vendors-node_modules_d","vendors-node_modules_i","vendors-node_modules_lodash_k","vendors-node_modules_lo","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_r","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_c","vendors-node_modules_next_dist_l","vendors-node_modules_next_d","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_p","default-_app-pages-browser_src_lib_session-manager_ts","default-_app-pages-browser_src_contexts_TradingContext_tsx","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","default-_app-pages-browser_src_components_ui_badge_tsx-_app-pages-browser_src_components_ui_s-f5a402","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);