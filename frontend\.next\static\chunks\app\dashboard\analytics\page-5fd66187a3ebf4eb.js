(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{709:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var r=s(5155),i=s(2115),a=s(3033),l=s(4530),o=s(6695),n=s(9409),d=s(6126),c=s(2346),m=s(7213),x=s(4553),p=s(659),u=s(7723),f=s(5263),h=s(1203),y=s(7648),g=s(8184);let j=(e,t)=>{if(!e||0===e.length)return{totalProfitLossCrypto1:0,totalProfitLossCrypto2:0,winRate:0,totalTradesExecuted:0,buyTrades:0,sellTrades:0,avgProfitPerTradeCrypto2:0,avgProfitPerTradeCrypto1:0};let s=e.filter(e=>void 0!==e.realizedProfitLossCrypto2&&null!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2),r=s.reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),i=s.reduce((e,t)=>e+(t.realizedProfitLossCrypto1||0),0),a=s.filter(e=>(e.realizedProfitLossCrypto2||0)>0).length,l=s.length>0?a/s.length*100:0,o=e.length;"StablecoinSwap"===t.tradingMode&&(o=Math.max(2*s.length,e.length));let n=e.filter(e=>"BUY"===e.orderType).length,d=e.filter(e=>"SELL"===e.orderType).length,c=s.length>0?r/s.length:0,m=s.length>0?i/s.length:0;return{totalProfitLossCrypto1:parseFloat(i.toFixed(t.numDigits)),totalProfitLossCrypto2:parseFloat(r.toFixed(t.numDigits)),winRate:parseFloat(l.toFixed(2)),totalTradesExecuted:o,buyTrades:n,sellTrades:d,avgProfitPerTradeCrypto2:parseFloat(c.toFixed(t.numDigits)),avgProfitPerTradeCrypto1:parseFloat(m.toFixed(t.numDigits))}},v=(e,t)=>{let s=e.filter(e=>void 0!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2),r=0;return s.map((e,t)=>(r+=e.realizedProfitLossCrypto2||0,{date:(0,g.GP)(new Date(e.timestamp),"MMM dd HH:mm"),pnl:parseFloat(r.toFixed(4)),trade:t+1}))};function b(){var e,t,s;let{orderHistory:a,config:l,getDisplayOrders:g}=(0,m.U)(),[b,C]=(0,i.useState)([]),[N,P]=(0,i.useState)("current"),[w,L]=(0,i.useState)([]),[T,S]=(0,i.useState)(l),A=x.SessionManager.getInstance();(0,i.useEffect)(()=>{F()},[]),(0,i.useEffect)(()=>{if("current"===N)L(a),S(l);else{let e=A.loadSession(N);e&&(L(e.orderHistory),S(e.config))}},[N,a,l]);let F=()=>{let e=A.getAllSessions(),t=A.getCurrentSessionId();C(e.filter(e=>e.id!==t).sort((e,t)=>t.lastModified-e.lastModified))},z=(0,i.useMemo)(()=>j(w,T),[w,T]),M=(0,i.useMemo)(()=>v(w,T.crypto2),[w,T.crypto2]),E=(0,i.useMemo)(()=>"current"!==N?"0.0000":g().reduce((e,t)=>"Full"===t.status&&void 0!==t.incomeCrypto2?e+t.incomeCrypto2:e,0).toFixed(T.numDigits),[g,T.numDigits,N]),D="current"===N?{name:"Current Session",pair:l.crypto1&&l.crypto2?"".concat(l.crypto1,"/").concat(l.crypto2):"Crypto 1/Crypto 2",isActive:!0}:b.find(e=>e.id===N),R=(0,i.useMemo)(()=>[{title:"Total Realized P/L (".concat(T.crypto1||"Crypto 1",")"),value:z.totalProfitLossCrypto1,icon:(0,r.jsx)(p.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto1",isProfit:z.totalProfitLossCrypto1>=0},{title:"Total Realized P/L (".concat(T.crypto2||"Crypto 2",")"),value:z.totalProfitLossCrypto2,icon:(0,r.jsx)(p.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto2",isProfit:z.totalProfitLossCrypto2>=0},{title:"Win Rate",value:"".concat(z.winRate,"%"),icon:(0,r.jsx)(u.A,{className:"h-6 w-6 text-primary"}),description:"Profitable sell trades / Total sell trades",isProfit:z.winRate>=50},{title:"Total Trades",value:z.totalTradesExecuted,icon:(0,r.jsx)(f.A,{className:"h-6 w-6 text-primary"}),description:"".concat(z.buyTrades," buys, ").concat(z.sellTrades," sells"),isProfit:!0},{title:"Avg Profit/Trade (".concat(T.crypto2||"Crypto 2",")"),value:z.avgProfitPerTradeCrypto2,icon:(0,r.jsx)(h.A,{className:"h-6 w-6 text-primary"}),description:"Average profit per sell trade",isProfit:z.avgProfitPerTradeCrypto2>=0},{title:"Current Unrealized P/L (".concat(T.crypto2||"Crypto 2",")"),value:E,icon:(0,r.jsx)(y.A,{className:"h-6 w-6 text-primary"}),description:"Unrealized profit/loss from active positions",isProfit:parseFloat(E)>=0,isCurrentOnly:!0}],[z,T,E]);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{className:"text-xl font-bold text-primary",children:"Session Analytics"}),(0,r.jsx)(o.BT,{children:"View trading analytics for current and past sessions."})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,r.jsxs)(n.l6,{value:N,onValueChange:P,children:[(0,r.jsx)(n.bq,{className:"w-full sm:w-[300px]",children:(0,r.jsx)(n.yv,{placeholder:"Select a session"})}),(0,r.jsxs)(n.gC,{children:[(0,r.jsx)(n.eb,{value:"current",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.E,{variant:"default",className:"text-xs",children:"Current"}),(0,r.jsxs)("span",{children:["Current Session (",l.crypto1&&l.crypto2?"".concat(l.crypto1,"/").concat(l.crypto2):"Crypto 1/Crypto 2 = 0",")"]})]})}),b.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.w,{className:"my-1"}),b.map(e=>(0,r.jsx)(n.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,r.jsx)("span",{children:e.name})]})},e.id))]})]})]})]})}),D&&(0,r.jsx)("div",{className:"bg-muted/50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:D.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:D.pair})]}),D.isActive&&(0,r.jsx)(d.E,{variant:"default",className:"text-xs",children:"Active"})]})})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:R.map((e,t)=>e.isCurrentOnly&&"current"!==N?null:(0,r.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-medium",children:e.title}),e.icon]}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold ".concat("number"==typeof e.value?e.isProfit?"text-green-600":"text-red-600":"text-foreground"),children:"number"==typeof e.value?e.value.toFixed(T.numDigits):e.value}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))}),(0,r.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-xl font-bold text-primary",children:["Cumulative Profit/Loss Over Time (",T.crypto2,")"]}),(0,r.jsxs)(o.BT,{children:["Chart visualization of trading performance for ",(null==D?void 0:D.name)||"selected session","."]})]}),(0,r.jsx)(o.Wu,{className:"h-80",children:M.length>0?(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg border-2 border-dashed border-border",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(f.A,{className:"h-16 w-16 mx-auto mb-4 text-primary opacity-60"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Chart Temporarily Disabled"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Chart functionality is being optimized for better performance"}),(0,r.jsxs)("div",{className:"bg-background/80 rounded-lg p-4 border border-border",children:[(0,r.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:"Quick Stats:"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{children:["Data Points: ",(0,r.jsx)("span",{className:"font-medium text-primary",children:M.length})]}),(0,r.jsxs)("div",{children:["Latest P/L: ",(0,r.jsxs)("span",{className:"font-medium ".concat((null===(e=M[M.length-1])||void 0===e?void 0:e.pnl)>=0?"text-green-500":"text-red-500"),children:["$",(null===(s=M[M.length-1])||void 0===s?void 0:null===(t=s.pnl)||void 0===t?void 0:t.toFixed(2))||"0.00"]})]})]})]})]})}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No sell trades recorded yet for this session."}),(0,r.jsx)("p",{className:"text-xs",children:"Chart will appear after first profitable trade."})]})})})]})]})}function C(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(a.A,{}),(0,r.jsx)(l.A,{}),(0,r.jsx)(b,{})]})}},5050:(e,t,s)=>{Promise.resolve().then(s.bind(s,709))}},e=>{var t=t=>e(e.s=t);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,213,92,358],()=>t(5050)),_N_E=e.O()}]);