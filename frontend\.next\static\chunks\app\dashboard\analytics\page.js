/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/analytics/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/analytics/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2FuYWx5dGljcyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsc01BQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxib3RcXFxcdHJhZGluZ2JvdF9maW5hbFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxhbmFseXRpY3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/analytics/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/analytics/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardAnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_SessionAwareAnalytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/SessionAwareAnalytics */ \"(app-pages-browser)/./src/components/dashboard/SessionAwareAnalytics.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardAnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionAwareAnalytics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = DashboardAnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardAnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2FuYWx5dGljcy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUMwQjtBQUN1QztBQUNJO0FBQ1k7QUFFbEUsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCwyRUFBYUE7Ozs7OzBCQUNkLDhEQUFDQyw2RUFBZUE7Ozs7OzBCQUNoQiw4REFBQ0MsbUZBQXFCQTs7Ozs7Ozs7Ozs7QUFHNUI7S0FSd0JDIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxhcHBcXGRhc2hib2FyZFxcYW5hbHl0aWNzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRGFzaGJvYXJkVGFicyBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Rhc2hib2FyZFRhYnMnO1xuaW1wb3J0IEJhbGFuY2VzRGlzcGxheSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL0JhbGFuY2VzRGlzcGxheSc7XG5pbXBvcnQgU2Vzc2lvbkF3YXJlQW5hbHl0aWNzIGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvU2Vzc2lvbkF3YXJlQW5hbHl0aWNzJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkQW5hbHl0aWNzUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgPERhc2hib2FyZFRhYnMgLz5cbiAgICAgIDxCYWxhbmNlc0Rpc3BsYXkgLz5cbiAgICAgIDxTZXNzaW9uQXdhcmVBbmFseXRpY3MgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkRhc2hib2FyZFRhYnMiLCJCYWxhbmNlc0Rpc3BsYXkiLCJTZXNzaW9uQXdhcmVBbmFseXRpY3MiLCJEYXNoYm9hcmRBbmFseXRpY3NQYWdlIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/analytics/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/SessionAwareAnalytics.tsx":
/*!************************************************************!*\
  !*** ./src/components/dashboard/SessionAwareAnalytics.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionAwareAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Import recharts components directly\n\n\n// Helper function to calculate KPIs from order history\nconst calculateKPIs = (orderHistory, config)=>{\n    if (!orderHistory || orderHistory.length === 0) {\n        return {\n            totalProfitLossCrypto1: 0,\n            totalProfitLossCrypto2: 0,\n            winRate: 0,\n            totalTradesExecuted: 0,\n            buyTrades: 0,\n            sellTrades: 0,\n            avgProfitPerTradeCrypto2: 0,\n            avgProfitPerTradeCrypto1: 0\n        };\n    }\n    // For profit calculation, include trades with actual profit/loss data\n    // In SimpleSpot: SELL trades have profit\n    // In StablecoinSwap: BUY trades (final step of SELL operation) have profit\n    const profitTrades = orderHistory.filter((trade)=>trade.realizedProfitLossCrypto2 !== undefined && trade.realizedProfitLossCrypto2 !== null && trade.realizedProfitLossCrypto2 !== 0);\n    console.log(\"\\uD83D\\uDCCA SessionAware Analytics Debug: Total trades=\".concat(orderHistory.length, \", Profit trades=\").concat(profitTrades.length));\n    console.log(\"\\uD83D\\uDCCA Trading mode: \".concat(config.tradingMode));\n    const totalProfitLossCrypto2 = profitTrades.reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0);\n    const totalProfitLossCrypto1 = profitTrades.reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto1 || 0), 0);\n    const profitableTrades = profitTrades.filter((trade)=>(trade.realizedProfitLossCrypto2 || 0) > 0).length;\n    const winRate = profitTrades.length > 0 ? profitableTrades / profitTrades.length * 100 : 0;\n    // For total trades executed, count meaningful trades based on mode\n    let totalTradesExecuted = orderHistory.length;\n    if (config.tradingMode === \"StablecoinSwap\") {\n        // In stablecoin swap, each logical trade creates 2 history entries\n        totalTradesExecuted = Math.max(profitTrades.length * 2, orderHistory.length);\n    }\n    const buyTrades = orderHistory.filter((trade)=>trade.orderType === 'BUY').length;\n    const sellTrades = orderHistory.filter((trade)=>trade.orderType === 'SELL').length;\n    const avgProfitPerTradeCrypto2 = profitTrades.length > 0 ? totalProfitLossCrypto2 / profitTrades.length : 0;\n    const avgProfitPerTradeCrypto1 = profitTrades.length > 0 ? totalProfitLossCrypto1 / profitTrades.length : 0;\n    return {\n        totalProfitLossCrypto1: parseFloat(totalProfitLossCrypto1.toFixed(config.numDigits)),\n        totalProfitLossCrypto2: parseFloat(totalProfitLossCrypto2.toFixed(config.numDigits)),\n        winRate: parseFloat(winRate.toFixed(2)),\n        totalTradesExecuted,\n        buyTrades,\n        sellTrades,\n        avgProfitPerTradeCrypto2: parseFloat(avgProfitPerTradeCrypto2.toFixed(config.numDigits)),\n        avgProfitPerTradeCrypto1: parseFloat(avgProfitPerTradeCrypto1.toFixed(config.numDigits))\n    };\n};\n// Generate P&L chart data\nconst generatePnlChartData = (orderHistory, crypto2Symbol)=>{\n    const profitTrades = orderHistory.filter((trade)=>trade.realizedProfitLossCrypto2 !== undefined && trade.realizedProfitLossCrypto2 !== 0);\n    let cumulativePnL = 0;\n    return profitTrades.map((trade, index)=>{\n        cumulativePnL += trade.realizedProfitLossCrypto2 || 0;\n        return {\n            date: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_8__.format)(new Date(trade.timestamp), 'MMM dd HH:mm'),\n            pnl: parseFloat(cumulativePnL.toFixed(4)),\n            trade: index + 1\n        };\n    });\n};\nfunction SessionAwareAnalytics() {\n    _s();\n    const { orderHistory, config, getDisplayOrders } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_6__.useTradingContext)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionId, setSelectedSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('current');\n    const [selectedSessionHistory, setSelectedSessionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionConfig, setSelectedSessionConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(config);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareAnalytics.useEffect\": ()=>{\n            loadSessions();\n        }\n    }[\"SessionAwareAnalytics.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareAnalytics.useEffect\": ()=>{\n            if (selectedSessionId === 'current') {\n                setSelectedSessionHistory(orderHistory);\n                setSelectedSessionConfig(config);\n            } else {\n                const session = sessionManager.loadSession(selectedSessionId);\n                if (session) {\n                    setSelectedSessionHistory(session.orderHistory);\n                    setSelectedSessionConfig(session.config);\n                }\n            }\n        }\n    }[\"SessionAwareAnalytics.useEffect\"], [\n        selectedSessionId,\n        orderHistory,\n        config\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // Filter out the current session to avoid duplicates\n        const pastSessions = allSessions.filter((session)=>session.id !== currentSessionId);\n        setSessions(pastSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const kpis = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[kpis]\": ()=>calculateKPIs(selectedSessionHistory, selectedSessionConfig)\n    }[\"SessionAwareAnalytics.useMemo[kpis]\"], [\n        selectedSessionHistory,\n        selectedSessionConfig\n    ]);\n    const pnlChartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[pnlChartData]\": ()=>generatePnlChartData(selectedSessionHistory, selectedSessionConfig.crypto2)\n    }[\"SessionAwareAnalytics.useMemo[pnlChartData]\"], [\n        selectedSessionHistory,\n        selectedSessionConfig.crypto2\n    ]);\n    const currentUnrealizedPL = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[currentUnrealizedPL]\": ()=>{\n            if (selectedSessionId !== 'current') return '0.0000'; // Only show unrealized P/L for current session\n            const currentActiveOrders = getDisplayOrders();\n            const rawValue = currentActiveOrders.reduce({\n                \"SessionAwareAnalytics.useMemo[currentUnrealizedPL].rawValue\": (sum, order)=>{\n                    if (order.status === 'Full' && order.incomeCrypto2 !== undefined) {\n                        return sum + order.incomeCrypto2;\n                    }\n                    return sum;\n                }\n            }[\"SessionAwareAnalytics.useMemo[currentUnrealizedPL].rawValue\"], 0);\n            return rawValue.toFixed(selectedSessionConfig.numDigits);\n        }\n    }[\"SessionAwareAnalytics.useMemo[currentUnrealizedPL]\"], [\n        getDisplayOrders,\n        selectedSessionConfig.numDigits,\n        selectedSessionId\n    ]);\n    const getSelectedSessionInfo = ()=>{\n        if (selectedSessionId === 'current') {\n            const displayPair = config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : \"Crypto 1/Crypto 2\";\n            return {\n                name: 'Current Session',\n                pair: displayPair,\n                isActive: true\n            };\n        }\n        return sessions.find((s)=>s.id === selectedSessionId);\n    };\n    const selectedSession = getSelectedSessionInfo();\n    const kpiCards = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[kpiCards]\": ()=>[\n                {\n                    title: \"Total Realized P/L (\".concat(selectedSessionConfig.crypto1 || \"Crypto 1\", \")\"),\n                    value: kpis.totalProfitLossCrypto1,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Sum of profits from sell trades in Crypto1\",\n                    isProfit: kpis.totalProfitLossCrypto1 >= 0\n                },\n                {\n                    title: \"Total Realized P/L (\".concat(selectedSessionConfig.crypto2 || \"Crypto 2\", \")\"),\n                    value: kpis.totalProfitLossCrypto2,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Sum of profits from sell trades in Crypto2\",\n                    isProfit: kpis.totalProfitLossCrypto2 >= 0\n                },\n                {\n                    title: \"Win Rate\",\n                    value: \"\".concat(kpis.winRate, \"%\"),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Profitable sell trades / Total sell trades\",\n                    isProfit: kpis.winRate >= 50\n                },\n                {\n                    title: \"Total Trades\",\n                    value: kpis.totalTradesExecuted,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, this),\n                    description: \"\".concat(kpis.buyTrades, \" buys, \").concat(kpis.sellTrades, \" sells\"),\n                    isProfit: true\n                },\n                {\n                    title: \"Avg Profit/Trade (\".concat(selectedSessionConfig.crypto2 || \"Crypto 2\", \")\"),\n                    value: kpis.avgProfitPerTradeCrypto2,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Average profit per sell trade\",\n                    isProfit: kpis.avgProfitPerTradeCrypto2 >= 0\n                },\n                {\n                    title: \"Current Unrealized P/L (\".concat(selectedSessionConfig.crypto2 || \"Crypto 2\", \")\"),\n                    value: currentUnrealizedPL,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Unrealized profit/loss from active positions\",\n                    isProfit: parseFloat(currentUnrealizedPL) >= 0,\n                    isCurrentOnly: true\n                }\n            ]\n    }[\"SessionAwareAnalytics.useMemo[kpiCards]\"], [\n        kpis,\n        selectedSessionConfig,\n        currentUnrealizedPL\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"Session Analytics\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"View trading analytics for current and past sessions.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium mb-2 block\",\n                                            children: \"Select Session:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedSessionId,\n                                            onValueChange: setSelectedSessionId,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full sm:w-[300px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select a session\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: \"current\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"default\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Current\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Current Session (\",\n                                                                            config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : 'Crypto 1/Crypto 2 = 0',\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        sessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {\n                                                                    className: \"my-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                        value: session.id,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: session.isActive ? \"default\" : \"secondary\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: session.isActive ? \"Current\" : \"Past\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                                    lineNumber: 235,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                                    lineNumber: 238,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                            lineNumber: 234,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, session.id, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            selectedSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium\",\n                                                    children: selectedSession.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: selectedSession.pair\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedSession.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"default\",\n                                            className: \"text-xs\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: kpiCards.map((kpi, index)=>{\n                    // Hide current-only KPIs for past sessions\n                    if (kpi.isCurrentOnly && selectedSessionId !== 'current') {\n                        return null;\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-2 border-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: kpi.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    kpi.icon\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(typeof kpi.value === 'number' ? kpi.isProfit ? 'text-green-600' : 'text-red-600' : 'text-foreground'),\n                                        children: typeof kpi.value === 'number' ? kpi.value.toFixed(selectedSessionConfig.numDigits) : kpi.value\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: kpi.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: [\n                                    \"Cumulative Profit/Loss Over Time (\",\n                                    selectedSessionConfig.crypto2,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Chart visualization of trading performance for \",\n                                    (selectedSession === null || selectedSession === void 0 ? void 0 : selectedSession.name) || 'selected session',\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"h-80\",\n                        children: pnlChartData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                            width: \"100%\",\n                            height: \"100%\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.LineChart, {\n                                data: pnlChartData,\n                                margin: {\n                                    top: 5,\n                                    right: 20,\n                                    left: -25,\n                                    bottom: 5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.CartesianGrid, {\n                                        strokeDasharray: \"3 3\",\n                                        stroke: \"hsl(var(--border))\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.XAxis, {\n                                        dataKey: \"date\",\n                                        stroke: \"hsl(var(--muted-foreground))\",\n                                        fontSize: 12,\n                                        tickLine: false,\n                                        axisLine: false\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.YAxis, {\n                                        stroke: \"hsl(var(--muted-foreground))\",\n                                        fontSize: 12,\n                                        tickLine: false,\n                                        axisLine: false,\n                                        tickFormatter: (value)=>\"\".concat(value.toFixed(2))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.Tooltip, {\n                                        content: (param)=>{\n                                            let { active, payload, label } = param;\n                                            if (active && payload && payload.length > 0 && payload[0] && typeof payload[0].value === 'number') {\n                                                var _firstPayload_payload;\n                                                const firstPayload = payload[0];\n                                                const value = firstPayload.value;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-card border border-border rounded-lg p-3 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Date: \".concat(label)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"P/L: \"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: value >= 0 ? 'text-green-600' : 'text-red-600',\n                                                                    children: [\n                                                                        value,\n                                                                        \" \",\n                                                                        selectedSessionConfig.crypto2 || \"Crypto 2\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 31\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"Trade #\",\n                                                                ((_firstPayload_payload = firstPayload.payload) === null || _firstPayload_payload === void 0 ? void 0 : _firstPayload_payload.trade) || 'N/A'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 29\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 27\n                                                }, void 0);\n                                            }\n                                            return null;\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Line, {\n                                        type: \"monotone\",\n                                        dataKey: \"pnl\",\n                                        stroke: \"hsl(var(--primary))\",\n                                        strokeWidth: 2,\n                                        dot: {\n                                            fill: 'hsl(var(--primary))',\n                                            strokeWidth: 2,\n                                            r: 4\n                                        },\n                                        activeDot: {\n                                            r: 6,\n                                            stroke: 'hsl(var(--primary))',\n                                            strokeWidth: 2\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No sell trades recorded yet for this session.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Chart will appear after first profitable trade.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAwareAnalytics, \"nUx++1OgfgHw76GRC/umR4thJFw=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_6__.useTradingContext\n    ];\n});\n_c = SessionAwareAnalytics;\nvar _c;\n$RefreshReg$(_c, \"SessionAwareAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SessionAwareAnalytics.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, orientation = \"horizontal\", decorative = true, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: decorative ? \"none\" : \"separator\",\n        \"aria-orientation\": orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Separator;\nSeparator.displayName = \"Separator\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Separator$React.forwardRef\");\n$RefreshReg$(_c1, \"Separator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRThCO0FBQ0U7QUFPaEMsTUFBTUUsMEJBQVlGLDZDQUFnQixNQUNoQyxRQUVFSTtRQURBLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU87eUJBR3RFLDhEQUFDQztRQUNDTCxLQUFLQTtRQUNMTSxNQUFNSCxhQUFhLFNBQVM7UUFDNUJJLG9CQUFrQkw7UUFDbEJELFdBQVdKLDhDQUFFQSxDQUNYLHNCQUNBSyxnQkFBZ0IsZUFBZSxtQkFBbUIsa0JBQ2xERDtRQUVELEdBQUdHLEtBQUs7Ozs7Ozs7O0FBSWZOLFVBQVVVLFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcc2VwYXJhdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5pbnRlcmZhY2UgU2VwYXJhdG9yUHJvcHMgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xuICBvcmllbnRhdGlvbj86IFwiaG9yaXpvbnRhbFwiIHwgXCJ2ZXJ0aWNhbFwiO1xuICBkZWNvcmF0aXZlPzogYm9vbGVhbjtcbn1cblxuY29uc3QgU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgU2VwYXJhdG9yUHJvcHM+KFxuICAoXG4gICAgeyBjbGFzc05hbWUsIG9yaWVudGF0aW9uID0gXCJob3Jpem9udGFsXCIsIGRlY29yYXRpdmUgPSB0cnVlLCAuLi5wcm9wcyB9LFxuICAgIHJlZlxuICApID0+IChcbiAgICA8ZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIHJvbGU9e2RlY29yYXRpdmUgPyBcIm5vbmVcIiA6IFwic2VwYXJhdG9yXCJ9XG4gICAgICBhcmlhLW9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwic2hyaW5rLTAgYmctYm9yZGVyXCIsXG4gICAgICAgIG9yaWVudGF0aW9uID09PSBcImhvcml6b250YWxcIiA/IFwiaC1bMXB4XSB3LWZ1bGxcIiA6IFwiaC1mdWxsIHctWzFweF1cIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbik7XG5TZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBcIlNlcGFyYXRvclwiO1xuXG5leHBvcnQgeyBTZXBhcmF0b3IgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiU2VwYXJhdG9yIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsIm9yaWVudGF0aW9uIiwiZGVjb3JhdGl2ZSIsInByb3BzIiwiZGl2Iiwicm9sZSIsImFyaWEtb3JpZW50YXRpb24iLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/separator.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["recharts-node_modules_recharts_es6_cartesian_B","recharts-node_modules_recharts_es6_chart_A","recharts-node_modules_recharts_es6_com","recharts-node_modules_recharts_es6_s","vendors-node_modules_b","vendors-node_modules_date-fns__","vendors-node_modules_d","vendors-node_modules_i","vendors-node_modules_lodash_k","vendors-node_modules_lo","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_r","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_c","vendors-node_modules_next_dist_l","vendors-node_modules_next_d","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_p","default-_app-pages-browser_src_lib_session-manager_ts","default-_app-pages-browser_src_contexts_TradingContext_tsx","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","default-_app-pages-browser_src_components_ui_badge_tsx-_app-pages-browser_src_components_ui_s-f5a402","default-_app-pages-browser_src_components_dashboard_BalancesDisplay_tsx-_app-pages-browser_sr-ac955a","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);