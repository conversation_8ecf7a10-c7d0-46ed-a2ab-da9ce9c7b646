(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[610],{205:(e,t,s)=>{Promise.resolve().then(s.bind(s,8620))},8620:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(5155),i=s(2115),o=s(3033),l=s(4530),a=s(6695),d=s(285),n=s(9409),c=s(6126),x=s(2346),p=s(7213),m=s(4553),y=s(7223),u=s(679),h=s(7648),f=s(8184);function j(){let{dispatch:e,orderHistory:t,config:s}=(0,p.U)(),[o,l]=(0,i.useState)([]),[h,j]=(0,i.useState)("current"),[b,g]=(0,i.useState)([]),N=m.SessionManager.getInstance();(0,i.useEffect)(()=>{C()},[]),(0,i.useEffect)(()=>{"current"===h?g(t):g(N.getSessionHistory(h))},[h,t]);let C=()=>{let e=N.getAllSessions(),t=N.getCurrentSessionId();l(e.filter(e=>e.id!==t).sort((e,t)=>t.lastModified-e.lastModified))},P=(()=>{if("current"===h){let e=t.filter(e=>void 0!==e.realizedProfitLossCrypto2&&null!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0);return{name:"Current Session",pair:"".concat(s.crypto1,"/").concat(s.crypto2),totalTrades:t.length,totalProfitLoss:e,lastModified:Date.now(),isActive:!0}}return o.find(e=>e.id===h)})();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(a.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,r.jsx)(a.BT,{children:"View trading history for current and past sessions."})]}),(0,r.jsxs)(a.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,r.jsxs)(n.l6,{value:h,onValueChange:j,children:[(0,r.jsx)(n.bq,{className:"w-full sm:w-[300px]",children:(0,r.jsx)(n.yv,{placeholder:"Select a session"})}),(0,r.jsxs)(n.gC,{children:[(0,r.jsx)(n.eb,{value:"current",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:"default",className:"text-xs",children:"Current"}),(0,r.jsxs)("span",{children:["Current Session (",s.crypto1&&s.crypto2?"".concat(s.crypto1,"/").concat(s.crypto2):"Crypto 1/Crypto 2 = 0",")"]})]})}),o.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.w,{className:"my-1"}),o.map(e=>(0,r.jsx)(n.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,r.jsx)("span",{children:e.name})]})},e.id))]})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{"current"===h&&e({type:"CLEAR_ORDER_HISTORY"})},className:"btn-outline-neo",disabled:"current"!==h,children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===b.length)return;if("current"===h)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...b.map(e=>{var t,r,i,o,l,a,d;return[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,(null===(t=e.amountCrypto1)||void 0===t?void 0:t.toFixed(s.numDigits))||"",(null===(r=e.avgPrice)||void 0===r?void 0:r.toFixed(s.numDigits))||"",(null===(i=e.valueCrypto2)||void 0===i?void 0:i.toFixed(s.numDigits))||"",(null===(o=e.price1)||void 0===o?void 0:o.toFixed(s.numDigits))||"",e.crypto1Symbol,(null===(l=e.price2)||void 0===l?void 0:l.toFixed(s.numDigits))||"",e.crypto2Symbol,(null===(a=e.realizedProfitLossCrypto1)||void 0===a?void 0:a.toFixed(s.numDigits))||"",(null===(d=e.realizedProfitLossCrypto2)||void 0===d?void 0:d.toFixed(s.numDigits))||""].join(",")})].join("\n"),t="current_session_history_".concat(new Date().toISOString().split("T")[0],".csv");else{let s=N.exportSessionToCSV(h);if(!s)return;e=s;let r=N.loadSession(h);t="".concat((null==r?void 0:r.name)||"session","_").concat(new Date().toISOString().split("T")[0],".csv")}if(!e)return;let r=new Blob([e],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),o=URL.createObjectURL(r);i.setAttribute("href",o),i.setAttribute("download",t),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)},className:"btn-outline-neo",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),P&&(0,r.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,r.jsx)("div",{className:"font-medium",children:P.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,r.jsx)("div",{className:"font-medium",children:P.pair})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,r.jsx)("div",{className:"font-medium",children:P.totalTrades})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,r.jsx)("div",{className:"font-medium ".concat(P.totalProfitLoss>=0?"text-green-600":"text-red-600"),children:P.totalProfitLoss.toFixed(4)})]})]}),"current"!==h&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",(0,f.GP)(new Date(P.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,r.jsxs)(a.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsxs)(a.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",(null==P?void 0:P.name)||"Unknown Session"]}),(0,r.jsx)(a.BT,{children:0===b.length?"No trades recorded for this session yet.":"Showing ".concat(b.length," trades for the selected session.")})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)(v,{history:b,config:s})})]})]})}function v(e){let{history:t,config:s}=e,i=e=>{var t;return null!==(t=null==e?void 0:e.toFixed(s.numDigits))&&void 0!==t?t:"-"},o=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:"Crypto (".concat(s.crypto1,")")},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:"Value (".concat(s.crypto2,")")},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:"Crypto (".concat(s.crypto1,")")},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:"Crypto (".concat(s.crypto2,")")},{key:"profitCrypto1",label:"Profit/Loss (".concat(s.crypto1,")")},{key:"profitCrypto2",label:"Profit/Loss (".concat(s.crypto2,")")}];return 0===t.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No trading history for this session yet."})]}):(0,r.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:o.map(e=>(0,r.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,r.jsx)("tbody",{children:t.map(e=>{var t;return(0,r.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,f.GP)(new Date(e.timestamp),"yyyy-MM-dd")}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,f.GP)(new Date(e.timestamp),"HH:mm:ss")}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs font-semibold ".concat("BUY"===e.orderType?"text-green-400":"text-destructive"),children:e.orderType}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e.amountCrypto1)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e.avgPrice)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e.valueCrypto2)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e.price1)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:null!==(t=i(e.price2))&&void 0!==t?t:"-"}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs ".concat(e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1>0?"text-green-400":e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1<0?"text-destructive":""),children:void 0!==e.realizedProfitLossCrypto1&&null!==e.realizedProfitLossCrypto1&&0!==e.realizedProfitLossCrypto1?i(e.realizedProfitLossCrypto1):"-"}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs ".concat(e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2>0?"text-green-400":e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2<0?"text-destructive":""),children:void 0!==e.realizedProfitLossCrypto2&&null!==e.realizedProfitLossCrypto2&&0!==e.realizedProfitLossCrypto2?i(e.realizedProfitLossCrypto2):"-"})]},e.id)})})]})})})}function b(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)(l.A,{}),(0,r.jsx)(j,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,213,92,358],()=>t(205)),_N_E=e.O()}]);