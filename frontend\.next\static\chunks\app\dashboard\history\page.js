/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/history/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Chistory%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Chistory%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/history/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/history/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2hpc3RvcnklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFnSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcaGlzdG9yeVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Chistory%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/history/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/history/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardHistoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_SessionAwareHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/SessionAwareHistory */ \"(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardHistoryPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionAwareHistory__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = DashboardHistoryPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardHistoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2hpc3RvcnkvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFMEI7QUFDdUM7QUFDSTtBQUNRO0FBRTlELFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0wsMkVBQWFBOzs7OzswQkFDZCw4REFBQ0MsNkVBQWVBOzs7OzswQkFDaEIsOERBQUNDLGlGQUFtQkE7Ozs7Ozs7Ozs7O0FBRzFCO0tBUndCQyIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXGhpc3RvcnlcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IERhc2hib2FyZFRhYnMgZnJvbSAnQC9jb21wb25lbnRzL2Rhc2hib2FyZC9EYXNoYm9hcmRUYWJzJztcbmltcG9ydCBCYWxhbmNlc0Rpc3BsYXkgZnJvbSAnQC9jb21wb25lbnRzL2Rhc2hib2FyZC9CYWxhbmNlc0Rpc3BsYXknO1xuaW1wb3J0IFNlc3Npb25Bd2FyZUhpc3RvcnkgZnJvbSAnQC9jb21wb25lbnRzL2Rhc2hib2FyZC9TZXNzaW9uQXdhcmVIaXN0b3J5JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkSGlzdG9yeVBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxEYXNoYm9hcmRUYWJzIC8+XG4gICAgICA8QmFsYW5jZXNEaXNwbGF5IC8+XG4gICAgICA8U2Vzc2lvbkF3YXJlSGlzdG9yeSAvPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiRGFzaGJvYXJkVGFicyIsIkJhbGFuY2VzRGlzcGxheSIsIlNlc3Npb25Bd2FyZUhpc3RvcnkiLCJEYXNoYm9hcmRIaXN0b3J5UGFnZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/history/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/SessionAwareHistory.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionAwareHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SessionAwareHistory() {\n    _s();\n    const { dispatch, orderHistory, config } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionId, setSelectedSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('current');\n    const [selectedSessionHistory, setSelectedSessionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareHistory.useEffect\": ()=>{\n            loadSessions();\n        }\n    }[\"SessionAwareHistory.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareHistory.useEffect\": ()=>{\n            if (selectedSessionId === 'current') {\n                setSelectedSessionHistory(orderHistory);\n            } else {\n                const sessionHistory = sessionManager.getSessionHistory(selectedSessionId);\n                setSelectedSessionHistory(sessionHistory);\n            }\n        }\n    }[\"SessionAwareHistory.useEffect\"], [\n        selectedSessionId,\n        orderHistory\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // Filter out the current session to avoid duplicates\n        const pastSessions = allSessions.filter((session)=>session.id !== currentSessionId);\n        setSessions(pastSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleClearHistory = ()=>{\n        if (selectedSessionId === 'current') {\n            dispatch({\n                type: 'CLEAR_ORDER_HISTORY'\n            });\n            console.log(\"History Cleared: Current session trade history has been cleared.\");\n        } else {\n            // For past sessions, we would need to implement session history clearing\n            console.log(\"Cannot clear history for past sessions. Use current session to clear history.\");\n        }\n    };\n    const handleExportHistory = ()=>{\n        if (selectedSessionHistory.length === 0) {\n            console.log(\"No Data to Export: There is no trade history to export for the selected session.\");\n            return;\n        }\n        let csvContent;\n        let filename;\n        if (selectedSessionId === 'current') {\n            // Use existing export logic for current session\n            const headers = [\n                'Date',\n                'Time',\n                'Pair',\n                'Crypto',\n                'Order Type',\n                'Amount',\n                'Avg Price',\n                'Value',\n                'Price 1',\n                'Crypto 1',\n                'Price 2',\n                'Crypto 2',\n                'Profit/Loss (Crypto1)',\n                'Profit/Loss (Crypto2)'\n            ];\n            csvContent = [\n                headers.join(','),\n                ...selectedSessionHistory.map((entry)=>{\n                    var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                    return [\n                        new Date(entry.timestamp).toISOString().split('T')[0],\n                        new Date(entry.timestamp).toTimeString().split(' ')[0],\n                        entry.pair,\n                        entry.crypto1Symbol,\n                        entry.orderType,\n                        ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(config.numDigits)) || '',\n                        ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(config.numDigits)) || '',\n                        ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(config.numDigits)) || '',\n                        ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(config.numDigits)) || '',\n                        entry.crypto1Symbol,\n                        ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(config.numDigits)) || '',\n                        entry.crypto2Symbol,\n                        ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(config.numDigits)) || '',\n                        ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(config.numDigits)) || ''\n                    ].join(',');\n                })\n            ].join('\\n');\n            filename = \"current_session_history_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        } else {\n            // Use session manager export for past sessions\n            const exportedContent = sessionManager.exportSessionToCSV(selectedSessionId);\n            if (!exportedContent) {\n                console.error(\"Export Failed: Failed to export session data.\");\n                return;\n            }\n            csvContent = exportedContent;\n            const session = sessionManager.loadSession(selectedSessionId);\n            filename = \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\");\n        }\n        if (!csvContent) {\n            console.error(\"Export Failed: Failed to generate CSV content.\");\n            return;\n        }\n        // Create and download file\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', filename);\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        console.log(\"Export Complete: Trade history has been exported to CSV file.\");\n    };\n    const getSelectedSessionInfo = ()=>{\n        if (selectedSessionId === 'current') {\n            // Calculate total profit/loss correctly for both trading modes\n            // In SimpleSpot: SELL trades have profit\n            // In StablecoinSwap: BUY trades (final step of SELL operation) have profit\n            const profitTrades = orderHistory.filter((trade)=>trade.realizedProfitLossCrypto2 !== undefined && trade.realizedProfitLossCrypto2 !== null && trade.realizedProfitLossCrypto2 !== 0);\n            const totalProfitLoss = profitTrades.reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0);\n            return {\n                name: 'Current Session',\n                pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                totalTrades: orderHistory.length,\n                totalProfitLoss: totalProfitLoss,\n                lastModified: Date.now(),\n                isActive: true\n            };\n        }\n        return sessions.find((s)=>s.id === selectedSessionId);\n    };\n    const selectedSession = getSelectedSessionInfo();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"Session History\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"View trading history for current and past sessions.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium mb-2 block\",\n                                                children: \"Select Session:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                value: selectedSessionId,\n                                                onValueChange: setSelectedSessionId,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                        className: \"w-full sm:w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                            placeholder: \"Select a session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: \"current\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: \"default\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Current\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 172,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Current Session (\",\n                                                                                config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : 'Crypto 1/Crypto 2 = 0',\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            sessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                                                        className: \"my-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                            value: session.id,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: session.isActive ? \"default\" : \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: session.isActive ? \"Current\" : \"Past\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                        lineNumber: 182,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: session.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                        lineNumber: 185,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                lineNumber: 181,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, session.id, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 180,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleClearHistory,\n                                                className: \"btn-outline-neo\",\n                                                disabled: selectedSessionId !== 'current',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Clear History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleExportHistory,\n                                                className: \"btn-outline-neo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            selectedSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Session:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Pair:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.pair\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Total Trades:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.totalTrades\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Total P/L:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(selectedSession.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        children: selectedSession.totalProfitLoss.toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedSessionId !== 'current' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Last modified: \",\n                                            (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(selectedSession.lastModified), 'MMM dd, yyyy HH:mm')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-lg font-bold text-primary\",\n                                children: [\n                                    \"Trade History - \",\n                                    (selectedSession === null || selectedSession === void 0 ? void 0 : selectedSession.name) || 'Unknown Session'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: selectedSessionHistory.length === 0 ? \"No trades recorded for this session yet.\" : \"Showing \".concat(selectedSessionHistory.length, \" trades for the selected session.\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionHistoryTable, {\n                            history: selectedSessionHistory,\n                            config: config\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAwareHistory, \"jYsY2BRkfpxzGuHo9R4MewqHh18=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext\n    ];\n});\n_c = SessionAwareHistory;\n// Custom history table component for session-specific history\nfunction SessionHistoryTable(param) {\n    let { history, config } = param;\n    const formatNum = (num)=>{\n        var _num_toFixed;\n        return (_num_toFixed = num === null || num === void 0 ? void 0 : num.toFixed(config.numDigits)) !== null && _num_toFixed !== void 0 ? _num_toFixed : '-';\n    };\n    const columns = [\n        {\n            key: \"date\",\n            label: \"Date\"\n        },\n        {\n            key: \"hour\",\n            label: \"Hour\"\n        },\n        {\n            key: \"pair\",\n            label: \"Couple\"\n        },\n        {\n            key: \"crypto\",\n            label: \"Crypto (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"orderType\",\n            label: \"Order Type\"\n        },\n        {\n            key: \"amount\",\n            label: \"Amount\"\n        },\n        {\n            key: \"avgPrice\",\n            label: \"Avg Price\"\n        },\n        {\n            key: \"value\",\n            label: \"Value (\".concat(config.crypto2, \")\")\n        },\n        {\n            key: \"price1\",\n            label: \"Price 1\"\n        },\n        {\n            key: \"crypto1Symbol\",\n            label: \"Crypto (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"price2\",\n            label: \"Price 2\"\n        },\n        {\n            key: \"crypto2Symbol\",\n            label: \"Crypto (\".concat(config.crypto2, \")\")\n        },\n        {\n            key: \"profitCrypto1\",\n            label: \"Profit/Loss (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"profitCrypto2\",\n            label: \"Profit/Loss (\".concat(config.crypto2, \")\")\n        }\n    ];\n    if (history.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No trading history for this session yet.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"bg-card hover:bg-card border-b\",\n                            children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left\",\n                                    children: col.label\n                                }, col.key, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: history.map((entry)=>{\n                            var _formatNum;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-card/80 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(entry.timestamp), 'yyyy-MM-dd')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(entry.timestamp), 'HH:mm:ss')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.pair\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto1Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs font-semibold \".concat(entry.orderType === \"BUY\" ? \"text-green-400\" : \"text-destructive\"),\n                                        children: entry.orderType\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.amountCrypto1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.avgPrice)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.valueCrypto2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.price1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto1Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (_formatNum = formatNum(entry.price2)) !== null && _formatNum !== void 0 ? _formatNum : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto2Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs \".concat(entry.realizedProfitLossCrypto1 && entry.realizedProfitLossCrypto1 > 0 ? \"text-green-400\" : entry.realizedProfitLossCrypto1 && entry.realizedProfitLossCrypto1 < 0 ? \"text-destructive\" : \"\"),\n                                        children: entry.realizedProfitLossCrypto1 !== undefined && entry.realizedProfitLossCrypto1 !== null && entry.realizedProfitLossCrypto1 !== 0 ? formatNum(entry.realizedProfitLossCrypto1) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs \".concat(entry.realizedProfitLossCrypto2 && entry.realizedProfitLossCrypto2 > 0 ? \"text-green-400\" : entry.realizedProfitLossCrypto2 && entry.realizedProfitLossCrypto2 < 0 ? \"text-destructive\" : \"\"),\n                                        children: entry.realizedProfitLossCrypto2 !== undefined && entry.realizedProfitLossCrypto2 !== null && entry.realizedProfitLossCrypto2 !== 0 ? formatNum(entry.realizedProfitLossCrypto2) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, entry.id, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SessionHistoryTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"SessionAwareHistory\");\n$RefreshReg$(_c1, \"SessionHistoryTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, orientation = \"horizontal\", decorative = true, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: decorative ? \"none\" : \"separator\",\n        \"aria-orientation\": orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Separator;\nSeparator.displayName = \"Separator\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Separator$React.forwardRef\");\n$RefreshReg$(_c1, \"Separator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRThCO0FBQ0U7QUFPaEMsTUFBTUUsMEJBQVlGLDZDQUFnQixNQUNoQyxRQUVFSTtRQURBLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU87eUJBR3RFLDhEQUFDQztRQUNDTCxLQUFLQTtRQUNMTSxNQUFNSCxhQUFhLFNBQVM7UUFDNUJJLG9CQUFrQkw7UUFDbEJELFdBQVdKLDhDQUFFQSxDQUNYLHNCQUNBSyxnQkFBZ0IsZUFBZSxtQkFBbUIsa0JBQ2xERDtRQUVELEdBQUdHLEtBQUs7Ozs7Ozs7O0FBSWZOLFVBQVVVLFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcc2VwYXJhdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5pbnRlcmZhY2UgU2VwYXJhdG9yUHJvcHMgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xuICBvcmllbnRhdGlvbj86IFwiaG9yaXpvbnRhbFwiIHwgXCJ2ZXJ0aWNhbFwiO1xuICBkZWNvcmF0aXZlPzogYm9vbGVhbjtcbn1cblxuY29uc3QgU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgU2VwYXJhdG9yUHJvcHM+KFxuICAoXG4gICAgeyBjbGFzc05hbWUsIG9yaWVudGF0aW9uID0gXCJob3Jpem9udGFsXCIsIGRlY29yYXRpdmUgPSB0cnVlLCAuLi5wcm9wcyB9LFxuICAgIHJlZlxuICApID0+IChcbiAgICA8ZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIHJvbGU9e2RlY29yYXRpdmUgPyBcIm5vbmVcIiA6IFwic2VwYXJhdG9yXCJ9XG4gICAgICBhcmlhLW9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwic2hyaW5rLTAgYmctYm9yZGVyXCIsXG4gICAgICAgIG9yaWVudGF0aW9uID09PSBcImhvcml6b250YWxcIiA/IFwiaC1bMXB4XSB3LWZ1bGxcIiA6IFwiaC1mdWxsIHctWzFweF1cIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbik7XG5TZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBcIlNlcGFyYXRvclwiO1xuXG5leHBvcnQgeyBTZXBhcmF0b3IgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiU2VwYXJhdG9yIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsIm9yaWVudGF0aW9uIiwiZGVjb3JhdGl2ZSIsInByb3BzIiwiZGl2Iiwicm9sZSIsImFyaWEtb3JpZW50YXRpb24iLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/separator.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_b","vendors-node_modules_date-fns__","vendors-node_modules_d","vendors-node_modules_i","vendors-node_modules_lodash_k","vendors-node_modules_lo","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_r","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_c","vendors-node_modules_next_dist_l","vendors-node_modules_next_d","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_p","default-_app-pages-browser_src_lib_session-manager_ts","default-_app-pages-browser_src_contexts_TradingContext_tsx","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","default-_app-pages-browser_src_components_ui_badge_tsx-_app-pages-browser_src_components_ui_s-f5a402","default-_app-pages-browser_src_components_dashboard_BalancesDisplay_tsx-_app-pages-browser_sr-ac955a","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Chistory%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);