(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{283:(e,r,t)=>{"use strict";t.d(r,{A:()=>d,O:()=>c});var s=t(5155),a=t(2115),n=t(5695),l=t(5731),i=t(4553);let o=(0,a.createContext)(void 0),c=e=>{let{children:r}=e,[c,d]=(0,a.useState)(!1),[u,p]=(0,a.useState)(!0),[m,x]=(0,a.useState)(!1),f=(0,n.useRouter)(),h=(0,n.usePathname)();(0,a.useEffect)(()=>{(async()=>{let e=localStorage.getItem("plutoAuth"),r=localStorage.getItem("plutoAuthToken");if("true"===e&&r){d(!0),p(!1),x(!0);try{i.SessionManager.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}return}try{await new Promise(e=>setTimeout(e,1e3));let e=await fetch("http://localhost:5000/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"testuser",password:"password123"})});if(e.ok){let r=await e.json();localStorage.setItem("plutoAuth","true"),localStorage.setItem("plutoAuthToken",r.access_token),localStorage.setItem("plutoRefreshToken",r.refresh_token),localStorage.setItem("plutoUser",JSON.stringify(r.user)),d(!0);try{let{SessionManager:e}=await Promise.resolve().then(t.bind(t,4553));e.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}}}catch(e){}p(!1),x(!0)})();let e=setTimeout(()=>{p(!1),x(!0)},1e4);return()=>clearTimeout(e)},[]),(0,a.useEffect)(()=>{c&&("/login"===h||"/"===h)?f.replace("/dashboard"):c||"/login"===h||"/"===h||f.replace("/login")},[c,h,f]);let g=async(e,r)=>{p(!0);try{if(await l.ZQ.login(e,r))return d(!0),f.push("/dashboard"),!0;return d(!1),!1}catch(e){return d(!1),!1}finally{p(!1)}},b=async()=>{try{await l.ZQ.logout()}catch(e){}finally{d(!1),f.push("/login")}};return(0,s.jsx)(o.Provider,{value:{isAuthenticated:c,login:g,logout:b,isLoading:u},children:r})},d=()=>{let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},285:(e,r,t)=>{"use strict";t.d(r,{$:()=>i});var s=t(5155),a=t(2115),n=t(9434);let l={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},i=a.forwardRef((e,r)=>{let{className:t,variant:a="default",size:i="default",asChild:o=!1,...c}=e,d=l.variant[a]||l.variant.default,u=l.size[i]||l.size.default;return(0,s.jsx)(o?"span":"button",{className:(0,n.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",d,u,t),ref:r,...c})});i.displayName="Button"},2346:(e,r,t)=>{"use strict";t.d(r,{w:()=>l});var s=t(5155),a=t(2115),n=t(9434);let l=a.forwardRef((e,r)=>{let{className:t,orientation:a="horizontal",decorative:l=!0,...i}=e;return(0,s.jsx)("div",{ref:r,role:l?"none":"separator","aria-orientation":a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",t),...i})});l.displayName="Separator"},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(5155),a=t(2115),n=t(9434);let l=a.forwardRef((e,r)=>{let{className:t,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...l})});l.displayName="Input"},2972:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>X});var s=t(5155),a=t(2115),n=t(6874),l=t.n(n),i=t(5695),o=t(9435),c=t(285),d=t(283),u=t(8186),p=t(7082),m=t(9532),x=t(3349),f=t(6126),h=t(6695),g=t(8639),b=t(2087),v=t(7648);function y(e){let{className:r=""}=e,[t,n]=(0,a.useState)(!0),[l,i]=(0,a.useState)(new Date),[o,c]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{c(!0),n(navigator.onLine);let e=()=>n(!0),r=()=>n(!1);window.addEventListener("online",e),window.addEventListener("offline",r);let t=setInterval(()=>{i(new Date)},1e3);return()=>{window.removeEventListener("online",e),window.removeEventListener("offline",r),clearInterval(t)}},[]),(0,s.jsxs)("div",{className:"flex items-center gap-2 ".concat(r),children:[(0,s.jsxs)(f.E,{variant:t?"default":"destructive",className:"flex items-center gap-1 ".concat(t?"bg-green-600 hover:bg-green-600/90 text-white":""),children:[t?(0,s.jsx)(g.A,{className:"h-3 w-3"}):(0,s.jsx)(b.A,{className:"h-3 w-3"}),t?"Online":"Offline"]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,s.jsx)(v.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:l.toLocaleTimeString()})]})]})}function j(){let{logout:e}=(0,d.A)();(0,i.useRouter)();let r=(0,i.usePathname)(),t=[{href:"/dashboard",label:"Home",icon:(0,s.jsx)(u.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,s.jsx)(p.A,{})}];return(0,s.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(o.A,{useFullName:!1}),(0,s.jsx)(y,{})]}),(0,s.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[t.map(e=>(0,s.jsx)(c.$,{variant:r===e.href?"default":"ghost",size:"sm",asChild:!0,className:"".concat(r===e.href?"btn-neo":"hover:bg-accent/50"),children:(0,s.jsxs)(l(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,s.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,s.jsx)(m.A,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,s.jsx)(x.A,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var N=t(7213),S=t(2523),w=t(5057),C=t(518),A=t(9434);let T=a.forwardRef((e,r)=>{let{className:t,checked:a,onCheckedChange:n,onChange:l,...i}=e;return(0,s.jsxs)("div",{className:"relative inline-flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",ref:r,className:"sr-only",checked:a,onChange:e=>{let r=e.target.checked;null==n||n(r),null==l||l(e)},...i}),(0,s.jsx)("div",{className:(0,A.cn)("h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer",a&&"bg-primary text-primary-foreground",t),onClick:()=>{if(!i.disabled){let e=!a;null==n||n(e)}},children:a&&(0,s.jsx)("div",{className:"flex items-center justify-center text-current",children:(0,s.jsx)(C.A,{className:"h-4 w-4"})})})]})});T.displayName="Checkbox";var E=t(9409),R=t(2346),k=t(4165);let O=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("textarea",{className:(0,A.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...a})});O.displayName="Textarea";var D=t(7313);function P(e){var r;let t,{isOpen:n,onClose:l,onSetTargetPrices:i}=e,[o,d]=(0,a.useState)("manual"),[u,p]=(0,a.useState)("");try{t=(0,N.U)()}catch(e){t=null}let[m,x]=(0,a.useState)("8"),[f,h]=(0,a.useState)("5"),[g,b]=(0,a.useState)("even"),v=(null==t?void 0:t.currentMarketPrice)||1e5,y=(null==t?void 0:null===(r=t.config)||void 0===r?void 0:r.slippagePercent)||.2,j=()=>{let e=parseInt(m),r=parseFloat(f);if(!e||e<2||e>20||!r||r<=0)return[];let t=[],s=v*(1-r/100),a=v*(1+r/100);if("even"===g)for(let r=0;r<e;r++){let n=s+r/(e-1)*(a-s);t.push(Math.round(n))}else if("fibonacci"===g){let r=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let n=0;n<e;n++){let l=s+(a-s)*(r[Math.min(n,r.length-1)]||n/(e-1));t.push(Math.round(l))}}else if("exponential"===g)for(let r=0;r<e;r++){let n=s+(a-s)*Math.pow(r/(e-1),1.5);t.push(Math.round(n))}let n=3*y/100*v,l=t.sort((e,r)=>e-r),i=[];for(let e=0;e<l.length;e++){let r=l[e];if(i.length>0){let e=i[i.length-1];r-e<n&&(r=e+n)}i.push(Math.round(r))}return i},S=()=>{let e=u.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,r)=>e-r);if(e.length<2)return{hasOverlap:!1,message:""};let r=y/100*v;for(let t=0;t<e.length-1;t++)if(e[t]+r>=e[t+1]-r){let s=2*r,a=e[t+1]-e[t];return{hasOverlap:!0,message:"Overlap detected between ".concat(e[t]," and ").concat(e[t+1],". Minimum gap needed: ").concat(s.toFixed(0),", actual gap: ").concat(a.toFixed(0))}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},C=S();return(0,s.jsx)(k.lG,{open:n,onOpenChange:l,children:(0,s.jsxs)(k.Cf,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,s.jsxs)(k.c7,{children:[(0,s.jsx)(k.L3,{className:"text-primary",children:"Set Target Prices"}),(0,s.jsx)(k.rr,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,s.jsxs)(D.tU,{value:o,onValueChange:d,className:"w-full",children:[(0,s.jsxs)(D.j7,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(D.Xi,{value:"manual",children:"Manual Entry"}),(0,s.jsx)(D.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,s.jsx)(D.av,{value:"manual",className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(w.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,s.jsx)(O,{id:"target-prices-input",value:u,onChange:e=>p(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),C.message&&(0,s.jsx)("p",{className:"text-sm ".concat(C.hasOverlap?"text-red-500":"text-green-500"),children:C.message})]})}),(0,s.jsxs)(D.av,{value:"automatic",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,s.jsxs)(E.l6,{value:m,onValueChange:x,children:[(0,s.jsx)(E.bq,{children:(0,s.jsx)(E.yv,{})}),(0,s.jsx)(E.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,s.jsxs)(E.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,s.jsxs)(E.l6,{value:f,onValueChange:h,children:[(0,s.jsx)(E.bq,{children:(0,s.jsx)(E.yv,{})}),(0,s.jsx)(E.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,s.jsxs)(E.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,s.jsxs)(E.l6,{value:g,onValueChange:b,children:[(0,s.jsx)(E.bq,{children:(0,s.jsx)(E.yv,{})}),(0,s.jsxs)(E.gC,{children:[(0,s.jsx)(E.eb,{value:"even",children:"Even Distribution"}),(0,s.jsx)(E.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,s.jsx)(E.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,s.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Current Market Price:"})," $",v.toLocaleString(),(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Slippage:"})," \xb1",y,"% ($",(v*y/100).toFixed(0),")",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Range:"})," $",(v*(1-parseFloat(f)/100)).toLocaleString()," - $",(v*(1+parseFloat(f)/100)).toLocaleString()]})}),(0,s.jsxs)(c.$,{onClick:()=>{p(j().join("\n"))},className:"w-full btn-neo",children:["Generate ",m," Target Prices"]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(w.J,{children:"Generated Prices (Preview)"}),(0,s.jsx)(O,{value:u,onChange:e=>p(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),C.message&&(0,s.jsx)("p",{className:"text-sm ".concat(C.hasOverlap?"text-red-500":"text-green-500"),children:C.message})]})]})]}),(0,s.jsxs)(k.Es,{children:[(0,s.jsx)(k.HM,{asChild:!0,children:(0,s.jsx)(c.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,s.jsx)(c.$,{type:"button",onClick:()=>{let e=u.split("\n").map(e=>e.trim()).filter(e=>""!==e),r=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);(0!==r.length||!(e.length>0))&&!S().hasOverlap&&(i(r),p(""),l())},disabled:C.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var B=t(9348),U=t(5318),F=t(1133);function I(e){let{label:r,value:t,allowedCryptos:n,onValidCrypto:l,placeholder:i="Enter crypto symbol",description:o,className:d}=e,[u,p]=(0,a.useState)(""),[m,x]=(0,a.useState)("idle"),[f,h]=(0,a.useState)(""),[g,b]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t&&t!==u?(p(t),x("valid"),b(!0)):t||b(!1)},[t]);let v=()=>{let e=u.toUpperCase().trim();if(!e){x("invalid"),h("Please enter a crypto symbol");return}if(!n||!Array.isArray(n)){x("invalid"),h("No allowed cryptocurrencies configured");return}n.includes(e)?(x("valid"),h(""),b(!0),l(e)):(x("invalid"),h("".concat(e," is not available. Allowed: ").concat(n.join(", "))))};return(0,s.jsxs)("div",{className:(0,A.cn)("space-y-2",d),children:[(0,s.jsx)(w.J,{htmlFor:"crypto-input-".concat(r),children:r}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(S.p,{id:"crypto-input-".concat(r),value:u||t,onChange:e=>{p(e.target.value),x("idle"),h("")},onKeyPress:e=>{"Enter"===e.key&&v()},placeholder:i,className:(0,A.cn)("pr-8",(()=>{switch(m){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==m&&(0,s.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(m){case"valid":return(0,s.jsx)(C.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,s.jsx)(U.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,s.jsx)(c.$,{onClick:v,variant:"outline",className:"btn-neo",disabled:!u.trim(),children:"Check"})]}),t&&g&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",t]})]}),"invalid"===m&&f&&(0,s.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,s.jsx)(F.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,s.jsx)("span",{children:f})]}),o&&(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:o}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[n&&Array.isArray(n)?n.length:0," cryptocurrencies available"]})]})}var L=t(8803),M=t(172),G=t(955),V=t(8531);let _=["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],J=["USDC","DAI","TUSD","FDUSD","USDT","EUR"],z=["USDT","USDC","BTC"],$=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function H(){let e;try{e=(0,N.U)()}catch(e){return(0,s.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:r,dispatch:t,botSystemStatus:n,appSettings:l,setTargetPrices:i}=e,o="Running"===n,d="WarmingUp"===n,[u,p]=(0,a.useState)(!1),m=e=>{let r;let{name:s,value:a,type:n,checked:l}=e.target;if("checkbox"===n)r=l;else if("number"===n){if(""===a||null==a)r=0;else{let e=parseFloat(a);r=isNaN(e)?0:e}}else r=a;t({type:"SET_CONFIG",payload:{[s]:r}})},x=(e,s)=>{if(t({type:"SET_CONFIG",payload:{[e]:s}}),"crypto1"===e){let e=B.vA[s]||z||["USDT","USDC","BTC"];r.crypto2&&Array.isArray(e)&&e.includes(r.crypto2)||t({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},f=(e,r)=>{let s=parseFloat(r);isNaN(s)&&(s=0),s<0&&(s=0),s>100&&(s=100),"incomeSplitCrypto1Percent"===e?t({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:s,incomeSplitCrypto2Percent:100-s}}):t({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:s,incomeSplitCrypto1Percent:100-s}})};return B.hg,"SimpleSpot"===r.tradingMode?B.vA[r.crypto1]:(B.hg||[]).filter(e=>e!==r.crypto1),(0,s.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"})}),(0,s.jsx)("div",{className:"flex-1 pr-2 overflow-y-auto",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(h.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,s.jsxs)(h.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(T,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===r.tradingMode,onCheckedChange:e=>{let s;let a=e?"StablecoinSwap":"SimpleSpot";s="StablecoinSwap"===a?(_||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==r.crypto1)[0]:(J||["USDC","DAI","TUSD","FDUSD","USDT","EUR"])[0],t({type:"SET_CONFIG",payload:{tradingMode:a,crypto2:s}})}}),(0,s.jsx)(w.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===r.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===r.tradingMode&&(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,s.jsxs)(E.l6,{value:r.preferredStablecoin,onValueChange:e=>x("preferredStablecoin",e),children:[(0,s.jsx)(E.bq,{id:"preferredStablecoin",children:(0,s.jsx)(E.yv,{placeholder:"Select stablecoin"})}),(0,s.jsx)(E.gC,{children:$.map(e=>(0,s.jsx)(E.eb,{value:e,children:e},e))})]})]})]})]}),(0,s.jsxs)(h.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsx)(I,{label:"Crypto 1 (Base)",value:r.crypto1,allowedCryptos:_||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],onValidCrypto:e=>{if(t({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===r.tradingMode){let s=(_||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(r=>r!==e);s.includes(r.crypto2)&&r.crypto2!==e||t({type:"SET_CONFIG",payload:{crypto2:s[0]}})}else{let e=J||["USDC","DAI","TUSD","FDUSD","USDT","EUR"];e.includes(r.crypto2)||t({type:"SET_CONFIG",payload:{crypto2:e[0]}})}},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,s.jsx)(I,{label:"StablecoinSwap"===r.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:r.crypto2,allowedCryptos:"StablecoinSwap"===r.tradingMode?(_||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==r.crypto1):J||["USDC","DAI","TUSD","FDUSD","USDT","EUR"],onValidCrypto:e=>{("StablecoinSwap"!==r.tradingMode||e!==r.crypto1)&&t({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===r.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===r.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,s.jsxs)(h.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(h.aR,{children:(0,s.jsx)(h.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,s.jsxs)(h.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:e.name,children:e.label}),(0,s.jsx)(S.p,{id:e.name,name:e.name,type:e.type,value:r[e.name],onChange:m,step:e.step,min:"0"})]},e.name)),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{children:"Couple Income % Split (must sum to 100)"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[r.crypto1||"Crypto 1","%"]}),(0,s.jsx)(S.p,{id:"incomeSplitCrypto1Percent",type:"number",value:r.incomeSplitCrypto1Percent,onChange:e=>f("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[r.crypto2||"Crypto 2","%"]}),(0,s.jsx)(S.p,{id:"incomeSplitCrypto2Percent",type:"number",value:r.incomeSplitCrypto2Percent,onChange:e=>f("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,s.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,s.jsx)(R.w,{className:"mb-4 bg-sidebar-border"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:(0,A.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",o?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[o?(0,s.jsx)(L.A,{className:"h-4 w-4"}):d?(0,s.jsx)(M.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(G.A,{className:"h-4 w-4"}),"Bot Status: ",o?"Running":d?"Warming Up":"Stopped"]}),(0,s.jsx)(c.$,{onClick:()=>p(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,s.jsxs)(c.$,{onClick:()=>{o?t({type:"SYSTEM_STOP_BOT"}):t({type:"SYSTEM_START_BOT_INITIATE"})},className:(0,A.cn)("w-full btn-neo",o||d?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:d,children:[o?(0,s.jsx)(L.A,{className:"h-4 w-4"}):d?(0,s.jsx)(M.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(G.A,{className:"h-4 w-4"}),o?"Stop Bot":d?"Warming Up...":"Start Bot"]}),(0,s.jsxs)(c.$,{onClick:()=>{t({type:"SYSTEM_RESET_BOT"})},variant:"outline",className:"w-full btn-outline-neo",disabled:d,children:[(0,s.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,s.jsx)(P,{isOpen:u,onClose:()=>p(!1),onSetTargetPrices:i})]})}function X(e){let{children:r}=e,{isAuthenticated:t,isLoading:n}=(0,d.A)(),l=(0,i.useRouter)();return n?(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,s.jsx)(M.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Loading..."})]}):t?(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,s.jsx)(j,{}),(0,s.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,s.jsx)(H,{}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:r})]})]}):((0,a.useEffect)(()=>{l.replace("/login")},[l]),(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,s.jsx)(M.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting..."})]}))}},4165:(e,r,t)=>{"use strict";t.d(r,{Cf:()=>p,Es:()=>x,HM:()=>d,L3:()=>f,c7:()=>m,lG:()=>o,rr:()=>h});var s=t(5155),a=t(2115),n=t(5318),l=t(9434);let i=a.createContext({open:!1,onOpenChange:()=>{}}),o=e=>{let{children:r,open:t=!1,onOpenChange:n}=e,[l,o]=a.useState(t);return a.useEffect(()=>{o(t)},[t]),(0,s.jsx)(i.Provider,{value:{open:l,onOpenChange:e=>{o(e),null==n||n(e)}},children:r})};a.forwardRef((e,r)=>{let{className:t,children:n,asChild:l=!1,...o}=e,{onOpenChange:c}=a.useContext(i);return(0,s.jsx)("button",{ref:r,className:t,onClick:()=>c(!0),...o,children:n})}).displayName="DialogTrigger";let c=e=>{let{children:r}=e;return(0,s.jsx)(s.Fragment,{children:r})},d=a.forwardRef((e,r)=>{let{className:t,children:n,asChild:l=!1,...o}=e,{onOpenChange:c}=a.useContext(i);return(0,s.jsx)("button",{ref:r,className:t,onClick:()=>c(!1),...o,children:n})});d.displayName="DialogClose";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80",t),...a})});u.displayName="DialogOverlay";let p=a.forwardRef((e,r)=>{let{className:t,children:o,...d}=e,{open:p,onOpenChange:m}=a.useContext(i);return p?(0,s.jsxs)(c,{children:[(0,s.jsx)(u,{onClick:()=>m(!1)}),(0,s.jsxs)("div",{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg",t),...d,children:[o,(0,s.jsxs)("button",{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",onClick:()=>m(!1),children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}):null});p.displayName="DialogContent";let m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})});m.displayName="DialogHeader";let x=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})});x.displayName="DialogFooter";let f=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h2",{ref:r,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",t),...a})});f.displayName="DialogTitle";let h=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});h.displayName="DialogDescription"},5057:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(5155),a=t(2115),n=t(9434);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("label",{ref:r,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",t),...a})});l.displayName="Label"},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(5155),a=t(2115),n=t(9434);let l={variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},i=a.forwardRef((e,r)=>{let{className:t,variant:a="default",...i}=e,o=l.variant[a]||l.variant.default;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",o,t),...i})});i.displayName="Badge"},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>i});var s=t(5155),a=t(2115),n=t(9434);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...a})});l.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-4 md:p-6 pt-0",t),...a})});d.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",t),...a})}).displayName="CardFooter"},7313:(e,r,t)=>{"use strict";t.d(r,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>i});var s=t(5155),a=t(2115),n=t(9434);let l=a.createContext({activeTab:"",setActiveTab:()=>{}}),i=a.forwardRef((e,r)=>{let{defaultValue:t="",value:n,onValueChange:i,children:o,className:c,...d}=e,[u,p]=a.useState(n||t);return a.useEffect(()=>{void 0!==n&&p(n)},[n]),(0,s.jsx)(l.Provider,{value:{activeTab:u,setActiveTab:e=>{void 0===n&&p(e),null==i||i(e)}},children:(0,s.jsx)("div",{ref:r,className:c,...d,children:o})})});i.displayName="Tabs";let o=a.forwardRef((e,r)=>{let{className:t,children:a,...l}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...l,children:a})});o.displayName="TabsList";let c=a.forwardRef((e,r)=>{let{className:t,value:i,children:o,onClick:c,...d}=e,{activeTab:u,setActiveTab:p}=a.useContext(l);return(0,s.jsx)("button",{ref:r,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",u===i&&"bg-background text-foreground shadow-sm",t),onClick:()=>{p(i),null==c||c()},...d,children:o})});c.displayName="TabsTrigger";let d=a.forwardRef((e,r)=>{let{className:t,value:i,children:o,...c}=e,{activeTab:d}=a.useContext(l);return d!==i?null:(0,s.jsx)("div",{ref:r,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...c,children:o})});d.displayName="TabsContent"},7926:(e,r,t)=>{Promise.resolve().then(t.bind(t,2972))},9409:(e,r,t)=>{"use strict";t.d(r,{bq:()=>c,eb:()=>u,gC:()=>d,l6:()=>o,yv:()=>p});var s=t(5155),a=t(2115),n=t(9556),l=t(9434);let i=a.createContext({value:"",onValueChange:()=>{},open:!1,setOpen:()=>{}}),o=a.forwardRef((e,r)=>{let{children:t,value:n,onValueChange:l,defaultValue:o,...c}=e,[d,u]=a.useState(n||o||""),[p,m]=a.useState(!1);return a.useEffect(()=>{void 0!==n&&u(n)},[n]),(0,s.jsx)(i.Provider,{value:{value:d,onValueChange:e=>{void 0===n&&u(e),null==l||l(e),m(!1)},open:p,setOpen:m},children:(0,s.jsx)("div",{ref:r,className:"relative",...c,children:t})})});o.displayName="Select";let c=a.forwardRef((e,r)=>{let{className:t,children:o,...c}=e,{open:d,setOpen:u}=a.useContext(i);return(0,s.jsxs)("button",{ref:r,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),onClick:()=>u(!d),...c,children:[o,(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})]})});c.displayName="SelectTrigger";let d=a.forwardRef((e,r)=>{let{className:t,children:n,...o}=e,{open:c}=a.useContext(i);return c?(0,s.jsx)("div",{ref:r,className:(0,l.cn)("absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md",t),...o,children:n}):null});d.displayName="SelectContent";let u=a.forwardRef((e,r)=>{let{className:t,children:n,value:o,...c}=e,{onValueChange:d}=a.useContext(i);return(0,s.jsx)("button",{ref:r,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",t),onClick:()=>d(o),...c,children:n})});u.displayName="SelectItem";let p=a.forwardRef((e,r)=>{let{placeholder:t,...n}=e,{value:l}=a.useContext(i);return(0,s.jsx)("span",{ref:r,...n,children:l||t})});p.displayName="SelectValue"},9435:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(5155);t(2115);var a=t(6766);let n=e=>{let{className:r,useFullName:t=!0}=e;return(0,s.jsxs)("div",{className:"flex items-center text-2xl font-bold text-primary ".concat(r),children:[(0,s.jsx)(a.default,{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",width:28,height:28,className:"mr-2 rounded-sm"}),(0,s.jsxs)("span",{children:["Pluto",t?" Trading Bot":""]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,213,358],()=>r(7926)),_N_E=e.O()}]);