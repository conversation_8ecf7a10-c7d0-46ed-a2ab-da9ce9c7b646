/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(app-pages-browser)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxzTEFBeUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./src/components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_TradingConfigSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/TradingConfigSidebar */ \"(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children } = param;\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // Show loading during hydration to prevent mismatch\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-12 w-12 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-xl\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated (only after hydration)\n    if (!isAuthenticated) {\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"DashboardLayout.useEffect\": ()=>{\n                router.replace('/login');\n            }\n        }[\"DashboardLayout.useEffect\"], [\n            router\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-12 w-12 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-xl\",\n                    children: \"Redirecting...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-background text-foreground\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_TradingConfigSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"XdJ6GKuWw9Y65Kq26+84BH9LjXo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/AppHeader.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppHeader.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_shared_Logo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/Logo */ \"(app-pages-browser)/./src/components/shared/Logo.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_backend_status__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/backend-status */ \"(app-pages-browser)/./src/components/ui/backend-status.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AppHeader() {\n    _s();\n    const { logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const handleNewSession = ()=>{\n        // Open a new tab/window with a parameter to indicate it's a new session\n        window.open('/dashboard?newSession=true', '_blank');\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Home',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 30,\n                columnNumber: 48\n            }, this)\n        },\n        {\n            href: '/admin',\n            label: 'Admin Panel',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 31,\n                columnNumber: 51\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Logo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        useFullName: false\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_backend_status__WEBPACK_IMPORTED_MODULE_7__.NetworkStatus, {}, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center gap-2 sm:gap-3\",\n                children: [\n                    navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: pathname === item.href ? \"default\" : \"ghost\",\n                            size: \"sm\",\n                            asChild: true,\n                            className: \"\".concat(pathname === item.href ? 'btn-neo' : 'hover:bg-accent/50'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, item.label, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleNewSession,\n                        className: \"hover:bg-accent/50 flex items-center gap-2\",\n                        title: \"Open a new independent trading session in a new tab\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"New Session\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 10\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleLogout,\n                        className: \"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(AppHeader, \"gxli3sVmWq4I33HjyK3OPLJOU50=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = AppHeader;\nvar _c;\n$RefreshReg$(_c, \"AppHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppHeader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Removed ScrollArea import to fix webpack issues\n\n\n\n// Define allowed cryptos locally to avoid import issues\nconst ALLOWED_CRYPTO1 = [\n    \"BTC\",\n    \"ETH\",\n    \"BNB\",\n    \"SOL\",\n    \"LINK\",\n    \"AVAX\",\n    \"DOT\",\n    \"UNI\",\n    \"NEAR\",\n    \"AAVE\",\n    \"ATOM\",\n    \"VET\",\n    \"RENDER\",\n    \"POL\",\n    \"ALGO\",\n    \"ARB\",\n    \"FET\",\n    \"PAXG\",\n    \"GALA\",\n    \"CRV\",\n    \"COMP\",\n    \"ENJ\"\n];\nconst ALLOWED_CRYPTO2 = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 pr-2 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_12__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_12__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ]).filter((c)=>c !== config.crypto1) : ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    console.log(\"Bot Reset: Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\");\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_10__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"c/Zp4j/duEwnX/DoCtSd6BP2fXE=\");\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx":
/*!****************************************************!*\
  !*** ./src/components/modals/TargetPriceModal.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TargetPriceModal: () => (/* binding */ TargetPriceModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ TargetPriceModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction TargetPriceModal(param) {\n    let { isOpen, onClose, onSetTargetPrices } = param;\n    var _tradingContext_config;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('manual');\n    const [priceInput, setPriceInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Safely get trading context with fallback\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    } catch (error) {\n        console.warn('Trading context not available:', error);\n        tradingContext = null;\n    }\n    // Auto generation settings\n    const [targetCount, setTargetCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('8');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('5'); // percentage range - start with 5% as default\n    const [distribution, setDistribution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('even'); // even, fibonacci, exponential\n    // Get current market price and slippage from context with safe fallbacks\n    const currentMarketPrice = (tradingContext === null || tradingContext === void 0 ? void 0 : tradingContext.currentMarketPrice) || 100000;\n    const slippagePercent = (tradingContext === null || tradingContext === void 0 ? void 0 : (_tradingContext_config = tradingContext.config) === null || _tradingContext_config === void 0 ? void 0 : _tradingContext_config.slippagePercent) || 0.2;\n    const generateAutomaticPrices = ()=>{\n        const count = parseInt(targetCount);\n        const range = parseFloat(priceRange);\n        if (!count || count < 2 || count > 20 || !range || range <= 0) {\n            return [];\n        }\n        const prices = [];\n        const minPrice = currentMarketPrice * (1 - range / 100);\n        const maxPrice = currentMarketPrice * (1 + range / 100);\n        if (distribution === 'even') {\n            // Even distribution\n            for(let i = 0; i < count; i++){\n                const price = minPrice + (maxPrice - minPrice) * (i / (count - 1));\n                prices.push(Math.round(price));\n            }\n        } else if (distribution === 'fibonacci') {\n            // Fibonacci-like distribution (more targets near current price)\n            const fibRatios = [\n                0,\n                0.236,\n                0.382,\n                0.5,\n                0.618,\n                0.764,\n                0.854,\n                0.927,\n                1\n            ];\n            for(let i = 0; i < count; i++){\n                const ratio = fibRatios[Math.min(i, fibRatios.length - 1)] || i / (count - 1);\n                const price = minPrice + (maxPrice - minPrice) * ratio;\n                prices.push(Math.round(price));\n            }\n        } else if (distribution === 'exponential') {\n            // Exponential distribution\n            for(let i = 0; i < count; i++){\n                const ratio = Math.pow(i / (count - 1), 1.5);\n                const price = minPrice + (maxPrice - minPrice) * ratio;\n                prices.push(Math.round(price));\n            }\n        }\n        // Ensure no overlap with slippage zones\n        const minGap = currentMarketPrice * (slippagePercent * 3 / 100); // 3x slippage as minimum gap\n        const sortedPrices = prices.sort((a, b)=>a - b);\n        const adjustedPrices = [];\n        for(let i = 0; i < sortedPrices.length; i++){\n            let price = sortedPrices[i];\n            // Ensure minimum gap from previous price\n            if (adjustedPrices.length > 0) {\n                const lastPrice = adjustedPrices[adjustedPrices.length - 1];\n                if (price - lastPrice < minGap) {\n                    price = lastPrice + minGap;\n                }\n            }\n            adjustedPrices.push(Math.round(price));\n        }\n        return adjustedPrices;\n    };\n    const handleAutoGenerate = ()=>{\n        const generatedPrices = generateAutomaticPrices();\n        setPriceInput(generatedPrices.join('\\n'));\n    };\n    const validateSlippageOverlap = ()=>{\n        const lines = priceInput.split('\\n').filter((line)=>line.trim() !== '');\n        const prices = lines.map((line)=>parseFloat(line.trim())).filter((p)=>!isNaN(p) && p > 0).sort((a, b)=>a - b);\n        if (prices.length < 2) return {\n            hasOverlap: false,\n            message: ''\n        };\n        const slippageAmount = currentMarketPrice * (slippagePercent / 100);\n        for(let i = 0; i < prices.length - 1; i++){\n            const currentMax = prices[i] + slippageAmount;\n            const nextMin = prices[i + 1] - slippageAmount;\n            if (currentMax >= nextMin) {\n                const minGap = slippageAmount * 2;\n                const actualGap = prices[i + 1] - prices[i];\n                return {\n                    hasOverlap: true,\n                    message: \"Overlap detected between \".concat(prices[i], \" and \").concat(prices[i + 1], \". Minimum gap needed: \").concat(minGap.toFixed(0), \", actual gap: \").concat(actualGap.toFixed(0))\n                };\n            }\n        }\n        return {\n            hasOverlap: false,\n            message: 'No slippage zone overlaps detected ✓'\n        };\n    };\n    const handleSave = ()=>{\n        const lines = priceInput.split('\\n').map((line)=>line.trim()).filter((line)=>line !== '');\n        const prices = lines.map((line)=>parseFloat(line)).filter((price)=>!isNaN(price) && price > 0);\n        if (prices.length === 0 && lines.length > 0) {\n            console.error(\"Invalid Input: No valid prices found. Please enter numbers, one per line.\");\n            return;\n        }\n        // Check for slippage overlaps\n        const validation = validateSlippageOverlap();\n        if (validation.hasOverlap) {\n            console.error(\"Slippage Zone Overlap: \".concat(validation.message));\n            return;\n        }\n        onSetTargetPrices(prices);\n        console.log(\"Target Prices Updated: \".concat(prices.length, \" target prices have been set.\"));\n        setPriceInput(''); // Clear input after saving\n        onClose();\n    };\n    const validation = validateSlippageOverlap();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-2xl bg-card border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"text-primary\",\n                            children: \"Set Target Prices\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"manual\",\n                                    children: \"Manual Entry\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"automatic\",\n                                    children: \"Automatic Generation\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"manual\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"target-prices-input\",\n                                        className: \"text-left\",\n                                        children: \"Target Prices\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        id: \"target-prices-input\",\n                                        value: priceInput,\n                                        onChange: (e)=>setPriceInput(e.target.value),\n                                        placeholder: \"50000 50500 49800\",\n                                        className: \"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    validation.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm \".concat(validation.hasOverlap ? 'text-red-500' : 'text-green-500'),\n                                        children: validation.message\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"automatic\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"targetCount\",\n                                                    children: \"Number of Targets\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: targetCount,\n                                                    onValueChange: setTargetCount,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                4,\n                                                                6,\n                                                                8,\n                                                                10,\n                                                                12,\n                                                                15,\n                                                                20\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: num.toString(),\n                                                                    children: [\n                                                                        num,\n                                                                        \" targets\"\n                                                                    ]\n                                                                }, num, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"priceRange\",\n                                                    children: \"Price Range (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: priceRange,\n                                                    onValueChange: setPriceRange,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                2,\n                                                                2.5,\n                                                                3,\n                                                                3.5,\n                                                                4,\n                                                                4.5,\n                                                                5,\n                                                                6,\n                                                                7,\n                                                                8,\n                                                                10\n                                                            ].map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: range.toString(),\n                                                                    children: [\n                                                                        \"\\xb1\",\n                                                                        range,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, range, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"distribution\",\n                                            children: \"Distribution Pattern\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: distribution,\n                                            onValueChange: setDistribution,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"even\",\n                                                            children: \"Even Distribution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"fibonacci\",\n                                                            children: \"Fibonacci (More near current price)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"exponential\",\n                                                            children: \"Exponential (Wider spread)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-muted p-3 rounded-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Current Market Price:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" $\",\n                                            currentMarketPrice.toLocaleString(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 94\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Slippage:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \\xb1\",\n                                            slippagePercent,\n                                            \"% ($\",\n                                            (currentMarketPrice * slippagePercent / 100).toFixed(0),\n                                            \")\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 124\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Range:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" $\",\n                                            (currentMarketPrice * (1 - parseFloat(priceRange) / 100)).toLocaleString(),\n                                            \" - $\",\n                                            (currentMarketPrice * (1 + parseFloat(priceRange) / 100)).toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleAutoGenerate,\n                                    className: \"w-full btn-neo\",\n                                    children: [\n                                        \"Generate \",\n                                        targetCount,\n                                        \" Target Prices\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            children: \"Generated Prices (Preview)\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            value: priceInput,\n                                            onChange: (e)=>setPriceInput(e.target.value),\n                                            className: \"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono\",\n                                            placeholder: \"Click 'Generate' to create automatic target prices...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        validation.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm \".concat(validation.hasOverlap ? 'text-red-500' : 'text-green-500'),\n                                            children: validation.message\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogClose, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                className: \"btn-outline-neo\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            onClick: handleSave,\n                            disabled: validation.hasOverlap,\n                            className: \"btn-neo\",\n                            children: \"Save Prices\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\TargetPriceModal.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(TargetPriceModal, \"crzD0y2j58sojUXU9FegnCfecYI=\");\n_c = TargetPriceModal;\nvar _c;\n$RefreshReg$(_c, \"TargetPriceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21vZGFscy9UYXJnZXRQcmljZU1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBU1I7QUFDZ0I7QUFDSTtBQUNOO0FBRWtDO0FBQ3VCO0FBRXpDO0FBUXZELFNBQVNzQixpQkFBaUIsS0FBNkQ7UUFBN0QsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLGlCQUFpQixFQUF5QixHQUE3RDtRQXFCUEM7O0lBcEJ4QixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRCLFlBQVlDLGNBQWMsR0FBRzdCLCtDQUFRQSxDQUFDO0lBRzdDLDJDQUEyQztJQUMzQyxJQUFJeUI7SUFDSixJQUFJO1FBQ0ZBLGlCQUFpQkwsMkVBQWlCQTtJQUNwQyxFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUMsSUFBSSxDQUFDLGtDQUFrQ0Y7UUFDL0NMLGlCQUFpQjtJQUNuQjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNLENBQUNRLGFBQWFDLGVBQWUsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ21DLFlBQVlDLGNBQWMsR0FBR3BDLCtDQUFRQSxDQUFDLE1BQU0sOENBQThDO0lBQ2pHLE1BQU0sQ0FBQ3FDLGNBQWNDLGdCQUFnQixHQUFHdEMsK0NBQVFBLENBQUMsU0FBUywrQkFBK0I7SUFFekYseUVBQXlFO0lBQ3pFLE1BQU11QyxxQkFBcUJkLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JjLGtCQUFrQixLQUFJO0lBQ2pFLE1BQU1DLGtCQUFrQmYsQ0FBQUEsMkJBQUFBLHNDQUFBQSx5QkFBQUEsZUFBZ0JnQixNQUFNLGNBQXRCaEIsNkNBQUFBLHVCQUF3QmUsZUFBZSxLQUFJO0lBRW5FLE1BQU1FLDBCQUEwQjtRQUM5QixNQUFNQyxRQUFRQyxTQUFTWDtRQUN2QixNQUFNWSxRQUFRQyxXQUFXWDtRQUV6QixJQUFJLENBQUNRLFNBQVNBLFFBQVEsS0FBS0EsUUFBUSxNQUFNLENBQUNFLFNBQVNBLFNBQVMsR0FBRztZQUM3RCxPQUFPLEVBQUU7UUFDWDtRQUVBLE1BQU1FLFNBQW1CLEVBQUU7UUFDM0IsTUFBTUMsV0FBV1QscUJBQXNCLEtBQUlNLFFBQVEsR0FBRTtRQUNyRCxNQUFNSSxXQUFXVixxQkFBc0IsS0FBSU0sUUFBUSxHQUFFO1FBRXJELElBQUlSLGlCQUFpQixRQUFRO1lBQzNCLG9CQUFvQjtZQUNwQixJQUFLLElBQUlhLElBQUksR0FBR0EsSUFBSVAsT0FBT08sSUFBSztnQkFDOUIsTUFBTUMsUUFBUUgsV0FBVyxDQUFDQyxXQUFXRCxRQUFPLElBQU1FLENBQUFBLElBQUtQLENBQUFBLFFBQVEsRUFBQztnQkFDaEVJLE9BQU9LLElBQUksQ0FBQ0MsS0FBS0MsS0FBSyxDQUFDSDtZQUN6QjtRQUNGLE9BQU8sSUFBSWQsaUJBQWlCLGFBQWE7WUFDdkMsZ0VBQWdFO1lBQ2hFLE1BQU1rQixZQUFZO2dCQUFDO2dCQUFHO2dCQUFPO2dCQUFPO2dCQUFLO2dCQUFPO2dCQUFPO2dCQUFPO2dCQUFPO2FBQUU7WUFDdkUsSUFBSyxJQUFJTCxJQUFJLEdBQUdBLElBQUlQLE9BQU9PLElBQUs7Z0JBQzlCLE1BQU1NLFFBQVFELFNBQVMsQ0FBQ0YsS0FBS0ksR0FBRyxDQUFDUCxHQUFHSyxVQUFVRyxNQUFNLEdBQUcsR0FBRyxJQUFLUixJQUFLUCxDQUFBQSxRQUFRO2dCQUM1RSxNQUFNUSxRQUFRSCxXQUFXLENBQUNDLFdBQVdELFFBQU8sSUFBS1E7Z0JBQ2pEVCxPQUFPSyxJQUFJLENBQUNDLEtBQUtDLEtBQUssQ0FBQ0g7WUFDekI7UUFDRixPQUFPLElBQUlkLGlCQUFpQixlQUFlO1lBQ3pDLDJCQUEyQjtZQUMzQixJQUFLLElBQUlhLElBQUksR0FBR0EsSUFBSVAsT0FBT08sSUFBSztnQkFDOUIsTUFBTU0sUUFBUUgsS0FBS00sR0FBRyxDQUFDVCxJQUFLUCxDQUFBQSxRQUFRLElBQUk7Z0JBQ3hDLE1BQU1RLFFBQVFILFdBQVcsQ0FBQ0MsV0FBV0QsUUFBTyxJQUFLUTtnQkFDakRULE9BQU9LLElBQUksQ0FBQ0MsS0FBS0MsS0FBSyxDQUFDSDtZQUN6QjtRQUNGO1FBRUEsd0NBQXdDO1FBQ3hDLE1BQU1TLFNBQVNyQixxQkFBc0JDLENBQUFBLGtCQUFrQixJQUFJLEdBQUUsR0FBSSw2QkFBNkI7UUFDOUYsTUFBTXFCLGVBQWVkLE9BQU9lLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztRQUMvQyxNQUFNQyxpQkFBMkIsRUFBRTtRQUVuQyxJQUFLLElBQUlmLElBQUksR0FBR0EsSUFBSVcsYUFBYUgsTUFBTSxFQUFFUixJQUFLO1lBQzVDLElBQUlDLFFBQVFVLFlBQVksQ0FBQ1gsRUFBRTtZQUUzQix5Q0FBeUM7WUFDekMsSUFBSWUsZUFBZVAsTUFBTSxHQUFHLEdBQUc7Z0JBQzdCLE1BQU1RLFlBQVlELGNBQWMsQ0FBQ0EsZUFBZVAsTUFBTSxHQUFHLEVBQUU7Z0JBQzNELElBQUlQLFFBQVFlLFlBQVlOLFFBQVE7b0JBQzlCVCxRQUFRZSxZQUFZTjtnQkFDdEI7WUFDRjtZQUVBSyxlQUFlYixJQUFJLENBQUNDLEtBQUtDLEtBQUssQ0FBQ0g7UUFDakM7UUFFQSxPQUFPYztJQUNUO0lBRUEsTUFBTUUscUJBQXFCO1FBQ3pCLE1BQU1DLGtCQUFrQjFCO1FBQ3hCYixjQUFjdUMsZ0JBQWdCQyxJQUFJLENBQUM7SUFDckM7SUFFQSxNQUFNQywwQkFBMEI7UUFDOUIsTUFBTUMsUUFBUTNDLFdBQVc0QyxLQUFLLENBQUMsTUFBTUMsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxJQUFJLE9BQU87UUFDcEUsTUFBTTVCLFNBQVN3QixNQUFNSyxHQUFHLENBQUNGLENBQUFBLE9BQVE1QixXQUFXNEIsS0FBS0MsSUFBSSxLQUFLRixNQUFNLENBQUNJLENBQUFBLElBQUssQ0FBQ0MsTUFBTUQsTUFBTUEsSUFBSSxHQUFHZixJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7UUFFN0csSUFBSWpCLE9BQU9XLE1BQU0sR0FBRyxHQUFHLE9BQU87WUFBRXFCLFlBQVk7WUFBT0MsU0FBUztRQUFHO1FBRS9ELE1BQU1DLGlCQUFpQjFDLHFCQUFzQkMsQ0FBQUEsa0JBQWtCLEdBQUU7UUFFakUsSUFBSyxJQUFJVSxJQUFJLEdBQUdBLElBQUlILE9BQU9XLE1BQU0sR0FBRyxHQUFHUixJQUFLO1lBQzFDLE1BQU1nQyxhQUFhbkMsTUFBTSxDQUFDRyxFQUFFLEdBQUcrQjtZQUMvQixNQUFNRSxVQUFVcEMsTUFBTSxDQUFDRyxJQUFJLEVBQUUsR0FBRytCO1lBRWhDLElBQUlDLGNBQWNDLFNBQVM7Z0JBQ3pCLE1BQU12QixTQUFTcUIsaUJBQWlCO2dCQUNoQyxNQUFNRyxZQUFZckMsTUFBTSxDQUFDRyxJQUFJLEVBQUUsR0FBR0gsTUFBTSxDQUFDRyxFQUFFO2dCQUMzQyxPQUFPO29CQUNMNkIsWUFBWTtvQkFDWkMsU0FBUyw0QkFBNkNqQyxPQUFqQkEsTUFBTSxDQUFDRyxFQUFFLEVBQUMsU0FBNkNVLE9BQXRDYixNQUFNLENBQUNHLElBQUksRUFBRSxFQUFDLDBCQUEwRGtDLE9BQWxDeEIsT0FBT3lCLE9BQU8sQ0FBQyxJQUFHLGtCQUFxQyxPQUFyQkQsVUFBVUMsT0FBTyxDQUFDO2dCQUNsSjtZQUNGO1FBQ0Y7UUFFQSxPQUFPO1lBQUVOLFlBQVk7WUFBT0MsU0FBUztRQUF1QztJQUM5RTtJQUVBLE1BQU1NLGFBQWE7UUFDakIsTUFBTWYsUUFBUTNDLFdBQVc0QyxLQUFLLENBQUMsTUFBTUksR0FBRyxDQUFDRixDQUFBQSxPQUFRQSxLQUFLQyxJQUFJLElBQUlGLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsU0FBUztRQUN0RixNQUFNM0IsU0FBU3dCLE1BQU1LLEdBQUcsQ0FBQ0YsQ0FBQUEsT0FBUTVCLFdBQVc0QixPQUFPRCxNQUFNLENBQUN0QixDQUFBQSxRQUFTLENBQUMyQixNQUFNM0IsVUFBVUEsUUFBUTtRQUU1RixJQUFJSixPQUFPVyxNQUFNLEtBQUssS0FBS2EsTUFBTWIsTUFBTSxHQUFHLEdBQUc7WUFDM0MzQixRQUFRRCxLQUFLLENBQUM7WUFDZDtRQUNGO1FBRUEsOEJBQThCO1FBQzlCLE1BQU15RCxhQUFhakI7UUFDbkIsSUFBSWlCLFdBQVdSLFVBQVUsRUFBRTtZQUN6QmhELFFBQVFELEtBQUssQ0FBQywwQkFBNkMsT0FBbkJ5RCxXQUFXUCxPQUFPO1lBQzFEO1FBQ0Y7UUFFQXhELGtCQUFrQnVCO1FBQ2xCaEIsUUFBUXlELEdBQUcsQ0FBQywwQkFBd0MsT0FBZHpDLE9BQU9XLE1BQU0sRUFBQztRQUNwRDdCLGNBQWMsS0FBSywyQkFBMkI7UUFDOUNOO0lBQ0Y7SUFFQSxNQUFNZ0UsYUFBYWpCO0lBRW5CLHFCQUNFLDhEQUFDckUseURBQU1BO1FBQUN3RixNQUFNbkU7UUFBUW9FLGNBQWNuRTtrQkFDbEMsNEVBQUNyQixnRUFBYUE7WUFBQ3lGLFdBQVU7OzhCQUN2Qiw4REFBQ3hGLCtEQUFZQTs7c0NBQ1gsOERBQUNDLDhEQUFXQTs0QkFBQ3VGLFdBQVU7c0NBQWU7Ozs7OztzQ0FDdEMsOERBQUN0RixvRUFBaUJBO3NDQUFDOzs7Ozs7Ozs7Ozs7OEJBS3JCLDhEQUFDTSxxREFBSUE7b0JBQUNpRixPQUFPbEU7b0JBQVdtRSxlQUFlbEU7b0JBQWNnRSxXQUFVOztzQ0FDN0QsOERBQUM5RSx5REFBUUE7NEJBQUM4RSxXQUFVOzs4Q0FDbEIsOERBQUM3RSw0REFBV0E7b0NBQUM4RSxPQUFNOzhDQUFTOzs7Ozs7OENBQzVCLDhEQUFDOUUsNERBQVdBO29DQUFDOEUsT0FBTTs4Q0FBWTs7Ozs7Ozs7Ozs7O3NDQUdqQyw4REFBQ2hGLDREQUFXQTs0QkFBQ2dGLE9BQU07NEJBQVNELFdBQVU7c0NBQ3BDLDRFQUFDRztnQ0FBSUgsV0FBVTs7a0RBQ2IsOERBQUNqRix1REFBS0E7d0NBQUNxRixTQUFRO3dDQUFzQkosV0FBVTtrREFBWTs7Ozs7O2tEQUczRCw4REFBQ2Q7d0NBQUVjLFdBQVU7a0RBQWdDOzs7Ozs7a0RBRzdDLDhEQUFDbEYsNkRBQVFBO3dDQUNQdUYsSUFBRzt3Q0FDSEosT0FBT2hFO3dDQUNQcUUsVUFBVSxDQUFDQyxJQUFNckUsY0FBY3FFLEVBQUVDLE1BQU0sQ0FBQ1AsS0FBSzt3Q0FDN0NRLGFBQVk7d0NBQ1pULFdBQVU7Ozs7OztvQ0FFWEosV0FBV1AsT0FBTyxrQkFDakIsOERBQUNIO3dDQUFFYyxXQUFXLFdBQXFFLE9BQTFESixXQUFXUixVQUFVLEdBQUcsaUJBQWlCO2tEQUMvRFEsV0FBV1AsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTNCLDhEQUFDcEUsNERBQVdBOzRCQUFDZ0YsT0FBTTs0QkFBWUQsV0FBVTs7OENBQ3ZDLDhEQUFDRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNHOzs4REFDQyw4REFBQ3BGLHVEQUFLQTtvREFBQ3FGLFNBQVE7OERBQWM7Ozs7Ozs4REFDN0IsOERBQUNoRix5REFBTUE7b0RBQUM2RSxPQUFPM0Q7b0RBQWE0RCxlQUFlM0Q7O3NFQUN6Qyw4REFBQ2hCLGdFQUFhQTtzRUFDWiw0RUFBQ0MsOERBQVdBOzs7Ozs7Ozs7O3NFQUVkLDhEQUFDSCxnRUFBYUE7c0VBQ1g7Z0VBQUM7Z0VBQUc7Z0VBQUc7Z0VBQUc7Z0VBQUk7Z0VBQUk7Z0VBQUk7NkRBQUcsQ0FBQzRELEdBQUcsQ0FBQ3lCLENBQUFBLG9CQUM3Qiw4REFBQ3BGLDZEQUFVQTtvRUFBVzJFLE9BQU9TLElBQUlDLFFBQVE7O3dFQUFLRDt3RUFBSTs7bUVBQWpDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFNekIsOERBQUNQOzs4REFDQyw4REFBQ3BGLHVEQUFLQTtvREFBQ3FGLFNBQVE7OERBQWE7Ozs7Ozs4REFDNUIsOERBQUNoRix5REFBTUE7b0RBQUM2RSxPQUFPekQ7b0RBQVkwRCxlQUFlekQ7O3NFQUN4Qyw4REFBQ2xCLGdFQUFhQTtzRUFDWiw0RUFBQ0MsOERBQVdBOzs7Ozs7Ozs7O3NFQUVkLDhEQUFDSCxnRUFBYUE7c0VBQ1g7Z0VBQUM7Z0VBQUc7Z0VBQUs7Z0VBQUc7Z0VBQUs7Z0VBQUc7Z0VBQUs7Z0VBQUc7Z0VBQUc7Z0VBQUc7Z0VBQUc7NkRBQUcsQ0FBQzRELEdBQUcsQ0FBQy9CLENBQUFBLHNCQUM1Qyw4REFBQzVCLDZEQUFVQTtvRUFBYTJFLE9BQU8vQyxNQUFNeUQsUUFBUTs7d0VBQUk7d0VBQUV6RDt3RUFBTTs7bUVBQXhDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPM0IsOERBQUNpRDs7c0RBQ0MsOERBQUNwRix1REFBS0E7NENBQUNxRixTQUFRO3NEQUFlOzs7Ozs7c0RBQzlCLDhEQUFDaEYseURBQU1BOzRDQUFDNkUsT0FBT3ZEOzRDQUFjd0QsZUFBZXZEOzs4REFDMUMsOERBQUNwQixnRUFBYUE7OERBQ1osNEVBQUNDLDhEQUFXQTs7Ozs7Ozs7Ozs4REFFZCw4REFBQ0gsZ0VBQWFBOztzRUFDWiw4REFBQ0MsNkRBQVVBOzREQUFDMkUsT0FBTTtzRUFBTzs7Ozs7O3NFQUN6Qiw4REFBQzNFLDZEQUFVQTs0REFBQzJFLE9BQU07c0VBQVk7Ozs7OztzRUFDOUIsOERBQUMzRSw2REFBVUE7NERBQUMyRSxPQUFNO3NFQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3RDLDhEQUFDRTtvQ0FBSUgsV0FBVTs4Q0FDYiw0RUFBQ2Q7d0NBQUVjLFdBQVU7OzBEQUNYLDhEQUFDWTswREFBTzs7Ozs7OzRDQUE4Qjs0Q0FBR2hFLG1CQUFtQmlFLGNBQWM7MERBQUcsOERBQUNDOzs7OzswREFDOUUsOERBQUNGOzBEQUFPOzs7Ozs7NENBQWtCOzRDQUFHL0Q7NENBQWdCOzRDQUFNRCxDQUFBQSxxQkFBcUJDLGtCQUFrQixHQUFFLEVBQUc2QyxPQUFPLENBQUM7NENBQUc7MERBQUMsOERBQUNvQjs7Ozs7MERBQzVHLDhEQUFDRjswREFBTzs7Ozs7OzRDQUFlOzRDQUFJaEUsQ0FBQUEscUJBQXNCLEtBQUlPLFdBQVdYLGNBQWMsR0FBRSxDQUFDLEVBQUdxRSxjQUFjOzRDQUFHOzRDQUFNakUsQ0FBQUEscUJBQXNCLEtBQUlPLFdBQVdYLGNBQWMsR0FBRSxDQUFDLEVBQUdxRSxjQUFjOzs7Ozs7Ozs7Ozs7OENBSXRMLDhEQUFDaEcseURBQU1BO29DQUFDa0csU0FBU3ZDO29DQUFvQndCLFdBQVU7O3dDQUFpQjt3Q0FDcEQxRDt3Q0FBWTs7Ozs7Ozs4Q0FHeEIsOERBQUM2RDtvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNqRix1REFBS0E7c0RBQUM7Ozs7OztzREFDUCw4REFBQ0QsNkRBQVFBOzRDQUNQbUYsT0FBT2hFOzRDQUNQcUUsVUFBVSxDQUFDQyxJQUFNckUsY0FBY3FFLEVBQUVDLE1BQU0sQ0FBQ1AsS0FBSzs0Q0FDN0NELFdBQVU7NENBQ1ZTLGFBQVk7Ozs7Ozt3Q0FFYmIsV0FBV1AsT0FBTyxrQkFDakIsOERBQUNIOzRDQUFFYyxXQUFXLFdBQXFFLE9BQTFESixXQUFXUixVQUFVLEdBQUcsaUJBQWlCO3NEQUMvRFEsV0FBV1AsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU83Qiw4REFBQzFFLCtEQUFZQTs7c0NBQ1gsOERBQUNDLDhEQUFXQTs0QkFBQ29HLE9BQU87c0NBQ2xCLDRFQUFDbkcseURBQU1BO2dDQUFDb0csTUFBSztnQ0FBU0MsU0FBUTtnQ0FBVWxCLFdBQVU7MENBQWtCOzs7Ozs7Ozs7OztzQ0FFdEUsOERBQUNuRix5REFBTUE7NEJBQUNvRyxNQUFLOzRCQUFTRixTQUFTcEI7NEJBQVl3QixVQUFVdkIsV0FBV1IsVUFBVTs0QkFBRVksV0FBVTtzQ0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPMUc7R0FsUWdCdEU7S0FBQUEiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXG1vZGFsc1xcVGFyZ2V0UHJpY2VNb2RhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIERpYWxvZyxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nRGVzY3JpcHRpb24sXG4gIERpYWxvZ0Zvb3RlcixcbiAgRGlhbG9nQ2xvc2UsXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCI7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RleHRhcmVhXCI7XG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCc7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCc7XG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RhYnMnO1xuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zZWxlY3QnO1xuXG5pbXBvcnQgeyB1c2VUcmFkaW5nQ29udGV4dCB9IGZyb20gJ0AvY29udGV4dHMvVHJhZGluZ0NvbnRleHQnO1xuXG5pbnRlcmZhY2UgVGFyZ2V0UHJpY2VNb2RhbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBvblNldFRhcmdldFByaWNlczogKHByaWNlczogbnVtYmVyW10pID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBUYXJnZXRQcmljZU1vZGFsKHsgaXNPcGVuLCBvbkNsb3NlLCBvblNldFRhcmdldFByaWNlcyB9OiBUYXJnZXRQcmljZU1vZGFsUHJvcHMpIHtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdtYW51YWwnKTtcbiAgY29uc3QgW3ByaWNlSW5wdXQsIHNldFByaWNlSW5wdXRdID0gdXNlU3RhdGUoJycpO1xuXG5cbiAgLy8gU2FmZWx5IGdldCB0cmFkaW5nIGNvbnRleHQgd2l0aCBmYWxsYmFja1xuICBsZXQgdHJhZGluZ0NvbnRleHQ7XG4gIHRyeSB7XG4gICAgdHJhZGluZ0NvbnRleHQgPSB1c2VUcmFkaW5nQ29udGV4dCgpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUud2FybignVHJhZGluZyBjb250ZXh0IG5vdCBhdmFpbGFibGU6JywgZXJyb3IpO1xuICAgIHRyYWRpbmdDb250ZXh0ID0gbnVsbDtcbiAgfVxuXG4gIC8vIEF1dG8gZ2VuZXJhdGlvbiBzZXR0aW5nc1xuICBjb25zdCBbdGFyZ2V0Q291bnQsIHNldFRhcmdldENvdW50XSA9IHVzZVN0YXRlKCc4Jyk7XG4gIGNvbnN0IFtwcmljZVJhbmdlLCBzZXRQcmljZVJhbmdlXSA9IHVzZVN0YXRlKCc1Jyk7IC8vIHBlcmNlbnRhZ2UgcmFuZ2UgLSBzdGFydCB3aXRoIDUlIGFzIGRlZmF1bHRcbiAgY29uc3QgW2Rpc3RyaWJ1dGlvbiwgc2V0RGlzdHJpYnV0aW9uXSA9IHVzZVN0YXRlKCdldmVuJyk7IC8vIGV2ZW4sIGZpYm9uYWNjaSwgZXhwb25lbnRpYWxcblxuICAvLyBHZXQgY3VycmVudCBtYXJrZXQgcHJpY2UgYW5kIHNsaXBwYWdlIGZyb20gY29udGV4dCB3aXRoIHNhZmUgZmFsbGJhY2tzXG4gIGNvbnN0IGN1cnJlbnRNYXJrZXRQcmljZSA9IHRyYWRpbmdDb250ZXh0Py5jdXJyZW50TWFya2V0UHJpY2UgfHwgMTAwMDAwO1xuICBjb25zdCBzbGlwcGFnZVBlcmNlbnQgPSB0cmFkaW5nQ29udGV4dD8uY29uZmlnPy5zbGlwcGFnZVBlcmNlbnQgfHwgMC4yO1xuXG4gIGNvbnN0IGdlbmVyYXRlQXV0b21hdGljUHJpY2VzID0gKCkgPT4ge1xuICAgIGNvbnN0IGNvdW50ID0gcGFyc2VJbnQodGFyZ2V0Q291bnQpO1xuICAgIGNvbnN0IHJhbmdlID0gcGFyc2VGbG9hdChwcmljZVJhbmdlKTtcblxuICAgIGlmICghY291bnQgfHwgY291bnQgPCAyIHx8IGNvdW50ID4gMjAgfHwgIXJhbmdlIHx8IHJhbmdlIDw9IDApIHtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG5cbiAgICBjb25zdCBwcmljZXM6IG51bWJlcltdID0gW107XG4gICAgY29uc3QgbWluUHJpY2UgPSBjdXJyZW50TWFya2V0UHJpY2UgKiAoMSAtIHJhbmdlIC8gMTAwKTtcbiAgICBjb25zdCBtYXhQcmljZSA9IGN1cnJlbnRNYXJrZXRQcmljZSAqICgxICsgcmFuZ2UgLyAxMDApO1xuXG4gICAgaWYgKGRpc3RyaWJ1dGlvbiA9PT0gJ2V2ZW4nKSB7XG4gICAgICAvLyBFdmVuIGRpc3RyaWJ1dGlvblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHByaWNlID0gbWluUHJpY2UgKyAobWF4UHJpY2UgLSBtaW5QcmljZSkgKiAoaSAvIChjb3VudCAtIDEpKTtcbiAgICAgICAgcHJpY2VzLnB1c2goTWF0aC5yb3VuZChwcmljZSkpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoZGlzdHJpYnV0aW9uID09PSAnZmlib25hY2NpJykge1xuICAgICAgLy8gRmlib25hY2NpLWxpa2UgZGlzdHJpYnV0aW9uIChtb3JlIHRhcmdldHMgbmVhciBjdXJyZW50IHByaWNlKVxuICAgICAgY29uc3QgZmliUmF0aW9zID0gWzAsIDAuMjM2LCAwLjM4MiwgMC41LCAwLjYxOCwgMC43NjQsIDAuODU0LCAwLjkyNywgMV07XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNvdW50OyBpKyspIHtcbiAgICAgICAgY29uc3QgcmF0aW8gPSBmaWJSYXRpb3NbTWF0aC5taW4oaSwgZmliUmF0aW9zLmxlbmd0aCAtIDEpXSB8fCAoaSAvIChjb3VudCAtIDEpKTtcbiAgICAgICAgY29uc3QgcHJpY2UgPSBtaW5QcmljZSArIChtYXhQcmljZSAtIG1pblByaWNlKSAqIHJhdGlvO1xuICAgICAgICBwcmljZXMucHVzaChNYXRoLnJvdW5kKHByaWNlKSk7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChkaXN0cmlidXRpb24gPT09ICdleHBvbmVudGlhbCcpIHtcbiAgICAgIC8vIEV4cG9uZW50aWFsIGRpc3RyaWJ1dGlvblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHJhdGlvID0gTWF0aC5wb3coaSAvIChjb3VudCAtIDEpLCAxLjUpO1xuICAgICAgICBjb25zdCBwcmljZSA9IG1pblByaWNlICsgKG1heFByaWNlIC0gbWluUHJpY2UpICogcmF0aW87XG4gICAgICAgIHByaWNlcy5wdXNoKE1hdGgucm91bmQocHJpY2UpKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBFbnN1cmUgbm8gb3ZlcmxhcCB3aXRoIHNsaXBwYWdlIHpvbmVzXG4gICAgY29uc3QgbWluR2FwID0gY3VycmVudE1hcmtldFByaWNlICogKHNsaXBwYWdlUGVyY2VudCAqIDMgLyAxMDApOyAvLyAzeCBzbGlwcGFnZSBhcyBtaW5pbXVtIGdhcFxuICAgIGNvbnN0IHNvcnRlZFByaWNlcyA9IHByaWNlcy5zb3J0KChhLCBiKSA9PiBhIC0gYik7XG4gICAgY29uc3QgYWRqdXN0ZWRQcmljZXM6IG51bWJlcltdID0gW107XG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNvcnRlZFByaWNlcy5sZW5ndGg7IGkrKykge1xuICAgICAgbGV0IHByaWNlID0gc29ydGVkUHJpY2VzW2ldO1xuXG4gICAgICAvLyBFbnN1cmUgbWluaW11bSBnYXAgZnJvbSBwcmV2aW91cyBwcmljZVxuICAgICAgaWYgKGFkanVzdGVkUHJpY2VzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3QgbGFzdFByaWNlID0gYWRqdXN0ZWRQcmljZXNbYWRqdXN0ZWRQcmljZXMubGVuZ3RoIC0gMV07XG4gICAgICAgIGlmIChwcmljZSAtIGxhc3RQcmljZSA8IG1pbkdhcCkge1xuICAgICAgICAgIHByaWNlID0gbGFzdFByaWNlICsgbWluR2FwO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGFkanVzdGVkUHJpY2VzLnB1c2goTWF0aC5yb3VuZChwcmljZSkpO1xuICAgIH1cblxuICAgIHJldHVybiBhZGp1c3RlZFByaWNlcztcbiAgfTtcblxuICBjb25zdCBoYW5kbGVBdXRvR2VuZXJhdGUgPSAoKSA9PiB7XG4gICAgY29uc3QgZ2VuZXJhdGVkUHJpY2VzID0gZ2VuZXJhdGVBdXRvbWF0aWNQcmljZXMoKTtcbiAgICBzZXRQcmljZUlucHV0KGdlbmVyYXRlZFByaWNlcy5qb2luKCdcXG4nKSk7XG4gIH07XG5cbiAgY29uc3QgdmFsaWRhdGVTbGlwcGFnZU92ZXJsYXAgPSAoKSA9PiB7XG4gICAgY29uc3QgbGluZXMgPSBwcmljZUlucHV0LnNwbGl0KCdcXG4nKS5maWx0ZXIobGluZSA9PiBsaW5lLnRyaW0oKSAhPT0gJycpO1xuICAgIGNvbnN0IHByaWNlcyA9IGxpbmVzLm1hcChsaW5lID0+IHBhcnNlRmxvYXQobGluZS50cmltKCkpKS5maWx0ZXIocCA9PiAhaXNOYU4ocCkgJiYgcCA+IDApLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcblxuICAgIGlmIChwcmljZXMubGVuZ3RoIDwgMikgcmV0dXJuIHsgaGFzT3ZlcmxhcDogZmFsc2UsIG1lc3NhZ2U6ICcnIH07XG5cbiAgICBjb25zdCBzbGlwcGFnZUFtb3VudCA9IGN1cnJlbnRNYXJrZXRQcmljZSAqIChzbGlwcGFnZVBlcmNlbnQgLyAxMDApO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwcmljZXMubGVuZ3RoIC0gMTsgaSsrKSB7XG4gICAgICBjb25zdCBjdXJyZW50TWF4ID0gcHJpY2VzW2ldICsgc2xpcHBhZ2VBbW91bnQ7XG4gICAgICBjb25zdCBuZXh0TWluID0gcHJpY2VzW2kgKyAxXSAtIHNsaXBwYWdlQW1vdW50O1xuXG4gICAgICBpZiAoY3VycmVudE1heCA+PSBuZXh0TWluKSB7XG4gICAgICAgIGNvbnN0IG1pbkdhcCA9IHNsaXBwYWdlQW1vdW50ICogMjtcbiAgICAgICAgY29uc3QgYWN0dWFsR2FwID0gcHJpY2VzW2kgKyAxXSAtIHByaWNlc1tpXTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBoYXNPdmVybGFwOiB0cnVlLFxuICAgICAgICAgIG1lc3NhZ2U6IGBPdmVybGFwIGRldGVjdGVkIGJldHdlZW4gJHtwcmljZXNbaV19IGFuZCAke3ByaWNlc1tpICsgMV19LiBNaW5pbXVtIGdhcCBuZWVkZWQ6ICR7bWluR2FwLnRvRml4ZWQoMCl9LCBhY3R1YWwgZ2FwOiAke2FjdHVhbEdhcC50b0ZpeGVkKDApfWBcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4geyBoYXNPdmVybGFwOiBmYWxzZSwgbWVzc2FnZTogJ05vIHNsaXBwYWdlIHpvbmUgb3ZlcmxhcHMgZGV0ZWN0ZWQg4pyTJyB9O1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSAoKSA9PiB7XG4gICAgY29uc3QgbGluZXMgPSBwcmljZUlucHV0LnNwbGl0KCdcXG4nKS5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkuZmlsdGVyKGxpbmUgPT4gbGluZSAhPT0gJycpO1xuICAgIGNvbnN0IHByaWNlcyA9IGxpbmVzLm1hcChsaW5lID0+IHBhcnNlRmxvYXQobGluZSkpLmZpbHRlcihwcmljZSA9PiAhaXNOYU4ocHJpY2UpICYmIHByaWNlID4gMCk7XG5cbiAgICBpZiAocHJpY2VzLmxlbmd0aCA9PT0gMCAmJiBsaW5lcy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiSW52YWxpZCBJbnB1dDogTm8gdmFsaWQgcHJpY2VzIGZvdW5kLiBQbGVhc2UgZW50ZXIgbnVtYmVycywgb25lIHBlciBsaW5lLlwiKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBmb3Igc2xpcHBhZ2Ugb3ZlcmxhcHNcbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVTbGlwcGFnZU92ZXJsYXAoKTtcbiAgICBpZiAodmFsaWRhdGlvbi5oYXNPdmVybGFwKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBTbGlwcGFnZSBab25lIE92ZXJsYXA6ICR7dmFsaWRhdGlvbi5tZXNzYWdlfWApO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIG9uU2V0VGFyZ2V0UHJpY2VzKHByaWNlcyk7XG4gICAgY29uc29sZS5sb2coYFRhcmdldCBQcmljZXMgVXBkYXRlZDogJHtwcmljZXMubGVuZ3RofSB0YXJnZXQgcHJpY2VzIGhhdmUgYmVlbiBzZXQuYCk7XG4gICAgc2V0UHJpY2VJbnB1dCgnJyk7IC8vIENsZWFyIGlucHV0IGFmdGVyIHNhdmluZ1xuICAgIG9uQ2xvc2UoKTtcbiAgfTtcblxuICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVTbGlwcGFnZU92ZXJsYXAoKTtcblxuICByZXR1cm4gKFxuICAgIDxEaWFsb2cgb3Blbj17aXNPcGVufSBvbk9wZW5DaGFuZ2U9e29uQ2xvc2V9PlxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwic206bWF4LXctMnhsIGJnLWNhcmQgYm9yZGVyLTIgYm9yZGVyLWJvcmRlclwiPlxuICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnlcIj5TZXQgVGFyZ2V0IFByaWNlczwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgU2V0IHRhcmdldCBwcmljZXMgbWFudWFsbHkgb3IgZ2VuZXJhdGUgdGhlbSBhdXRvbWF0aWNhbGx5IHdpdGggb3B0aW1hbCBzcGFjaW5nIHRvIGF2b2lkIHNsaXBwYWdlIHpvbmUgb3ZlcmxhcHMuXG4gICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgPFRhYnMgdmFsdWU9e2FjdGl2ZVRhYn0gb25WYWx1ZUNoYW5nZT17c2V0QWN0aXZlVGFifSBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cIm1hbnVhbFwiPk1hbnVhbCBFbnRyeTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJhdXRvbWF0aWNcIj5BdXRvbWF0aWMgR2VuZXJhdGlvbjwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cIm1hbnVhbFwiIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidGFyZ2V0LXByaWNlcy1pbnB1dFwiIGNsYXNzTmFtZT1cInRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgIFRhcmdldCBQcmljZXNcbiAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICBQYXN0ZSB0YXJnZXQgcHJpY2VzIGZyb20gRXhjZWwgb3IgZW50ZXIgbWFudWFsbHksIG9uZSBwcmljZSBwZXIgbGluZS4gSW52YWxpZCBlbnRyaWVzIHdpbGwgYmUgaWdub3JlZC5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICBpZD1cInRhcmdldC1wcmljZXMtaW5wdXRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtwcmljZUlucHV0fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJpY2VJbnB1dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCI1MDAwMCYjMTA7NTA1MDAmIzEwOzQ5ODAwXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtaW4taC1bMjAwcHhdIGJnLWlucHV0IGJvcmRlci0yIGJvcmRlci1ib3JkZXIgZm9jdXM6Ym9yZGVyLXByaW1hcnkgZm9udC1tb25vXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAge3ZhbGlkYXRpb24ubWVzc2FnZSAmJiAoXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3ZhbGlkYXRpb24uaGFzT3ZlcmxhcCA/ICd0ZXh0LXJlZC01MDAnIDogJ3RleHQtZ3JlZW4tNTAwJ31gfT5cbiAgICAgICAgICAgICAgICAgIHt2YWxpZGF0aW9uLm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImF1dG9tYXRpY1wiIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ0YXJnZXRDb3VudFwiPk51bWJlciBvZiBUYXJnZXRzPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXt0YXJnZXRDb3VudH0gb25WYWx1ZUNoYW5nZT17c2V0VGFyZ2V0Q291bnR9PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIHtbNCwgNiwgOCwgMTAsIDEyLCAxNSwgMjBdLm1hcChudW0gPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17bnVtfSB2YWx1ZT17bnVtLnRvU3RyaW5nKCl9PntudW19IHRhcmdldHM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicHJpY2VSYW5nZVwiPlByaWNlIFJhbmdlICglKTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17cHJpY2VSYW5nZX0gb25WYWx1ZUNoYW5nZT17c2V0UHJpY2VSYW5nZX0+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAge1syLCAyLjUsIDMsIDMuNSwgNCwgNC41LCA1LCA2LCA3LCA4LCAxMF0ubWFwKHJhbmdlID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSBrZXk9e3JhbmdlfSB2YWx1ZT17cmFuZ2UudG9TdHJpbmcoKX0+wrF7cmFuZ2V9JTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGlzdHJpYnV0aW9uXCI+RGlzdHJpYnV0aW9uIFBhdHRlcm48L0xhYmVsPlxuICAgICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtkaXN0cmlidXRpb259IG9uVmFsdWVDaGFuZ2U9e3NldERpc3RyaWJ1dGlvbn0+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgLz5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImV2ZW5cIj5FdmVuIERpc3RyaWJ1dGlvbjwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiZmlib25hY2NpXCI+Rmlib25hY2NpIChNb3JlIG5lYXIgY3VycmVudCBwcmljZSk8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImV4cG9uZW50aWFsXCI+RXhwb25lbnRpYWwgKFdpZGVyIHNwcmVhZCk8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW11dGVkIHAtMyByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8c3Ryb25nPkN1cnJlbnQgTWFya2V0IFByaWNlOjwvc3Ryb25nPiAke2N1cnJlbnRNYXJrZXRQcmljZS50b0xvY2FsZVN0cmluZygpfTxici8+XG4gICAgICAgICAgICAgICAgPHN0cm9uZz5TbGlwcGFnZTo8L3N0cm9uZz4gwrF7c2xpcHBhZ2VQZXJjZW50fSUgKCR7KGN1cnJlbnRNYXJrZXRQcmljZSAqIHNsaXBwYWdlUGVyY2VudCAvIDEwMCkudG9GaXhlZCgwKX0pPGJyLz5cbiAgICAgICAgICAgICAgICA8c3Ryb25nPlJhbmdlOjwvc3Ryb25nPiAkeyhjdXJyZW50TWFya2V0UHJpY2UgKiAoMSAtIHBhcnNlRmxvYXQocHJpY2VSYW5nZSkgLyAxMDApKS50b0xvY2FsZVN0cmluZygpfSAtICR7KGN1cnJlbnRNYXJrZXRQcmljZSAqICgxICsgcGFyc2VGbG9hdChwcmljZVJhbmdlKSAvIDEwMCkpLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUF1dG9HZW5lcmF0ZX0gY2xhc3NOYW1lPVwidy1mdWxsIGJ0bi1uZW9cIj5cbiAgICAgICAgICAgICAgR2VuZXJhdGUge3RhcmdldENvdW50fSBUYXJnZXQgUHJpY2VzXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxMYWJlbD5HZW5lcmF0ZWQgUHJpY2VzIChQcmV2aWV3KTwvTGFiZWw+XG4gICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgIHZhbHVlPXtwcmljZUlucHV0fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJpY2VJbnB1dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWluLWgtWzE1MHB4XSBiZy1pbnB1dCBib3JkZXItMiBib3JkZXItYm9yZGVyIGZvY3VzOmJvcmRlci1wcmltYXJ5IGZvbnQtbW9ub1wiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDbGljayAnR2VuZXJhdGUnIHRvIGNyZWF0ZSBhdXRvbWF0aWMgdGFyZ2V0IHByaWNlcy4uLlwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIHt2YWxpZGF0aW9uLm1lc3NhZ2UgJiYgKFxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gJHt2YWxpZGF0aW9uLmhhc092ZXJsYXAgPyAndGV4dC1yZWQtNTAwJyA6ICd0ZXh0LWdyZWVuLTUwMCd9YH0+XG4gICAgICAgICAgICAgICAgICB7dmFsaWRhdGlvbi5tZXNzYWdlfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgIDwvVGFicz5cblxuICAgICAgICA8RGlhbG9nRm9vdGVyPlxuICAgICAgICAgIDxEaWFsb2dDbG9zZSBhc0NoaWxkPlxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJidG4tb3V0bGluZS1uZW9cIj5DYW5jZWw8L0J1dHRvbj5cbiAgICAgICAgICA8L0RpYWxvZ0Nsb3NlPlxuICAgICAgICAgIDxCdXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9e2hhbmRsZVNhdmV9IGRpc2FibGVkPXt2YWxpZGF0aW9uLmhhc092ZXJsYXB9IGNsYXNzTmFtZT1cImJ0bi1uZW9cIj5cbiAgICAgICAgICAgIFNhdmUgUHJpY2VzXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvRGlhbG9nRm9vdGVyPlxuICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgIDwvRGlhbG9nPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0Zvb3RlciIsIkRpYWxvZ0Nsb3NlIiwiQnV0dG9uIiwiVGV4dGFyZWEiLCJMYWJlbCIsIlRhYnMiLCJUYWJzQ29udGVudCIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsInVzZVRyYWRpbmdDb250ZXh0IiwiVGFyZ2V0UHJpY2VNb2RhbCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblNldFRhcmdldFByaWNlcyIsInRyYWRpbmdDb250ZXh0IiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwicHJpY2VJbnB1dCIsInNldFByaWNlSW5wdXQiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwidGFyZ2V0Q291bnQiLCJzZXRUYXJnZXRDb3VudCIsInByaWNlUmFuZ2UiLCJzZXRQcmljZVJhbmdlIiwiZGlzdHJpYnV0aW9uIiwic2V0RGlzdHJpYnV0aW9uIiwiY3VycmVudE1hcmtldFByaWNlIiwic2xpcHBhZ2VQZXJjZW50IiwiY29uZmlnIiwiZ2VuZXJhdGVBdXRvbWF0aWNQcmljZXMiLCJjb3VudCIsInBhcnNlSW50IiwicmFuZ2UiLCJwYXJzZUZsb2F0IiwicHJpY2VzIiwibWluUHJpY2UiLCJtYXhQcmljZSIsImkiLCJwcmljZSIsInB1c2giLCJNYXRoIiwicm91bmQiLCJmaWJSYXRpb3MiLCJyYXRpbyIsIm1pbiIsImxlbmd0aCIsInBvdyIsIm1pbkdhcCIsInNvcnRlZFByaWNlcyIsInNvcnQiLCJhIiwiYiIsImFkanVzdGVkUHJpY2VzIiwibGFzdFByaWNlIiwiaGFuZGxlQXV0b0dlbmVyYXRlIiwiZ2VuZXJhdGVkUHJpY2VzIiwiam9pbiIsInZhbGlkYXRlU2xpcHBhZ2VPdmVybGFwIiwibGluZXMiLCJzcGxpdCIsImZpbHRlciIsImxpbmUiLCJ0cmltIiwibWFwIiwicCIsImlzTmFOIiwiaGFzT3ZlcmxhcCIsIm1lc3NhZ2UiLCJzbGlwcGFnZUFtb3VudCIsImN1cnJlbnRNYXgiLCJuZXh0TWluIiwiYWN0dWFsR2FwIiwidG9GaXhlZCIsImhhbmRsZVNhdmUiLCJ2YWxpZGF0aW9uIiwibG9nIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsImNsYXNzTmFtZSIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsImRpdiIsImh0bWxGb3IiLCJpZCIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwibnVtIiwidG9TdHJpbmciLCJzdHJvbmciLCJ0b0xvY2FsZVN0cmluZyIsImJyIiwib25DbGljayIsImFzQ2hpbGQiLCJ0eXBlIiwidmFyaWFudCIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/backend-status.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/backend-status.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackendStatus: () => (/* binding */ BackendStatus),\n/* harmony export */   NetworkStatus: () => (/* binding */ NetworkStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ NetworkStatus,BackendStatus auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction NetworkStatus(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Default to true for SSR\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NetworkStatus.useEffect\": ()=>{\n            // Set client flag and initialize navigator state\n            setIsClient(true);\n            setIsOnline(navigator.onLine);\n            const handleOnline = {\n                \"NetworkStatus.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"NetworkStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"NetworkStatus.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"NetworkStatus.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            // Update time every second\n            const timeInterval = setInterval({\n                \"NetworkStatus.useEffect.timeInterval\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"NetworkStatus.useEffect.timeInterval\"], 1000);\n            return ({\n                \"NetworkStatus.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    clearInterval(timeInterval);\n                }\n            })[\"NetworkStatus.useEffect\"];\n        }\n    }[\"NetworkStatus.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: isOnline ? \"default\" : \"destructive\",\n                className: \"flex items-center gap-1 \".concat(isOnline ? \"bg-green-600 hover:bg-green-600/90 text-white\" : \"\"),\n                children: [\n                    isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 52\n                    }, this),\n                    isOnline ? \"Online\" : \"Offline\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: currentTime.toLocaleTimeString()\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(NetworkStatus, \"bOlrrEPNLpXmDG5nXPPQBoRWWoA=\");\n_c = NetworkStatus;\nfunction BackendStatus(param) {\n    let { className = \"\", showDetails = false } = param;\n    _s1();\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkBackendStatus = async ()=>{\n        setIsChecking(true);\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 2000);\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            setIsOnline(response.status < 500);\n            setLastCheck(new Date());\n        } catch (error) {\n            setIsOnline(false);\n            setLastCheck(new Date());\n        } finally{\n            setIsChecking(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BackendStatus.useEffect\": ()=>{\n            checkBackendStatus();\n            // Check every 30 seconds\n            const interval = setInterval(checkBackendStatus, 30000);\n            return ({\n                \"BackendStatus.useEffect\": ()=>clearInterval(interval)\n            })[\"BackendStatus.useEffect\"];\n        }\n    }[\"BackendStatus.useEffect\"], []);\n    if (!showDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n            variant: isOnline ? \"default\" : \"destructive\",\n            className: \"flex items-center gap-1 \".concat(className),\n            children: [\n                isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 52\n                }, this),\n                isOnline ? \"Backend Online\" : \"Backend Offline\"\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"\".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Backend Status: \",\n                                        isOnline ? \"Online\" : \"Offline\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: checkBackendStatus,\n                            disabled: isChecking,\n                            children: [\n                                isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                \"Check\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-muted-foreground mt-2\",\n                    children: [\n                        \"Last checked: \",\n                        lastCheck.toLocaleTimeString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                !isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium text-yellow-600 mb-1\",\n                                        children: \"Backend Offline\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-700\",\n                                        children: \"Some features may be limited. Session management will use local storage.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-yellow-600 mt-2\",\n                                        children: [\n                                            \"To start the backend: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-yellow-100 px-1 rounded\",\n                                                children: \"python run.py\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this),\n                isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 p-3 bg-green-500/10 border border-green-500/20 rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-700\",\n                                children: \"All features available. Backend connected successfully.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s1(BackendStatus, \"Hl8rfuJr7E5y7GxmtjkRpiA4cDc=\");\n_c1 = BackendStatus;\nvar _c, _c1;\n$RefreshReg$(_c, \"NetworkStatus\");\n$RefreshReg$(_c1, \"BackendStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/backend-status.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst badgeVariants = {\n    variant: {\n        default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\"\n    }\n};\nconst Badge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant = \"default\", ...props } = param;\n    const variantClasses = badgeVariants.variant[variant] || badgeVariants.variant.default;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", variantClasses, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 22,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Badge;\nBadge.displayName = \"Badge\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Badge$React.forwardRef\");\n$RefreshReg$(_c1, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/checkbox.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/checkbox.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Checkbox auto */ \n\n\n\nconst Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, checked, onCheckedChange, onChange, ...props } = param;\n    const handleChange = (event)=>{\n        const newChecked = event.target.checked;\n        onCheckedChange === null || onCheckedChange === void 0 ? void 0 : onCheckedChange(newChecked);\n        onChange === null || onChange === void 0 ? void 0 : onChange(event);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative inline-flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"checkbox\",\n                ref: ref,\n                className: \"sr-only\",\n                checked: checked,\n                onChange: handleChange,\n                ...props\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", checked && \"bg-primary text-primary-foreground\", className),\n                children: checked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center text-current\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\checkbox.tsx\",\n        lineNumber: 21,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Checkbox;\nCheckbox.displayName = \"Checkbox\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Checkbox$React.forwardRef\");\n$RefreshReg$(_c1, \"Checkbox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/checkbox.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/crypto-input.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/crypto-input.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CryptoInput: () => (/* binding */ CryptoInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CryptoInput auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CryptoInput(param) {\n    let { label, value, allowedCryptos, onValidCrypto, placeholder = \"Enter crypto symbol\", description, className } = param;\n    _s();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [validationState, setValidationState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [userValidated, setUserValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track if user has validated this crypto\n    // Sync inputValue with value prop when value changes from outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CryptoInput.useEffect\": ()=>{\n            if (value && value !== inputValue) {\n                setInputValue(value);\n                setValidationState('valid');\n                setUserValidated(true);\n            } else if (!value) {\n                setUserValidated(false);\n            }\n        }\n    }[\"CryptoInput.useEffect\"], [\n        value\n    ]);\n    const handleCheck = ()=>{\n        const upperCaseInput = inputValue.toUpperCase().trim();\n        if (!upperCaseInput) {\n            setValidationState('invalid');\n            setErrorMessage('Please enter a crypto symbol');\n            return;\n        }\n        if (!allowedCryptos || !Array.isArray(allowedCryptos)) {\n            setValidationState('invalid');\n            setErrorMessage('No allowed cryptocurrencies configured');\n            return;\n        }\n        if (allowedCryptos.includes(upperCaseInput)) {\n            setValidationState('valid');\n            setErrorMessage('');\n            setUserValidated(true); // Mark as user validated\n            onValidCrypto(upperCaseInput);\n        } else {\n            setValidationState('invalid');\n            setErrorMessage(\"\".concat(upperCaseInput, \" is not available. Allowed: \").concat(allowedCryptos.join(', ')));\n        }\n    };\n    const handleInputChange = (e)=>{\n        setInputValue(e.target.value);\n        setValidationState('idle');\n        setErrorMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter') {\n            handleCheck();\n        }\n    };\n    const getValidationIcon = ()=>{\n        switch(validationState){\n            case 'valid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            case 'invalid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getValidationColor = ()=>{\n        switch(validationState){\n            case 'valid':\n                return 'border-green-500';\n            case 'invalid':\n                return 'border-red-500';\n            default:\n                return '';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"space-y-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                htmlFor: \"crypto-input-\".concat(label),\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                id: \"crypto-input-\".concat(label),\n                                value: inputValue || value,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: placeholder,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"pr-8\", getValidationColor())\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            validationState !== 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                children: getValidationIcon()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleCheck,\n                        variant: \"outline\",\n                        className: \"btn-neo\",\n                        disabled: !inputValue.trim(),\n                        children: \"Check\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            value && userValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-green-600 font-medium\",\n                        children: [\n                            \"Selected: \",\n                            value\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this),\n            validationState === 'invalid' && errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-2 text-sm text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: errorMessage\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-muted-foreground\",\n                children: description\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-muted-foreground\",\n                children: [\n                    allowedCryptos && Array.isArray(allowedCryptos) ? allowedCryptos.length : 0,\n                    \" cryptocurrencies available\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(CryptoInput, \"qYdmLyPGBPsxTk9d08Ie1+WsdNo=\");\n_c = CryptoInput;\nvar _c;\n$RefreshReg$(_c, \"CryptoInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/crypto-input.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst DialogContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    open: false,\n    onOpenChange: ()=>{}\n});\nconst Dialog = (param)=>{\n    let { children, open = false, onOpenChange } = param;\n    _s();\n    const [internalOpen, setInternalOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(open);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Dialog.useEffect\": ()=>{\n            setInternalOpen(open);\n        }\n    }[\"Dialog.useEffect\"], [\n        open\n    ]);\n    const handleOpenChange = (newOpen)=>{\n        setInternalOpen(newOpen);\n        onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(newOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContext.Provider, {\n        value: {\n            open: internalOpen,\n            onOpenChange: handleOpenChange\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dialog, \"H7R/imfsAt8ZOKR9FmCf+hkSf6o=\");\n_c = Dialog;\nconst DialogTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c1 = _s1((param, ref)=>{\n    let { className, children, asChild = false, ...props } = param;\n    _s1();\n    const { onOpenChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(DialogContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: className,\n        onClick: ()=>onOpenChange(true),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 63,\n        columnNumber: 7\n    }, undefined);\n}, \"ntPtLpKBO4sLQI001ClAIvJHiws=\")), \"ntPtLpKBO4sLQI001ClAIvJHiws=\");\n_c2 = DialogTrigger;\nDialogTrigger.displayName = \"DialogTrigger\";\nconst DialogPortal = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_c3 = DialogPortal;\nconst DialogClose = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s2((param, ref)=>{\n    let { className, children, asChild = false, ...props } = param;\n    _s2();\n    const { onOpenChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(DialogContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: className,\n        onClick: ()=>onOpenChange(false),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 85,\n        columnNumber: 7\n    }, undefined);\n}, \"ntPtLpKBO4sLQI001ClAIvJHiws=\")), \"ntPtLpKBO4sLQI001ClAIvJHiws=\");\n_c5 = DialogClose;\nDialogClose.displayName = \"DialogClose\";\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef((param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n});\n_c6 = DialogOverlay;\nDialogOverlay.displayName = \"DialogOverlay\";\nconst DialogContent = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c7 = _s3((param, ref)=>{\n    let { className, children, ...props } = param;\n    _s3();\n    const { open, onOpenChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(DialogContext);\n    if (!open) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {\n                onClick: ()=>onOpenChange(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n                        onClick: ()=>onOpenChange(false),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 119,\n        columnNumber: 7\n    }, undefined);\n}, \"8AKSzg5XJRYR1b/tpX+fcz88l5I=\")), \"8AKSzg5XJRYR1b/tpX+fcz88l5I=\");\n_c8 = DialogContent;\nDialogContent.displayName = \"DialogContent\";\nconst DialogHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c9 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, undefined);\n});\n_c10 = DialogHeader;\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c11 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, undefined);\n});\n_c12 = DialogFooter;\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c13 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n});\n_c14 = DialogTitle;\nDialogTitle.displayName = \"DialogTitle\";\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c15 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n});\n_c16 = DialogDescription;\nDialogDescription.displayName = \"DialogDescription\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c1, \"DialogTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"DialogTrigger\");\n$RefreshReg$(_c3, \"DialogPortal\");\n$RefreshReg$(_c4, \"DialogClose$React.forwardRef\");\n$RefreshReg$(_c5, \"DialogClose\");\n$RefreshReg$(_c6, \"DialogOverlay\");\n$RefreshReg$(_c7, \"DialogContent$React.forwardRef\");\n$RefreshReg$(_c8, \"DialogContent\");\n$RefreshReg$(_c9, \"DialogHeader$React.forwardRef\");\n$RefreshReg$(_c10, \"DialogHeader\");\n$RefreshReg$(_c11, \"DialogFooter$React.forwardRef\");\n$RefreshReg$(_c12, \"DialogFooter\");\n$RefreshReg$(_c13, \"DialogTitle$React.forwardRef\");\n$RefreshReg$(_c14, \"DialogTitle\");\n$RefreshReg$(_c15, \"DialogDescription$React.forwardRef\");\n$RefreshReg$(_c16, \"DialogDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/select.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/select.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectTrigger,SelectContent,SelectItem,SelectValue auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n\n\n\nconst SelectContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    value: '',\n    onValueChange: ()=>{},\n    open: false,\n    setOpen: ()=>{}\n});\nconst Select = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { children, value, onValueChange, defaultValue, ...props } = param;\n    _s();\n    const [internalValue, setInternalValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(value || defaultValue || '');\n    const [open, setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Select.useEffect\": ()=>{\n            if (value !== undefined) {\n                setInternalValue(value);\n            }\n        }\n    }[\"Select.useEffect\"], [\n        value\n    ]);\n    const handleValueChange = (newValue)=>{\n        if (value === undefined) {\n            setInternalValue(newValue);\n        }\n        onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(newValue);\n        setOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectContext.Provider, {\n        value: {\n            value: internalValue,\n            onValueChange: handleValueChange,\n            open,\n            setOpen\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: \"relative\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 69,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 63,\n        columnNumber: 7\n    }, undefined);\n}, \"lSUTN4XRulCUNUFGOoILJ6/ZSw4=\")), \"lSUTN4XRulCUNUFGOoILJ6/ZSw4=\");\n_c1 = Select;\nSelect.displayName = \"Select\";\nconst SelectTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = _s1((param, ref)=>{\n    let { className, children, ...props } = param;\n    _s1();\n    const { open, setOpen } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        onClick: ()=>setOpen(!open),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4 opacity-50\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 83,\n        columnNumber: 7\n    }, undefined);\n}, \"VpbSSxC/M+z7dVARcY658GIy3+c=\")), \"VpbSSxC/M+z7dVARcY658GIy3+c=\");\n_c3 = SelectTrigger;\nSelectTrigger.displayName = \"SelectTrigger\";\nconst SelectContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s2((param, ref)=>{\n    let { className, children, ...props } = param;\n    _s2();\n    const { open } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    if (!open) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 107,\n        columnNumber: 7\n    }, undefined);\n}, \"wYP5SOVhXN0aIMoJb6iaO4GLNnk=\")), \"wYP5SOVhXN0aIMoJb6iaO4GLNnk=\");\n_c5 = SelectContent;\nSelectContent.displayName = \"SelectContent\";\nconst SelectItem = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = _s3((param, ref)=>{\n    let { className, children, value, ...props } = param;\n    _s3();\n    const { onValueChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\", className),\n        onClick: ()=>onValueChange(value),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 127,\n        columnNumber: 7\n    }, undefined);\n}, \"UXUFR3e0lgZn85KZNGvOuyNsyrg=\")), \"UXUFR3e0lgZn85KZNGvOuyNsyrg=\");\n_c7 = SelectItem;\nSelectItem.displayName = \"SelectItem\";\nconst SelectValue = /*#__PURE__*/ _s4(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = _s4((param, ref)=>{\n    let { placeholder, ...props } = param;\n    _s4();\n    const { value } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: ref,\n        ...props,\n        children: value || placeholder\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 148,\n        columnNumber: 7\n    }, undefined);\n}, \"n0+zAVIeEUubPncMMcj8hAd8Nyo=\")), \"n0+zAVIeEUubPncMMcj8hAd8Nyo=\");\n_c9 = SelectValue;\nSelectValue.displayName = \"SelectValue\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Select$React.forwardRef\");\n$RefreshReg$(_c1, \"Select\");\n$RefreshReg$(_c2, \"SelectTrigger$React.forwardRef\");\n$RefreshReg$(_c3, \"SelectTrigger\");\n$RefreshReg$(_c4, \"SelectContent$React.forwardRef\");\n$RefreshReg$(_c5, \"SelectContent\");\n$RefreshReg$(_c6, \"SelectItem$React.forwardRef\");\n$RefreshReg$(_c7, \"SelectItem\");\n$RefreshReg$(_c8, \"SelectValue$React.forwardRef\");\n$RefreshReg$(_c9, \"SelectValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/select.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, orientation = \"horizontal\", decorative = true, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: decorative ? \"none\" : \"separator\",\n        \"aria-orientation\": orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Separator;\nSeparator.displayName = \"Separator\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Separator$React.forwardRef\");\n$RefreshReg$(_c1, \"Separator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRThCO0FBQ0U7QUFPaEMsTUFBTUUsMEJBQVlGLDZDQUFnQixNQUNoQyxRQUVFSTtRQURBLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU87eUJBR3RFLDhEQUFDQztRQUNDTCxLQUFLQTtRQUNMTSxNQUFNSCxhQUFhLFNBQVM7UUFDNUJJLG9CQUFrQkw7UUFDbEJELFdBQVdKLDhDQUFFQSxDQUNYLHNCQUNBSyxnQkFBZ0IsZUFBZSxtQkFBbUIsa0JBQ2xERDtRQUVELEdBQUdHLEtBQUs7Ozs7Ozs7O0FBSWZOLFVBQVVVLFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcc2VwYXJhdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5pbnRlcmZhY2UgU2VwYXJhdG9yUHJvcHMgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xuICBvcmllbnRhdGlvbj86IFwiaG9yaXpvbnRhbFwiIHwgXCJ2ZXJ0aWNhbFwiO1xuICBkZWNvcmF0aXZlPzogYm9vbGVhbjtcbn1cblxuY29uc3QgU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgU2VwYXJhdG9yUHJvcHM+KFxuICAoXG4gICAgeyBjbGFzc05hbWUsIG9yaWVudGF0aW9uID0gXCJob3Jpem9udGFsXCIsIGRlY29yYXRpdmUgPSB0cnVlLCAuLi5wcm9wcyB9LFxuICAgIHJlZlxuICApID0+IChcbiAgICA8ZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIHJvbGU9e2RlY29yYXRpdmUgPyBcIm5vbmVcIiA6IFwic2VwYXJhdG9yXCJ9XG4gICAgICBhcmlhLW9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwic2hyaW5rLTAgYmctYm9yZGVyXCIsXG4gICAgICAgIG9yaWVudGF0aW9uID09PSBcImhvcml6b250YWxcIiA/IFwiaC1bMXB4XSB3LWZ1bGxcIiA6IFwiaC1mdWxsIHctWzFweF1cIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbik7XG5TZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBcIlNlcGFyYXRvclwiO1xuXG5leHBvcnQgeyBTZXBhcmF0b3IgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiU2VwYXJhdG9yIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsIm9yaWVudGF0aW9uIiwiZGVjb3JhdGl2ZSIsInByb3BzIiwiZGl2Iiwicm9sZSIsImFyaWEtb3JpZW50YXRpb24iLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/separator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst TabsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    activeTab: '',\n    setActiveTab: ()=>{}\n});\nconst Tabs = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { defaultValue = '', value, onValueChange, children, className, ...props } = param;\n    _s();\n    const [activeTab, setActiveTab] = react__WEBPACK_IMPORTED_MODULE_1__.useState(value || defaultValue);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Tabs.useEffect\": ()=>{\n            if (value !== undefined) {\n                setActiveTab(value);\n            }\n        }\n    }[\"Tabs.useEffect\"], [\n        value\n    ]);\n    const handleTabChange = (newValue)=>{\n        if (value === undefined) {\n            setActiveTab(newValue);\n        }\n        onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(newValue);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContext.Provider, {\n        value: {\n            activeTab,\n            setActiveTab: handleTabChange\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: className,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n            lineNumber: 59,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 58,\n        columnNumber: 7\n    }, undefined);\n}, \"k0FhWJTCExh/hFGuT1R1UiXiugA=\")), \"k0FhWJTCExh/hFGuT1R1UiXiugA=\");\n_c1 = Tabs;\nTabs.displayName = \"Tabs\";\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n});\n_c3 = TabsList;\nTabsList.displayName = \"TabsList\";\nconst TabsTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s1((param, ref)=>{\n    let { className, value, children, onClick, ...props } = param;\n    _s1();\n    const { activeTab, setActiveTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    const isActive = activeTab === value;\n    const handleClick = ()=>{\n        setActiveTab(value);\n        onClick === null || onClick === void 0 ? void 0 : onClick();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", isActive && \"bg-background text-foreground shadow-sm\", className),\n        onClick: handleClick,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 95,\n        columnNumber: 7\n    }, undefined);\n}, \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\")), \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\");\n_c5 = TabsTrigger;\nTabsTrigger.displayName = \"TabsTrigger\";\nconst TabsContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = _s2((param, ref)=>{\n    let { className, value, children, ...props } = param;\n    _s2();\n    const { activeTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    if (activeTab !== value) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 121,\n        columnNumber: 7\n    }, undefined);\n}, \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\")), \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\");\n_c7 = TabsContent;\nTabsContent.displayName = \"TabsContent\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Tabs$React.forwardRef\");\n$RefreshReg$(_c1, \"Tabs\");\n$RefreshReg$(_c2, \"TabsList$React.forwardRef\");\n$RefreshReg$(_c3, \"TabsList\");\n$RefreshReg$(_c4, \"TabsTrigger$React.forwardRef\");\n$RefreshReg$(_c5, \"TabsTrigger\");\n$RefreshReg$(_c6, \"TabsContent$React.forwardRef\");\n$RefreshReg$(_c7, \"TabsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tabs.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Textarea;\nTextarea.displayName = 'Textarea';\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Textarea$React.forwardRef\");\n$RefreshReg$(_c1, \"Textarea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0I7QUFFQTtBQUUvQixNQUFNRSx5QkFBV0YsNkNBQWdCLE1BQy9CLFFBQXdCSTtRQUF2QixFQUFDQyxTQUFTLEVBQUUsR0FBR0MsT0FBTTtJQUNwQixxQkFDRSw4REFBQ0M7UUFDQ0YsV0FBV0osOENBQUVBLENBQ1gscVRBQ0FJO1FBRUZELEtBQUtBO1FBQ0osR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7O0FBRUZKLFNBQVNNLFdBQVcsR0FBRztBQUVMIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcdGV4dGFyZWEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW1wb3J0IHtjbn0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5jb25zdCBUZXh0YXJlYSA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTFRleHRBcmVhRWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8J3RleHRhcmVhJz4+KFxuICAoe2NsYXNzTmFtZSwgLi4ucHJvcHN9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgJ2ZsZXggbWluLWgtWzgwcHhdIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtJyxcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblRleHRhcmVhLmRpc3BsYXlOYW1lID0gJ1RleHRhcmVhJztcblxuZXhwb3J0IHtUZXh0YXJlYX07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlRleHRhcmVhIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwidGV4dGFyZWEiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/textarea.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_c","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_f","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_det","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_h","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_n","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_compiled_react-","vendors-node_modules_next_dist_compiled_r","vendors-node_modules_next_dist_lib_c","vendors-node_modules_next_dist_p","vendors-node_modules_next_dist_shared_lib_h","vendors-node_modules_next_dist_shared_lib_m","vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23","vendors-node_modules_next_dist_shared_lib_ro","vendors-node_modules_next_dist_shared_lib_se","vendors-node_modules_n","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_s","default-_app-pages-browser_src_lib_session-manager_ts","default-_app-pages-browser_src_contexts_TradingContext_tsx","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","default-_app-pages-browser_src_components_shared_Logo_tsx-_app-pages-browser_src_components_u-94452f","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);