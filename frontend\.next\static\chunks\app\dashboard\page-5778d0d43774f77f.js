(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155),s=r(2115),o=r(9434);let n={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},l=s.forwardRef((e,t)=>{let{className:r,variant:s="default",size:l="default",asChild:c=!1,...i}=e,d=n.variant[s]||n.variant.default,u=n.size[l]||n.size.default;return(0,a.jsx)(c?"span":"button",{className:(0,o.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",d,u,r),ref:t,...i})});l.displayName="Button"},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155),s=r(2115),o=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"},3033:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(5155);r(2115);var s=r(7313),o=r(5695),n=r(1462),l=r(3638),c=r(908);let i=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,a.jsx)(n.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,a.jsx)(l.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,a.jsx)(c.A,{})}];function d(){let e=(0,o.useRouter)(),t=(0,o.usePathname)(),r="orders";return"/dashboard/history"===t?r="history":"/dashboard/analytics"===t&&(r="analytics"),(0,a.jsx)(s.tU,{value:r,onValueChange:t=>{let r=i.find(e=>e.value===t);r&&e.push(r.href)},className:"w-full mb-6",children:(0,a.jsx)(s.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:i.map(e=>(0,a.jsx)(s.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},4530:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var a=r(5155),s=r(2115),o=r(7213),n=r(6695),l=r(2523),c=r(285),i=r(518),d=r(5318),u=r(5222),p=r(6392),x=r(5219),m=r(8988);function f(){let{crypto1Balance:e,crypto2Balance:t,stablecoinBalance:r,config:f,dispatch:b}=(0,o.U)(),[y,g]=(0,s.useState)(null),[h,v]=(0,s.useState)({crypto1:e.toString(),crypto2:t.toString(),stablecoin:r.toString()}),N=e=>e.toFixed(f.numDigits),j=a=>{g(a),v({crypto1:e.toString(),crypto2:t.toString(),stablecoin:r.toString()})},w=r=>{let a=parseFloat(h[r]);!isNaN(a)&&a>=0&&("crypto1"===r?b({type:"UPDATE_BALANCES",payload:{crypto1:a,crypto2:t}}):"crypto2"===r?b({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:a}}):"stablecoin"===r&&b({type:"UPDATE_STABLECOIN_BALANCE",payload:a})),g(null)},C=()=>{g(null),v({crypto1:e.toString(),crypto2:t.toString(),stablecoin:r.toString()})},S=(e,t,r,s,o)=>(0,a.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),s]}),(0,a.jsx)(n.Wu,{children:y===r?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.p,{type:"number",value:h[r],onChange:e=>v(t=>({...t,[r]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(c.$,{size:"sm",onClick:()=>w(r),className:"flex-1",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,a.jsxs)(c.$,{size:"sm",variant:"outline",onClick:C,className:"flex-1",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground",children:N(t)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",o]})]}),(0,a.jsx)(c.$,{size:"sm",variant:"ghost",onClick:()=>j(r),className:"ml-2",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})]})})]});return(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3 mb-6",children:[S("".concat(f.crypto1||"Crypto 1"," Balance"),e,"crypto1",(0,a.jsx)(p.A,{className:"h-5 w-5 text-primary"}),f.crypto1||"Crypto 1"),S("".concat(f.crypto2||"Crypto 2"," Balance"),t,"crypto2",(0,a.jsx)(x.A,{className:"h-5 w-5 text-primary"}),f.crypto2||"Crypto 2"),S("Stablecoin Balance (".concat(f.preferredStablecoin||"N/A",")"),r,"stablecoin",(0,a.jsx)(m.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})}},5791:(e,t,r)=>{Promise.resolve().then(r.bind(r,9366))},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(5155),s=r(2115),o=r(9434);let n={variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},l=s.forwardRef((e,t)=>{let{className:r,variant:s="default",...l}=e,c=n.variant[s]||n.variant.default;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",c,r),...l})});l.displayName="Badge"},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>l});var a=r(5155),s=r(2115),o=r(9434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",r),...s})});n.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-4 md:p-6",r),...s})});l.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,o.cn)("text-xl font-semibold leading-none tracking-tight",r),...s})});c.displayName="CardTitle";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});i.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("p-4 md:p-6 pt-0",r),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-4 md:p-6 pt-0",r),...s})}).displayName="CardFooter"},7313:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>i,av:()=>d,j7:()=>c,tU:()=>l});var a=r(5155),s=r(2115),o=r(9434);let n=s.createContext({activeTab:"",setActiveTab:()=>{}}),l=s.forwardRef((e,t)=>{let{defaultValue:r="",value:o,onValueChange:l,children:c,className:i,...d}=e,[u,p]=s.useState(o||r);return s.useEffect(()=>{void 0!==o&&p(o)},[o]),(0,a.jsx)(n.Provider,{value:{activeTab:u,setActiveTab:e=>{void 0===o&&p(e),null==l||l(e)}},children:(0,a.jsx)("div",{ref:t,className:i,...d,children:c})})});l.displayName="Tabs";let c=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...n,children:s})});c.displayName="TabsList";let i=s.forwardRef((e,t)=>{let{className:r,value:l,children:c,onClick:i,...d}=e,{activeTab:u,setActiveTab:p}=s.useContext(n);return(0,a.jsx)("button",{ref:t,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",u===l&&"bg-background text-foreground shadow-sm",r),onClick:()=>{p(l),null==i||i()},...d,children:c})});i.displayName="TabsTrigger";let d=s.forwardRef((e,t)=>{let{className:r,value:l,children:c,...i}=e,{activeTab:d}=s.useContext(n);return d!==l?null:(0,a.jsx)("div",{ref:t,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...i,children:c})});d.displayName="TabsContent"},9366:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var a=r(5155),s=r(2115),o=r(6695),n=r(7213),l=r(4553),c=r(9434);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,c.cn)("w-full caption-bottom text-sm",r),...s})})});i.displayName="Table";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("thead",{ref:t,className:(0,c.cn)("[&_tr]:border-b",r),...s})});d.displayName="TableHeader";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,c.cn)("[&_tr:last-child]:border-0",r),...s})});u.displayName="TableBody",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,c.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tr",{ref:t,className:(0,c.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})});p.displayName="TableRow";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("th",{ref:t,className:(0,c.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})});x.displayName="TableHead";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("td",{ref:t,className:(0,c.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})});m.displayName="TableCell",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("caption",{ref:t,className:(0,c.cn)("mt-4 text-sm text-muted-foreground",r),...s})}).displayName="TableCaption";var f=r(6126);function b(){let{getDisplayOrders:e,config:t,currentMarketPrice:r}=(0,n.U)(),s=e(),o=function(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==e||isNaN(e))return"-";let a=e.toFixed(t.numDigits);return r&&e>0?"+".concat(a):a},l=e=>null==e||isNaN(e)?"-":"".concat(e.toFixed(2),"%"),b=[{key:"#",label:"#"},{key:"status",label:"Status"},{key:"orderLevel",label:"Level"},{key:"valueLevel",label:"Value"},{key:"crypto2Var",label:"".concat(t.crypto2||"Crypto 2"," Var.")},{key:"crypto1Var",label:"".concat(t.crypto1||"Crypto 1"," Var.")},{key:"targetPrice",label:"Target Price"},{key:"percentFromActualPrice",label:"% from Actual"},{key:"incomeCrypto1",label:"Income ".concat(t.crypto1||"Crypto 1")},{key:"incomeCrypto2",label:"Income ".concat(t.crypto2||"Crypto 2")},{key:"originalCostCrypto2",label:"Original Cost ".concat(t.crypto2||"Crypto 2")}];return(0,a.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,a.jsx)("div",{className:"w-full overflow-x-auto whitespace-nowrap",children:(0,a.jsxs)(i,{className:"min-w-full",children:[(0,a.jsx)(d,{children:(0,a.jsx)(p,{className:"bg-card hover:bg-card",children:b.map(e=>(0,a.jsx)(x,{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm",children:e.label},e.key))})}),(0,a.jsx)(u,{children:0===s.length?(0,a.jsx)(p,{children:(0,a.jsx)(m,{colSpan:b.length,className:"h-24 text-center text-muted-foreground",children:'No target prices set. Use "Set Target Prices" in the sidebar.'})}):s.map(e=>(0,a.jsxs)(p,{className:"hover:bg-card/80",children:[(0,a.jsx)(m,{className:"px-3 py-2 text-xs",children:e.counter}),(0,a.jsx)(m,{className:"px-3 py-2 text-xs",children:(0,a.jsx)(f.E,{variant:"Full"===e.status?"default":"secondary",className:(0,c.cn)("Full"===e.status?"bg-green-600 text-white":"bg-yellow-500 text-black","font-bold"),children:e.status})}),(0,a.jsx)(m,{className:"px-3 py-2 text-xs",children:e.orderLevel}),(0,a.jsx)(m,{className:"px-3 py-2 text-xs",children:o(e.valueLevel)}),(0,a.jsx)(m,{className:(0,c.cn)("px-3 py-2 text-xs",e.crypto2Var&&e.crypto2Var<0?"text-destructive":"text-green-400"),children:o(e.crypto2Var,!0)}),(0,a.jsx)(m,{className:(0,c.cn)("px-3 py-2 text-xs",e.crypto1Var&&e.crypto1Var<0?"text-destructive":"text-green-400"),children:o(e.crypto1Var,!0)}),(0,a.jsx)(m,{className:"px-3 py-2 text-xs font-semibold text-primary",children:o(e.targetPrice)}),(0,a.jsx)(m,{className:(0,c.cn)("px-3 py-2 text-xs",e.percentFromActualPrice<0?"text-destructive":"text-green-400"),children:l(e.percentFromActualPrice)}),(0,a.jsx)(m,{className:(0,c.cn)("px-3 py-2 text-xs",e.incomeCrypto1&&e.incomeCrypto1<0?"text-destructive":"text-green-400"),children:o(e.incomeCrypto1)}),(0,a.jsx)(m,{className:(0,c.cn)("px-3 py-2 text-xs",e.incomeCrypto2&&e.incomeCrypto2<0?"text-destructive":"text-green-400"),children:o(e.incomeCrypto2)}),(0,a.jsx)(m,{className:"px-3 py-2 text-xs",children:o(e.originalCostCrypto2)})]},e.id))})]})})})}var y=r(3033),g=r(4530),h=r(659);function v(){let{config:e,currentMarketPrice:t}=(0,n.U)(),r=t=>t.toFixed(e.numDigits),s=e.crypto1&&e.crypto2,o="Crypto 1/Crypto 2",l="0",c="$";return s&&t>0&&("StablecoinSwap"===e.tradingMode?(o="".concat(e.crypto1,"/").concat(e.crypto2),l=r(t),c=""):(o="".concat(e.crypto1,"/").concat(e.crypto2),l=r(t),c="$")),(0,a.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-500"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Current Market Price",(0,a.jsxs)("span",{className:"ml-1 text-xs",children:["(","StablecoinSwap"===e.tradingMode?"Stablecoin Swap":"Simple Spot",")"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-foreground",children:[o,":"]}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-primary",children:[c,l]})]})]})})}function N(){let{config:e,saveCurrentSession:t,targetPriceRows:r,orderHistory:c}=(0,n.U)(),[i,d]=(0,s.useState)(""),[u,p]=(0,s.useState)(null);(0,s.useEffect)(()=>{p(l.SessionManager.getInstance())},[]),(0,s.useEffect)(()=>{u&&(()=>{let t=u.getCurrentSessionId();if(t){let e=u.loadSession(t);if(e){d(e.name);return}}e.crypto1&&e.crypto2?d("".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode||"SimpleSpot")):d("Crypto 1/Crypto 2 = 0")})()},[e.crypto1,e.crypto2,e.tradingMode,u,r.length,c.length]);let x=i||(e.crypto1&&e.crypto2?"".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode||"SimpleSpot"):"Crypto 1/Crypto 2 = 0");return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(y.A,{}),(0,a.jsx)(g.A,{}),(0,a.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"text-2xl font-bold text-primary",children:["Active Orders (",x,")"]}),(0,a.jsx)(o.BT,{children:"Current state of your target price levels. Prices update in real-time."})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)(v,{}),(0,a.jsx)(b,{})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,213,358],()=>t(5791)),_N_E=e.O()}]);