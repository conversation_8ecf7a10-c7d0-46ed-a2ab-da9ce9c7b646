/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQXVHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxib3RcXFxcdHJhZGluZ2JvdF9maW5hbFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// SessionManager will be imported dynamically to avoid SSR issues\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config, saveCurrentSession, targetPriceRows, orderHistory } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_3__.useTradingContext)();\n    const [currentSessionName, setCurrentSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sessionManager, setSessionManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize SessionManager on client side only\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardOrdersPage.useEffect\": ()=>{\n            setSessionManager(_lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance());\n        }\n    }[\"DashboardOrdersPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardOrdersPage.useEffect\": ()=>{\n            if (!sessionManager) return;\n            const updateSessionName = {\n                \"DashboardOrdersPage.useEffect.updateSessionName\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        const session = sessionManager.loadSession(currentSessionId);\n                        if (session) {\n                            setCurrentSessionName(session.name);\n                            return;\n                        }\n                    }\n                    // No session or session not found, generate default name\n                    if (config.crypto1 && config.crypto2) {\n                        const defaultName = \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot');\n                        setCurrentSessionName(defaultName);\n                    } else {\n                        setCurrentSessionName('Crypto 1/Crypto 2 = 0');\n                    }\n                }\n            }[\"DashboardOrdersPage.useEffect.updateSessionName\"];\n            updateSessionName();\n        }\n    }[\"DashboardOrdersPage.useEffect\"], [\n        config.crypto1,\n        config.crypto2,\n        config.tradingMode,\n        sessionManager,\n        targetPriceRows.length,\n        orderHistory.length\n    ]);\n    // Show placeholder text when cryptos are not selected\n    const displayTitle = currentSessionName || (config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot') : \"Crypto 1/Crypto 2 = 0\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    displayTitle,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"vCtTpucoSdGAXGiIIXgbhqcQOGQ=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_3__.useTradingContext\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx":
/*!*********************************************************!*\
  !*** ./src/components/dashboard/MarketPriceDisplay.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarketPriceDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MarketPriceDisplay() {\n    _s();\n    const { config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const formatPrice = (price)=>price.toFixed(config.numDigits);\n    // Check if both cryptos are selected\n    const hasBothCryptos = config.crypto1 && config.crypto2;\n    // Calculate display pair and price based on trading mode\n    let displayPair = \"Crypto 1/Crypto 2\";\n    let displayPrice = \"0\";\n    let currencySymbol = \"$\";\n    if (hasBothCryptos && currentMarketPrice > 0) {\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // For stablecoin swap mode: Crypto1/Crypto2 = Current Market Price (no currency symbol)\n            displayPair = \"\".concat(config.crypto1, \"/\").concat(config.crypto2);\n            displayPrice = formatPrice(currentMarketPrice);\n            currencySymbol = ''; // No currency symbol for crypto-to-crypto ratio\n        } else {\n            // Simple Spot mode: Crypto1/Crypto2 = Current Market Price\n            displayPair = \"\".concat(config.crypto1, \"/\").concat(config.crypto2);\n            displayPrice = formatPrice(currentMarketPrice);\n            currencySymbol = '$';\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-5 w-5 text-green-500\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: [\n                                \"Current Market Price\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1 text-xs\",\n                                    children: [\n                                        \"(\",\n                                        config.tradingMode === \"StablecoinSwap\" ? \"Stablecoin Swap\" : \"Simple Spot\",\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-foreground\",\n                            children: [\n                                displayPair,\n                                \":\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-2xl font-bold text-primary\",\n                            children: [\n                                currencySymbol,\n                                displayPrice\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketPriceDisplay, \"b4ROlSOR9OZ7niaJVf3M+kC2fh8=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = MarketPriceDisplay;\nvar _c;\n$RefreshReg$(_c, \"MarketPriceDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/OrdersTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction OrdersTable() {\n    _s();\n    const { getDisplayOrders, config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const displayOrders = getDisplayOrders();\n    const formatNumber = function(num) {\n        let forceSign = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        const fixedNum = num.toFixed(config.numDigits);\n        if (forceSign && num > 0) return \"+\".concat(fixedNum);\n        return fixedNum;\n    };\n    const formatPercent = (num)=>{\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        return \"\".concat(num.toFixed(2), \"%\");\n    };\n    const columns = [\n        {\n            key: \"#\",\n            label: \"#\"\n        },\n        {\n            key: \"status\",\n            label: \"Status\"\n        },\n        {\n            key: \"orderLevel\",\n            label: \"Level\"\n        },\n        {\n            key: \"valueLevel\",\n            label: \"Value\"\n        },\n        {\n            key: \"crypto2Var\",\n            label: \"\".concat(config.crypto2 || \"Crypto 2\", \" Var.\")\n        },\n        {\n            key: \"crypto1Var\",\n            label: \"\".concat(config.crypto1 || \"Crypto 1\", \" Var.\")\n        },\n        {\n            key: \"targetPrice\",\n            label: \"Target Price\"\n        },\n        {\n            key: \"percentFromActualPrice\",\n            label: \"% from Actual\"\n        },\n        {\n            key: \"incomeCrypto1\",\n            label: \"Income \".concat(config.crypto1 || \"Crypto 1\")\n        },\n        {\n            key: \"incomeCrypto2\",\n            label: \"Income \".concat(config.crypto2 || \"Crypto 2\")\n        },\n        {\n            key: \"originalCostCrypto2\",\n            label: \"Original Cost \".concat(config.crypto2 || \"Crypto 2\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full overflow-x-auto whitespace-nowrap\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                className: \"min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                            className: \"bg-card hover:bg-card\",\n                            children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                    className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm\",\n                                    children: col.label\n                                }, col.key, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                        children: displayOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                colSpan: columns.length,\n                                className: \"h-24 text-center text-muted-foreground\",\n                                children: 'No target prices set. Use \"Set Target Prices\" in the sidebar.'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 15\n                        }, this) : displayOrders.map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                className: \"hover:bg-card/80\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: row.counter\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: row.status === \"Full\" ? \"default\" : \"secondary\",\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(row.status === \"Full\" ? \"bg-green-600 text-white\" : \"bg-yellow-500 text-black\", \"font-bold\"),\n                                            children: row.status\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: row.orderLevel\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNumber(row.valueLevel)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-3 py-2 text-xs\", row.crypto2Var && row.crypto2Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                        children: formatNumber(row.crypto2Var, true)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-3 py-2 text-xs\", row.crypto1Var && row.crypto1Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                        children: formatNumber(row.crypto1Var, true)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: \"px-3 py-2 text-xs font-semibold text-primary\",\n                                        children: formatNumber(row.targetPrice)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-3 py-2 text-xs\", row.percentFromActualPrice < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                        children: formatPercent(row.percentFromActualPrice)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-3 py-2 text-xs\", row.incomeCrypto1 && row.incomeCrypto1 < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                        children: formatNumber(row.incomeCrypto1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 20\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-3 py-2 text-xs\", row.incomeCrypto2 && row.incomeCrypto2 < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                        children: formatNumber(row.incomeCrypto2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNumber(row.originalCostCrypto2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.id, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersTable, \"UL7ACZxyGIzSlHTRji6CgpbrwIo=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = OrdersTable;\nvar _c;\n$RefreshReg$(_c, \"OrdersTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9PcmRlcnNUYWJsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUUwQjtBQUNvQztBQVEvQjtBQUNlO0FBRWI7QUFFbEIsU0FBU1U7O0lBQ3RCLE1BQU0sRUFBRUMsZ0JBQWdCLEVBQUVDLE1BQU0sRUFBRUMsa0JBQWtCLEVBQUUsR0FBR1osMkVBQWlCQTtJQUMxRSxNQUFNYSxnQkFBZ0JIO0lBRXRCLE1BQU1JLGVBQWUsU0FBQ0M7WUFBY0MsNkVBQVk7UUFDOUMsSUFBSUQsUUFBUUUsYUFBYUYsUUFBUSxRQUFRRyxNQUFNSCxNQUFNLE9BQU87UUFDNUQsTUFBTUksV0FBV0osSUFBSUssT0FBTyxDQUFDVCxPQUFPVSxTQUFTO1FBQzdDLElBQUlMLGFBQWFELE1BQU0sR0FBRyxPQUFPLElBQWEsT0FBVEk7UUFDckMsT0FBT0E7SUFDVDtJQUVBLE1BQU1HLGdCQUFnQixDQUFDUDtRQUNyQixJQUFJQSxRQUFRRSxhQUFhRixRQUFRLFFBQVFHLE1BQU1ILE1BQU0sT0FBTztRQUM1RCxPQUFPLEdBQWtCLE9BQWZBLElBQUlLLE9BQU8sQ0FBQyxJQUFHO0lBQzNCO0lBRUEsTUFBTUcsVUFBVTtRQUNkO1lBQUVDLEtBQUs7WUFBS0MsT0FBTztRQUFJO1FBQ3ZCO1lBQUVELEtBQUs7WUFBVUMsT0FBTztRQUFTO1FBQ2pDO1lBQUVELEtBQUs7WUFBY0MsT0FBTztRQUFRO1FBQ3BDO1lBQUVELEtBQUs7WUFBY0MsT0FBTztRQUFRO1FBQ3BDO1lBQUVELEtBQUs7WUFBY0MsT0FBTyxHQUFnQyxPQUE3QmQsT0FBT2UsT0FBTyxJQUFJLFlBQVc7UUFBTztRQUNuRTtZQUFFRixLQUFLO1lBQWNDLE9BQU8sR0FBZ0MsT0FBN0JkLE9BQU9nQixPQUFPLElBQUksWUFBVztRQUFPO1FBQ25FO1lBQUVILEtBQUs7WUFBZUMsT0FBTztRQUFlO1FBQzVDO1lBQUVELEtBQUs7WUFBMEJDLE9BQU87UUFBZ0I7UUFDeEQ7WUFBRUQsS0FBSztZQUFpQkMsT0FBTyxVQUF1QyxPQUE3QmQsT0FBT2dCLE9BQU8sSUFBSTtRQUFhO1FBQ3hFO1lBQUVILEtBQUs7WUFBaUJDLE9BQU8sVUFBdUMsT0FBN0JkLE9BQU9lLE9BQU8sSUFBSTtRQUFhO1FBQ3hFO1lBQUVGLEtBQUs7WUFBdUJDLE9BQU8saUJBQThDLE9BQTdCZCxPQUFPZSxPQUFPLElBQUk7UUFBYTtLQUN0RjtJQUVELHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDNUIsdURBQUtBO2dCQUFDNEIsV0FBVTs7a0NBQ2YsOERBQUN4Qiw2REFBV0E7a0NBQ1YsNEVBQUNDLDBEQUFRQTs0QkFBQ3VCLFdBQVU7c0NBQ2pCTixRQUFRTyxHQUFHLENBQUMsQ0FBQ0Msb0JBQ1osOERBQUMzQiwyREFBU0E7b0NBQWV5QixXQUFVOzhDQUNoQ0UsSUFBSU4sS0FBSzttQ0FESU0sSUFBSVAsR0FBRzs7Ozs7Ozs7Ozs7Ozs7O2tDQU03Qiw4REFBQ3RCLDJEQUFTQTtrQ0FDUFcsY0FBY21CLE1BQU0sS0FBSyxrQkFDeEIsOERBQUMxQiwwREFBUUE7c0NBQ1AsNEVBQUNILDJEQUFTQTtnQ0FBQzhCLFNBQVNWLFFBQVFTLE1BQU07Z0NBQUVILFdBQVU7MENBQXlDOzs7Ozs7Ozs7O21DQUt6RmhCLGNBQWNpQixHQUFHLENBQUMsQ0FBQ0ksb0JBQ2pCLDhEQUFDNUIsMERBQVFBO2dDQUFjdUIsV0FBVTs7a0RBQy9CLDhEQUFDMUIsMkRBQVNBO3dDQUFDMEIsV0FBVTtrREFBcUJLLElBQUlDLE9BQU87Ozs7OztrREFDckQsOERBQUNoQywyREFBU0E7d0NBQUMwQixXQUFVO2tEQUNuQiw0RUFBQ3RCLHVEQUFLQTs0Q0FBQzZCLFNBQVNGLElBQUlHLE1BQU0sS0FBSyxTQUFTLFlBQVk7NENBQzdDUixXQUFXckIsOENBQUVBLENBQUMwQixJQUFJRyxNQUFNLEtBQUssU0FBUyw0QkFBNEIsNEJBQTRCO3NEQUNsR0gsSUFBSUcsTUFBTTs7Ozs7Ozs7Ozs7a0RBR2YsOERBQUNsQywyREFBU0E7d0NBQUMwQixXQUFVO2tEQUFxQkssSUFBSUksVUFBVTs7Ozs7O2tEQUN4RCw4REFBQ25DLDJEQUFTQTt3Q0FBQzBCLFdBQVU7a0RBQXFCZixhQUFhb0IsSUFBSUssVUFBVTs7Ozs7O2tEQUNyRSw4REFBQ3BDLDJEQUFTQTt3Q0FBQzBCLFdBQVdyQiw4Q0FBRUEsQ0FBQyxxQkFBcUIwQixJQUFJTSxVQUFVLElBQUlOLElBQUlNLFVBQVUsR0FBRyxJQUFJLHFCQUFxQjtrREFDdkcxQixhQUFhb0IsSUFBSU0sVUFBVSxFQUFFOzs7Ozs7a0RBRWhDLDhEQUFDckMsMkRBQVNBO3dDQUFDMEIsV0FBV3JCLDhDQUFFQSxDQUFDLHFCQUFxQjBCLElBQUlPLFVBQVUsSUFBSVAsSUFBSU8sVUFBVSxHQUFHLElBQUkscUJBQXFCO2tEQUN2RzNCLGFBQWFvQixJQUFJTyxVQUFVLEVBQUU7Ozs7OztrREFFaEMsOERBQUN0QywyREFBU0E7d0NBQUMwQixXQUFVO2tEQUFnRGYsYUFBYW9CLElBQUlRLFdBQVc7Ozs7OztrREFDakcsOERBQUN2QywyREFBU0E7d0NBQUMwQixXQUFXckIsOENBQUVBLENBQUMscUJBQXFCMEIsSUFBSVMsc0JBQXNCLEdBQUcsSUFBSSxxQkFBcUI7a0RBQ2pHckIsY0FBY1ksSUFBSVMsc0JBQXNCOzs7Ozs7a0RBRTFDLDhEQUFDeEMsMkRBQVNBO3dDQUFDMEIsV0FBV3JCLDhDQUFFQSxDQUFDLHFCQUFxQjBCLElBQUlVLGFBQWEsSUFBSVYsSUFBSVUsYUFBYSxHQUFHLElBQUkscUJBQXFCO2tEQUM5RzlCLGFBQWFvQixJQUFJVSxhQUFhOzs7Ozs7a0RBRWpDLDhEQUFDekMsMkRBQVNBO3dDQUFDMEIsV0FBV3JCLDhDQUFFQSxDQUFDLHFCQUFxQjBCLElBQUlXLGFBQWEsSUFBSVgsSUFBSVcsYUFBYSxHQUFHLElBQUkscUJBQXFCO2tEQUM3Ry9CLGFBQWFvQixJQUFJVyxhQUFhOzs7Ozs7a0RBRWpDLDhEQUFDMUMsMkRBQVNBO3dDQUFDMEIsV0FBVTtrREFBcUJmLGFBQWFvQixJQUFJWSxtQkFBbUI7Ozs7Ozs7K0JBMUJqRVosSUFBSWEsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFtQ3JDO0dBdkZ3QnRDOztRQUNtQ1QsdUVBQWlCQTs7O0tBRHBEUyIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcZGFzaGJvYXJkXFxPcmRlcnNUYWJsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFkaW5nQ29udGV4dCB9IGZyb20gJ0AvY29udGV4dHMvVHJhZGluZ0NvbnRleHQnO1xuaW1wb3J0IHtcbiAgVGFibGUsXG4gIFRhYmxlQm9keSxcbiAgVGFibGVDZWxsLFxuICBUYWJsZUhlYWQsXG4gIFRhYmxlSGVhZGVyLFxuICBUYWJsZVJvdyxcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90YWJsZVwiO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JhZGdlXCI7XG5pbXBvcnQgdHlwZSB7IERpc3BsYXlPcmRlclJvdyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBPcmRlcnNUYWJsZSgpIHtcbiAgY29uc3QgeyBnZXREaXNwbGF5T3JkZXJzLCBjb25maWcsIGN1cnJlbnRNYXJrZXRQcmljZSB9ID0gdXNlVHJhZGluZ0NvbnRleHQoKTtcbiAgY29uc3QgZGlzcGxheU9yZGVycyA9IGdldERpc3BsYXlPcmRlcnMoKTtcblxuICBjb25zdCBmb3JtYXROdW1iZXIgPSAobnVtPzogbnVtYmVyLCBmb3JjZVNpZ24gPSBmYWxzZSkgPT4ge1xuICAgIGlmIChudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09IG51bGwgfHwgaXNOYU4obnVtKSkgcmV0dXJuIFwiLVwiO1xuICAgIGNvbnN0IGZpeGVkTnVtID0gbnVtLnRvRml4ZWQoY29uZmlnLm51bURpZ2l0cyk7XG4gICAgaWYgKGZvcmNlU2lnbiAmJiBudW0gPiAwKSByZXR1cm4gYCske2ZpeGVkTnVtfWA7XG4gICAgcmV0dXJuIGZpeGVkTnVtO1xuICB9O1xuICBcbiAgY29uc3QgZm9ybWF0UGVyY2VudCA9IChudW0/OiBudW1iZXIpID0+IHtcbiAgICBpZiAobnVtID09PSB1bmRlZmluZWQgfHwgbnVtID09PSBudWxsIHx8IGlzTmFOKG51bSkpIHJldHVybiBcIi1cIjtcbiAgICByZXR1cm4gYCR7bnVtLnRvRml4ZWQoMil9JWA7XG4gIH1cblxuICBjb25zdCBjb2x1bW5zID0gW1xuICAgIHsga2V5OiBcIiNcIiwgbGFiZWw6IFwiI1wiIH0sXG4gICAgeyBrZXk6IFwic3RhdHVzXCIsIGxhYmVsOiBcIlN0YXR1c1wiIH0sXG4gICAgeyBrZXk6IFwib3JkZXJMZXZlbFwiLCBsYWJlbDogXCJMZXZlbFwiIH0sXG4gICAgeyBrZXk6IFwidmFsdWVMZXZlbFwiLCBsYWJlbDogXCJWYWx1ZVwiIH0sXG4gICAgeyBrZXk6IFwiY3J5cHRvMlZhclwiLCBsYWJlbDogYCR7Y29uZmlnLmNyeXB0bzIgfHwgXCJDcnlwdG8gMlwifSBWYXIuYCB9LFxuICAgIHsga2V5OiBcImNyeXB0bzFWYXJcIiwgbGFiZWw6IGAke2NvbmZpZy5jcnlwdG8xIHx8IFwiQ3J5cHRvIDFcIn0gVmFyLmAgfSxcbiAgICB7IGtleTogXCJ0YXJnZXRQcmljZVwiLCBsYWJlbDogXCJUYXJnZXQgUHJpY2VcIiB9LFxuICAgIHsga2V5OiBcInBlcmNlbnRGcm9tQWN0dWFsUHJpY2VcIiwgbGFiZWw6IFwiJSBmcm9tIEFjdHVhbFwiIH0sXG4gICAgeyBrZXk6IFwiaW5jb21lQ3J5cHRvMVwiLCBsYWJlbDogYEluY29tZSAke2NvbmZpZy5jcnlwdG8xIHx8IFwiQ3J5cHRvIDFcIn1gIH0sXG4gICAgeyBrZXk6IFwiaW5jb21lQ3J5cHRvMlwiLCBsYWJlbDogYEluY29tZSAke2NvbmZpZy5jcnlwdG8yIHx8IFwiQ3J5cHRvIDJcIn1gIH0sXG4gICAgeyBrZXk6IFwib3JpZ2luYWxDb3N0Q3J5cHRvMlwiLCBsYWJlbDogYE9yaWdpbmFsIENvc3QgJHtjb25maWcuY3J5cHRvMiB8fCBcIkNyeXB0byAyXCJ9YCB9LFxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItMiBib3JkZXItYm9yZGVyIHJvdW5kZWQtc21cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG92ZXJmbG93LXgtYXV0byB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICA8VGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbFwiPlxuICAgICAgICAgIDxUYWJsZUhlYWRlcj5cbiAgICAgICAgICAgIDxUYWJsZVJvdyBjbGFzc05hbWU9XCJiZy1jYXJkIGhvdmVyOmJnLWNhcmRcIj5cbiAgICAgICAgICAgICAge2NvbHVtbnMubWFwKChjb2wpID0+IChcbiAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGtleT17Y29sLmtleX0gY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZCB3aGl0ZXNwYWNlLW5vd3JhcCBweC0zIHB5LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAge2NvbC5sYWJlbH1cbiAgICAgICAgICAgICAgICA8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgIDwvVGFibGVIZWFkZXI+XG4gICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgIHtkaXNwbGF5T3JkZXJzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY29sU3Bhbj17Y29sdW1ucy5sZW5ndGh9IGNsYXNzTmFtZT1cImgtMjQgdGV4dC1jZW50ZXIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICBObyB0YXJnZXQgcHJpY2VzIHNldC4gVXNlIFwiU2V0IFRhcmdldCBQcmljZXNcIiBpbiB0aGUgc2lkZWJhci5cbiAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIGRpc3BsYXlPcmRlcnMubWFwKChyb3c6IERpc3BsYXlPcmRlclJvdykgPT4gKFxuICAgICAgICAgICAgICAgIDxUYWJsZVJvdyBrZXk9e3Jvdy5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctY2FyZC84MFwiPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC14c1wiPntyb3cuY291bnRlcn08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e3Jvdy5zdGF0dXMgPT09IFwiRnVsbFwiID8gXCJkZWZhdWx0XCIgOiBcInNlY29uZGFyeVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihyb3cuc3RhdHVzID09PSBcIkZ1bGxcIiA/IFwiYmctZ3JlZW4tNjAwIHRleHQtd2hpdGVcIiA6IFwiYmcteWVsbG93LTUwMCB0ZXh0LWJsYWNrXCIsIFwiZm9udC1ib2xkXCIpfT5cbiAgICAgICAgICAgICAgICAgICAgICB7cm93LnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC14c1wiPntyb3cub3JkZXJMZXZlbH08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQteHNcIj57Zm9ybWF0TnVtYmVyKHJvdy52YWx1ZUxldmVsKX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPXtjbihcInB4LTMgcHktMiB0ZXh0LXhzXCIsIHJvdy5jcnlwdG8yVmFyICYmIHJvdy5jcnlwdG8yVmFyIDwgMCA/IFwidGV4dC1kZXN0cnVjdGl2ZVwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiKX0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LmNyeXB0bzJWYXIsIHRydWUpfVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT17Y24oXCJweC0zIHB5LTIgdGV4dC14c1wiLCByb3cuY3J5cHRvMVZhciAmJiByb3cuY3J5cHRvMVZhciA8IDAgPyBcInRleHQtZGVzdHJ1Y3RpdmVcIiA6IFwidGV4dC1ncmVlbi00MDBcIil9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHJvdy5jcnlwdG8xVmFyLCB0cnVlKX1cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeVwiPntmb3JtYXROdW1iZXIocm93LnRhcmdldFByaWNlKX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPXtjbihcInB4LTMgcHktMiB0ZXh0LXhzXCIsIHJvdy5wZXJjZW50RnJvbUFjdHVhbFByaWNlIDwgMCA/IFwidGV4dC1kZXN0cnVjdGl2ZVwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiKX0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQZXJjZW50KHJvdy5wZXJjZW50RnJvbUFjdHVhbFByaWNlKX1cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPXtjbihcInB4LTMgcHktMiB0ZXh0LXhzXCIsIHJvdy5pbmNvbWVDcnlwdG8xICYmIHJvdy5pbmNvbWVDcnlwdG8xIDwgMCA/IFwidGV4dC1kZXN0cnVjdGl2ZVwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiKX0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LmluY29tZUNyeXB0bzEpfVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT17Y24oXCJweC0zIHB5LTIgdGV4dC14c1wiLCByb3cuaW5jb21lQ3J5cHRvMiAmJiByb3cuaW5jb21lQ3J5cHRvMiA8IDAgPyBcInRleHQtZGVzdHJ1Y3RpdmVcIiA6IFwidGV4dC1ncmVlbi00MDBcIil9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHJvdy5pbmNvbWVDcnlwdG8yKX1cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC14c1wiPntmb3JtYXROdW1iZXIocm93Lm9yaWdpbmFsQ29zdENyeXB0bzIpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICkpXG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICA8L1RhYmxlPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VUcmFkaW5nQ29udGV4dCIsIlRhYmxlIiwiVGFibGVCb2R5IiwiVGFibGVDZWxsIiwiVGFibGVIZWFkIiwiVGFibGVIZWFkZXIiLCJUYWJsZVJvdyIsIkJhZGdlIiwiY24iLCJPcmRlcnNUYWJsZSIsImdldERpc3BsYXlPcmRlcnMiLCJjb25maWciLCJjdXJyZW50TWFya2V0UHJpY2UiLCJkaXNwbGF5T3JkZXJzIiwiZm9ybWF0TnVtYmVyIiwibnVtIiwiZm9yY2VTaWduIiwidW5kZWZpbmVkIiwiaXNOYU4iLCJmaXhlZE51bSIsInRvRml4ZWQiLCJudW1EaWdpdHMiLCJmb3JtYXRQZXJjZW50IiwiY29sdW1ucyIsImtleSIsImxhYmVsIiwiY3J5cHRvMiIsImNyeXB0bzEiLCJkaXYiLCJjbGFzc05hbWUiLCJtYXAiLCJjb2wiLCJsZW5ndGgiLCJjb2xTcGFuIiwicm93IiwiY291bnRlciIsInZhcmlhbnQiLCJzdGF0dXMiLCJvcmRlckxldmVsIiwidmFsdWVMZXZlbCIsImNyeXB0bzJWYXIiLCJjcnlwdG8xVmFyIiwidGFyZ2V0UHJpY2UiLCJwZXJjZW50RnJvbUFjdHVhbFByaWNlIiwiaW5jb21lQ3J5cHRvMSIsImluY29tZUNyeXB0bzIiLCJvcmlnaW5hbENvc3RDcnlwdG8yIiwiaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst badgeVariants = {\n    variant: {\n        default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\"\n    }\n};\nconst Badge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant = \"default\", ...props } = param;\n    const variantClasses = badgeVariants.variant[variant] || badgeVariants.variant.default;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", variantClasses, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 22,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Badge;\nBadge.displayName = \"Badge\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Badge$React.forwardRef\");\n$RefreshReg$(_c1, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/table.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/table.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   TableBody: () => (/* binding */ TableBody),\n/* harmony export */   TableCaption: () => (/* binding */ TableCaption),\n/* harmony export */   TableCell: () => (/* binding */ TableCell),\n/* harmony export */   TableFooter: () => (/* binding */ TableFooter),\n/* harmony export */   TableHead: () => (/* binding */ TableHead),\n/* harmony export */   TableHeader: () => (/* binding */ TableHeader),\n/* harmony export */   TableRow: () => (/* binding */ TableRow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Table = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full overflow-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full caption-bottom text-sm\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Table;\nTable.displayName = \"Table\";\nconst TableHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&_tr]:border-b\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = TableHeader;\nTableHeader.displayName = \"TableHeader\";\nconst TableBody = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&_tr:last-child]:border-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = TableBody;\nTableBody.displayName = \"TableBody\";\nconst TableFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tfoot\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 43,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = TableFooter;\nTableFooter.displayName = \"TableFooter\";\nconst TableRow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = TableRow;\nTableRow.displayName = \"TableRow\";\nconst TableHead = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = TableHead;\nTableHead.displayName = \"TableHead\";\nconst TableCell = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c12 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined);\n});\n_c13 = TableCell;\nTableCell.displayName = \"TableCell\";\nconst TableCaption = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c14 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"caption\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-4 text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 100,\n        columnNumber: 3\n    }, undefined);\n});\n_c15 = TableCaption;\nTableCaption.displayName = \"TableCaption\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"Table$React.forwardRef\");\n$RefreshReg$(_c1, \"Table\");\n$RefreshReg$(_c2, \"TableHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"TableHeader\");\n$RefreshReg$(_c4, \"TableBody$React.forwardRef\");\n$RefreshReg$(_c5, \"TableBody\");\n$RefreshReg$(_c6, \"TableFooter$React.forwardRef\");\n$RefreshReg$(_c7, \"TableFooter\");\n$RefreshReg$(_c8, \"TableRow$React.forwardRef\");\n$RefreshReg$(_c9, \"TableRow\");\n$RefreshReg$(_c10, \"TableHead$React.forwardRef\");\n$RefreshReg$(_c11, \"TableHead\");\n$RefreshReg$(_c12, \"TableCell$React.forwardRef\");\n$RefreshReg$(_c13, \"TableCell\");\n$RefreshReg$(_c14, \"TableCaption$React.forwardRef\");\n$RefreshReg$(_c15, \"TableCaption\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/table.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst TabsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    activeTab: '',\n    setActiveTab: ()=>{}\n});\nconst Tabs = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { defaultValue = '', value, onValueChange, children, className, ...props } = param;\n    _s();\n    const [activeTab, setActiveTab] = react__WEBPACK_IMPORTED_MODULE_1__.useState(value || defaultValue);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Tabs.useEffect\": ()=>{\n            if (value !== undefined) {\n                setActiveTab(value);\n            }\n        }\n    }[\"Tabs.useEffect\"], [\n        value\n    ]);\n    const handleTabChange = (newValue)=>{\n        if (value === undefined) {\n            setActiveTab(newValue);\n        }\n        onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(newValue);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContext.Provider, {\n        value: {\n            activeTab,\n            setActiveTab: handleTabChange\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: className,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n            lineNumber: 59,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 58,\n        columnNumber: 7\n    }, undefined);\n}, \"k0FhWJTCExh/hFGuT1R1UiXiugA=\")), \"k0FhWJTCExh/hFGuT1R1UiXiugA=\");\n_c1 = Tabs;\nTabs.displayName = \"Tabs\";\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n});\n_c3 = TabsList;\nTabsList.displayName = \"TabsList\";\nconst TabsTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s1((param, ref)=>{\n    let { className, value, children, onClick, ...props } = param;\n    _s1();\n    const { activeTab, setActiveTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    const isActive = activeTab === value;\n    const handleClick = ()=>{\n        setActiveTab(value);\n        onClick === null || onClick === void 0 ? void 0 : onClick();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", isActive && \"bg-background text-foreground shadow-sm\", className),\n        onClick: handleClick,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 95,\n        columnNumber: 7\n    }, undefined);\n}, \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\")), \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\");\n_c5 = TabsTrigger;\nTabsTrigger.displayName = \"TabsTrigger\";\nconst TabsContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = _s2((param, ref)=>{\n    let { className, value, children, ...props } = param;\n    _s2();\n    const { activeTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    if (activeTab !== value) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 121,\n        columnNumber: 7\n    }, undefined);\n}, \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\")), \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\");\n_c7 = TabsContent;\nTabsContent.displayName = \"TabsContent\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Tabs$React.forwardRef\");\n$RefreshReg$(_c1, \"Tabs\");\n$RefreshReg$(_c2, \"TabsList$React.forwardRef\");\n$RefreshReg$(_c3, \"TabsList\");\n$RefreshReg$(_c4, \"TabsTrigger$React.forwardRef\");\n$RefreshReg$(_c5, \"TabsTrigger\");\n$RefreshReg$(_c6, \"TabsContent$React.forwardRef\");\n$RefreshReg$(_c7, \"TabsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tabs.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_b","vendors-node_modules_date-fns__","vendors-node_modules_d","vendors-node_modules_i","vendors-node_modules_lodash_k","vendors-node_modules_lo","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_r","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_c","vendors-node_modules_next_dist_l","vendors-node_modules_next_d","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_p","default-_app-pages-browser_src_lib_session-manager_ts","default-_app-pages-browser_src_contexts_TradingContext_tsx","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","default-_app-pages-browser_src_components_dashboard_BalancesDisplay_tsx-_app-pages-browser_sr-ac955a","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);