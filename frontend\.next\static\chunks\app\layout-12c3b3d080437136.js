(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,O:()=>c});var a=r(5155),o=r(2115),n=r(5695),s=r(5731),l=r(4553);let i=(0,o.createContext)(void 0),c=e=>{let{children:t}=e,[c,u]=(0,o.useState)(!1),[h,d]=(0,o.useState)(!0),[g,p]=(0,o.useState)(!1),f=(0,n.useRouter)(),m=(0,n.usePathname)();(0,o.useEffect)(()=>{(async()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");if("true"===e&&t){u(!0),d(!1),p(!0);try{l.SessionManager.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}return}try{await new Promise(e=>setTimeout(e,1e3));let e=await fetch("http://localhost:5000/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"testuser",password:"password123"})});if(e.ok){let t=await e.json();localStorage.setItem("plutoAuth","true"),localStorage.setItem("plutoAuthToken",t.access_token),localStorage.setItem("plutoRefreshToken",t.refresh_token),localStorage.setItem("plutoUser",JSON.stringify(t.user)),u(!0);try{let{SessionManager:e}=await Promise.resolve().then(r.bind(r,4553));e.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}}}catch(e){}d(!1),p(!0)})();let e=setTimeout(()=>{d(!1),p(!0)},1e4);return()=>clearTimeout(e)},[]),(0,o.useEffect)(()=>{c&&("/login"===m||"/"===m)?f.replace("/dashboard"):c||"/login"===m||"/"===m||f.replace("/login")},[c,m,f]);let y=async(e,t)=>{d(!0);try{if(await s.ZQ.login(e,t))return u(!0),f.push("/dashboard"),!0;return u(!1),!1}catch(e){return u(!1),!1}finally{d(!1)}},k=async()=>{try{await s.ZQ.logout()}catch(e){}finally{u(!1),f.push("/login")}};return(0,a.jsx)(i.Provider,{value:{isAuthenticated:c,login:y,logout:k,isLoading:h},children:t})},u=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},809:(e,t,r)=>{"use strict";r.d(t,{ClientProviders:()=>l});var a=r(5155),o=r(283),n=r(7213),s=r(6982);function l(e){let{children:t}=e;return(0,a.jsx)(o.O,{children:(0,a.jsx)(n.r,{children:(0,a.jsx)(s.t,{children:t})})})}},3380:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1383,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,809))}},e=>{var t=t=>e(e.s=t);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,690,553,213,358],()=>t(3380)),_N_E=e.O()}]);