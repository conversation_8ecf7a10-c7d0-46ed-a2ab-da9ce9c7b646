(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,O:()=>c});var r=s(5155),a=s(2115),l=s(5695),n=s(5731),i=s(4553);let o=(0,a.createContext)(void 0),c=e=>{let{children:t}=e,[c,d]=(0,a.useState)(!1),[m,x]=(0,a.useState)(!0),[u,h]=(0,a.useState)(!1),p=(0,l.useRouter)(),f=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");if("true"===e&&t){d(!0),x(!1),h(!0);try{i.SessionManager.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}return}try{await new Promise(e=>setTimeout(e,1e3));let e=await fetch("http://localhost:5000/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"testuser",password:"password123"})});if(e.ok){let t=await e.json();localStorage.setItem("plutoAuth","true"),localStorage.setItem("plutoAuthToken",t.access_token),localStorage.setItem("plutoRefreshToken",t.refresh_token),localStorage.setItem("plutoUser",JSON.stringify(t.user)),d(!0);try{let{SessionManager:e}=await Promise.resolve().then(s.bind(s,4553));e.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}}}catch(e){}x(!1),h(!0)})();let e=setTimeout(()=>{x(!1),h(!0)},1e4);return()=>clearTimeout(e)},[]),(0,a.useEffect)(()=>{c&&("/login"===f||"/"===f)?p.replace("/dashboard"):c||"/login"===f||"/"===f||p.replace("/login")},[c,f,p]);let g=async(e,t)=>{x(!0);try{if(await n.ZQ.login(e,t))return d(!0),p.push("/dashboard"),!0;return d(!1),!1}catch(e){return d(!1),!1}finally{x(!1)}},b=async()=>{try{await n.ZQ.logout()}catch(e){}finally{d(!1),p.push("/login")}};return(0,r.jsx)(o.Provider,{value:{isAuthenticated:c,login:g,logout:b,isLoading:m},children:t})},d=()=>{let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var r=s(5155),a=s(2115),l=s(9434);let n={variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},i=a.forwardRef((e,t)=>{let{className:s,variant:a="default",size:i="default",asChild:o=!1,...c}=e,d=n.variant[a]||n.variant.default,m=n.size[i]||n.size.default;return(0,r.jsx)(o?"span":"button",{className:(0,l.cn)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",d,m,s),ref:t,...c})});i.displayName="Button"},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(5155),a=s(2115),l=s(9434);let n=a.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});n.displayName="Input"},3870:(e,t,s)=>{Promise.resolve().then(s.bind(s,9690))},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var r=s(5155),a=s(2115),l=s(9434);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("label",{ref:t,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",s),...a})});n.displayName="Label"},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var r=s(5155),a=s(2115),l=s(9434);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",s),...a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-4 md:p-6",s),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-4 md:p-6 pt-0",s),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-4 md:p-6 pt-0",s),...a})}).displayName="CardFooter"},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(2596),a=s(9688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},9435:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(5155);s(2115);var a=s(6766);let l=e=>{let{className:t,useFullName:s=!0}=e;return(0,r.jsxs)("div",{className:"flex items-center text-2xl font-bold text-primary ".concat(t),children:[(0,r.jsx)(a.default,{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",width:28,height:28,className:"mr-2 rounded-sm"}),(0,r.jsxs)("span",{children:["Pluto",s?" Trading Bot":""]})]})}},9690:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(5155),a=s(2115),l=s(283),n=s(285),i=s(2523),o=s(5057),c=s(6695),d=s(9435),m=s(172),x=s(7104),u=s(3896),h=s(4607),p=s(7607),f=s(8639),g=s(2087),b=s(659),y=s(9615),j=s(286),N=s(6570),v=s(6874),w=s.n(v),S=s(5731);function k(){let[e,t]=(0,a.useState)(""),[s,v]=(0,a.useState)(""),[k,A]=(0,a.useState)(""),[C,P]=(0,a.useState)(!1),{login:T,isLoading:R,isAuthenticated:I}=(0,l.A)(),[B,F]=(0,a.useState)(""),[E,Z]=(0,a.useState)(""),[_,L]=(0,a.useState)(!1),[q,z]=(0,a.useState)(!1),[J,W]=(0,a.useState)(new Date().getFullYear()),[D,$]=(0,a.useState)("checking");(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch("http://localhost:5000/health/",{method:"GET"});$(e.ok?"online":"offline")}catch(e){$("offline")}};e();let t=setInterval(e,1e4);return W(new Date().getFullYear()),()=>clearInterval(t)},[]);let O=async t=>{t.preventDefault(),F("");try{await T(e,s)||F("Invalid credentials. Try using testuser/password123")}catch(e){F("Login failed. Please try again.")}},H=async r=>{if(r.preventDefault(),F(""),Z(""),!e||!s||!k){F("All fields are required");return}try{await S.ZQ.register(e,s,k),Z("Registration successful! You can now log in."),setTimeout(()=>{z(!1),L(!0),t(""),v(""),A(""),Z("")},2e3)}catch(e){F(e.message||"Could not connect to server. Please try again later.")}},M=()=>{z(!1),L(!0),F(""),Z("")},Q=()=>{L(!1),z(!0),F(""),Z("")};return R&&!I?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background p-4",children:(0,r.jsx)(m.A,{className:"h-8 w-8 animate-spin text-primary"})}):I?null:(0,r.jsxs)("div",{className:"min-h-screen bg-background text-foreground flex flex-col",children:[(0,r.jsxs)("header",{className:"py-4 px-6 md:px-10 flex justify-between items-center border-b border-border",children:[(0,r.jsx)(d.A,{className:"text-2xl"}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6 text-sm",children:[(0,r.jsx)(w(),{href:"#",className:"hover:text-primary",children:"Home"}),(0,r.jsx)(w(),{href:"#",className:"hover:text-primary",children:"Features"}),(0,r.jsx)(w(),{href:"#",className:"hover:text-primary",children:"Pricing"}),(0,r.jsx)(w(),{href:"#",className:"hover:text-primary",children:"Contact"})]}),(0,r.jsxs)("div",{className:"space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>M(),children:"Login"}),(0,r.jsx)(n.$,{className:"btn-neo",onClick:()=>Q(),children:"Register"})]})]}),(0,r.jsxs)("main",{className:"flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20",children:[(0,r.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold mb-6",children:[(0,r.jsx)("span",{className:"text-primary",children:"Pluto"})," Trading Bot Platform"]}),(0,r.jsx)("p",{className:"text-lg md:text-xl text-muted-foreground max-w-2xl mb-10",children:"Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort."}),!_&&!q&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,r.jsxs)(n.$,{size:"lg",className:"btn-outline-neo text-lg px-8 py-4",onClick:()=>M(),children:[(0,r.jsx)(x.A,{className:"mr-2 h-5 w-5"})," Login to Trading Platform"]}),(0,r.jsxs)(n.$,{size:"lg",className:"btn-neo text-lg px-8 py-4",onClick:()=>Q(),children:[(0,r.jsx)(u.A,{className:"mr-2 h-5 w-5"})," Create Free Account"]})]}),_&&(0,r.jsxs)(c.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,r.jsxs)(c.aR,{className:"text-center",children:[(0,r.jsx)(c.ZB,{className:"text-3xl font-bold",children:"Account Login"}),(0,r.jsx)(c.BT,{children:"Access your Pluto Trading Bot dashboard."})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"username",className:"text-lg sr-only",children:"Username"}),(0,r.jsx)(i.p,{id:"username",type:"text",value:e,onChange:e=>t(e.target.value),required:!0,className:"text-base",placeholder:"Username"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"password",className:"text-lg sr-only",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.p,{id:"password",type:C?"text":"password",value:s,onChange:e=>v(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Password (try: password123)"}),(0,r.jsx)("button",{type:"button",onClick:()=>P(!C),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":C?"Hide password":"Show password",children:C?(0,r.jsx)(h.A,{className:"h-5 w-5"}):(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]}),B&&(0,r.jsx)("p",{className:"text-destructive text-sm",children:B}),E&&(0,r.jsx)("p",{className:"text-green-500 text-sm",children:E}),(0,r.jsx)(n.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:R||"offline"===D,children:R?(0,r.jsx)(m.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Login"}),(0,r.jsxs)("div",{className:"flex items-center justify-center mt-2 text-sm",children:["checking"===D&&(0,r.jsxs)("div",{className:"flex items-center text-orange-500",children:[(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1 animate-spin"}),(0,r.jsx)("span",{children:"Checking server connection..."})]}),"online"===D&&(0,r.jsxs)("div",{className:"flex items-center text-green-500",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1"}),(0,r.jsx)("span",{children:"Server connected"})]}),"offline"===D&&(0,r.jsxs)("div",{className:"flex items-center text-destructive",children:[(0,r.jsx)(g.A,{className:"h-3 w-3 mr-1"}),(0,r.jsx)("span",{children:"Server offline - Please start the backend"})]})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-muted-foreground space-y-2 pt-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("a",{href:"#",className:"hover:text-primary underline",children:"Forgot password?"})," (Simulated)"]}),(0,r.jsxs)("p",{children:["Don't have an account? ",(0,r.jsx)("button",{type:"button",onClick:Q,className:"hover:text-primary underline",children:"Create Account"})]})]})]})})]}),q&&(0,r.jsxs)(c.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,r.jsxs)(c.aR,{className:"text-center",children:[(0,r.jsx)(c.ZB,{className:"text-3xl font-bold",children:"Create Account"}),(0,r.jsx)(c.BT,{children:"Join Pluto Trading Bot platform."})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("form",{onSubmit:H,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"reg-username",className:"text-lg",children:"Username"}),(0,r.jsx)(i.p,{id:"reg-username",type:"text",value:e,onChange:e=>t(e.target.value),required:!0,className:"text-base",placeholder:"Choose a username"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"reg-email",className:"text-lg",children:"Email"}),(0,r.jsx)(i.p,{id:"reg-email",type:"email",value:k,onChange:e=>A(e.target.value),required:!0,className:"text-base",placeholder:"Your email address"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"reg-password",className:"text-lg",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.p,{id:"reg-password",type:C?"text":"password",value:s,onChange:e=>v(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Create a password"}),(0,r.jsx)("button",{type:"button",onClick:()=>P(!C),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":C?"Hide password":"Show password",children:C?(0,r.jsx)(h.A,{className:"h-5 w-5"}):(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]}),B&&(0,r.jsx)("p",{className:"text-destructive text-sm",children:B}),E&&(0,r.jsx)("p",{className:"text-green-500 text-sm",children:E}),(0,r.jsx)(n.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:R,children:R?(0,r.jsx)(m.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Register"}),(0,r.jsx)("div",{className:"text-center text-sm text-muted-foreground pt-2",children:(0,r.jsxs)("p",{children:["Already have an account? ",(0,r.jsx)("button",{type:"button",onClick:M,className:"hover:text-primary underline",children:"Login"})]})})]})})]})]}),(0,r.jsx)("section",{className:"py-10 md:py-16 bg-card border-t border-border",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary",children:"Trading Strategies"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12",children:[(0,r.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Simple Spot Mode"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Execute direct buy and sell orders based on your target prices. Ideal for straightforward market participation and capitalizing on price movements."})]}),(0,r.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Stablecoin Swap Mode"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leverage stablecoins in your trading strategy, aiming for value preservation while capitalizing on market volatility against your chosen crypto assets."})]})]}),(0,r.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"Why Pluto?"}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto mb-12",children:[{icon:(0,r.jsx)(b.A,{className:"h-10 w-10 text-primary mb-3"}),title:"Automated Profits",description:"24/7 trading execution, so you never miss an opportunity."},{icon:(0,r.jsx)(y.A,{className:"h-10 w-10 text-primary mb-3"}),title:"AI-Powered Insights",description:"Smart suggestions to help you choose the best trading mode."},{icon:(0,r.jsx)(j.A,{className:"h-10 w-10 text-primary mb-3"}),title:"Dual Strategy Modes",description:"Flexible Simple Spot and Stablecoin Swap options to fit your style."},{icon:(0,r.jsx)(N.A,{className:"h-10 w-10 text-primary mb-3"}),title:"Secure Simulation",description:"Test strategies risk-free in a simulated environment."}].map(e=>(0,r.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30 flex flex-col items-center",children:[e.icon,(0,r.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]},e.title))}),(0,r.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"What Our Users Say"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,r.jsx)(c.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,r.jsxs)(c.Wu,{className:"p-0",children:[(0,r.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"Pluto\'s Simple Spot mode helped me capture quick profits effortlessly! The interface is so intuitive."'}),(0,r.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- TraderX (Simulated)"})]})}),(0,r.jsx)(c.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,r.jsxs)(c.Wu,{className:"p-0",children:[(0,r.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"The Stablecoin Swap is perfect for my long-term strategy. Love the AI suggestions for mode selection!"'}),(0,r.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- Crypto Enthusiast (Simulated)"})]})})]})]})}),(0,r.jsxs)("footer",{className:"py-6 text-center text-sm text-muted-foreground border-t border-border",children:["\xa9 ",J," Pluto Trading. All Rights Reserved (Simulation)."]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,358],()=>t(3870)),_N_E=e.O()}]);