/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/login/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(app-pages-browser)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_shared_Logo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/shared/Logo */ \"(app-pages-browser)/./src/components/shared/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot-message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, isLoading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showLoginForm, setShowLoginForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRegisterForm, setShowRegisterForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentYear, setCurrentYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear());\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            // Check backend connectivity on load\n            const checkBackendStatus = {\n                \"LoginPage.useEffect.checkBackendStatus\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/health/', {\n                            method: 'GET'\n                        });\n                        setBackendStatus(response.ok ? 'online' : 'offline');\n                    } catch (error) {\n                        console.error('Backend connectivity check failed:', error);\n                        setBackendStatus('offline');\n                    }\n                }\n            }[\"LoginPage.useEffect.checkBackendStatus\"];\n            // Initial check\n            checkBackendStatus();\n            // Set up periodic checks\n            const intervalId = setInterval(checkBackendStatus, 10000);\n            // Set current year\n            setCurrentYear(new Date().getFullYear());\n            // Cleanup interval on unmount\n            return ({\n                \"LoginPage.useEffect\": ()=>clearInterval(intervalId)\n            })[\"LoginPage.useEffect\"];\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        try {\n            const success = await login(username, password);\n            if (!success) {\n                setError('Invalid credentials. Try using testuser/password123');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            setError('Login failed. Please try again.');\n        }\n    };\n    const handleRegister = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setSuccessMessage('');\n        if (!username || !password || !email) {\n            setError('All fields are required');\n            return;\n        }\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.authApi.register(username, password, email);\n            setSuccessMessage('Registration successful! You can now log in.');\n            setTimeout(()=>{\n                setShowRegisterForm(false);\n                setShowLoginForm(true);\n                setUsername('');\n                setPassword('');\n                setEmail('');\n                setSuccessMessage('');\n            }, 2000);\n        } catch (error) {\n            console.error('Registration error:', error);\n            setError(error.message || 'Could not connect to server. Please try again later.');\n        }\n    };\n    const openLoginForm = ()=>{\n        setShowRegisterForm(false);\n        setShowLoginForm(true);\n        setError('');\n        setSuccessMessage('');\n    };\n    const openRegisterForm = ()=>{\n        setShowLoginForm(false);\n        setShowRegisterForm(true);\n        setError('');\n        setSuccessMessage('');\n    };\n    if (isLoading && !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-background p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    if (isAuthenticated) return null; // AuthContext handles redirect\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background text-foreground flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"py-4 px-6 md:px-10 flex justify-between items-center border-b border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Logo__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"text-2xl\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                className: \"btn-outline-neo\",\n                                onClick: ()=>openLoginForm(),\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"btn-neo\",\n                                onClick: ()=>openRegisterForm(),\n                                children: \"Register\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl sm:text-5xl md:text-6xl font-bold mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary\",\n                                children: \"Pluto\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            \" Trading Bot Platform\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-muted-foreground max-w-2xl mb-10\",\n                        children: \"Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    !showLoginForm && !showRegisterForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"btn-outline-neo text-lg px-8 py-4\",\n                                onClick: ()=>openLoginForm(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Login to Trading Platform\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"btn-neo text-lg px-8 py-4\",\n                                onClick: ()=>openRegisterForm(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Create Free Account\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    showLoginForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"Account Login\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                        children: \"Access your Pluto Trading Bot dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"username\",\n                                                    className: \"text-lg sr-only\",\n                                                    children: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"username\",\n                                                    type: \"text\",\n                                                    value: username,\n                                                    onChange: (e)=>setUsername(e.target.value),\n                                                    required: true,\n                                                    className: \"text-base\",\n                                                    placeholder: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-lg sr-only\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true,\n                                                            className: \"text-base pr-10\",\n                                                            placeholder: \"Password (try: password123)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground\",\n                                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 39\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 72\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-destructive text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 27\n                                        }, this),\n                                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-500 text-sm\",\n                                            children: successMessage\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 36\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full btn-neo text-lg py-3\",\n                                            disabled: isLoading || backendStatus === 'offline',\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 32\n                                            }, this) : \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mt-2 text-sm\",\n                                            children: [\n                                                backendStatus === 'checking' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-orange-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Checking server connection...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 21\n                                                }, this),\n                                                backendStatus === 'online' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-green-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Server connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this),\n                                                backendStatus === 'offline' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-destructive\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Server offline - Please start the backend\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-sm text-muted-foreground space-y-2 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"hover:text-primary underline\",\n                                                            children: \"Forgot password?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" (Simulated)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"Don't have an account? \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: openRegisterForm,\n                                                            className: \"hover:text-primary underline\",\n                                                            children: \"Create Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 44\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    showRegisterForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"Create Account\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                        children: \"Join Pluto Trading Bot platform.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleRegister,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"reg-username\",\n                                                    className: \"text-lg\",\n                                                    children: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"reg-username\",\n                                                    type: \"text\",\n                                                    value: username,\n                                                    onChange: (e)=>setUsername(e.target.value),\n                                                    required: true,\n                                                    className: \"text-base\",\n                                                    placeholder: \"Choose a username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"reg-email\",\n                                                    className: \"text-lg\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"reg-email\",\n                                                    type: \"email\",\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    required: true,\n                                                    className: \"text-base\",\n                                                    placeholder: \"Your email address\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"reg-password\",\n                                                    className: \"text-lg\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"reg-password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true,\n                                                            className: \"text-base pr-10\",\n                                                            placeholder: \"Create a password\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground\",\n                                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 39\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 72\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-destructive text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 27\n                                        }, this),\n                                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-500 text-sm\",\n                                            children: successMessage\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 36\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full btn-neo text-lg py-3\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 32\n                                            }, this) : \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-sm text-muted-foreground pt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Already have an account? \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: openLoginForm,\n                                                        className: \"hover:text-primary underline\",\n                                                        children: \"Login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 46\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-10 md:py-16 bg-card border-t border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-primary\",\n                            children: \"Trading Strategies\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-border p-6 rounded-sm bg-background/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Simple Spot Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Execute direct buy and sell orders based on your target prices. Ideal for straightforward market participation and capitalizing on price movements.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-border p-6 rounded-sm bg-background/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Stablecoin Swap Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Leverage stablecoins in your trading strategy, aiming for value preservation while capitalizing on market volatility against your chosen crypto assets.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-primary mt-16\",\n                            children: \"Why Pluto?\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto mb-12\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"Automated Profits\",\n                                    description: \"24/7 trading execution, so you never miss an opportunity.\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"AI-Powered Insights\",\n                                    description: \"Smart suggestions to help you choose the best trading mode.\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"Dual Strategy Modes\",\n                                    description: \"Flexible Simple Spot and Stablecoin Swap options to fit your style.\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"Secure Simulation\",\n                                    description: \"Test strategies risk-free in a simulated environment.\"\n                                }\n                            ].map((advantage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-border p-6 rounded-sm bg-background/30 flex flex-col items-center\",\n                                    children: [\n                                        advantage.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-2\",\n                                            children: advantage.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: advantage.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, advantage.title, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-primary mt-16\",\n                            children: \"What Our Users Say\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    className: \"border-2 border-border bg-background/30 p-6 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-muted-foreground italic\",\n                                                children: '\"Pluto\\'s Simple Spot mode helped me capture quick profits effortlessly! The interface is so intuitive.\"'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-right font-semibold text-primary mt-3\",\n                                                children: \"- TraderX (Simulated)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    className: \"border-2 border-border bg-background/30 p-6 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-muted-foreground italic\",\n                                                children: '\"The Stablecoin Swap is perfect for my long-term strategy. Love the AI suggestions for mode selection!\"'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-right font-semibold text-primary mt-3\",\n                                                children: \"- Crypto Enthusiast (Simulated)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"py-6 text-center text-sm text-muted-foreground border-t border-border\",\n                children: [\n                    \"\\xa9 \",\n                    currentYear,\n                    \" Pluto Trading. All Rights Reserved (Simulation).\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"rOBi94UP5V0smbJXmUU6mS96d/c=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shared/Logo.tsx":
/*!****************************************!*\
  !*** ./src/components/shared/Logo.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\nconst Logo = (param)=>{\n    let { className, useFullName = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center text-2xl font-bold text-primary \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: \"https://i.imgur.com/Q0HDcMH.png\",\n                alt: \"Pluto Trading Bot Logo\",\n                width: 28,\n                height: 28,\n                className: \"mr-2 rounded-sm\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\shared\\\\Logo.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: [\n                    \"Pluto\",\n                    useFullName ? \" Trading Bot\" : \"\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\shared\\\\Logo.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\shared\\\\Logo.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Logo;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NoYXJlZC9Mb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDMEI7QUFDSztBQU8vQixNQUFNRSxPQUE0QjtRQUFDLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxJQUFJLEVBQUU7SUFDbEUscUJBQ0UsOERBQUNDO1FBQUlGLFdBQVcscURBQStELE9BQVZBOzswQkFDbkUsOERBQUNGLGtEQUFLQTtnQkFDSkssS0FBSTtnQkFDSkMsS0FBSTtnQkFDSkMsT0FBTztnQkFDUEMsUUFBUTtnQkFDUk4sV0FBVTs7Ozs7OzBCQUVaLDhEQUFDTzs7b0JBQUs7b0JBQU1OLGNBQWMsaUJBQWlCOzs7Ozs7Ozs7Ozs7O0FBR2pEO0tBYk1GO0FBZU4saUVBQWVBLElBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHNoYXJlZFxcTG9nby50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuXG5pbnRlcmZhY2UgTG9nb1Byb3BzIHtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICB1c2VGdWxsTmFtZT86IGJvb2xlYW47IC8vIEFkZGVkIHByb3AgdG8gY29udHJvbCB0ZXh0XG59XG5cbmNvbnN0IExvZ286IFJlYWN0LkZDPExvZ29Qcm9wcz4gPSAoeyBjbGFzc05hbWUsIHVzZUZ1bGxOYW1lID0gdHJ1ZSB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgPEltYWdlXG4gICAgICAgIHNyYz1cImh0dHBzOi8vaS5pbWd1ci5jb20vUTBIRGNNSC5wbmdcIlxuICAgICAgICBhbHQ9XCJQbHV0byBUcmFkaW5nIEJvdCBMb2dvXCJcbiAgICAgICAgd2lkdGg9ezI4fVxuICAgICAgICBoZWlnaHQ9ezI4fVxuICAgICAgICBjbGFzc05hbWU9XCJtci0yIHJvdW5kZWQtc21cIlxuICAgICAgLz5cbiAgICAgIDxzcGFuPlBsdXRve3VzZUZ1bGxOYW1lID8gXCIgVHJhZGluZyBCb3RcIiA6IFwiXCJ9PC9zcGFuPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTG9nbztcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkltYWdlIiwiTG9nbyIsImNsYXNzTmFtZSIsInVzZUZ1bGxOYW1lIiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/Logo.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = \"Label\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFDRTtBQU1oQyxNQUFNRSxzQkFBUUYsNkNBQWdCLE1BQzVCLFFBQTBCSTtRQUF6QixFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBTzt5QkFDdEIsOERBQUNDO1FBQ0NILEtBQUtBO1FBQ0xDLFdBQVdKLDhDQUFFQSxDQUNYLDhGQUNBSTtRQUVELEdBQUdDLEtBQUs7Ozs7Ozs7O0FBSWZKLE1BQU1NLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmludGVyZmFjZSBMYWJlbFByb3BzIGV4dGVuZHMgUmVhY3QuTGFiZWxIVE1MQXR0cmlidXRlczxIVE1MTGFiZWxFbGVtZW50PiB7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxMYWJlbEVsZW1lbnQsIExhYmVsUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxsYWJlbFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxuKTtcbkxhYmVsLmRpc3BsYXlOYW1lID0gXCJMYWJlbFwiO1xuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwibGFiZWwiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_c","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_r","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_c","vendors-node_modules_next_dist_l","vendors-node_modules_n","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_t","default-_app-pages-browser_src_contexts_AuthContext_tsx","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);