/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/login/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(app-pages-browser)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_shared_Logo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/shared/Logo */ \"(app-pages-browser)/./src/components/shared/Logo.tsx\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot-message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BotMessageSquare,Eye,EyeOff,Loader2,LogIn,ShieldCheck,TrendingUp,UserPlus,Users,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, isLoading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showLoginForm, setShowLoginForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRegisterForm, setShowRegisterForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentYear, setCurrentYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear());\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            // Check backend connectivity on load\n            const checkBackendStatus = {\n                \"LoginPage.useEffect.checkBackendStatus\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/health/', {\n                            method: 'GET'\n                        });\n                        setBackendStatus(response.ok ? 'online' : 'offline');\n                    } catch (error) {\n                        console.error('Backend connectivity check failed:', error);\n                        setBackendStatus('offline');\n                    }\n                }\n            }[\"LoginPage.useEffect.checkBackendStatus\"];\n            // Initial check\n            checkBackendStatus();\n            // Set up periodic checks\n            const intervalId = setInterval(checkBackendStatus, 10000);\n            // Set current year\n            setCurrentYear(new Date().getFullYear());\n            // Cleanup interval on unmount\n            return ({\n                \"LoginPage.useEffect\": ()=>clearInterval(intervalId)\n            })[\"LoginPage.useEffect\"];\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        try {\n            const success = await login(username, password);\n            if (!success) {\n                setError('Invalid credentials. Try using testuser/password123');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            setError('Login failed. Please try again.');\n        }\n    };\n    const handleRegister = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setSuccessMessage('');\n        if (!username || !password || !email) {\n            setError('All fields are required');\n            return;\n        }\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.authApi.register(username, password, email);\n            setSuccessMessage('Registration successful! You can now log in.');\n            setTimeout(()=>{\n                setShowRegisterForm(false);\n                setShowLoginForm(true);\n                setUsername('');\n                setPassword('');\n                setEmail('');\n                setSuccessMessage('');\n            }, 2000);\n        } catch (error) {\n            console.error('Registration error:', error);\n            setError(error.message || 'Could not connect to server. Please try again later.');\n        }\n    };\n    const openLoginForm = ()=>{\n        setShowRegisterForm(false);\n        setShowLoginForm(true);\n        setError('');\n        setSuccessMessage('');\n    };\n    const openRegisterForm = ()=>{\n        setShowLoginForm(false);\n        setShowRegisterForm(true);\n        setError('');\n        setSuccessMessage('');\n    };\n    if (isLoading && !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-background p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    if (isAuthenticated) return null; // AuthContext handles redirect\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background text-foreground flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"py-4 px-6 md:px-10 flex justify-between items-center border-b border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Logo__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"text-2xl\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                href: \"#\",\n                                className: \"hover:text-primary\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                className: \"btn-outline-neo\",\n                                onClick: ()=>openLoginForm(),\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"btn-neo\",\n                                onClick: ()=>openRegisterForm(),\n                                children: \"Register\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl sm:text-5xl md:text-6xl font-bold mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary\",\n                                children: \"Pluto\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            \" Trading Bot Platform\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-muted-foreground max-w-2xl mb-10\",\n                        children: \"Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    !showLoginForm && !showRegisterForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"btn-outline-neo text-lg px-8 py-4\",\n                                onClick: ()=>openLoginForm(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Login to Trading Platform\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"btn-neo text-lg px-8 py-4\",\n                                onClick: ()=>openRegisterForm(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Create Free Account\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    showLoginForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"Account Login\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                        children: \"Access your Pluto Trading Bot dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"username\",\n                                                    className: \"text-lg sr-only\",\n                                                    children: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"username\",\n                                                    type: \"text\",\n                                                    value: username,\n                                                    onChange: (e)=>setUsername(e.target.value),\n                                                    required: true,\n                                                    className: \"text-base\",\n                                                    placeholder: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"password\",\n                                                    className: \"text-lg sr-only\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true,\n                                                            className: \"text-base pr-10\",\n                                                            placeholder: \"Password (try: password123)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground\",\n                                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 39\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 72\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-destructive text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 27\n                                        }, this),\n                                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-500 text-sm\",\n                                            children: successMessage\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 36\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full btn-neo text-lg py-3\",\n                                            disabled: isLoading || backendStatus === 'offline',\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 32\n                                            }, this) : \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mt-2 text-sm\",\n                                            children: [\n                                                backendStatus === 'checking' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-orange-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Checking server connection...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 21\n                                                }, this),\n                                                backendStatus === 'online' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-green-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Server connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this),\n                                                backendStatus === 'offline' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-destructive\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Server offline - Please start the backend\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-sm text-muted-foreground space-y-2 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"hover:text-primary underline\",\n                                                            children: \"Forgot password?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" (Simulated)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"Don't have an account? \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: openRegisterForm,\n                                                            className: \"hover:text-primary underline\",\n                                                            children: \"Create Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 44\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    showRegisterForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"Create Account\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                        children: \"Join Pluto Trading Bot platform.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleRegister,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"reg-username\",\n                                                    className: \"text-lg\",\n                                                    children: \"Username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"reg-username\",\n                                                    type: \"text\",\n                                                    value: username,\n                                                    onChange: (e)=>setUsername(e.target.value),\n                                                    required: true,\n                                                    className: \"text-base\",\n                                                    placeholder: \"Choose a username\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"reg-email\",\n                                                    className: \"text-lg\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"reg-email\",\n                                                    type: \"email\",\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    required: true,\n                                                    className: \"text-base\",\n                                                    placeholder: \"Your email address\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"reg-password\",\n                                                    className: \"text-lg\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"reg-password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            required: true,\n                                                            className: \"text-base pr-10\",\n                                                            placeholder: \"Create a password\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground\",\n                                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 39\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 72\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-destructive text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 27\n                                        }, this),\n                                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-500 text-sm\",\n                                            children: successMessage\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 36\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full btn-neo text-lg py-3\",\n                                            disabled: isLoading,\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 32\n                                            }, this) : \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-sm text-muted-foreground pt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Already have an account? \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: openLoginForm,\n                                                        className: \"hover:text-primary underline\",\n                                                        children: \"Login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 46\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-10 md:py-16 bg-card border-t border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-primary\",\n                            children: \"Trading Strategies\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-border p-6 rounded-sm bg-background/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Simple Spot Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Execute direct buy and sell orders based on your target prices. Ideal for straightforward market participation and capitalizing on price movements.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-border p-6 rounded-sm bg-background/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Stablecoin Swap Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Leverage stablecoins in your trading strategy, aiming for value preservation while capitalizing on market volatility against your chosen crypto assets.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-primary mt-16\",\n                            children: \"Why Pluto?\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto mb-12\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"Automated Profits\",\n                                    description: \"24/7 trading execution, so you never miss an opportunity.\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"AI-Powered Insights\",\n                                    description: \"Smart suggestions to help you choose the best trading mode.\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"Dual Strategy Modes\",\n                                    description: \"Flexible Simple Spot and Stablecoin Swap options to fit your style.\"\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BotMessageSquare_Eye_EyeOff_Loader2_LogIn_ShieldCheck_TrendingUp_UserPlus_Users_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-10 w-10 text-primary mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: \"Secure Simulation\",\n                                    description: \"Test strategies risk-free in a simulated environment.\"\n                                }\n                            ].map((advantage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-border p-6 rounded-sm bg-background/30 flex flex-col items-center\",\n                                    children: [\n                                        advantage.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-2\",\n                                            children: advantage.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: advantage.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, advantage.title, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-primary mt-16\",\n                            children: \"What Our Users Say\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    className: \"border-2 border-border bg-background/30 p-6 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-muted-foreground italic\",\n                                                children: '\"Pluto\\'s Simple Spot mode helped me capture quick profits effortlessly! The interface is so intuitive.\"'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-right font-semibold text-primary mt-3\",\n                                                children: \"- TraderX (Simulated)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    className: \"border-2 border-border bg-background/30 p-6 text-left\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-muted-foreground italic\",\n                                                children: '\"The Stablecoin Swap is perfect for my long-term strategy. Love the AI suggestions for mode selection!\"'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-right font-semibold text-primary mt-3\",\n                                                children: \"- Crypto Enthusiast (Simulated)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"py-6 text-center text-sm text-muted-foreground border-t border-border\",\n                children: [\n                    \"\\xa9 \",\n                    currentYear,\n                    \" Pluto Trading. All Rights Reserved (Simulation).\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"rOBi94UP5V0smbJXmUU6mS96d/c=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shared/Logo.tsx":
/*!****************************************!*\
  !*** ./src/components/shared/Logo.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\nconst Logo = (param)=>{\n    let { className, useFullName = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center text-2xl font-bold text-primary \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: \"https://i.imgur.com/Q0HDcMH.png\",\n                alt: \"Pluto Trading Bot Logo\",\n                width: 28,\n                height: 28,\n                className: \"mr-2 rounded-sm\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\shared\\\\Logo.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: [\n                    \"Pluto\",\n                    useFullName ? \" Trading Bot\" : \"\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\shared\\\\Logo.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\shared\\\\Logo.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Logo;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NoYXJlZC9Mb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDMEI7QUFDSztBQU8vQixNQUFNRSxPQUE0QjtRQUFDLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxJQUFJLEVBQUU7SUFDbEUscUJBQ0UsOERBQUNDO1FBQUlGLFdBQVcscURBQStELE9BQVZBOzswQkFDbkUsOERBQUNGLGtEQUFLQTtnQkFDSkssS0FBSTtnQkFDSkMsS0FBSTtnQkFDSkMsT0FBTztnQkFDUEMsUUFBUTtnQkFDUk4sV0FBVTs7Ozs7OzBCQUVaLDhEQUFDTzs7b0JBQUs7b0JBQU1OLGNBQWMsaUJBQWlCOzs7Ozs7Ozs7Ozs7O0FBR2pEO0tBYk1GO0FBZU4saUVBQWVBLElBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHNoYXJlZFxcTG9nby50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuXG5pbnRlcmZhY2UgTG9nb1Byb3BzIHtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICB1c2VGdWxsTmFtZT86IGJvb2xlYW47IC8vIEFkZGVkIHByb3AgdG8gY29udHJvbCB0ZXh0XG59XG5cbmNvbnN0IExvZ286IFJlYWN0LkZDPExvZ29Qcm9wcz4gPSAoeyBjbGFzc05hbWUsIHVzZUZ1bGxOYW1lID0gdHJ1ZSB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgPEltYWdlXG4gICAgICAgIHNyYz1cImh0dHBzOi8vaS5pbWd1ci5jb20vUTBIRGNNSC5wbmdcIlxuICAgICAgICBhbHQ9XCJQbHV0byBUcmFkaW5nIEJvdCBMb2dvXCJcbiAgICAgICAgd2lkdGg9ezI4fVxuICAgICAgICBoZWlnaHQ9ezI4fVxuICAgICAgICBjbGFzc05hbWU9XCJtci0yIHJvdW5kZWQtc21cIlxuICAgICAgLz5cbiAgICAgIDxzcGFuPlBsdXRve3VzZUZ1bGxOYW1lID8gXCIgVHJhZGluZyBCb3RcIiA6IFwiXCJ9PC9zcGFuPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTG9nbztcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkltYWdlIiwiTG9nbyIsImNsYXNzTmFtZSIsInVzZUZ1bGxOYW1lIiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/Logo.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = \"Label\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFDRTtBQU1oQyxNQUFNRSxzQkFBUUYsNkNBQWdCLE1BQzVCLFFBQTBCSTtRQUF6QixFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBTzt5QkFDdEIsOERBQUNDO1FBQ0NILEtBQUtBO1FBQ0xDLFdBQVdKLDhDQUFFQSxDQUNYLDhGQUNBSTtRQUVELEdBQUdDLEtBQUs7Ozs7Ozs7O0FBSWZKLE1BQU1NLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmludGVyZmFjZSBMYWJlbFByb3BzIGV4dGVuZHMgUmVhY3QuTGFiZWxIVE1MQXR0cmlidXRlczxIVE1MTGFiZWxFbGVtZW50PiB7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxMYWJlbEVsZW1lbnQsIExhYmVsUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxsYWJlbFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxuKTtcbkxhYmVsLmRpc3BsYXlOYW1lID0gXCJMYWJlbFwiO1xuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwibGFiZWwiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Start with false to ensure server-client consistency\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start with true to prevent hydration mismatch\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track hydration state\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Hydration effect - runs only on client after mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    console.log('🔐 Initializing authentication...');\n                    // Check existing authentication from localStorage\n                    const storedAuthStatus = localStorage.getItem('plutoAuth');\n                    const authToken = localStorage.getItem('plutoAuthToken');\n                    if (storedAuthStatus === 'true' && authToken) {\n                        console.log('🔐 Found existing auth token - authenticating user');\n                        setIsAuthenticated(true);\n                        setIsLoading(false);\n                        setIsHydrated(true);\n                        // Now that authentication is confirmed, check backend connection\n                        try {\n                            const { SessionManager } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_un\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_l\"), __webpack_require__.e(\"vendors-node_modules_n\"), __webpack_require__.e(\"vendors-node_modules_next_font_local_target_css-1d2c50c7\"), __webpack_require__.e(\"vendors-node_modules_t\"), __webpack_require__.e(\"default-_app-pages-browser_src_lib_session-manager_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\"));\n                            const sessionManager = SessionManager.getInstance();\n                            sessionManager.checkBackendConnectionWhenReady().catch({\n                                \"AuthProvider.useEffect.initializeAuth\": ()=>{\n                                // Silent fail - session manager will handle fallback\n                                }\n                            }[\"AuthProvider.useEffect.initializeAuth\"]);\n                        } catch (error) {\n                            console.error('🔐 Error loading SessionManager:', error);\n                        }\n                        return;\n                    }\n                    // If not authenticated, try auto-login for development\n                    console.log('🔐 No existing auth found - attempting auto-login');\n                    try {\n                        // Small delay to ensure backend is ready\n                        await new Promise({\n                            \"AuthProvider.useEffect.initializeAuth\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"AuthProvider.useEffect.initializeAuth\"]);\n                        const response = await fetch('http://localhost:5000/auth/login', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                username: 'testuser',\n                                password: 'password123'\n                            })\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            localStorage.setItem('plutoAuth', 'true');\n                            localStorage.setItem('plutoAuthToken', data.access_token);\n                            localStorage.setItem('plutoRefreshToken', data.refresh_token);\n                            localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                            setIsAuthenticated(true);\n                            console.log('🔐 Auto-logged in with test user for development');\n                            // Now that authentication is established, check backend connection\n                            try {\n                                const { SessionManager } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_un\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_l\"), __webpack_require__.e(\"vendors-node_modules_n\"), __webpack_require__.e(\"vendors-node_modules_next_font_local_target_css-1d2c50c7\"), __webpack_require__.e(\"vendors-node_modules_t\"), __webpack_require__.e(\"default-_app-pages-browser_src_lib_session-manager_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\"));\n                                const sessionManager = SessionManager.getInstance();\n                                sessionManager.checkBackendConnectionWhenReady().catch({\n                                    \"AuthProvider.useEffect.initializeAuth\": ()=>{\n                                    // Silent fail - session manager will handle fallback\n                                    }\n                                }[\"AuthProvider.useEffect.initializeAuth\"]);\n                            } catch (error) {\n                                console.error('🔐 Error loading SessionManager after auto-login:', error);\n                            }\n                        } else {\n                            console.log('🔐 Auto-login failed - server response not ok:', response.status);\n                        }\n                    } catch (error) {\n                        console.log('🔐 Auto-login failed - network error:', error);\n                    }\n                    console.log('🔐 Authentication initialization complete');\n                    setIsLoading(false);\n                    setIsHydrated(true);\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            // Initialize authentication\n            initializeAuth();\n            // Failsafe timeout to prevent infinite loading\n            const timeoutId = setTimeout({\n                \"AuthProvider.useEffect.timeoutId\": ()=>{\n                    console.warn('🔐 Authentication initialization timeout - forcing completion');\n                    setIsLoading(false);\n                    setIsHydrated(true);\n                }\n            }[\"AuthProvider.useEffect.timeoutId\"], 10000); // 10 second timeout\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Only run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Route based on authentication status\n            if (isAuthenticated && (pathname === '/login' || pathname === '/')) {\n                router.replace('/dashboard');\n            } else if (!isAuthenticated && pathname !== '/login' && pathname !== '/') {\n                router.replace('/login');\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        pathname,\n        router\n    ]);\n    const login = async (username, password)=>{\n        setIsLoading(true);\n        try {\n            const success = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(username, password);\n            if (success) {\n                setIsAuthenticated(true);\n                router.push('/dashboard');\n                return true;\n            }\n            setIsAuthenticated(false);\n            return false;\n        } catch (error) {\n            console.error('Login failed:', error);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setIsAuthenticated(false);\n            router.push('/login');\n        }\n    };\n    // No loading screen needed since we initialize immediately from localStorage\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"WOFDDoScdEyRYXUfzRRWUPu8dM8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiApi: () => (/* binding */ aiApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   sessionApi: () => (/* binding */ sessionApi),\n/* harmony export */   tradingApi: () => (/* binding */ tradingApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n// API utility functions for backend communication\n// Configure base URL - can be overridden if needed for different environments\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\nconsole.log('API Base URL:', API_BASE_URL); // Log the URL to help with debugging\n// Generic fetch wrapper with error handling\nasync function fetchWithAuth(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Add auth token if available\n    const token = localStorage.getItem('plutoAuthToken');\n    const headers = {\n        'Content-Type': 'application/json',\n        ...token ? {\n            'Authorization': \"Bearer \".concat(token)\n        } : {},\n        ...options.headers\n    };\n    try {\n        // Add timeout to fetch request\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout\n        const response = await fetch(url, {\n            ...options,\n            headers,\n            signal: controller.signal\n        }).finally(()=>clearTimeout(timeoutId));\n        // Handle unauthorized responses\n        if (response.status === 401) {\n            localStorage.removeItem('plutoAuth');\n            localStorage.removeItem('plutoAuthToken');\n            localStorage.removeItem('plutoUser');\n            if (true) {\n                window.location.href = '/login';\n            }\n            throw new Error('Authentication expired. Please login again.');\n        }\n        // Parse JSON response (safely)\n        let data;\n        const contentType = response.headers.get('content-type');\n        if (contentType && contentType.includes('application/json')) {\n            data = await response.json();\n        } else {\n            // Handle non-JSON responses\n            const text = await response.text();\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                // If it's not parseable JSON, use the text as is\n                data = {\n                    message: text\n                };\n            }\n        }\n        // Handle API errors\n        if (!response.ok) {\n            // Provide more detailed error information\n            const errorMessage = (data === null || data === void 0 ? void 0 : data.error) || (data === null || data === void 0 ? void 0 : data.message) || \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            // Handle authentication errors gracefully - return null instead of throwing\n            if (response.status === 401 || response.status === 422) {\n                console.log('🔐 API authentication required - returning null:', {\n                    status: response.status,\n                    url: url.replace(API_BASE_URL, '')\n                });\n                return null; // Return null instead of throwing for auth errors\n            } else {\n                console.error('API error response:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    data: data || 'No response data',\n                    url: url\n                });\n                throw new Error(errorMessage);\n            }\n        }\n        return data;\n    } catch (error) {\n        // Handle network errors specifically\n        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n            console.error('Network error - Is the backend server running?:', error);\n            throw new Error('Cannot connect to server. Please check if the backend is running.');\n        }\n        // Handle timeout\n        if (error.name === 'AbortError') {\n            console.error('Request timeout:', error);\n            throw new Error('Request timed out. Server may be unavailable.');\n        }\n        console.error('API request failed:', error);\n        throw error;\n    }\n}\n// Auth API functions\nconst authApi = {\n    login: async (username, password)=>{\n        try {\n            // Use retry mechanism for login attempts\n            const data = await retryWithBackoff(async ()=>{\n                return await fetchWithAuth('/auth/login', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        username,\n                        password\n                    })\n                });\n            });\n            // The backend returns access_token in the response\n            if (data && data.access_token) {\n                localStorage.setItem('plutoAuthToken', data.access_token);\n                localStorage.setItem('plutoAuth', 'true');\n                // Also store user data if available\n                if (data.user) {\n                    localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                }\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login API error:', error);\n            return false;\n        }\n    },\n    register: async (username, password, email)=>{\n        // Use retry mechanism for registration attempts\n        return retryWithBackoff(async ()=>{\n            return await fetchWithAuth('/auth/register', {\n                method: 'POST',\n                body: JSON.stringify({\n                    username,\n                    password,\n                    email\n                })\n            });\n        });\n    },\n    logout: async ()=>{\n        localStorage.removeItem('plutoAuth');\n        localStorage.removeItem('plutoAuthToken');\n        localStorage.removeItem('plutoUser');\n        return true;\n    }\n};\n// Trading API functions\nconst tradingApi = {\n    getConfig: async (configId)=>{\n        return fetchWithAuth(configId ? \"/trading/config/\".concat(configId) : '/trading/config');\n    },\n    saveConfig: async (config)=>{\n        return fetchWithAuth('/trading/config', {\n            method: 'POST',\n            body: JSON.stringify(config)\n        });\n    },\n    updateConfig: async (configId, config)=>{\n        return fetchWithAuth(\"/trading/config/\".concat(configId), {\n            method: 'PUT',\n            body: JSON.stringify(config)\n        });\n    },\n    startBot: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/start/\".concat(configId), {\n            method: 'POST'\n        });\n    },\n    stopBot: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/stop/\".concat(configId), {\n            method: 'POST'\n        });\n    },\n    getBotStatus: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/status/\".concat(configId));\n    },\n    getTradeHistory: async (configId)=>{\n        const params = configId ? \"?configId=\".concat(configId) : '';\n        return fetchWithAuth(\"/trading/history\".concat(params));\n    },\n    getBalances: async ()=>{\n        return fetchWithAuth('/trading/balances');\n    },\n    getMarketPrice: async (symbol)=>{\n        return fetchWithAuth(\"/trading/market-data/\".concat(symbol));\n    },\n    getTradingPairs: async function() {\n        let exchange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'binance';\n        return fetchWithAuth(\"/trading/exchange/trading-pairs?exchange=\".concat(exchange));\n    },\n    getCryptocurrencies: async function() {\n        let exchange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'binance';\n        return fetchWithAuth(\"/trading/exchange/cryptocurrencies?exchange=\".concat(exchange));\n    }\n};\n// User API functions\nconst userApi = {\n    getProfile: async ()=>{\n        return fetchWithAuth('/user/profile');\n    },\n    updateProfile: async (profileData)=>{\n        return fetchWithAuth('/user/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    },\n    saveApiKeys: async (exchangeData)=>{\n        return fetchWithAuth('/user/apikeys', {\n            method: 'POST',\n            body: JSON.stringify(exchangeData)\n        });\n    },\n    getApiKeys: async ()=>{\n        return fetchWithAuth('/user/apikeys');\n    }\n};\n// AI API functions\nconst aiApi = {\n    getTradingSuggestion: async (data)=>{\n        return fetchWithAuth('/ai/trading-suggestion', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n};\n// Session API functions\nconst sessionApi = {\n    getAllSessions: async function() {\n        let includeInactive = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return fetchWithAuth(\"/sessions/?include_inactive=\".concat(includeInactive));\n    },\n    createSession: async (sessionData)=>{\n        return fetchWithAuth('/sessions/', {\n            method: 'POST',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    getSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId));\n    },\n    updateSession: async (sessionId, sessionData)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId), {\n            method: 'PUT',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    deleteSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId), {\n            method: 'DELETE'\n        });\n    },\n    activateSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId, \"/activate\"), {\n            method: 'POST'\n        });\n    },\n    getSessionHistory: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId, \"/history\"));\n    },\n    getActiveSession: async ()=>{\n        return fetchWithAuth('/sessions/active');\n    }\n};\n// Add a retry mechanism for transient connection issues\nconst retryWithBackoff = async function(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    let retries = 0;\n    const execute = async ()=>{\n        try {\n            return await fn();\n        } catch (error) {\n            // Only retry on network errors, not on 4xx/5xx responses\n            if (error instanceof TypeError && error.message.includes('Failed to fetch') || error.name === 'AbortError') {\n                if (retries < maxRetries) {\n                    const delay = Math.pow(2, retries) * 500; // Exponential backoff\n                    console.log(\"Retrying after \".concat(delay, \"ms (attempt \").concat(retries + 1, \"/\").concat(maxRetries, \")...\"));\n                    retries++;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return execute();\n                }\n            }\n            throw error;\n        }\n    };\n    return execute();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_c","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_r","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_c","vendors-node_modules_next_dist_l","vendors-node_modules_n","vendors-node_modules_next_font_local_target_css-1d2c50c7","vendors-node_modules_t","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-45a3a8","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);