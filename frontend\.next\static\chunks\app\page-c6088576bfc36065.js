(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,O:()=>u});var r=a(5155),s=a(2115),n=a(5695),o=a(5731),c=a(4553);let l=(0,s.createContext)(void 0),u=e=>{let{children:t}=e,[u,i]=(0,s.useState)(!1),[h,d]=(0,s.useState)(!0),[g,f]=(0,s.useState)(!1),m=(0,n.useRouter)(),p=(0,n.usePathname)();(0,s.useEffect)(()=>{(async()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");if("true"===e&&t){i(!0),d(!1),f(!0);try{c.SessionManager.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}return}try{await new Promise(e=>setTimeout(e,1e3));let e=await fetch("http://localhost:5000/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"testuser",password:"password123"})});if(e.ok){let t=await e.json();localStorage.setItem("plutoAuth","true"),localStorage.setItem("plutoAuthToken",t.access_token),localStorage.setItem("plutoRefreshToken",t.refresh_token),localStorage.setItem("plutoUser",JSON.stringify(t.user)),i(!0);try{let{SessionManager:e}=await Promise.resolve().then(a.bind(a,4553));e.getInstance().checkBackendConnectionWhenReady().catch(()=>{})}catch(e){}}}catch(e){}d(!1),f(!0)})();let e=setTimeout(()=>{d(!1),f(!0)},1e4);return()=>clearTimeout(e)},[]),(0,s.useEffect)(()=>{u&&("/login"===p||"/"===p)?m.replace("/dashboard"):u||"/login"===p||"/"===p||m.replace("/login")},[u,p,m]);let y=async(e,t)=>{d(!0);try{if(await o.ZQ.login(e,t))return i(!0),m.push("/dashboard"),!0;return i(!1),!1}catch(e){return i(!1),!1}finally{d(!1)}},k=async()=>{try{await o.ZQ.logout()}catch(e){}finally{i(!1),m.push("/login")}};return(0,r.jsx)(l.Provider,{value:{isAuthenticated:u,login:y,logout:k,isLoading:h},children:t})},i=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3792:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(5155),s=a(2115),n=a(5695),o=a(283),c=a(172);function l(){let e=(0,n.useRouter)(),{isAuthenticated:t,isLoading:a}=(0,o.A)();return(0,s.useEffect)(()=>{a||e.replace(t?"/dashboard":"/login")},[t,a,e]),(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,r.jsx)(c.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting..."})]})}},9225:(e,t,a)=>{Promise.resolve().then(a.bind(a,3792))}},e=>{var t=t=>e(e.s=t);e.O(0,[563,127,432,979,899,744,33,322,592,940,652,30,465,173,960,219,553,358],()=>t(9225)),_N_E=e.O()}]);