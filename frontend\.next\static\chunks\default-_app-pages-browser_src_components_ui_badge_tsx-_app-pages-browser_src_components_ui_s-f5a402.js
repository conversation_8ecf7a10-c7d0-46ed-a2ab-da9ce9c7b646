"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["default-_app-pages-browser_src_components_ui_badge_tsx-_app-pages-browser_src_components_ui_s-f5a402"],{

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst badgeVariants = {\n    variant: {\n        default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\"\n    }\n};\nconst Badge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant = \"default\", ...props } = param;\n    const variantClasses = badgeVariants.variant[variant] || badgeVariants.variant.default;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", variantClasses, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 22,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Badge;\nBadge.displayName = \"Badge\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Badge$React.forwardRef\");\n$RefreshReg$(_c1, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/select.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/select.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectTrigger,SelectContent,SelectItem,SelectValue auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n\n\n\nconst SelectContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    value: '',\n    onValueChange: ()=>{},\n    open: false,\n    setOpen: ()=>{}\n});\nconst Select = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { children, value, onValueChange, defaultValue, ...props } = param;\n    _s();\n    const [internalValue, setInternalValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(value || defaultValue || '');\n    const [open, setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Select.useEffect\": ()=>{\n            if (value !== undefined) {\n                setInternalValue(value);\n            }\n        }\n    }[\"Select.useEffect\"], [\n        value\n    ]);\n    const handleValueChange = (newValue)=>{\n        if (value === undefined) {\n            setInternalValue(newValue);\n        }\n        onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(newValue);\n        setOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectContext.Provider, {\n        value: {\n            value: internalValue,\n            onValueChange: handleValueChange,\n            open,\n            setOpen\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: \"relative\",\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 69,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 63,\n        columnNumber: 7\n    }, undefined);\n}, \"lSUTN4XRulCUNUFGOoILJ6/ZSw4=\")), \"lSUTN4XRulCUNUFGOoILJ6/ZSw4=\");\n_c1 = Select;\nSelect.displayName = \"Select\";\nconst SelectTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = _s1((param, ref)=>{\n    let { className, children, ...props } = param;\n    _s1();\n    const { open, setOpen } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        onClick: ()=>setOpen(!open),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4 opacity-50\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 83,\n        columnNumber: 7\n    }, undefined);\n}, \"VpbSSxC/M+z7dVARcY658GIy3+c=\")), \"VpbSSxC/M+z7dVARcY658GIy3+c=\");\n_c3 = SelectTrigger;\nSelectTrigger.displayName = \"SelectTrigger\";\nconst SelectContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s2((param, ref)=>{\n    let { className, children, ...props } = param;\n    _s2();\n    const { open } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    if (!open) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute top-full z-50 w-full rounded-md border bg-popover p-1 text-popover-foreground shadow-md\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 107,\n        columnNumber: 7\n    }, undefined);\n}, \"wYP5SOVhXN0aIMoJb6iaO4GLNnk=\")), \"wYP5SOVhXN0aIMoJb6iaO4GLNnk=\");\n_c5 = SelectContent;\nSelectContent.displayName = \"SelectContent\";\nconst SelectItem = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = _s3((param, ref)=>{\n    let { className, children, value, ...props } = param;\n    _s3();\n    const { onValueChange } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\", className),\n        onClick: ()=>onValueChange(value),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 127,\n        columnNumber: 7\n    }, undefined);\n}, \"UXUFR3e0lgZn85KZNGvOuyNsyrg=\")), \"UXUFR3e0lgZn85KZNGvOuyNsyrg=\");\n_c7 = SelectItem;\nSelectItem.displayName = \"SelectItem\";\nconst SelectValue = /*#__PURE__*/ _s4(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = _s4((param, ref)=>{\n    let { placeholder, ...props } = param;\n    _s4();\n    const { value } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SelectContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: ref,\n        ...props,\n        children: value || placeholder\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 148,\n        columnNumber: 7\n    }, undefined);\n}, \"n0+zAVIeEUubPncMMcj8hAd8Nyo=\")), \"n0+zAVIeEUubPncMMcj8hAd8Nyo=\");\n_c9 = SelectValue;\nSelectValue.displayName = \"SelectValue\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Select$React.forwardRef\");\n$RefreshReg$(_c1, \"Select\");\n$RefreshReg$(_c2, \"SelectTrigger$React.forwardRef\");\n$RefreshReg$(_c3, \"SelectTrigger\");\n$RefreshReg$(_c4, \"SelectContent$React.forwardRef\");\n$RefreshReg$(_c5, \"SelectContent\");\n$RefreshReg$(_c6, \"SelectItem$React.forwardRef\");\n$RefreshReg$(_c7, \"SelectItem\");\n$RefreshReg$(_c8, \"SelectValue$React.forwardRef\");\n$RefreshReg$(_c9, \"SelectValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/select.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst TabsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    activeTab: '',\n    setActiveTab: ()=>{}\n});\nconst Tabs = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { defaultValue = '', value, onValueChange, children, className, ...props } = param;\n    _s();\n    const [activeTab, setActiveTab] = react__WEBPACK_IMPORTED_MODULE_1__.useState(value || defaultValue);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Tabs.useEffect\": ()=>{\n            if (value !== undefined) {\n                setActiveTab(value);\n            }\n        }\n    }[\"Tabs.useEffect\"], [\n        value\n    ]);\n    const handleTabChange = (newValue)=>{\n        if (value === undefined) {\n            setActiveTab(newValue);\n        }\n        onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(newValue);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContext.Provider, {\n        value: {\n            activeTab,\n            setActiveTab: handleTabChange\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: className,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n            lineNumber: 59,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 58,\n        columnNumber: 7\n    }, undefined);\n}, \"k0FhWJTCExh/hFGuT1R1UiXiugA=\")), \"k0FhWJTCExh/hFGuT1R1UiXiugA=\");\n_c1 = Tabs;\nTabs.displayName = \"Tabs\";\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n});\n_c3 = TabsList;\nTabsList.displayName = \"TabsList\";\nconst TabsTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s1((param, ref)=>{\n    let { className, value, children, onClick, ...props } = param;\n    _s1();\n    const { activeTab, setActiveTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    const isActive = activeTab === value;\n    const handleClick = ()=>{\n        setActiveTab(value);\n        onClick === null || onClick === void 0 ? void 0 : onClick();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", isActive && \"bg-background text-foreground shadow-sm\", className),\n        onClick: handleClick,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 95,\n        columnNumber: 7\n    }, undefined);\n}, \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\")), \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\");\n_c5 = TabsTrigger;\nTabsTrigger.displayName = \"TabsTrigger\";\nconst TabsContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = _s2((param, ref)=>{\n    let { className, value, children, ...props } = param;\n    _s2();\n    const { activeTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    if (activeTab !== value) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 121,\n        columnNumber: 7\n    }, undefined);\n}, \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\")), \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\");\n_c7 = TabsContent;\nTabsContent.displayName = \"TabsContent\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Tabs$React.forwardRef\");\n$RefreshReg$(_c1, \"Tabs\");\n$RefreshReg$(_c2, \"TabsList$React.forwardRef\");\n$RefreshReg$(_c3, \"TabsList\");\n$RefreshReg$(_c4, \"TabsTrigger$React.forwardRef\");\n$RefreshReg$(_c5, \"TabsTrigger\");\n$RefreshReg$(_c6, \"TabsContent$React.forwardRef\");\n$RefreshReg$(_c7, \"TabsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tabs.tsx\n"));

/***/ })

}]);