"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["default-_app-pages-browser_src_contexts_TradingContext_tsx"],{

/***/ "(app-pages-browser)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider(param) {\n    let { children } = param;\n    _s();\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast]\": (toast)=>{\n            const id = Math.random().toString(36).substr(2, 9);\n            var _toast_duration;\n            const newToast = {\n                ...toast,\n                id,\n                duration: (_toast_duration = toast.duration) !== null && _toast_duration !== void 0 ? _toast_duration : 2000\n            };\n            setToasts({\n                \"ToastProvider.useCallback[toast]\": (prev)=>[\n                        ...prev,\n                        newToast\n                    ]\n            }[\"ToastProvider.useCallback[toast]\"]);\n            // Auto dismiss after duration\n            setTimeout({\n                \"ToastProvider.useCallback[toast]\": ()=>{\n                    dismiss(id);\n                }\n            }[\"ToastProvider.useCallback[toast]\"], newToast.duration);\n        }\n    }[\"ToastProvider.useCallback[toast]\"], []);\n    const dismiss = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[dismiss]\": (toastId)=>{\n            setToasts({\n                \"ToastProvider.useCallback[dismiss]\": (prev)=>prev.filter({\n                        \"ToastProvider.useCallback[dismiss]\": (t)=>t.id !== toastId\n                    }[\"ToastProvider.useCallback[dismiss]\"])\n            }[\"ToastProvider.useCallback[dismiss]\"]);\n        }\n    }[\"ToastProvider.useCallback[dismiss]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            toast,\n            dismiss\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(ToastProvider, \"Dfj/Dm7hf738sfBZqVXle+2Bqxc=\");\n_c = ToastProvider;\nfunction useToast() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error('useToast must be used within a ToastProvider');\n    }\n    return context;\n}\n_s1(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction ToastContainer() {\n    _s2();\n    const { toasts, dismiss } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast,\n                onDismiss: dismiss\n            }, toast.id, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s2(ToastContainer, \"z212JZX1cpfWKIcwkgC+EGizOlw=\", false, function() {\n    return [\n        useToast\n    ];\n});\n_c1 = ToastContainer;\nfunction ToastItem(param) {\n    let { toast, onDismiss } = param;\n    _s3();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToastItem.useEffect\": ()=>{\n            // Trigger animation\n            const timer = setTimeout({\n                \"ToastItem.useEffect.timer\": ()=>setIsVisible(true)\n            }[\"ToastItem.useEffect.timer\"], 10);\n            return ({\n                \"ToastItem.useEffect\": ()=>clearTimeout(timer)\n            })[\"ToastItem.useEffect\"];\n        }\n    }[\"ToastItem.useEffect\"], []);\n    const handleDismiss = ()=>{\n        setIsVisible(false);\n        setTimeout(()=>onDismiss(toast.id), 150);\n    };\n    const getIcon = ()=>{\n        switch(toast.type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 16\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBorderColor = ()=>{\n        switch(toast.type){\n            case 'success':\n                return 'border-green-200';\n            case 'error':\n                return 'border-red-200';\n            case 'warning':\n                return 'border-yellow-200';\n            default:\n                return 'border-blue-200';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-start gap-3 p-4 bg-white border-2 rounded-lg shadow-lg transition-all duration-150 ease-out min-w-[300px] max-w-[400px]\", getBorderColor(), isVisible ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"),\n        children: [\n            getIcon(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-1\",\n                children: [\n                    toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-sm text-gray-900\",\n                        children: toast.title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    toast.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: toast.description\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleDismiss,\n                className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s3(ToastItem, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c2 = ToastItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ToastProvider\");\n$RefreshReg$(_c1, \"ToastContainer\");\n$RefreshReg$(_c2, \"ToastItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/toast.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For StablecoinSwap mode, we need to calculate the ratio via stablecoin prices\n        if (config.tradingMode === \"StablecoinSwap\" && config.preferredStablecoin) {\n            try {\n                // Get both crypto prices in terms of the preferred stablecoin\n                const crypto1StablecoinPrice = await getStablecoinExchangeRate(config.crypto1, config.preferredStablecoin);\n                const crypto2StablecoinPrice = await getStablecoinExchangeRate(config.crypto2, config.preferredStablecoin);\n                if (crypto1StablecoinPrice > 0 && crypto2StablecoinPrice > 0) {\n                    // Calculate Crypto1/Crypto2 ratio via stablecoin\n                    const ratio = crypto1StablecoinPrice / crypto2StablecoinPrice;\n                    console.log(\"✅ StablecoinSwap price via \".concat(config.preferredStablecoin, \": \").concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio.toFixed(6)));\n                    console.log(\"   \".concat(config.crypto1, \"/\").concat(config.preferredStablecoin, \" = \").concat(crypto1StablecoinPrice));\n                    console.log(\"   \".concat(config.crypto2, \"/\").concat(config.preferredStablecoin, \" = \").concat(crypto2StablecoinPrice));\n                    return ratio;\n                }\n            } catch (stablecoinError) {\n                console.warn('Stablecoin price calculation failed, falling back to direct pair...', stablecoinError);\n            }\n        }\n        // For SimpleSpot mode or fallback: try direct pair fetching\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            // For both modes, we want Crypto1/Crypto2 ratio\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (crypto.toUpperCase() === stablecoin.toUpperCase()) return 1.0;\n        // First try Binance API for direct pair\n        const binanceSymbol = \"\".concat(crypto.toUpperCase()).concat(stablecoin.toUpperCase());\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(binanceSymbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Stablecoin rate from Binance: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn(\"Binance API failed for \".concat(binanceSymbol, \", trying CoinGecko...\"));\n        }\n        // Fallback to CoinGecko API\n        const cryptoId = getCoinGeckoId(crypto);\n        const stablecoinId = getCoinGeckoId(stablecoin);\n        if (cryptoId && stablecoinId) {\n            // Try direct conversion\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId];\n                if (rate > 0) {\n                    console.log(\"✅ Stablecoin rate from CoinGecko: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Final fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"⚠️ Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 108000,\n        'ETH': 2100,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    let basePrice;\n    if (config.tradingMode === \"StablecoinSwap\") {\n        // For stablecoin swap mode: Crypto1/Crypto2 = Current market Price\n        // This shows how many units of Crypto2 equals 1 unit of Crypto1\n        basePrice = crypto1USDPrice / crypto2USDPrice;\n        console.log(\"\\uD83D\\uDCCA StablecoinSwap price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    } else {\n        // Simple Spot mode: Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n        basePrice = crypto1USDPrice / crypto2USDPrice;\n        console.log(\"\\uD83D\\uDCCA SimpleSpot price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    }\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    return finalPrice;\n};\n// Duplicate TradingAction type removed - using the one defined above\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\n// Initial state with default balances (will be updated by global balances later)\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst GLOBAL_BALANCE_KEY = 'pluto_global_balances';\nconst BOT_STATUS_KEY = 'pluto_bot_status';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\n// Global balance persistence functions\nconst saveGlobalBalances = (crypto1Balance, crypto2Balance, stablecoinBalance)=>{\n    try {\n        if (true) {\n            const balances = {\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(GLOBAL_BALANCE_KEY, JSON.stringify(balances));\n            console.log('💰 Global balances saved:', balances);\n        }\n    } catch (error) {\n        console.error('Failed to save global balances:', error);\n    }\n};\nconst loadGlobalBalances = ()=>{\n    try {\n        if (true) {\n            const savedBalances = localStorage.getItem(GLOBAL_BALANCE_KEY);\n            if (savedBalances) {\n                const parsed = JSON.parse(savedBalances);\n                console.log('💰 Global balances loaded:', parsed);\n                return {\n                    crypto1Balance: parsed.crypto1Balance || 10,\n                    crypto2Balance: parsed.crypto2Balance || 100000,\n                    stablecoinBalance: parsed.stablecoinBalance || 0\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load global balances:', error);\n    }\n    return {\n        crypto1Balance: 10,\n        crypto2Balance: 100000,\n        stablecoinBalance: 0\n    };\n};\nconst saveBotStatus = (status)=>{\n    if (true) {\n        try {\n            localStorage.setItem(BOT_STATUS_KEY, status);\n        } catch (error) {\n            console.error('Failed to save bot status to localStorage:', error);\n        }\n    }\n};\nconst loadBotStatus = ()=>{\n    if (true) {\n        try {\n            const saved = localStorage.getItem(BOT_STATUS_KEY);\n            if (saved && (saved === 'Stopped' || saved === 'WarmingUp' || saved === 'Running')) {\n                return saved;\n            }\n        } catch (error) {\n            console.error('Failed to load bot status from localStorage:', error);\n        }\n    }\n    // Return default status if loading fails\n    return 'Stopped';\n};\n// Create initial state with global balances - called after loadGlobalBalances is defined\nconst createInitialTradingState = ()=>{\n    const globalBalances = loadGlobalBalances();\n    return {\n        config: initialBaseConfig,\n        targetPriceRows: [],\n        orderHistory: [],\n        appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n        currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n        botSystemStatus: 'Stopped',\n        crypto1Balance: globalBalances.crypto1Balance,\n        crypto2Balance: globalBalances.crypto2Balance,\n        stablecoinBalance: globalBalances.stablecoinBalance,\n        backendStatus: 'unknown'\n    };\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2\n            };\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            saveBotStatus('WarmingUp');\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            saveBotStatus('Running');\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            saveBotStatus('Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            saveBotStatus('Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            saveBotStatus('Stopped');\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Toast hook for notifications\n    const { toast } = (0,_components_ui_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        console.log('🔄 TradingContext: Initializing state...');\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with fresh state but keeping global balances');\n                // Clear the URL parameter to avoid confusion - use setTimeout to avoid render issues\n                setTimeout(()=>{\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }, 0);\n                return createInitialTradingState(); // Use fresh state with current global balances\n            }\n        }\n        // Try to get session manager, but handle case where it might not be ready yet\n        let sessionManager;\n        let currentSessionId;\n        try {\n            sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            currentSessionId = sessionManager.getCurrentSessionId();\n            console.log('🔄 TradingContext: SessionManager available, current session:', currentSessionId);\n        } catch (error) {\n            console.log('🔄 TradingContext: SessionManager not ready yet, falling back to localStorage');\n            sessionManager = null;\n            currentSessionId = null;\n        }\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 No current session - starting with fresh state but keeping global balances');\n            return createInitialTradingState(); // Use fresh state with current global balances\n        }\n        // If we have a session and session manager is available, try to load it\n        if (sessionManager && currentSessionId) {\n            try {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession) {\n                    const savedBotStatus = loadBotStatus();\n                    console.log('🔄 Restoring session from session manager:', currentSession.name);\n                    console.log('🔄 Bot status restored to:', savedBotStatus);\n                    return {\n                        ...initialTradingState,\n                        config: currentSession.config,\n                        targetPriceRows: currentSession.targetPriceRows,\n                        orderHistory: currentSession.orderHistory,\n                        currentMarketPrice: currentSession.currentMarketPrice,\n                        crypto1Balance: currentSession.crypto1Balance,\n                        crypto2Balance: currentSession.crypto2Balance,\n                        stablecoinBalance: currentSession.stablecoinBalance,\n                        botSystemStatus: savedBotStatus // Restore previous bot status to maintain continuity\n                    };\n                }\n            } catch (error) {\n                console.error('🔄 Error loading session from session manager:', error);\n            }\n        }\n        // Fallback to localStorage if session manager fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            const savedBotStatus = loadBotStatus();\n            console.log('🔄 Restoring from localStorage backup');\n            console.log('🔄 Bot status restored to:', savedBotStatus);\n            const globalBalances = loadGlobalBalances();\n            return {\n                ...createInitialTradingState(),\n                ...savedState,\n                // Always use current global balances\n                crypto1Balance: globalBalances.crypto1Balance,\n                crypto2Balance: globalBalances.crypto2Balance,\n                stablecoinBalance: globalBalances.stablecoinBalance,\n                // Restore previous bot status to maintain continuity\n                botSystemStatus: savedBotStatus\n            };\n        }\n        console.log('⚠️ No session data found, starting fresh with global balances');\n        return createInitialTradingState();\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Telegram error notification function - defined early to avoid initialization issues\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage, context)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping error notification');\n                    return;\n                }\n                // Format error message with emoji and structure\n                let message = \"⚠️ <b>Error Alert</b>\\n\\n\";\n                message += \"<b>Type:</b> \".concat(errorType, \"\\n\");\n                message += \"<b>Error:</b> \".concat(errorMessage, \"\\n\");\n                if (context) {\n                    message += \"<b>Context:</b> \".concat(context, \"\\n\");\n                }\n                if (state.config.crypto1 && state.config.crypto2) {\n                    message += \"<b>Trading Pair:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"\\n\");\n                }\n                message += \"<b>Time:</b> \".concat(new Date().toLocaleString(), \"\\n\");\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram error notification:', response.statusText);\n                } else {\n                    console.log('✅ Telegram error notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                // Send error notification for price fetching failures\n                sendTelegramErrorNotification('Price Fetch Error', \"Failed to fetch market price: \".concat(error instanceof Error ? error.message : 'Unknown error'), \"Trading pair: \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2));\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch,\n        sendTelegramErrorNotification\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    // Audio queue system for handling multiple simultaneous sound requests\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const isPlayingAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingAudioRef.current || audioQueueRef.current.length === 0 || !audioRef.current) return;\n            isPlayingAudioRef.current = true;\n            const { soundKey, sessionId } = audioQueueRef.current.shift();\n            try {\n                // Get session-specific alarm settings\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                const targetSessionId = sessionId || sessionManager.getCurrentSessionId();\n                const targetSession = targetSessionId ? sessionManager.loadSession(targetSessionId) : null;\n                const alarmSettings = (targetSession === null || targetSession === void 0 ? void 0 : targetSession.alarmSettings) || state.appSettings;\n                if (!alarmSettings.soundAlertsEnabled) {\n                    return; // Skip if sound is disabled\n                }\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    // Validate and fix sound path if needed\n                    let validSoundPath = soundPath;\n                    // Fix old /sounds/ paths to /ringtones/\n                    if (soundPath.startsWith('/sounds/')) {\n                        validSoundPath = soundPath.replace('/sounds/', '/ringtones/');\n                        console.warn(\"Fixed deprecated sound path: \".concat(soundPath, \" -> \").concat(validSoundPath));\n                    }\n                    // Fallback to default sound if path doesn't exist in ringtones\n                    if (validSoundPath.startsWith('/ringtones/') && !validSoundPath.includes('data:audio')) {\n                        const knownRingtones = [\n                            'cheer.wav',\n                            'chest1.wav',\n                            'chime2.wav',\n                            'bells.wav',\n                            'bird1.wav',\n                            'bird7.wav',\n                            'sparrow1.wav',\n                            'space_bells4a.wav',\n                            'sanctuary1.wav',\n                            'marble1.wav',\n                            'foundry2.wav',\n                            'G_hades_curse.wav',\n                            'G_hades_demat.wav',\n                            'G_hades_sanctify.wav',\n                            'dark2.wav',\n                            'Satyr_atk4.wav',\n                            'S_mon1.mp3',\n                            'S_mon2.mp3',\n                            'wolf4.wav',\n                            'goatherd1.wav',\n                            'tax3.wav',\n                            'G_hades_mat.wav'\n                        ];\n                        const filename = validSoundPath.split('/').pop();\n                        if (filename && !knownRingtones.includes(filename)) {\n                            validSoundPath = '/ringtones/cheer.wav'; // Default fallback\n                            console.warn(\"Unknown ringtone file: \".concat(soundPath, \", using default: \").concat(validSoundPath));\n                        }\n                    }\n                    // Stop any currently playing audio to prevent conflicts\n                    audioRef.current.pause();\n                    audioRef.current.currentTime = 0;\n                    audioRef.current.src = validSoundPath;\n                    // Wait for audio to load before playing\n                    await new Promise({\n                        \"TradingProvider.useCallback[processAudioQueue]\": (resolve, reject)=>{\n                            var _audioRef_current, _audioRef_current1, _audioRef_current2;\n                            const onCanPlay = {\n                                \"TradingProvider.useCallback[processAudioQueue].onCanPlay\": ()=>{\n                                    var _audioRef_current, _audioRef_current1;\n                                    (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.removeEventListener('canplaythrough', onCanPlay);\n                                    (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.removeEventListener('error', onError);\n                                    resolve();\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue].onCanPlay\"];\n                            const onError = {\n                                \"TradingProvider.useCallback[processAudioQueue].onError\": (e)=>{\n                                    var _audioRef_current, _audioRef_current1;\n                                    (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.removeEventListener('canplaythrough', onCanPlay);\n                                    (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.removeEventListener('error', onError);\n                                    reject(e);\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue].onError\"];\n                            (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.addEventListener('canplaythrough', onCanPlay, {\n                                once: true\n                            });\n                            (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.addEventListener('error', onError, {\n                                once: true\n                            });\n                            (_audioRef_current2 = audioRef.current) === null || _audioRef_current2 === void 0 ? void 0 : _audioRef_current2.load();\n                        }\n                    }[\"TradingProvider.useCallback[processAudioQueue]\"]);\n                    // Play the sound with timeout\n                    await audioRef.current.play();\n                    // Set timeout to stop audio after 2 seconds\n                    setTimeout({\n                        \"TradingProvider.useCallback[processAudioQueue]\": ()=>{\n                            if (audioRef.current) {\n                                audioRef.current.pause();\n                                audioRef.current.currentTime = 0;\n                            }\n                        }\n                    }[\"TradingProvider.useCallback[processAudioQueue]\"], 2000);\n                }\n            } catch (error) {\n                // Handle audio errors gracefully\n                if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('interrupted') || error.message.includes('play() request'))) {\n                    console.debug('Audio play interrupted (non-critical):', error.message);\n                } else {\n                    console.error(\"Error playing sound:\", error);\n                    // Try fallback sound for critical errors\n                    if (audioRef.current && audioRef.current.src !== '/ringtones/cheer.wav') {\n                        try {\n                            audioRef.current.src = '/ringtones/cheer.wav';\n                            await audioRef.current.play();\n                            setTimeout({\n                                \"TradingProvider.useCallback[processAudioQueue]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0;\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue]\"], 2000);\n                        } catch (fallbackError) {\n                            console.error(\"Fallback sound also failed:\", fallbackError);\n                        }\n                    }\n                }\n            } finally{\n                isPlayingAudioRef.current = false;\n                // Process next item in queue after a short delay\n                setTimeout({\n                    \"TradingProvider.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"TradingProvider.useCallback[processAudioQueue]\"], 150);\n            }\n        }\n    }[\"TradingProvider.useCallback[processAudioQueue]\"], [\n        state.appSettings\n    ]);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey, sessionId)=>{\n            // Add to queue instead of playing immediately\n            audioQueueRef.current.push({\n                soundKey,\n                sessionId\n            });\n            processAudioQueue();\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        processAudioQueue\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                playSound('soundOrderExecution');\n                                // Show toast notification for BUY\n                                toast({\n                                    type: 'success',\n                                    title: '🟢 BUY EXECUTED',\n                                    description: \"Bought \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)),\n                                    duration: 2000\n                                });\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Send error notification for insufficient balance\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for BUY order\"));\n                                sendTelegramErrorNotification('Insufficient Balance', \"Cannot execute BUY order - insufficient \".concat(config.crypto2, \" balance\"), \"Required: \".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \", Available: \").concat(currentCrypto2Balance.toFixed(2), \" \").concat(config.crypto2));\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss - convert USDT profit to BTC equivalent\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"\\uD83D\\uDCB0 SELL Trade P/L: Crypto2=\".concat(realizedProfit.toFixed(6), \", Crypto1=\").concat(realizedProfitCrypto1.toFixed(6)));\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            playSound('soundOrderExecution');\n                            // Show toast notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            toast({\n                                type: realizedProfit > 0 ? 'success' : realizedProfit < 0 ? 'warning' : 'info',\n                                title: '🔴 SELL EXECUTED',\n                                description: \"Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" | \").concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            // Send Telegram notification for SELL\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Calculate cost basis for this BUY operation in terms of crypto2\n                                const costBasisCrypto2 = amountCrypto2ToUse;\n                                const costBasisStablecoin = stablecoinObtained;\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        // For SELL step of BUY operation: show cost basis as negative (money spent)\n                                        realizedProfitLossCrypto2: -costBasisCrypto2,\n                                        realizedProfitLossCrypto1: crypto1StablecoinPrice > 0 ? -costBasisCrypto2 / crypto1StablecoinPrice : 0\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        // For BUY step: show the value acquired (positive)\n                                        realizedProfitLossCrypto2: costBasisCrypto2,\n                                        realizedProfitLossCrypto1: crypto1Bought // Amount of crypto1 acquired\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                playSound('soundOrderExecution');\n                                // Show toast notification for Stablecoin BUY\n                                toast({\n                                    type: 'success',\n                                    title: '🟢 BUY EXECUTED (Stablecoin)',\n                                    description: \"Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss - convert Crypto2 profit to Crypto1 equivalent\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    // For SELL step: show the stablecoin value received (positive)\n                                    realizedProfitLossCrypto2: stablecoinFromC1Sell / crypto2StablecoinPrice,\n                                    realizedProfitLossCrypto1: amountCrypto1ToSell // Amount of crypto1 sold (negative impact on holdings)\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    // For BUY step: show the net profit/loss from the complete operation\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"\\uD83D\\uDCB0 STABLECOIN SWAP P/L: Crypto2=\".concat(realizedProfitInCrypto2.toFixed(6), \", Crypto1=\").concat(realizedProfitCrypto1.toFixed(6)));\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            playSound('soundOrderExecution');\n                            // Show toast notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            toast({\n                                type: realizedProfitInCrypto2 > 0 ? 'success' : realizedProfitInCrypto2 < 0 ? 'warning' : 'info',\n                                title: '🔴 SELL EXECUTED (Stablecoin)',\n                                description: \"Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" | \").concat(profitEmoji, \" Profit: $\").concat(realizedProfitInCrypto2.toFixed(2)),\n                                duration: 2000\n                            });\n                            // Send Telegram notification for Stablecoin SELL\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        // Calculate unrealized profit/loss based on current market price vs original cost\n                        if (state.config.tradingMode === \"StablecoinSwap\") {\n                            // For stablecoin swap: Calculate profit based on current market price vs target price\n                            // If current price > target price = profit (positive income)\n                            // If current price < target price = loss (negative income)\n                            const targetPrice = row.targetPrice || 0;\n                            const priceDifference = currentPrice - targetPrice;\n                            const profitPercentage = targetPrice > 0 ? priceDifference / targetPrice : 0;\n                            // Calculate profit/loss based on the amount held and price difference\n                            const totalUnrealizedProfitInCrypto2 = row.crypto1AmountHeld * priceDifference;\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;\n                            }\n                        } else {\n                            // Simple spot mode logic\n                            const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;\n                            }\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], []);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], []);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config, undefined, {\n                        crypto1: state.crypto1Balance,\n                        crypto2: state.crypto2Balance,\n                        stablecoin: state.stablecoinBalance\n                    }).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Note: No need to stop backend bot during crypto pair change\n                        // Frontend bot system is separate from backend bot system\n                        // The frontend bot status will be reset with the new crypto pair\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config, {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        }).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]).catch({\n                            \"TradingProvider.useEffect\": (error)=>{\n                                console.error('❌ Failed to auto-save session during crypto pair change:', error);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Don't auto-create new session for crypto pair change\n                    // User should manually create session after setting target prices\n                    console.log('🔄 Crypto pair changed, session cleared. Set target prices and start bot to create new session.');\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config, {\n                                        crypto1: state.crypto1Balance,\n                                        crypto2: state.crypto2Balance,\n                                        stablecoin: state.stablecoinBalance\n                                    }).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]).catch({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (error)=>{\n                                            console.error('❌ Failed to create offline backup session:', error);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        console.log(\"Network Disconnected: Bot stopped and session saved. Trading paused until connection restored.\");\n                    } else if (isOnline && !isInitial) {\n                        console.log(\"Network Reconnected: Connection restored. You can resume trading.\");\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Session is active if it's the current session\n                            );\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Save global balances whenever they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveGlobalBalances(state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Initialize with global balances on mount (only if using default balances)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const isDefaultBalances = state.crypto1Balance === 10 && state.crypto2Balance === 100000 && state.stablecoinBalance === 0;\n            if (isDefaultBalances) {\n                const globalBalances = loadGlobalBalances();\n                if (globalBalances.crypto1Balance !== 10 || globalBalances.crypto2Balance !== 100000 || globalBalances.stablecoinBalance !== 0) {\n                    dispatch({\n                        type: 'UPDATE_BALANCES',\n                        payload: {\n                            crypto1: globalBalances.crypto1Balance,\n                            crypto2: globalBalances.crypto2Balance,\n                            stablecoin: globalBalances.stablecoinBalance\n                        }\n                    });\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Only run once on mount\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        }).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // New session is active when created\n                                );\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]).catch({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (error)=>{\n                                console.error('Failed to create new session:', error);\n                                sendTelegramErrorNotification('Session Creation Error', 'Failed to create new trading session', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    sendTelegramErrorNotification('Session Save Error', 'Cannot save session - no trading pair selected', 'Please select both crypto1 and crypto2 before saving');\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Current session is active\n                );\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                sendTelegramErrorNotification('Session Save Error', 'Unexpected error while saving session', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state,\n        sendTelegramErrorNotification\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1990,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"aHMGgxGGYmlLVrWusBOKYNwtiqk=\", false, function() {\n    return [\n        _components_ui_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/network-monitor.ts":
/*!************************************!*\
  !*** ./src/lib/network-monitor.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSaveManager: () => (/* binding */ AutoSaveManager),\n/* harmony export */   MemoryMonitor: () => (/* binding */ MemoryMonitor),\n/* harmony export */   NetworkMonitor: () => (/* binding */ NetworkMonitor)\n/* harmony export */ });\nclass NetworkMonitor {\n    static getInstance() {\n        if (!NetworkMonitor.instance) {\n            NetworkMonitor.instance = new NetworkMonitor();\n        }\n        return NetworkMonitor.instance;\n    }\n    setupEventListeners() {\n        if (true) {\n            window.addEventListener('online', this.handleOnline.bind(this));\n            window.addEventListener('offline', this.handleOffline.bind(this));\n        }\n        // Listen for visibility change to check connection when tab becomes active\n        if (typeof document !== 'undefined') {\n            document.addEventListener('visibilitychange', ()=>{\n                if (!document.hidden) {\n                    this.checkConnection();\n                }\n            });\n        }\n    }\n    handleOnline() {\n        console.log('🌐 Network: Back online');\n        this.isOnline = true;\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.notifyListeners(true, !this.hasInitialized);\n    }\n    handleOffline() {\n        console.log('🌐 Network: Gone offline');\n        this.isOnline = false;\n        this.notifyListeners(false, !this.hasInitialized);\n    }\n    async checkConnection() {\n        // Simply use navigator.onLine for now to avoid false positives\n        // This is more reliable than trying to fetch external resources\n        const isConnected = typeof navigator !== 'undefined' ? navigator.onLine : true;\n        if (isConnected !== this.isOnline) {\n            this.isOnline = isConnected;\n            this.notifyListeners(isConnected, !this.hasInitialized);\n            if (isConnected) {\n                this.lastOnlineTime = Date.now();\n                this.reconnectAttempts = 0;\n            }\n        }\n        return isConnected;\n    }\n    startPeriodicCheck() {\n        // Use a more efficient interval with cleanup\n        const interval = setInterval(()=>{\n            this.checkConnection();\n        }, 60000); // Check every 60 seconds (reduced frequency)\n        // Store interval for cleanup\n        this.periodicInterval = interval;\n    }\n    cleanup() {\n        if (this.periodicInterval) {\n            clearInterval(this.periodicInterval);\n        }\n        this.listeners.clear();\n    }\n    notifyListeners(isOnline) {\n        let isInitial = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(isOnline, isInitial);\n            } catch (error) {\n                console.error('Error in network status listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    getStatus() {\n        return {\n            isOnline: this.isOnline,\n            lastOnlineTime: this.lastOnlineTime,\n            reconnectAttempts: this.reconnectAttempts\n        };\n    }\n    async forceCheck() {\n        return await this.checkConnection();\n    }\n    // Attempt to reconnect with exponential backoff\n    async attemptReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('🌐 Network: Max reconnect attempts reached');\n            return false;\n        }\n        this.reconnectAttempts++;\n        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);\n        console.log(\"\\uD83C\\uDF10 Network: Attempting reconnect \".concat(this.reconnectAttempts, \"/\").concat(this.maxReconnectAttempts, \" in \").concat(delay, \"ms\"));\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n        const isConnected = await this.checkConnection();\n        if (!isConnected && this.reconnectAttempts < this.maxReconnectAttempts) {\n            // Schedule next attempt\n            setTimeout(()=>this.attemptReconnect(), 1000);\n        }\n        return isConnected;\n    }\n    constructor(){\n        this.isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;\n        this.listeners = new Set();\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 5000 // 5 seconds\n        ;\n        this.hasInitialized = false;\n        this.setupEventListeners();\n        this.startPeriodicCheck();\n        // Mark as initialized after a short delay to avoid initial notifications\n        setTimeout(()=>{\n            this.hasInitialized = true;\n        }, 1000);\n    }\n}\n// Auto-save functionality\nclass AutoSaveManager {\n    static getInstance() {\n        if (!AutoSaveManager.instance) {\n            AutoSaveManager.instance = new AutoSaveManager();\n        }\n        return AutoSaveManager.instance;\n    }\n    setupNetworkListener() {\n        this.networkMonitor.addListener((isOnline)=>{\n            if (isOnline && this.saveFunction) {\n                // Save immediately when coming back online\n                console.log('💾 Auto-save: Saving on network reconnection');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    setupBeforeUnloadListener() {\n        if (true) {\n            window.addEventListener('beforeunload', ()=>{\n                if (this.saveFunction) {\n                    console.log('💾 Auto-save: Saving before page unload');\n                    this.saveFunction();\n                }\n            });\n        }\n        // Also save on page visibility change (when user switches tabs)\n        if (typeof document !== 'undefined') {\n            document.addEventListener('visibilitychange', ()=>{\n                if (document.hidden && this.saveFunction) {\n                    console.log('💾 Auto-save: Saving on tab switch');\n                    this.saveFunction();\n                    this.lastSaveTime = Date.now();\n                }\n            });\n        }\n    }\n    enable(saveFunction) {\n        let intervalMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30000;\n        this.saveFunction = saveFunction;\n        this.intervalMs = intervalMs;\n        this.isEnabled = true;\n        this.stop(); // Clear any existing interval\n        this.saveInterval = setInterval(()=>{\n            if (this.isEnabled && this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n                console.log('💾 Auto-save: Periodic save');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        }, this.intervalMs);\n        console.log(\"\\uD83D\\uDCBE Auto-save: Enabled with \".concat(intervalMs, \"ms interval\"));\n    }\n    disable() {\n        this.isEnabled = false;\n        this.stop();\n        console.log('💾 Auto-save: Disabled');\n    }\n    stop() {\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n            this.saveInterval = null;\n        }\n    }\n    saveNow() {\n        if (this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n            console.log('💾 Auto-save: Manual save triggered');\n            this.saveFunction();\n            this.lastSaveTime = Date.now();\n        }\n    }\n    getStatus() {\n        return {\n            isEnabled: this.isEnabled,\n            lastSaveTime: this.lastSaveTime,\n            intervalMs: this.intervalMs,\n            isOnline: this.networkMonitor.getStatus().isOnline\n        };\n    }\n    constructor(){\n        this.saveInterval = null;\n        this.saveFunction = null;\n        this.intervalMs = 30000 // 30 seconds default\n        ;\n        this.isEnabled = true;\n        this.lastSaveTime = 0;\n        this.networkMonitor = NetworkMonitor.getInstance();\n        this.setupNetworkListener();\n        this.setupBeforeUnloadListener();\n    }\n}\n// Memory usage monitor to prevent memory leaks\nclass MemoryMonitor {\n    static getInstance() {\n        if (!MemoryMonitor.instance) {\n            MemoryMonitor.instance = new MemoryMonitor();\n        }\n        return MemoryMonitor.instance;\n    }\n    startMonitoring() {\n        // Disable memory monitoring to prevent frequent notifications\n        console.log('📊 Memory monitoring disabled to prevent frequent notifications');\n        return;\n    // Original code commented out to stop memory notifications:\n    /*\n    // Only monitor if performance.memory is available (Chrome)\n    if ('memory' in performance) {\n      this.checkInterval = setInterval(() => {\n        this.checkMemoryUsage();\n      }, 300000); // Check every 5 minutes (reduced frequency)\n    }\n    */ }\n    checkMemoryUsage() {\n        if (typeof performance !== 'undefined' && 'memory' in performance) {\n            const memory = performance.memory;\n            const usedJSHeapSize = memory.usedJSHeapSize;\n            this.notifyListeners(memory);\n            if (usedJSHeapSize > this.criticalThreshold) {\n                console.warn('🧠 Memory: Critical memory usage detected:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    limit: \"\".concat((memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2), \"MB\")\n                });\n                // Trigger garbage collection if possible\n                if ( true && 'gc' in window) {\n                    window.gc();\n                }\n            } else if (usedJSHeapSize > this.warningThreshold) {\n                console.log('🧠 Memory: High memory usage:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\")\n                });\n            }\n        }\n    }\n    notifyListeners(memory) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(memory);\n            } catch (error) {\n                console.error('Error in memory monitor listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    getMemoryUsage() {\n        if (typeof performance !== 'undefined' && 'memory' in performance) {\n            return performance.memory;\n        }\n        return null;\n    }\n    stop() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n            this.checkInterval = null;\n        }\n    }\n    constructor(){\n        this.checkInterval = null;\n        this.warningThreshold = 250 * 1024 * 1024 // 250MB (increased)\n        ;\n        this.criticalThreshold = 400 * 1024 * 1024 // 400MB (increased)\n        ;\n        this.listeners = new Set();\n        this.startMonitoring();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/network-monitor.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/types.tsx":
/*!***************************!*\
  !*** ./src/lib/types.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AVAILABLE_CRYPTOS: () => (/* binding */ AVAILABLE_CRYPTOS),\n/* harmony export */   AVAILABLE_QUOTES_SIMPLE: () => (/* binding */ AVAILABLE_QUOTES_SIMPLE),\n/* harmony export */   AVAILABLE_STABLECOINS: () => (/* binding */ AVAILABLE_STABLECOINS),\n/* harmony export */   DEFAULT_APP_SETTINGS: () => (/* binding */ DEFAULT_APP_SETTINGS)\n/* harmony export */ });\nconst DEFAULT_APP_SETTINGS = {\n    soundAlertsEnabled: true,\n    alertOnOrderExecution: true,\n    alertOnError: true,\n    soundOrderExecution: '/sounds/order-executed.mp3',\n    soundError: '/sounds/error.mp3',\n    clearOrderHistoryOnStart: false\n};\n// Dummy constants, replace with actual values or logic as needed\nconst AVAILABLE_CRYPTOS = [\n    'BTC',\n    'ETH',\n    'ADA',\n    'SOL',\n    'DOGE',\n    'LINK',\n    'MATIC',\n    'DOT',\n    'AVAX',\n    'XRP',\n    'LTC',\n    'BCH',\n    'BNB',\n    'SHIB'\n];\nconst AVAILABLE_QUOTES_SIMPLE = {\n    BTC: [\n        'USDT',\n        'USDC',\n        'FDUSD',\n        'EUR'\n    ],\n    ETH: [\n        'USDT',\n        'USDC',\n        'FDUSD',\n        'BTC',\n        'EUR'\n    ],\n    ADA: [\n        'USDT',\n        'USDC',\n        'BTC',\n        'ETH'\n    ],\n    SOL: [\n        'USDT',\n        'USDC',\n        'BTC',\n        'ETH'\n    ]\n};\nconst AVAILABLE_STABLECOINS = [\n    'USDT',\n    'USDC',\n    'FDUSD',\n    'DAI'\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/types.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0U7SUFBRztRQUFHQyxPQUFILHVCQUF1Qjs7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

}]);