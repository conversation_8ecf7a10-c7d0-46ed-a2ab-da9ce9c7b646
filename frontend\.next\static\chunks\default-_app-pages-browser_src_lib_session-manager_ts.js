"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["default-_app-pages-browser_src_lib_session-manager_ts"],{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n// Define default session alarm settings locally to avoid import issues\nconst DEFAULT_SESSION_ALARM_SETTINGS = {\n    soundAlertsEnabled: true,\n    alertOnOrderExecution: true,\n    alertOnError: true,\n    soundOrderExecution: \"/sounds/trade_success.mp3\",\n    soundError: \"/sounds/trade_error.mp3\"\n};\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    // Public method to check backend connection when authentication is ready\n    async checkBackendConnectionWhenReady() {\n        await this.checkBackendConnection();\n    }\n    async checkBackendConnection() {\n        try {\n            // Check if user is authenticated first\n            const token = localStorage.getItem('plutoAuthToken');\n            if (!token) {\n                console.log('📱 Session Manager: No auth token found - using localStorage only');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 3000); // 3 second timeout for better reliability\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal,\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            clearTimeout(timeoutId);\n            if (response.status === 200) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established - using database persistence');\n                // Only load sessions from backend if we have authentication\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token) {\n                    await this.loadSessionsFromBackend();\n                } else {\n                    console.log('📱 Session Manager: No auth token - skipping backend session loading');\n                    this.loadSessionsFromStorage();\n                }\n            } else if (response.status === 401 || response.status === 422) {\n                // Authentication issues - fall back to localStorage\n                console.log('🔐 Session Manager: Authentication required for backend - using localStorage fallback');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return; // Don't throw error, just fall back silently\n            } else {\n                throw new Error(\"Backend returned error: \".concat(response.status));\n            }\n        } catch (error) {\n            this.useBackend = false;\n            // Only log actual connection errors, not authentication issues\n            if (error.name === 'AbortError') {\n                console.log('⏱️ Session Manager: Backend connection timeout - using localStorage fallback');\n            } else if (!error.message.includes('Authentication required') && !error.message.includes('422') && !error.message.includes('401')) {\n                console.log('🔌 Session Manager: Backend unavailable - using localStorage fallback');\n            }\n            // Load from localStorage as fallback\n            this.loadSessionsFromStorage();\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    setupBeforeUnloadHandler() {\n        if (false) {}\n        // Handle browser tab/window closing\n        window.addEventListener('beforeunload', ()=>{\n            // Mark current session as inactive when tab is closed\n            if (this.currentSessionId) {\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && session.isActive) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    // Stop runtime tracking\n                    this.stopSessionRuntime(this.currentSessionId);\n                    // Clean up heartbeat for this window\n                    const heartbeatKey = \"pluto_heartbeat_\".concat(this.currentSessionId, \"_\").concat(this.windowId);\n                    localStorage.removeItem(heartbeatKey);\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"\\uD83D\\uDEAA Session \".concat(this.currentSessionId, \" marked as inactive due to window close\"));\n                }\n            }\n        });\n        // Handle page visibility changes (tab switching, minimizing)\n        document.addEventListener('visibilitychange', ()=>{\n            if (document.hidden && this.currentSessionId) {\n                // Tab became hidden - mark session as inactive\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && session.isActive) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.stopSessionRuntime(this.currentSessionId);\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"\\uD83D\\uDC41️ Session \".concat(this.currentSessionId, \" marked as inactive due to tab hidden\"));\n                }\n            } else if (!document.hidden && this.currentSessionId) {\n                // Tab became visible - mark session as active again\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && !session.isActive) {\n                    session.isActive = true;\n                    session.lastModified = Date.now();\n                    this.startSessionRuntime(this.currentSessionId);\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"\\uD83D\\uDC41️ Session \".concat(this.currentSessionId, \" marked as active due to tab visible\"));\n                }\n            }\n        });\n    }\n    startHeartbeat() {\n        if (false) {}\n        // Send heartbeat every 10 seconds to mark session as active\n        this.heartbeatInterval = setInterval(()=>{\n            if (this.currentSessionId) {\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && session.isActive) {\n                    // Update last modified to indicate this session is still active\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    // Store heartbeat timestamp for this window\n                    const heartbeatKey = \"pluto_heartbeat_\".concat(this.currentSessionId, \"_\").concat(this.windowId);\n                    localStorage.setItem(heartbeatKey, Date.now().toString());\n                }\n            }\n            // Clean up stale sessions (no heartbeat for more than 30 seconds)\n            this.cleanupStaleSessions();\n        }, 10000); // Every 10 seconds\n    }\n    cleanupStaleSessions() {\n        const now = Date.now();\n        const staleThreshold = 30000; // 30 seconds\n        this.sessions.forEach((session, sessionId)=>{\n            if (session.isActive) {\n                // Check if there's a recent heartbeat for this session from any window\n                const heartbeatKeys = [];\n                for(let i = 0; i < localStorage.length; i++){\n                    const key = localStorage.key(i);\n                    if (key && key.startsWith(\"pluto_heartbeat_\".concat(sessionId, \"_\"))) {\n                        heartbeatKeys.push(key);\n                    }\n                }\n                let hasRecentHeartbeat = false;\n                for (const key of heartbeatKeys){\n                    const heartbeatTime = parseInt(localStorage.getItem(key) || '0');\n                    if (now - heartbeatTime < staleThreshold) {\n                        hasRecentHeartbeat = true;\n                        break;\n                    }\n                }\n                // If no recent heartbeat, mark session as inactive\n                if (!hasRecentHeartbeat) {\n                    session.isActive = false;\n                    session.lastModified = now;\n                    this.sessions.set(sessionId, session);\n                    console.log(\"\\uD83E\\uDDF9 Session \".concat(sessionId, \" marked as inactive due to stale heartbeat\"));\n                    // Clean up old heartbeat keys\n                    heartbeatKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            }\n        });\n        this.saveSessionsToStorage();\n    }\n    async loadSessionsFromBackend() {\n        // Check authentication before making any backend calls\n        const token = localStorage.getItem('plutoAuthToken');\n        if (!token) {\n            console.log('📱 Session Manager: No auth token - skipping backend session loading');\n            this.loadSessionsFromStorage();\n            return;\n        }\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.getAllSessions();\n            // Handle null response from authentication errors\n            if (!response) {\n                console.log('📱 Session Manager: Backend authentication failed - using localStorage fallback');\n                this.loadSessionsFromStorage();\n                return;\n            }\n            if (response && response.sessions) {\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const tradingSession = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: session.config_snapshot,\n                        targetPriceRows: session.target_price_rows || [],\n                        orderHistory: session.order_history || [],\n                        currentMarketPrice: session.current_market_price || 0,\n                        crypto1Balance: session.crypto1_balance || 10,\n                        crypto2Balance: session.crypto2_balance || 100000,\n                        stablecoinBalance: session.stablecoin_balance || 0,\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified || session.created_at).getTime(),\n                        isActive: session.is_active || false,\n                        runtime: session.runtime_seconds ? session.runtime_seconds * 1000 : 0,\n                        alarmSettings: session.alarm_settings || {\n                            ...DEFAULT_SESSION_ALARM_SETTINGS\n                        }\n                    };\n                    this.sessions.set(session.session_uuid, tradingSession);\n                });\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend database\"));\n            }\n        } catch (error) {\n            // Don't log authentication errors as they're expected when not logged in\n            if (error.message && (error.message.includes('422') || error.message.includes('401') || error.message.includes('UNPROCESSABLE ENTITY'))) {\n            // Silent fallback for authentication issues\n            } else {\n                console.error('Failed to load sessions from backend:', error);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Load current session from window-specific storage (each window has its own current session)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const currentSessionId = localStorage.getItem(currentSessionKey);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions from localStorage for window \").concat(this.windowId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async saveSessionToBackend(sessionId, session) {\n        try {\n            // Ensure all numeric values are valid numbers\n            const sessionData = {\n                name: session.name || 'Untitled Session',\n                config: session.config,\n                targetPriceRows: session.targetPriceRows || [],\n                currentMarketPrice: Number(session.currentMarketPrice) || 0,\n                crypto1Balance: Number(session.crypto1Balance) || 0,\n                crypto2Balance: Number(session.crypto2Balance) || 0,\n                stablecoinBalance: Number(session.stablecoinBalance) || 0,\n                isActive: Boolean(session.isActive),\n                additionalRuntime: Math.floor(Number(session.runtime || 0) / 1000) // Convert to seconds, ensure it's a number\n            };\n            console.log('🔍 Sending session data to backend:', JSON.stringify(sessionData, null, 2));\n            console.log('🔍 Session config details:', JSON.stringify(sessionData.config, null, 2));\n            try {\n                // Try to update first\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" updated in backend database\"));\n            } catch (updateError) {\n                // If update fails with 404, try to create the session\n                const errorMessage = updateError instanceof Error ? updateError.message : String(updateError);\n                if (errorMessage.includes('404') || errorMessage.includes('not found')) {\n                    console.log('🔄 Session not found, creating new session in backend...');\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                    console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" created in backend database\"));\n                } else {\n                    throw updateError; // Re-throw other errors\n                }\n            }\n        } catch (error) {\n            console.error('❌ Backend save failed with full error:', error);\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            const errorStatus = error === null || error === void 0 ? void 0 : error.status;\n            const errorResponse = error === null || error === void 0 ? void 0 : error.response;\n            console.error('❌ Error details:', {\n                message: errorMessage,\n                status: errorStatus,\n                response: errorResponse\n            });\n            // Don't log authentication errors as errors - they're expected when not logged in\n            if (errorMessage.includes('422') || errorMessage.includes('401') || errorMessage.includes('UNPROCESSABLE ENTITY')) {\n                console.warn('Backend validation failed - check data format');\n            } else {\n                console.error('Failed to save session to backend:', error);\n            }\n            throw error; // Re-throw to trigger fallback\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n            console.log(\"\\uD83D\\uDCBE Sessions saved to localStorage (\".concat(this.sessions.size, \" sessions)\"));\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                });\n                // Validate response structure\n                if (!response || !response.session) {\n                    throw new Error('Invalid response from server: missing session data');\n                }\n                const sessionId = response.session.id; // Backend maps session_uuid to id in to_dict()\n                // Add to local sessions map for immediate access\n                const now = Date.now();\n                const newSession = {\n                    id: sessionId,\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin,\n                    createdAt: now,\n                    lastModified: now,\n                    isActive: false,\n                    runtime: 0,\n                    alarmSettings: {\n                        ...DEFAULT_SESSION_ALARM_SETTINGS\n                    }\n                };\n                this.sessions.set(sessionId, newSession);\n                console.log('✅ Session created on backend database:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0,\n            alarmSettings: {\n                ...DEFAULT_SESSION_ALARM_SETTINGS\n            }\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        console.log('✅ Session created in localStorage:', sessionId);\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // Save to backend if available, otherwise fallback to localStorage\n            if (this.useBackend) {\n                this.saveSessionToBackend(sessionId, updatedSession).catch((error)=>{\n                    // Check if it's an authentication error\n                    if (error.message.includes('422') || error.message.includes('401') || error.message.includes('UNPROCESSABLE ENTITY')) {\n                        console.warn('⚠️ Backend authentication failed, disabling backend and using localStorage');\n                        this.useBackend = false;\n                    } else {\n                        console.error('Failed to save to backend, falling back to localStorage:', error);\n                    }\n                    this.saveSessionsToStorage();\n                });\n            } else {\n                this.saveSessionsToStorage();\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = {\n            ...alarmSettings\n        };\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available, otherwise fallback to localStorage\n        if (this.useBackend) {\n            this.saveSessionToBackend(sessionId, session).catch((error)=>{\n                // Check if it's an authentication error\n                if (error.message.includes('422') || error.message.includes('401') || error.message.includes('UNPROCESSABLE ENTITY')) {\n                    console.warn('⚠️ Backend authentication failed, disabling backend and using localStorage');\n                    this.useBackend = false;\n                } else {\n                    console.error('Failed to save session alarm settings to backend, falling back to localStorage:', error);\n                }\n                this.saveSessionsToStorage();\n            });\n        } else {\n            this.saveSessionsToStorage();\n        }\n        return true;\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    destroy() {\n        // Clean up heartbeat interval\n        if (this.heartbeatInterval) {\n            clearInterval(this.heartbeatInterval);\n            this.heartbeatInterval = null;\n        }\n        // Mark current session as inactive\n        this.clearCurrentSession();\n        // Clean up heartbeat keys for this window\n        if (this.currentSessionId) {\n            const heartbeatKey = \"pluto_heartbeat_\".concat(this.currentSessionId, \"_\").concat(this.windowId);\n            localStorage.removeItem(heartbeatKey);\n        }\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.heartbeatInterval = null;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Temporarily disable backend to avoid 422 errors during testing\n        this.useBackend = false;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        this.setupBeforeUnloadHandler();\n        this.startHeartbeat();\n        // Don't check backend connection immediately - wait for explicit authentication\n        // This prevents HTTP 422 errors during app startup\n        console.log('📱 Session Manager: Initialized - backend connection will be checked when needed');\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

}]);