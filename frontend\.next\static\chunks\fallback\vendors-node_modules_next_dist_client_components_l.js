"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_l"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    unmountLinkInstance: function() {\n        return unmountLinkInstance;\n    }\n});\nconst _actionqueue = __webpack_require__(/*! ../../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction mountLinkInstance(element, href, router, kind) {\n    let prefetchUrl = null;\n    try {\n        prefetchUrl = (0, _approuter.createPrefetchURL)(href);\n        if (prefetchUrl === null) {\n            // We only track the link if it's prefetchable. For example, this excludes\n            // links to external URLs.\n            return;\n        }\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return;\n    }\n    const instance = {\n        prefetchHref: prefetchUrl.href,\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1\n    };\n    const existingInstance = links.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountLinkInstance(element);\n    }\n    links.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction unmountLinkInstance(element) {\n    const instance = links.get(element);\n    if (instance !== undefined) {\n        links.delete(element);\n        visibleLinks.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        visibleLinks.add(instance);\n    } else {\n        visibleLinks.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element) {\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with bumpPrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    if (existingPrefetchTask === null) {\n        // Initiate a prefetch task.\n        const appRouterState = (0, _actionqueue.getCurrentAppRouterState)();\n        if (appRouterState !== null) {\n            const nextUrl = appRouterState.nextUrl;\n            const treeAtTimeOfPrefetch = appRouterState.tree;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n            instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n        }\n    } else {\n        // We already have an old task object that we can reschedule. This is\n        // effectively the same as canceling the old task and creating a new one.\n        (0, _segmentcache.bumpPrefetchTask)(existingPrefetchTask, priority);\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of visibleLinks){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/match-segments.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"matchSegment\", ({\n    enumerable: true,\n    get: function() {\n        return matchSegment;\n    }\n}));\nconst matchSegment = (existingSegment, segment)=>{\n    // segment is either Array or string\n    if (typeof existingSegment === 'string') {\n        if (typeof segment === 'string') {\n            // Common case: segment is just a string\n            return existingSegment === segment;\n        }\n        return false;\n    }\n    if (typeof segment === 'string') {\n        return false;\n    }\n    return existingSegment[0] === segment[0] && existingSegment[1] === segment[1];\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=match-segments.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWF0Y2gtc2VnbWVudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztnREFFYUE7OztlQUFBQTs7O0FBQU4sTUFBTUEsZUFBZSxDQUMxQkMsaUJBQ0FDO0lBRUEsb0NBQW9DO0lBQ3BDLElBQUksT0FBT0Qsb0JBQW9CLFVBQVU7UUFDdkMsSUFBSSxPQUFPQyxZQUFZLFVBQVU7WUFDL0Isd0NBQXdDO1lBQ3hDLE9BQU9ELG9CQUFvQkM7UUFDN0I7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxJQUFJLE9BQU9BLFlBQVksVUFBVTtRQUMvQixPQUFPO0lBQ1Q7SUFDQSxPQUFPRCxlQUFlLENBQUMsRUFBRSxLQUFLQyxPQUFPLENBQUMsRUFBRSxJQUFJRCxlQUFlLENBQUMsRUFBRSxLQUFLQyxPQUFPLENBQUMsRUFBRTtBQUMvRSIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxtYXRjaC1zZWdtZW50cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IFNlZ21lbnQgfSBmcm9tICcuLi8uLi9zZXJ2ZXIvYXBwLXJlbmRlci90eXBlcydcblxuZXhwb3J0IGNvbnN0IG1hdGNoU2VnbWVudCA9IChcbiAgZXhpc3RpbmdTZWdtZW50OiBTZWdtZW50LFxuICBzZWdtZW50OiBTZWdtZW50XG4pOiBib29sZWFuID0+IHtcbiAgLy8gc2VnbWVudCBpcyBlaXRoZXIgQXJyYXkgb3Igc3RyaW5nXG4gIGlmICh0eXBlb2YgZXhpc3RpbmdTZWdtZW50ID09PSAnc3RyaW5nJykge1xuICAgIGlmICh0eXBlb2Ygc2VnbWVudCA9PT0gJ3N0cmluZycpIHtcbiAgICAgIC8vIENvbW1vbiBjYXNlOiBzZWdtZW50IGlzIGp1c3QgYSBzdHJpbmdcbiAgICAgIHJldHVybiBleGlzdGluZ1NlZ21lbnQgPT09IHNlZ21lbnRcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICBpZiAodHlwZW9mIHNlZ21lbnQgPT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbiAgcmV0dXJuIGV4aXN0aW5nU2VnbWVudFswXSA9PT0gc2VnbWVudFswXSAmJiBleGlzdGluZ1NlZ21lbnRbMV0gPT09IHNlZ21lbnRbMV1cbn1cbiJdLCJuYW1lcyI6WyJtYXRjaFNlZ21lbnQiLCJleGlzdGluZ1NlZ21lbnQiLCJzZWdtZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/nav-failure-handler.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleHardNavError: function() {\n        return handleHardNavError;\n    },\n    useNavFailureHandler: function() {\n        return useNavFailureHandler;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nfunction handleHardNavError(error) {\n    if (error && \"object\" !== 'undefined' && window.next.__pendingUrl && (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== (0, _createhreffromurl.createHrefFromUrl)(window.next.__pendingUrl)) {\n        console.error(\"Error occurred during navigation, falling back to hard navigation\", error);\n        window.location.href = window.next.__pendingUrl.toString();\n        return true;\n    }\n    return false;\n}\nfunction useNavFailureHandler() {\n    if (false) {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=nav-failure-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/navigation-untracked.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useUntrackedPathname\", ({\n    enumerable: true,\n    get: function() {\n        return useUntrackedPathname;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */ function hasFallbackRouteParams() {\n    if (false) {}\n    return false;\n}\nfunction useUntrackedPathname() {\n    // If there are any unknown route parameters we would typically throw\n    // an error, but this internal method allows us to return a null value instead\n    // for components that do not propagate the pathname to the static shell (like\n    // the error boundary).\n    if (hasFallbackRouteParams()) {\n        return null;\n    }\n    // This shouldn't cause any issues related to conditional rendering because\n    // the environment will be consistent for the render.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return (0, _react.useContext)(_hooksclientcontextsharedruntime.PathnameContext);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=navigation-untracked.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/navigation.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ReadonlyURLSearchParams: function() {\n        return _navigationreactserver.ReadonlyURLSearchParams;\n    },\n    RedirectType: function() {\n        return _navigationreactserver.RedirectType;\n    },\n    ServerInsertedHTMLContext: function() {\n        return _serverinsertedhtmlsharedruntime.ServerInsertedHTMLContext;\n    },\n    forbidden: function() {\n        return _navigationreactserver.forbidden;\n    },\n    notFound: function() {\n        return _navigationreactserver.notFound;\n    },\n    permanentRedirect: function() {\n        return _navigationreactserver.permanentRedirect;\n    },\n    redirect: function() {\n        return _navigationreactserver.redirect;\n    },\n    unauthorized: function() {\n        return _navigationreactserver.unauthorized;\n    },\n    unstable_rethrow: function() {\n        return _navigationreactserver.unstable_rethrow;\n    },\n    useParams: function() {\n        return useParams;\n    },\n    usePathname: function() {\n        return usePathname;\n    },\n    useRouter: function() {\n        return useRouter;\n    },\n    useSearchParams: function() {\n        return useSearchParams;\n    },\n    useSelectedLayoutSegment: function() {\n        return useSelectedLayoutSegment;\n    },\n    useSelectedLayoutSegments: function() {\n        return useSelectedLayoutSegments;\n    },\n    useServerInsertedHTML: function() {\n        return _serverinsertedhtmlsharedruntime.useServerInsertedHTML;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _segment = __webpack_require__(/*! ../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst _navigationreactserver = __webpack_require__(/*! ./navigation.react-server */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.react-server.js\");\nconst _serverinsertedhtmlsharedruntime = __webpack_require__(/*! ../../shared/lib/server-inserted-html.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js\");\nconst useDynamicRouteParams =  false ? 0 : undefined;\nfunction useSearchParams() {\n    const searchParams = (0, _react.useContext)(_hooksclientcontextsharedruntime.SearchParamsContext);\n    // In the case where this is `null`, the compat types added in\n    // `next-env.d.ts` will add a new overload that changes the return type to\n    // include `null`.\n    const readonlySearchParams = (0, _react.useMemo)(()=>{\n        if (!searchParams) {\n            // When the router is not ready in pages, we won't have the search params\n            // available.\n            return null;\n        }\n        return new _navigationreactserver.ReadonlyURLSearchParams(searchParams);\n    }, [\n        searchParams\n    ]);\n    if (false) {}\n    return readonlySearchParams;\n}\nfunction usePathname() {\n    _s();\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('usePathname()');\n    // In the case where this is `null`, the compat types added in `next-env.d.ts`\n    // will add a new overload that changes the return type to include `null`.\n    return (0, _react.useContext)(_hooksclientcontextsharedruntime.PathnameContext);\n}\n_s(usePathname, \"rJhb7jJF4Q92igNmh5lAnMUEkkY=\", false, function() {\n    return [\n        useDynamicRouteParams\n    ];\n});\nfunction useRouter() {\n    const router = (0, _react.useContext)(_approutercontextsharedruntime.AppRouterContext);\n    if (router === null) {\n        throw Object.defineProperty(new Error('invariant expected app router to be mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E238\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return router;\n}\nfunction useParams() {\n    _s1();\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('useParams()');\n    return (0, _react.useContext)(_hooksclientcontextsharedruntime.PathParamsContext);\n}\n_s1(useParams, \"rJhb7jJF4Q92igNmh5lAnMUEkkY=\", false, function() {\n    return [\n        useDynamicRouteParams\n    ];\n});\n/** Get the canonical parameters from the current level to the leaf node. */ // Client components API\nfunction getSelectedLayoutSegmentPath(tree, parallelRouteKey, first, segmentPath) {\n    if (first === void 0) first = true;\n    if (segmentPath === void 0) segmentPath = [];\n    let node;\n    if (first) {\n        // Use the provided parallel route key on the first parallel route\n        node = tree[1][parallelRouteKey];\n    } else {\n        // After first parallel route prefer children, if there's no children pick the first parallel route.\n        const parallelRoutes = tree[1];\n        var _parallelRoutes_children;\n        node = (_parallelRoutes_children = parallelRoutes.children) != null ? _parallelRoutes_children : Object.values(parallelRoutes)[0];\n    }\n    if (!node) return segmentPath;\n    const segment = node[0];\n    let segmentValue = (0, _getsegmentvalue.getSegmentValue)(segment);\n    if (!segmentValue || segmentValue.startsWith(_segment.PAGE_SEGMENT_KEY)) {\n        return segmentPath;\n    }\n    segmentPath.push(segmentValue);\n    return getSelectedLayoutSegmentPath(node, parallelRouteKey, false, segmentPath);\n}\nfunction useSelectedLayoutSegments(parallelRouteKey) {\n    _s2();\n    if (parallelRouteKey === void 0) parallelRouteKey = 'children';\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('useSelectedLayoutSegments()');\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n    if (!context) return null;\n    return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey);\n}\n_s2(useSelectedLayoutSegments, \"rJhb7jJF4Q92igNmh5lAnMUEkkY=\", false, function() {\n    return [\n        useDynamicRouteParams\n    ];\n});\nfunction useSelectedLayoutSegment(parallelRouteKey) {\n    _s3();\n    if (parallelRouteKey === void 0) parallelRouteKey = 'children';\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('useSelectedLayoutSegment()');\n    const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey);\n    if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n        return null;\n    }\n    const selectedLayoutSegment = parallelRouteKey === 'children' ? selectedLayoutSegments[0] : selectedLayoutSegments[selectedLayoutSegments.length - 1];\n    // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n    // and returning an internal value like `__DEFAULT__` would be confusing.\n    return selectedLayoutSegment === _segment.DEFAULT_SEGMENT_KEY ? null : selectedLayoutSegment;\n}\n_s3(useSelectedLayoutSegment, \"GQkIYFIXjatgPrznv5JwL5TXjn8=\", false, function() {\n    return [\n        useDynamicRouteParams,\n        useSelectedLayoutSegments\n    ];\n});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=navigation.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztJQTBSRUEsdUJBQXVCO2VBQXZCQSx1QkFBQUEsdUJBQXVCOztJQUR2QkMsWUFBWTtlQUFaQSx1QkFBQUEsWUFBWTs7SUFwTFpDLHlCQUF5QjtlQUF6QkEsaUNBQUFBLHlCQUF5Qjs7SUFnTHpCQyxTQUFTO2VBQVRBLHVCQUFBQSxTQUFTOztJQURUQyxRQUFRO2VBQVJBLHVCQUFBQSxRQUFROztJQUlSQyxpQkFBaUI7ZUFBakJBLHVCQUFBQSxpQkFBaUI7O0lBRGpCQyxRQUFRO2VBQVJBLHVCQUFBQSxRQUFROztJQURSQyxZQUFZO2VBQVpBLHVCQUFBQSxZQUFZOztJQUtaQyxnQkFBZ0I7ZUFBaEJBLHVCQUFBQSxnQkFBZ0I7O0lBcElGQyxTQUFTO2VBQVRBOztJQTVEQUMsV0FBVztlQUFYQTs7SUFpQ0FDLFNBQVM7ZUFBVEE7O0lBOUVBQyxlQUFlO2VBQWZBOztJQTZNQUMsd0JBQXdCO2VBQXhCQTs7SUEvQkFDLHlCQUF5QjtlQUF6QkE7O0lBdEhkQyxxQkFBcUI7ZUFBckJBLGlDQUFBQSxxQkFBcUI7OzttQ0FuR2E7MkRBSzdCOzZEQUtBOzZDQUN5QjtxQ0FDc0I7bURBQ2Q7NkRBdUZqQztBQXJGUCxNQUFNQyx3QkFDSixNQUE2QixHQUV2QkUsQ0FDcUIsR0FDdkJDO0FBdUJDLFNBQVNQO0lBQ2QsTUFBTVEsZUFBZUMsQ0FBQUEsR0FBQUEsT0FBQUEsVUFBQUEsRUFBV0MsaUNBQUFBLG1CQUFtQjtJQUVuRCw4REFBOEQ7SUFDOUQsMEVBQTBFO0lBQzFFLGtCQUFrQjtJQUNsQixNQUFNQyx1QkFBdUJDLENBQUFBLEdBQUFBLE9BQUFBLE9BQUFBLEVBQVE7UUFDbkMsSUFBSSxDQUFDSixjQUFjO1lBQ2pCLHlFQUF5RTtZQUN6RSxhQUFhO1lBQ2IsT0FBTztRQUNUO1FBRUEsT0FBTyxJQUFJcEIsdUJBQUFBLHVCQUF1QixDQUFDb0I7SUFDckMsR0FBRztRQUFDQTtLQUFhO0lBRWpCLElBQUksS0FBNkIsRUFBRSxFQU1sQztJQUVELE9BQU9HO0FBQ1Q7QUFvQk87O0lBQ0xQLHlCQUFBQSxPQUFBQSxLQUFBQSwwQkFBd0I7SUFFeEIsOEVBQThFO0lBQzlFLDBFQUEwRTtJQUMxRSxPQUFPSyxDQUFBQSxHQUFBQSxPQUFBQSxVQUFBQSxFQUFXSyxpQ0FBQUEsZUFBZTtBQUNuQztHQU5nQmhCOztRQUNkTTs7O0FBZ0NLLFNBQVNMO0lBQ2QsTUFBTWdCLFNBQVNOLENBQUFBLEdBQUFBLE9BQUFBLFVBQUFBLEVBQVdPLCtCQUFBQSxnQkFBZ0I7SUFDMUMsSUFBSUQsV0FBVyxNQUFNO1FBQ25CLE1BQU0scUJBQXdELENBQXhELElBQUlFLE1BQU0sZ0RBQVY7bUJBQUE7d0JBQUE7MEJBQUE7UUFBdUQ7SUFDL0Q7SUFFQSxPQUFPRjtBQUNUO0FBb0JPOztJQUNMWCx5QkFBQUEsT0FBQUEsS0FBQUEsMEJBQXdCO0lBRXhCLE9BQU9LLENBQUFBLEdBQUFBLE9BQUFBLFVBQUFBLEVBQVdTLGlDQUFBQSxpQkFBaUI7QUFDckM7SUFKZ0JyQjs7UUFDZE87OztBQUtGLDBFQUEwRSxHQUMxRSx3QkFBd0I7QUFDeEIsU0FBU2UsNkJBQ1BDLElBQXVCLEVBQ3ZCQyxnQkFBd0IsRUFDeEJDLEtBQVksRUFDWkMsV0FBMEI7SUFEMUJELElBQUFBLFVBQUFBLEtBQUFBLEdBQUFBLFFBQVE7SUFDUkMsSUFBQUEsZ0JBQUFBLEtBQUFBLEdBQUFBLGNBQXdCLEVBQUU7SUFFMUIsSUFBSUM7SUFDSixJQUFJRixPQUFPO1FBQ1Qsa0VBQWtFO1FBQ2xFRSxPQUFPSixJQUFJLENBQUMsRUFBRSxDQUFDQyxpQkFBaUI7SUFDbEMsT0FBTztRQUNMLG9HQUFvRztRQUNwRyxNQUFNSSxpQkFBaUJMLElBQUksQ0FBQyxFQUFFO1lBQ3ZCSztRQUFQRCxPQUFPQyxDQUFBQSwyQkFBQUEsZUFBZUMsUUFBQUEsS0FBUSxPQUF2QkQsMkJBQTJCRSxPQUFPQyxNQUFNLENBQUNILGVBQWUsQ0FBQyxFQUFFO0lBQ3BFO0lBRUEsSUFBSSxDQUFDRCxNQUFNLE9BQU9EO0lBQ2xCLE1BQU1NLFVBQVVMLElBQUksQ0FBQyxFQUFFO0lBRXZCLElBQUlNLGVBQWVDLENBQUFBLEdBQUFBLGlCQUFBQSxlQUFBQSxFQUFnQkY7SUFFbkMsSUFBSSxDQUFDQyxnQkFBZ0JBLGFBQWFFLFVBQVUsQ0FBQ0MsU0FBQUEsZ0JBQWdCLEdBQUc7UUFDOUQsT0FBT1Y7SUFDVDtJQUVBQSxZQUFZVyxJQUFJLENBQUNKO0lBRWpCLE9BQU9YLDZCQUNMSyxNQUNBSCxrQkFDQSxPQUNBRTtBQUVKO0FBNEJPLG1DQUNMRixnQkFBcUM7O0lBQXJDQSxJQUFBQSxxQkFBQUEsS0FBQUEsR0FBQUEsbUJBQTJCO0lBRTNCakIseUJBQUFBLE9BQUFBLEtBQUFBLDBCQUF3QjtJQUV4QixNQUFNK0IsVUFBVTFCLENBQUFBLEdBQUFBLE9BQUFBLFVBQUFBLEVBQVcyQiwrQkFBQUEsbUJBQW1CO0lBQzlDLHdGQUF3RjtJQUN4RixJQUFJLENBQUNELFNBQVMsT0FBTztJQUVyQixPQUFPaEIsNkJBQTZCZ0IsUUFBUUUsVUFBVSxFQUFFaEI7QUFDMUQ7SUFWZ0JuQjs7UUFHZEU7OztBQTRCSyxrQ0FDTGlCLGdCQUFxQzs7SUFBckNBLElBQUFBLHFCQUFBQSxLQUFBQSxHQUFBQSxtQkFBMkI7SUFFM0JqQix5QkFBQUEsT0FBQUEsS0FBQUEsMEJBQXdCO0lBRXhCLE1BQU1rQyxtREFBbURqQjtJQUV6RCxJQUFJLENBQUNpQiwwQkFBMEJBLHVCQUF1QkMsTUFBTSxLQUFLLEdBQUc7UUFDbEUsT0FBTztJQUNUO0lBRUEsTUFBTUMsd0JBQ0puQixxQkFBcUIsYUFDakJpQixzQkFBc0IsQ0FBQyxFQUFFLEdBQ3pCQSxzQkFBc0IsQ0FBQ0EsdUJBQXVCQyxNQUFNLEdBQUcsRUFBRTtJQUUvRCx5R0FBeUc7SUFDekcseUVBQXlFO0lBQ3pFLE9BQU9DLDBCQUEwQkMsU0FBQUEsbUJBQW1CLEdBQ2hELE9BQ0FEO0FBQ047SUFyQmdCdkM7O1FBR2RHO1FBRStCRiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxuYXZpZ2F0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgRmxpZ2h0Um91dGVyU3RhdGUgfSBmcm9tICcuLi8uLi9zZXJ2ZXIvYXBwLXJlbmRlci90eXBlcydcbmltcG9ydCB0eXBlIHsgUGFyYW1zIH0gZnJvbSAnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJ1xuXG5pbXBvcnQgeyB1c2VDb250ZXh0LCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBBcHBSb3V0ZXJDb250ZXh0LFxuICBMYXlvdXRSb3V0ZXJDb250ZXh0LFxuICB0eXBlIEFwcFJvdXRlckluc3RhbmNlLFxufSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2FwcC1yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7XG4gIFNlYXJjaFBhcmFtc0NvbnRleHQsXG4gIFBhdGhuYW1lQ29udGV4dCxcbiAgUGF0aFBhcmFtc0NvbnRleHQsXG59IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvaG9va3MtY2xpZW50LWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUnXG5pbXBvcnQgeyBnZXRTZWdtZW50VmFsdWUgfSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL3JlZHVjZXJzL2dldC1zZWdtZW50LXZhbHVlJ1xuaW1wb3J0IHsgUEFHRV9TRUdNRU5UX0tFWSwgREVGQVVMVF9TRUdNRU5UX0tFWSB9IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvc2VnbWVudCdcbmltcG9ydCB7IFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zIH0gZnJvbSAnLi9uYXZpZ2F0aW9uLnJlYWN0LXNlcnZlcidcblxuY29uc3QgdXNlRHluYW1pY1JvdXRlUGFyYW1zID1cbiAgdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCdcbiAgICA/IChcbiAgICAgICAgcmVxdWlyZSgnLi4vLi4vc2VydmVyL2FwcC1yZW5kZXIvZHluYW1pYy1yZW5kZXJpbmcnKSBhcyB0eXBlb2YgaW1wb3J0KCcuLi8uLi9zZXJ2ZXIvYXBwLXJlbmRlci9keW5hbWljLXJlbmRlcmluZycpXG4gICAgICApLnVzZUR5bmFtaWNSb3V0ZVBhcmFtc1xuICAgIDogdW5kZWZpbmVkXG5cbi8qKlxuICogQSBbQ2xpZW50IENvbXBvbmVudF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL2NsaWVudC1jb21wb25lbnRzKSBob29rXG4gKiB0aGF0IGxldHMgeW91ICpyZWFkKiB0aGUgY3VycmVudCBVUkwncyBzZWFyY2ggcGFyYW1ldGVycy5cbiAqXG4gKiBMZWFybiBtb3JlIGFib3V0IFtgVVJMU2VhcmNoUGFyYW1zYCBvbiBNRE5dKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2RvY3MvV2ViL0FQSS9VUkxTZWFyY2hQYXJhbXMpXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBcInVzZSBjbGllbnRcIlxuICogaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuICpcbiAqIGV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gKiAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXG4gKiAgIHNlYXJjaFBhcmFtcy5nZXQoJ2ZvbycpIC8vIHJldHVybnMgJ2Jhcicgd2hlbiA/Zm9vPWJhclxuICogICAvLyAuLi5cbiAqIH1cbiAqIGBgYFxuICpcbiAqIFJlYWQgbW9yZTogW05leHQuanMgRG9jczogYHVzZVNlYXJjaFBhcmFtc2BdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9hcGktcmVmZXJlbmNlL2Z1bmN0aW9ucy91c2Utc2VhcmNoLXBhcmFtcylcbiAqL1xuLy8gQ2xpZW50IGNvbXBvbmVudHMgQVBJXG5leHBvcnQgZnVuY3Rpb24gdXNlU2VhcmNoUGFyYW1zKCk6IFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zIHtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlQ29udGV4dChTZWFyY2hQYXJhbXNDb250ZXh0KVxuXG4gIC8vIEluIHRoZSBjYXNlIHdoZXJlIHRoaXMgaXMgYG51bGxgLCB0aGUgY29tcGF0IHR5cGVzIGFkZGVkIGluXG4gIC8vIGBuZXh0LWVudi5kLnRzYCB3aWxsIGFkZCBhIG5ldyBvdmVybG9hZCB0aGF0IGNoYW5nZXMgdGhlIHJldHVybiB0eXBlIHRvXG4gIC8vIGluY2x1ZGUgYG51bGxgLlxuICBjb25zdCByZWFkb25seVNlYXJjaFBhcmFtcyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGlmICghc2VhcmNoUGFyYW1zKSB7XG4gICAgICAvLyBXaGVuIHRoZSByb3V0ZXIgaXMgbm90IHJlYWR5IGluIHBhZ2VzLCB3ZSB3b24ndCBoYXZlIHRoZSBzZWFyY2ggcGFyYW1zXG4gICAgICAvLyBhdmFpbGFibGUuXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cblxuICAgIHJldHVybiBuZXcgUmVhZG9ubHlVUkxTZWFyY2hQYXJhbXMoc2VhcmNoUGFyYW1zKVxuICB9LCBbc2VhcmNoUGFyYW1zXSkgYXMgUmVhZG9ubHlVUkxTZWFyY2hQYXJhbXNcblxuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAvLyBBc3luY0xvY2FsU3RvcmFnZSBzaG91bGQgbm90IGJlIGluY2x1ZGVkIGluIHRoZSBjbGllbnQgYnVuZGxlLlxuICAgIGNvbnN0IHsgYmFpbG91dFRvQ2xpZW50UmVuZGVyaW5nIH0gPVxuICAgICAgcmVxdWlyZSgnLi9iYWlsb3V0LXRvLWNsaWVudC1yZW5kZXJpbmcnKSBhcyB0eXBlb2YgaW1wb3J0KCcuL2JhaWxvdXQtdG8tY2xpZW50LXJlbmRlcmluZycpXG4gICAgLy8gVE9ETy1BUFA6IGhhbmRsZSBkeW5hbWljID0gJ2ZvcmNlLXN0YXRpYycgaGVyZSBhbmQgb24gdGhlIGNsaWVudFxuICAgIGJhaWxvdXRUb0NsaWVudFJlbmRlcmluZygndXNlU2VhcmNoUGFyYW1zKCknKVxuICB9XG5cbiAgcmV0dXJuIHJlYWRvbmx5U2VhcmNoUGFyYW1zXG59XG5cbi8qKlxuICogQSBbQ2xpZW50IENvbXBvbmVudF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL2NsaWVudC1jb21wb25lbnRzKSBob29rXG4gKiB0aGF0IGxldHMgeW91IHJlYWQgdGhlIGN1cnJlbnQgVVJMJ3MgcGF0aG5hbWUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBcInVzZSBjbGllbnRcIlxuICogaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG4gKlxuICogZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAqICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCkgLy8gcmV0dXJucyBcIi9kYXNoYm9hcmRcIiBvbiAvZGFzaGJvYXJkP2Zvbz1iYXJcbiAqICAvLyAuLi5cbiAqIH1cbiAqIGBgYFxuICpcbiAqIFJlYWQgbW9yZTogW05leHQuanMgRG9jczogYHVzZVBhdGhuYW1lYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3VzZS1wYXRobmFtZSlcbiAqL1xuLy8gQ2xpZW50IGNvbXBvbmVudHMgQVBJXG5leHBvcnQgZnVuY3Rpb24gdXNlUGF0aG5hbWUoKTogc3RyaW5nIHtcbiAgdXNlRHluYW1pY1JvdXRlUGFyYW1zPy4oJ3VzZVBhdGhuYW1lKCknKVxuXG4gIC8vIEluIHRoZSBjYXNlIHdoZXJlIHRoaXMgaXMgYG51bGxgLCB0aGUgY29tcGF0IHR5cGVzIGFkZGVkIGluIGBuZXh0LWVudi5kLnRzYFxuICAvLyB3aWxsIGFkZCBhIG5ldyBvdmVybG9hZCB0aGF0IGNoYW5nZXMgdGhlIHJldHVybiB0eXBlIHRvIGluY2x1ZGUgYG51bGxgLlxuICByZXR1cm4gdXNlQ29udGV4dChQYXRobmFtZUNvbnRleHQpIGFzIHN0cmluZ1xufVxuXG4vLyBDbGllbnQgY29tcG9uZW50cyBBUElcbmV4cG9ydCB7XG4gIFNlcnZlckluc2VydGVkSFRNTENvbnRleHQsXG4gIHVzZVNlcnZlckluc2VydGVkSFRNTCxcbn0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9zZXJ2ZXItaW5zZXJ0ZWQtaHRtbC5zaGFyZWQtcnVudGltZSdcblxuLyoqXG4gKlxuICogVGhpcyBob29rIGFsbG93cyB5b3UgdG8gcHJvZ3JhbW1hdGljYWxseSBjaGFuZ2Ugcm91dGVzIGluc2lkZSBbQ2xpZW50IENvbXBvbmVudF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL2NsaWVudC1jb21wb25lbnRzKS5cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqIFwidXNlIGNsaWVudFwiXG4gKiBpbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG4gKlxuICogZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAqICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICogIC8vIC4uLlxuICogIHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkJykgLy8gTmF2aWdhdGUgdG8gL2Rhc2hib2FyZFxuICogfVxuICogYGBgXG4gKlxuICogUmVhZCBtb3JlOiBbTmV4dC5qcyBEb2NzOiBgdXNlUm91dGVyYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3VzZS1yb3V0ZXIpXG4gKi9cbi8vIENsaWVudCBjb21wb25lbnRzIEFQSVxuZXhwb3J0IGZ1bmN0aW9uIHVzZVJvdXRlcigpOiBBcHBSb3V0ZXJJbnN0YW5jZSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZUNvbnRleHQoQXBwUm91dGVyQ29udGV4dClcbiAgaWYgKHJvdXRlciA9PT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBFcnJvcignaW52YXJpYW50IGV4cGVjdGVkIGFwcCByb3V0ZXIgdG8gYmUgbW91bnRlZCcpXG4gIH1cblxuICByZXR1cm4gcm91dGVyXG59XG5cbi8qKlxuICogQSBbQ2xpZW50IENvbXBvbmVudF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL2NsaWVudC1jb21wb25lbnRzKSBob29rXG4gKiB0aGF0IGxldHMgeW91IHJlYWQgYSByb3V0ZSdzIGR5bmFtaWMgcGFyYW1zIGZpbGxlZCBpbiBieSB0aGUgY3VycmVudCBVUkwuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiBcInVzZSBjbGllbnRcIlxuICogaW1wb3J0IHsgdXNlUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuICpcbiAqIGV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gKiAgIC8vIG9uIC9kYXNoYm9hcmQvW3RlYW1dIHdoZXJlIHBhdGhuYW1lIGlzIC9kYXNoYm9hcmQvbmV4dGpzXG4gKiAgIGNvbnN0IHsgdGVhbSB9ID0gdXNlUGFyYW1zKCkgLy8gdGVhbSA9PT0gXCJuZXh0anNcIlxuICogfVxuICogYGBgXG4gKlxuICogUmVhZCBtb3JlOiBbTmV4dC5qcyBEb2NzOiBgdXNlUGFyYW1zYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3VzZS1wYXJhbXMpXG4gKi9cbi8vIENsaWVudCBjb21wb25lbnRzIEFQSVxuZXhwb3J0IGZ1bmN0aW9uIHVzZVBhcmFtczxUIGV4dGVuZHMgUGFyYW1zID0gUGFyYW1zPigpOiBUIHtcbiAgdXNlRHluYW1pY1JvdXRlUGFyYW1zPy4oJ3VzZVBhcmFtcygpJylcblxuICByZXR1cm4gdXNlQ29udGV4dChQYXRoUGFyYW1zQ29udGV4dCkgYXMgVFxufVxuXG4vKiogR2V0IHRoZSBjYW5vbmljYWwgcGFyYW1ldGVycyBmcm9tIHRoZSBjdXJyZW50IGxldmVsIHRvIHRoZSBsZWFmIG5vZGUuICovXG4vLyBDbGllbnQgY29tcG9uZW50cyBBUElcbmZ1bmN0aW9uIGdldFNlbGVjdGVkTGF5b3V0U2VnbWVudFBhdGgoXG4gIHRyZWU6IEZsaWdodFJvdXRlclN0YXRlLFxuICBwYXJhbGxlbFJvdXRlS2V5OiBzdHJpbmcsXG4gIGZpcnN0ID0gdHJ1ZSxcbiAgc2VnbWVudFBhdGg6IHN0cmluZ1tdID0gW11cbik6IHN0cmluZ1tdIHtcbiAgbGV0IG5vZGU6IEZsaWdodFJvdXRlclN0YXRlXG4gIGlmIChmaXJzdCkge1xuICAgIC8vIFVzZSB0aGUgcHJvdmlkZWQgcGFyYWxsZWwgcm91dGUga2V5IG9uIHRoZSBmaXJzdCBwYXJhbGxlbCByb3V0ZVxuICAgIG5vZGUgPSB0cmVlWzFdW3BhcmFsbGVsUm91dGVLZXldXG4gIH0gZWxzZSB7XG4gICAgLy8gQWZ0ZXIgZmlyc3QgcGFyYWxsZWwgcm91dGUgcHJlZmVyIGNoaWxkcmVuLCBpZiB0aGVyZSdzIG5vIGNoaWxkcmVuIHBpY2sgdGhlIGZpcnN0IHBhcmFsbGVsIHJvdXRlLlxuICAgIGNvbnN0IHBhcmFsbGVsUm91dGVzID0gdHJlZVsxXVxuICAgIG5vZGUgPSBwYXJhbGxlbFJvdXRlcy5jaGlsZHJlbiA/PyBPYmplY3QudmFsdWVzKHBhcmFsbGVsUm91dGVzKVswXVxuICB9XG5cbiAgaWYgKCFub2RlKSByZXR1cm4gc2VnbWVudFBhdGhcbiAgY29uc3Qgc2VnbWVudCA9IG5vZGVbMF1cblxuICBsZXQgc2VnbWVudFZhbHVlID0gZ2V0U2VnbWVudFZhbHVlKHNlZ21lbnQpXG5cbiAgaWYgKCFzZWdtZW50VmFsdWUgfHwgc2VnbWVudFZhbHVlLnN0YXJ0c1dpdGgoUEFHRV9TRUdNRU5UX0tFWSkpIHtcbiAgICByZXR1cm4gc2VnbWVudFBhdGhcbiAgfVxuXG4gIHNlZ21lbnRQYXRoLnB1c2goc2VnbWVudFZhbHVlKVxuXG4gIHJldHVybiBnZXRTZWxlY3RlZExheW91dFNlZ21lbnRQYXRoKFxuICAgIG5vZGUsXG4gICAgcGFyYWxsZWxSb3V0ZUtleSxcbiAgICBmYWxzZSxcbiAgICBzZWdtZW50UGF0aFxuICApXG59XG5cbi8qKlxuICogQSBbQ2xpZW50IENvbXBvbmVudF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL2NsaWVudC1jb21wb25lbnRzKSBob29rXG4gKiB0aGF0IGxldHMgeW91IHJlYWQgdGhlIGFjdGl2ZSByb3V0ZSBzZWdtZW50cyAqKmJlbG93KiogdGhlIExheW91dCBpdCBpcyBjYWxsZWQgZnJvbS5cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHNcbiAqICd1c2UgY2xpZW50J1xuICpcbiAqIGltcG9ydCB7IHVzZVNlbGVjdGVkTGF5b3V0U2VnbWVudHMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG4gKlxuICogZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRXhhbXBsZUNsaWVudENvbXBvbmVudCgpIHtcbiAqICAgY29uc3Qgc2VnbWVudHMgPSB1c2VTZWxlY3RlZExheW91dFNlZ21lbnRzKClcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPHVsPlxuICogICAgICAge3NlZ21lbnRzLm1hcCgoc2VnbWVudCwgaW5kZXgpID0+IChcbiAqICAgICAgICAgPGxpIGtleT17aW5kZXh9PntzZWdtZW50fTwvbGk+XG4gKiAgICAgICApKX1cbiAqICAgICA8L3VsPlxuICogICApXG4gKiB9XG4gKiBgYGBcbiAqXG4gKiBSZWFkIG1vcmU6IFtOZXh0LmpzIERvY3M6IGB1c2VTZWxlY3RlZExheW91dFNlZ21lbnRzYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3VzZS1zZWxlY3RlZC1sYXlvdXQtc2VnbWVudHMpXG4gKi9cbi8vIENsaWVudCBjb21wb25lbnRzIEFQSVxuZXhwb3J0IGZ1bmN0aW9uIHVzZVNlbGVjdGVkTGF5b3V0U2VnbWVudHMoXG4gIHBhcmFsbGVsUm91dGVLZXk6IHN0cmluZyA9ICdjaGlsZHJlbidcbik6IHN0cmluZ1tdIHtcbiAgdXNlRHluYW1pY1JvdXRlUGFyYW1zPy4oJ3VzZVNlbGVjdGVkTGF5b3V0U2VnbWVudHMoKScpXG5cbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoTGF5b3V0Um91dGVyQ29udGV4dClcbiAgLy8gQHRzLWV4cGVjdC1lcnJvciBUaGlzIG9ubHkgaGFwcGVucyBpbiBgcGFnZXNgLiBUeXBlIGlzIG92ZXJ3cml0dGVuIGluIG5hdmlnYXRpb24uZC50c1xuICBpZiAoIWNvbnRleHQpIHJldHVybiBudWxsXG5cbiAgcmV0dXJuIGdldFNlbGVjdGVkTGF5b3V0U2VnbWVudFBhdGgoY29udGV4dC5wYXJlbnRUcmVlLCBwYXJhbGxlbFJvdXRlS2V5KVxufVxuXG4vKipcbiAqIEEgW0NsaWVudCBDb21wb25lbnRdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL3JlbmRlcmluZy9jbGllbnQtY29tcG9uZW50cykgaG9va1xuICogdGhhdCBsZXRzIHlvdSByZWFkIHRoZSBhY3RpdmUgcm91dGUgc2VnbWVudCAqKm9uZSBsZXZlbCBiZWxvdyoqIHRoZSBMYXlvdXQgaXQgaXMgY2FsbGVkIGZyb20uXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiAndXNlIGNsaWVudCdcbiAqIGltcG9ydCB7IHVzZVNlbGVjdGVkTGF5b3V0U2VnbWVudCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbiAqXG4gKiBleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFeGFtcGxlQ2xpZW50Q29tcG9uZW50KCkge1xuICogICBjb25zdCBzZWdtZW50ID0gdXNlU2VsZWN0ZWRMYXlvdXRTZWdtZW50KClcbiAqXG4gKiAgIHJldHVybiA8cD5BY3RpdmUgc2VnbWVudDoge3NlZ21lbnR9PC9wPlxuICogfVxuICogYGBgXG4gKlxuICogUmVhZCBtb3JlOiBbTmV4dC5qcyBEb2NzOiBgdXNlU2VsZWN0ZWRMYXlvdXRTZWdtZW50YF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3VzZS1zZWxlY3RlZC1sYXlvdXQtc2VnbWVudClcbiAqL1xuLy8gQ2xpZW50IGNvbXBvbmVudHMgQVBJXG5leHBvcnQgZnVuY3Rpb24gdXNlU2VsZWN0ZWRMYXlvdXRTZWdtZW50KFxuICBwYXJhbGxlbFJvdXRlS2V5OiBzdHJpbmcgPSAnY2hpbGRyZW4nXG4pOiBzdHJpbmcgfCBudWxsIHtcbiAgdXNlRHluYW1pY1JvdXRlUGFyYW1zPy4oJ3VzZVNlbGVjdGVkTGF5b3V0U2VnbWVudCgpJylcblxuICBjb25zdCBzZWxlY3RlZExheW91dFNlZ21lbnRzID0gdXNlU2VsZWN0ZWRMYXlvdXRTZWdtZW50cyhwYXJhbGxlbFJvdXRlS2V5KVxuXG4gIGlmICghc2VsZWN0ZWRMYXlvdXRTZWdtZW50cyB8fCBzZWxlY3RlZExheW91dFNlZ21lbnRzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICBjb25zdCBzZWxlY3RlZExheW91dFNlZ21lbnQgPVxuICAgIHBhcmFsbGVsUm91dGVLZXkgPT09ICdjaGlsZHJlbidcbiAgICAgID8gc2VsZWN0ZWRMYXlvdXRTZWdtZW50c1swXVxuICAgICAgOiBzZWxlY3RlZExheW91dFNlZ21lbnRzW3NlbGVjdGVkTGF5b3V0U2VnbWVudHMubGVuZ3RoIC0gMV1cblxuICAvLyBpZiB0aGUgZGVmYXVsdCBzbG90IGlzIHNob3dpbmcsIHdlIHJldHVybiBudWxsIHNpbmNlIGl0J3Mgbm90IHRlY2huaWNhbGx5IFwic2VsZWN0ZWRcIiAoaXQncyBhIGZhbGxiYWNrKVxuICAvLyBhbmQgcmV0dXJuaW5nIGFuIGludGVybmFsIHZhbHVlIGxpa2UgYF9fREVGQVVMVF9fYCB3b3VsZCBiZSBjb25mdXNpbmcuXG4gIHJldHVybiBzZWxlY3RlZExheW91dFNlZ21lbnQgPT09IERFRkFVTFRfU0VHTUVOVF9LRVlcbiAgICA/IG51bGxcbiAgICA6IHNlbGVjdGVkTGF5b3V0U2VnbWVudFxufVxuXG4vLyBTaGFyZWQgY29tcG9uZW50cyBBUElzXG5leHBvcnQge1xuICBub3RGb3VuZCxcbiAgZm9yYmlkZGVuLFxuICB1bmF1dGhvcml6ZWQsXG4gIHJlZGlyZWN0LFxuICBwZXJtYW5lbnRSZWRpcmVjdCxcbiAgUmVkaXJlY3RUeXBlLFxuICBSZWFkb25seVVSTFNlYXJjaFBhcmFtcyxcbiAgdW5zdGFibGVfcmV0aHJvdyxcbn0gZnJvbSAnLi9uYXZpZ2F0aW9uLnJlYWN0LXNlcnZlcidcbiJdLCJuYW1lcyI6WyJSZWFkb25seVVSTFNlYXJjaFBhcmFtcyIsIlJlZGlyZWN0VHlwZSIsIlNlcnZlckluc2VydGVkSFRNTENvbnRleHQiLCJmb3JiaWRkZW4iLCJub3RGb3VuZCIsInBlcm1hbmVudFJlZGlyZWN0IiwicmVkaXJlY3QiLCJ1bmF1dGhvcml6ZWQiLCJ1bnN0YWJsZV9yZXRocm93IiwidXNlUGFyYW1zIiwidXNlUGF0aG5hbWUiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJ1c2VTZWxlY3RlZExheW91dFNlZ21lbnQiLCJ1c2VTZWxlY3RlZExheW91dFNlZ21lbnRzIiwidXNlU2VydmVySW5zZXJ0ZWRIVE1MIiwidXNlRHluYW1pY1JvdXRlUGFyYW1zIiwid2luZG93IiwicmVxdWlyZSIsInVuZGVmaW5lZCIsInNlYXJjaFBhcmFtcyIsInVzZUNvbnRleHQiLCJTZWFyY2hQYXJhbXNDb250ZXh0IiwicmVhZG9ubHlTZWFyY2hQYXJhbXMiLCJ1c2VNZW1vIiwiYmFpbG91dFRvQ2xpZW50UmVuZGVyaW5nIiwiUGF0aG5hbWVDb250ZXh0Iiwicm91dGVyIiwiQXBwUm91dGVyQ29udGV4dCIsIkVycm9yIiwiUGF0aFBhcmFtc0NvbnRleHQiLCJnZXRTZWxlY3RlZExheW91dFNlZ21lbnRQYXRoIiwidHJlZSIsInBhcmFsbGVsUm91dGVLZXkiLCJmaXJzdCIsInNlZ21lbnRQYXRoIiwibm9kZSIsInBhcmFsbGVsUm91dGVzIiwiY2hpbGRyZW4iLCJPYmplY3QiLCJ2YWx1ZXMiLCJzZWdtZW50Iiwic2VnbWVudFZhbHVlIiwiZ2V0U2VnbWVudFZhbHVlIiwic3RhcnRzV2l0aCIsIlBBR0VfU0VHTUVOVF9LRVkiLCJwdXNoIiwiY29udGV4dCIsIkxheW91dFJvdXRlckNvbnRleHQiLCJwYXJlbnRUcmVlIiwic2VsZWN0ZWRMYXlvdXRTZWdtZW50cyIsImxlbmd0aCIsInNlbGVjdGVkTGF5b3V0U2VnbWVudCIsIkRFRkFVTFRfU0VHTUVOVF9LRVkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/navigation.react-server.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/navigation.react-server.js ***!
  \*****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/** @internal */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ReadonlyURLSearchParams: function() {\n        return ReadonlyURLSearchParams;\n    },\n    RedirectType: function() {\n        return _redirecterror.RedirectType;\n    },\n    forbidden: function() {\n        return _forbidden.forbidden;\n    },\n    notFound: function() {\n        return _notfound.notFound;\n    },\n    permanentRedirect: function() {\n        return _redirect.permanentRedirect;\n    },\n    redirect: function() {\n        return _redirect.redirect;\n    },\n    unauthorized: function() {\n        return _unauthorized.unauthorized;\n    },\n    unstable_rethrow: function() {\n        return _unstablerethrow.unstable_rethrow;\n    }\n});\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _notfound = __webpack_require__(/*! ./not-found */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found.js\");\nconst _forbidden = __webpack_require__(/*! ./forbidden */ \"(app-pages-browser)/./node_modules/next/dist/client/components/forbidden.js\");\nconst _unauthorized = __webpack_require__(/*! ./unauthorized */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unauthorized.js\");\nconst _unstablerethrow = __webpack_require__(/*! ./unstable-rethrow */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unstable-rethrow.js\");\nclass ReadonlyURLSearchParamsError extends Error {\n    constructor(){\n        super('Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams');\n    }\n}\nclass ReadonlyURLSearchParams extends URLSearchParams {\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ append() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ delete() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ set() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ sort() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=navigation.react-server.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbi5yZWFjdC1zZXJ2ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsY0FBYzs7Ozs7Ozs7Ozs7O0lBa0NMQSx1QkFBdUI7ZUFBdkJBOztJQUxBQyxZQUFZO2VBQVpBLGVBQUFBLFlBQVk7O0lBRVpDLFNBQVM7ZUFBVEEsV0FBQUEsU0FBUzs7SUFEVEMsUUFBUTtlQUFSQSxVQUFBQSxRQUFROztJQUZFQyxpQkFBaUI7ZUFBakJBLFVBQUFBLGlCQUFpQjs7SUFBM0JDLFFBQVE7ZUFBUkEsVUFBQUEsUUFBUTs7SUFJUkMsWUFBWTtlQUFaQSxjQUFBQSxZQUFZOztJQUNaQyxnQkFBZ0I7ZUFBaEJBLGlCQUFBQSxnQkFBZ0I7OztzQ0FMbUI7MkNBQ2Y7c0NBQ0o7dUNBQ0M7MENBQ0c7NkNBQ0k7QUFoQ2pDLE1BQU1DLHFDQUFxQ0M7SUFDekNDLGFBQWM7UUFDWixLQUFLLENBQ0g7SUFFSjtBQUNGO0FBRUEsTUFBTVYsZ0NBQWdDVztJQUNwQyx3S0FBd0ssR0FDeEtDLFNBQVM7UUFDUCxNQUFNLElBQUlKO0lBQ1o7SUFDQSx3S0FBd0ssR0FDeEtLLFNBQVM7UUFDUCxNQUFNLElBQUlMO0lBQ1o7SUFDQSx3S0FBd0ssR0FDeEtNLE1BQU07UUFDSixNQUFNLElBQUlOO0lBQ1o7SUFDQSx3S0FBd0ssR0FDeEtPLE9BQU87UUFDTCxNQUFNLElBQUlQO0lBQ1o7QUFDRiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxuYXZpZ2F0aW9uLnJlYWN0LXNlcnZlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQGludGVybmFsICovXG5jbGFzcyBSZWFkb25seVVSTFNlYXJjaFBhcmFtc0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICBzdXBlcihcbiAgICAgICdNZXRob2QgdW5hdmFpbGFibGUgb24gYFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zYC4gUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9mdW5jdGlvbnMvdXNlLXNlYXJjaC1wYXJhbXMjdXBkYXRpbmctc2VhcmNocGFyYW1zJ1xuICAgIClcbiAgfVxufVxuXG5jbGFzcyBSZWFkb25seVVSTFNlYXJjaFBhcmFtcyBleHRlbmRzIFVSTFNlYXJjaFBhcmFtcyB7XG4gIC8qKiBAZGVwcmVjYXRlZCBNZXRob2QgdW5hdmFpbGFibGUgb24gYFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zYC4gUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9mdW5jdGlvbnMvdXNlLXNlYXJjaC1wYXJhbXMjdXBkYXRpbmctc2VhcmNocGFyYW1zICovXG4gIGFwcGVuZCgpIHtcbiAgICB0aHJvdyBuZXcgUmVhZG9ubHlVUkxTZWFyY2hQYXJhbXNFcnJvcigpXG4gIH1cbiAgLyoqIEBkZXByZWNhdGVkIE1ldGhvZCB1bmF2YWlsYWJsZSBvbiBgUmVhZG9ubHlVUkxTZWFyY2hQYXJhbXNgLiBSZWFkIG1vcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL2FwcC9hcGktcmVmZXJlbmNlL2Z1bmN0aW9ucy91c2Utc2VhcmNoLXBhcmFtcyN1cGRhdGluZy1zZWFyY2hwYXJhbXMgKi9cbiAgZGVsZXRlKCkge1xuICAgIHRocm93IG5ldyBSZWFkb25seVVSTFNlYXJjaFBhcmFtc0Vycm9yKClcbiAgfVxuICAvKiogQGRlcHJlY2F0ZWQgTWV0aG9kIHVuYXZhaWxhYmxlIG9uIGBSZWFkb25seVVSTFNlYXJjaFBhcmFtc2AuIFJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3VzZS1zZWFyY2gtcGFyYW1zI3VwZGF0aW5nLXNlYXJjaHBhcmFtcyAqL1xuICBzZXQoKSB7XG4gICAgdGhyb3cgbmV3IFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zRXJyb3IoKVxuICB9XG4gIC8qKiBAZGVwcmVjYXRlZCBNZXRob2QgdW5hdmFpbGFibGUgb24gYFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zYC4gUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9mdW5jdGlvbnMvdXNlLXNlYXJjaC1wYXJhbXMjdXBkYXRpbmctc2VhcmNocGFyYW1zICovXG4gIHNvcnQoKSB7XG4gICAgdGhyb3cgbmV3IFJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zRXJyb3IoKVxuICB9XG59XG5cbmV4cG9ydCB7IHJlZGlyZWN0LCBwZXJtYW5lbnRSZWRpcmVjdCB9IGZyb20gJy4vcmVkaXJlY3QnXG5leHBvcnQgeyBSZWRpcmVjdFR5cGUgfSBmcm9tICcuL3JlZGlyZWN0LWVycm9yJ1xuZXhwb3J0IHsgbm90Rm91bmQgfSBmcm9tICcuL25vdC1mb3VuZCdcbmV4cG9ydCB7IGZvcmJpZGRlbiB9IGZyb20gJy4vZm9yYmlkZGVuJ1xuZXhwb3J0IHsgdW5hdXRob3JpemVkIH0gZnJvbSAnLi91bmF1dGhvcml6ZWQnXG5leHBvcnQgeyB1bnN0YWJsZV9yZXRocm93IH0gZnJvbSAnLi91bnN0YWJsZS1yZXRocm93J1xuZXhwb3J0IHsgUmVhZG9ubHlVUkxTZWFyY2hQYXJhbXMgfVxuIl0sIm5hbWVzIjpbIlJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zIiwiUmVkaXJlY3RUeXBlIiwiZm9yYmlkZGVuIiwibm90Rm91bmQiLCJwZXJtYW5lbnRSZWRpcmVjdCIsInJlZGlyZWN0IiwidW5hdXRob3JpemVkIiwidW5zdGFibGVfcmV0aHJvdyIsIlJlYWRvbmx5VVJMU2VhcmNoUGFyYW1zRXJyb3IiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwiVVJMU2VhcmNoUGFyYW1zIiwiYXBwZW5kIiwiZGVsZXRlIiwic2V0Iiwic29ydCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/navigation.react-server.js\n"));

/***/ })

}]);