"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_d"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"initializeDevBuildIndicatorForAppRouter\", ({\n    enumerable: true,\n    get: function() {\n        return initializeDevBuildIndicatorForAppRouter;\n    }\n}));\nconst _devbuildindicator = __webpack_require__(/*! ./internal/dev-build-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst initializeDevBuildIndicatorForAppRouter = ()=>{\n    if (false) {}\n    _devbuildindicator.devBuildIndicator.initialize();\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize-for-app-router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2luaXRpYWxpemUtZm9yLWFwcC1yb3V0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7OzsyRUFHYUE7OztlQUFBQTs7OytDQUhxQjtBQUczQixNQUFNQSwwQ0FBMEM7SUFDckQsSUFBSSxLQUFpQyxFQUFFLEVBRXRDO0lBRURJLG1CQUFBQSxpQkFBaUIsQ0FBQ0MsVUFBVTtBQUM5QiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxkZXZcXGRldi1idWlsZC1pbmRpY2F0b3JcXGluaXRpYWxpemUtZm9yLWFwcC1yb3V0ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGV2QnVpbGRJbmRpY2F0b3IgfSBmcm9tICcuL2ludGVybmFsL2Rldi1idWlsZC1pbmRpY2F0b3InXG5cbi8qKiBJbnRlZ3JhdGVzIHRoZSBnZW5lcmljIGRldiBidWlsZCBpbmRpY2F0b3Igd2l0aCB0aGUgQXBwIFJvdXRlci4gKi9cbmV4cG9ydCBjb25zdCBpbml0aWFsaXplRGV2QnVpbGRJbmRpY2F0b3JGb3JBcHBSb3V0ZXIgPSAoKSA9PiB7XG4gIGlmICghcHJvY2Vzcy5lbnYuX19ORVhUX0RFVl9JTkRJQ0FUT1IpIHtcbiAgICByZXR1cm5cbiAgfVxuXG4gIGRldkJ1aWxkSW5kaWNhdG9yLmluaXRpYWxpemUoKVxufVxuIl0sIm5hbWVzIjpbImluaXRpYWxpemVEZXZCdWlsZEluZGljYXRvckZvckFwcFJvdXRlciIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfREVWX0lORElDQVRPUiIsImRldkJ1aWxkSW5kaWNhdG9yIiwiaW5pdGlhbGl6ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"devBuildIndicator\", ({\n    enumerable: true,\n    get: function() {\n        return devBuildIndicator;\n    }\n}));\nconst _initialize = __webpack_require__(/*! ./initialize */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst NOOP = ()=>{};\n_c = NOOP;\nconst devBuildIndicator = {\n    /** Shows build indicator when Next.js is compiling. Requires initialize() first. */ show: NOOP,\n    /** Hides build indicator when Next.js finishes compiling. Requires initialize() first. */ hide: NOOP,\n    /** Sets up the build indicator UI component. Call this before using show/hide. */ initialize: _initialize.initialize\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-build-indicator.js.map\nvar _c;\n$RefreshReg$(_c, \"NOOP\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2ludGVybmFsL2Rldi1idWlsZC1pbmRpY2F0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFJYUE7OztlQUFBQTs7O3dDQUpjO0FBRTNCLGFBQWEsS0FBTztLQUFkQztBQUVDLE1BQU1ELG9CQUFvQjtJQUMvQixrRkFBa0YsR0FDbEZFLE1BQU1EO0lBQ04sd0ZBQXdGLEdBQ3hGRSxNQUFNRjtJQUNOLGdGQUFnRixHQUNoRkcsWUFBQUEsWUFBQUEsVUFBVTtBQUNaIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcZGV2LWJ1aWxkLWluZGljYXRvclxcaW50ZXJuYWxcXGRldi1idWlsZC1pbmRpY2F0b3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW5pdGlhbGl6ZSB9IGZyb20gJy4vaW5pdGlhbGl6ZSdcblxuY29uc3QgTk9PUCA9ICgpID0+IHt9XG5cbmV4cG9ydCBjb25zdCBkZXZCdWlsZEluZGljYXRvciA9IHtcbiAgLyoqIFNob3dzIGJ1aWxkIGluZGljYXRvciB3aGVuIE5leHQuanMgaXMgY29tcGlsaW5nLiBSZXF1aXJlcyBpbml0aWFsaXplKCkgZmlyc3QuICovXG4gIHNob3c6IE5PT1AsXG4gIC8qKiBIaWRlcyBidWlsZCBpbmRpY2F0b3Igd2hlbiBOZXh0LmpzIGZpbmlzaGVzIGNvbXBpbGluZy4gUmVxdWlyZXMgaW5pdGlhbGl6ZSgpIGZpcnN0LiAqL1xuICBoaWRlOiBOT09QLFxuICAvKiogU2V0cyB1cCB0aGUgYnVpbGQgaW5kaWNhdG9yIFVJIGNvbXBvbmVudC4gQ2FsbCB0aGlzIGJlZm9yZSB1c2luZyBzaG93L2hpZGUuICovXG4gIGluaXRpYWxpemUsXG59XG4iXSwibmFtZXMiOlsiZGV2QnVpbGRJbmRpY2F0b3IiLCJOT09QIiwic2hvdyIsImhpZGUiLCJpbml0aWFsaXplIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js ***!
  \*****************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleDevBuildIndicatorHmrEvents\", ({\n    enumerable: true,\n    get: function() {\n        return handleDevBuildIndicatorHmrEvents;\n    }\n}));\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst handleDevBuildIndicatorHmrEvents = (obj)=>{\n    try {\n        if (!('action' in obj)) {\n            return;\n        }\n        // eslint-disable-next-line default-case\n        switch(obj.action){\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n                _devbuildindicator.devBuildIndicator.show();\n                break;\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n                _devbuildindicator.devBuildIndicator.hide();\n                break;\n        }\n    } catch (e) {}\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-dev-build-indicator-hmr-events.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    initialize: function() {\n        return initialize;\n    },\n    useIsDevBuilding: function() {\n        return useIsDevBuilding;\n    }\n});\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nlet isVisible = false;\nlet listeners = [];\nconst subscribe = (listener)=>{\n    listeners.push(listener);\n    return ()=>{\n        listeners = listeners.filter((l)=>l !== listener);\n    };\n};\nconst getSnapshot = ()=>isVisible;\nfunction useIsDevBuilding() {\n    return (0, _react.useSyncExternalStore)(subscribe, getSnapshot);\n}\nfunction initialize() {\n    _devbuildindicator.devBuildIndicator.show = ()=>{\n        isVisible = true;\n        listeners.forEach((listener)=>listener());\n    };\n    _devbuildindicator.devBuildIndicator.hide = ()=>{\n        isVisible = false;\n        listeners.forEach((listener)=>listener());\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/noop-turbopack-hmr.js ***!
  \*****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// The Turbopack HMR client can't be properly omitted at the moment (WEB-1589),\n// so instead we remap its import to this file in webpack builds.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"connect\", ({\n    enumerable: true,\n    get: function() {\n        return connect;\n    }\n}));\nfunction connect() {}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-turbopack-hmr.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9ub29wLXR1cmJvcGFjay1obXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsK0VBQStFO0FBQy9FLGlFQUFpRTs7Ozs7MkNBQ2pEQTs7O2VBQUFBOzs7QUFBVCxTQUFTQSxXQUFXIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcbm9vcC10dXJib3BhY2staG1yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoZSBUdXJib3BhY2sgSE1SIGNsaWVudCBjYW4ndCBiZSBwcm9wZXJseSBvbWl0dGVkIGF0IHRoZSBtb21lbnQgKFdFQi0xNTg5KSxcbi8vIHNvIGluc3RlYWQgd2UgcmVtYXAgaXRzIGltcG9ydCB0byB0aGlzIGZpbGUgaW4gd2VicGFjayBidWlsZHMuXG5leHBvcnQgZnVuY3Rpb24gY29ubmVjdCgpIHt9XG4iXSwibmFtZXMiOlsiY29ubmVjdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/client/flight-data-helpers.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFlightDataPartsFromPath: function() {\n        return getFlightDataPartsFromPath;\n    },\n    getNextFlightSegmentPath: function() {\n        return getNextFlightSegmentPath;\n    },\n    normalizeFlightData: function() {\n        return normalizeFlightData;\n    }\n});\nfunction getFlightDataPartsFromPath(flightDataPath) {\n    // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n    const flightDataPathLength = 4;\n    // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n    const [tree, seedData, head, isHeadPartial] = flightDataPath.slice(-flightDataPathLength);\n    // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n    const segmentPath = flightDataPath.slice(0, -flightDataPathLength);\n    var _segmentPath_;\n    return {\n        // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n        // to the start of the segment path in some places which makes it hard to use solely the segment path.\n        // Look for \"// TODO-APP: remove ''\" in the codebase.\n        pathToSegment: segmentPath.slice(0, -1),\n        segmentPath,\n        // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n        // in which case we default to ''.\n        segment: (_segmentPath_ = segmentPath[segmentPath.length - 1]) != null ? _segmentPath_ : '',\n        tree,\n        seedData,\n        head,\n        isHeadPartial,\n        isRootRender: flightDataPath.length === flightDataPathLength\n    };\n}\nfunction getNextFlightSegmentPath(flightSegmentPath) {\n    // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n    // to get the next segment path.\n    return flightSegmentPath.slice(2);\n}\nfunction normalizeFlightData(flightData) {\n    // FlightData can be a string when the server didn't respond with a proper flight response,\n    // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n    if (typeof flightData === 'string') {\n        return flightData;\n    }\n    return flightData.map(getFlightDataPartsFromPath);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=flight-data-helpers.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/has-base-path.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hasBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return hasBasePath;\n    }\n}));\nconst _pathhasprefix = __webpack_require__(/*! ../shared/lib/router/utils/path-has-prefix */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js\");\nconst basePath =  false || '';\nfunction hasBasePath(path) {\n    return (0, _pathhasprefix.pathHasPrefix)(path, basePath);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-base-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2hhcy1iYXNlLXBhdGguanMiLCJtYXBwaW5ncyI6Ijs7OzsrQ0FJZ0JBOzs7ZUFBQUE7OzsyQ0FKYztBQUU5QixNQUFNQyxXQUFZQyxNQUFrQyxJQUFlO0FBRTVELFNBQVNGLFlBQVlLLElBQVk7SUFDdEMsT0FBT0MsQ0FBQUEsR0FBQUEsZUFBQUEsYUFBQUEsRUFBY0QsTUFBTUo7QUFDN0IiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHNyY1xcY2xpZW50XFxoYXMtYmFzZS1wYXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBhdGhIYXNQcmVmaXggfSBmcm9tICcuLi9zaGFyZWQvbGliL3JvdXRlci91dGlscy9wYXRoLWhhcy1wcmVmaXgnXG5cbmNvbnN0IGJhc2VQYXRoID0gKHByb2Nlc3MuZW52Ll9fTkVYVF9ST1VURVJfQkFTRVBBVEggYXMgc3RyaW5nKSB8fCAnJ1xuXG5leHBvcnQgZnVuY3Rpb24gaGFzQmFzZVBhdGgocGF0aDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBwYXRoSGFzUHJlZml4KHBhdGgsIGJhc2VQYXRoKVxufVxuIl0sIm5hbWVzIjpbImhhc0Jhc2VQYXRoIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJwYXRoSGFzUHJlZml4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/amp-dev.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/dev/amp-dev.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* globals __webpack_hash__ */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _fouc = __webpack_require__(/*! ./fouc */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/fouc.js\");\nconst _ondemandentriesclient = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./on-demand-entries-client */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/on-demand-entries-client.js\"));\nconst _websocket = __webpack_require__(/*! ../components/react-dev-overlay/pages/websocket */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _hotreloadertypes = __webpack_require__(/*! ../../server/dev/hot-reloader-types */ \"(pages-dir-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst data = JSON.parse(document.getElementById('__NEXT_DATA__').textContent);\nwindow.__NEXT_DATA__ = data;\nlet { assetPrefix, page } = data;\nassetPrefix = assetPrefix || '';\nlet mostRecentHash = null;\n/* eslint-disable-next-line */ let curHash = __webpack_require__.h();\nconst hotUpdatePath = assetPrefix + (assetPrefix.endsWith('/') ? '' : '/') + '_next/static/webpack/';\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n    // __webpack_hash__ is the hash of the current compilation.\n    // It's a global variable injected by Webpack.\n    /* eslint-disable-next-line */ return mostRecentHash !== __webpack_require__.h();\n}\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    return module.hot.status() === 'idle';\n}\n// This function reads code updates on the fly and hard\n// reloads the page when it has changed.\nasync function tryApplyUpdates() {\n    if (!isUpdateAvailable() || !canApplyUpdates()) {\n        return;\n    }\n    try {\n        const res = await fetch(typeof __webpack_require__.j !== 'undefined' ? \"\" + hotUpdatePath + curHash + \".\" + __webpack_require__.j + \".hot-update.json\" : \"\" + hotUpdatePath + curHash + \".hot-update.json\");\n        const jsonData = await res.json();\n        const curPage = page === '/' ? 'index' : page;\n        // webpack 5 uses an array instead\n        const pageUpdated = (Array.isArray(jsonData.c) ? jsonData.c : Object.keys(jsonData.c)).some((mod)=>{\n            return mod.indexOf(\"pages\" + (curPage.startsWith('/') ? curPage : \"/\" + curPage)) !== -1 || mod.indexOf((\"pages\" + (curPage.startsWith('/') ? curPage : \"/\" + curPage)).replace(/\\//g, '\\\\')) !== -1;\n        });\n        if (pageUpdated) {\n            window.location.reload();\n        } else {\n            curHash = mostRecentHash;\n        }\n    } catch (err) {\n        console.error('Error occurred checking for update', err);\n        window.location.reload();\n    }\n}\n(0, _websocket.addMessageListener)((message)=>{\n    if (!('action' in message)) {\n        return;\n    }\n    try {\n        // actions which are not related to amp-dev\n        if (message.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR || message.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE) {\n            return;\n        }\n        if (message.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC || message.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n            if (!message.hash) {\n                return;\n            }\n            mostRecentHash = message.hash;\n            tryApplyUpdates();\n        } else if (message.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE) {\n            window.location.reload();\n        }\n    } catch (err) {\n        var _err_stack;\n        console.warn('[HMR] Invalid message: ' + JSON.stringify(message) + '\\n' + ((_err_stack = err == null ? void 0 : err.stack) != null ? _err_stack : ''));\n    }\n});\n(0, _websocket.connectHMR)({\n    assetPrefix,\n    path: '/_next/webpack-hmr'\n});\n(0, _fouc.displayContent)();\n(0, _ondemandentriesclient.default)(data.page);\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=amp-dev.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/amp-dev.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-page-router.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-page-router.js ***!
  \*********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"initializeDevBuildIndicatorForPageRouter\", ({\n    enumerable: true,\n    get: function() {\n        return initializeDevBuildIndicatorForPageRouter;\n    }\n}));\nconst _websocket = __webpack_require__(/*! ../../components/react-dev-overlay/pages/websocket */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _devbuildindicator = __webpack_require__(/*! ./internal/dev-build-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst _handledevbuildindicatorhmrevents = __webpack_require__(/*! ./internal/handle-dev-build-indicator-hmr-events */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\");\nconst initializeDevBuildIndicatorForPageRouter = ()=>{\n    if (false) {}\n    _devbuildindicator.devBuildIndicator.initialize();\n    // Add message listener specifically for Pages Router to handle lifecycle events\n    // related to dev builds (building, built, sync)\n    (0, _websocket.addMessageListener)(_handledevbuildindicatorhmrevents.handleDevBuildIndicatorHmrEvents);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize-for-page-router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2luaXRpYWxpemUtZm9yLXBhZ2Utcm91dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7NEVBS2FBOzs7ZUFBQUE7Ozt1Q0FMc0I7K0NBQ0Q7OERBQ2U7QUFHMUMsTUFBTUEsMkNBQTJDO0lBQ3RELElBQUksS0FBaUMsRUFBRSxFQUV0QztJQUVESSxtQkFBQUEsaUJBQWlCLENBQUNDLFVBQVU7SUFFNUIsZ0ZBQWdGO0lBQ2hGLGdEQUFnRDtJQUNoREMsQ0FBQUEsR0FBQUEsV0FBQUEsa0JBQUFBLEVBQW1CQyxrQ0FBQUEsZ0NBQWdDO0FBQ3JEIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcZGV2LWJ1aWxkLWluZGljYXRvclxcaW5pdGlhbGl6ZS1mb3ItcGFnZS1yb3V0ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWRkTWVzc2FnZUxpc3RlbmVyIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9wYWdlcy93ZWJzb2NrZXQnXG5pbXBvcnQgeyBkZXZCdWlsZEluZGljYXRvciB9IGZyb20gJy4vaW50ZXJuYWwvZGV2LWJ1aWxkLWluZGljYXRvcidcbmltcG9ydCB7IGhhbmRsZURldkJ1aWxkSW5kaWNhdG9ySG1yRXZlbnRzIH0gZnJvbSAnLi9pbnRlcm5hbC9oYW5kbGUtZGV2LWJ1aWxkLWluZGljYXRvci1obXItZXZlbnRzJ1xuXG4vKiogSW50ZWdyYXRlcyB0aGUgZ2VuZXJpYyBkZXYgYnVpbGQgaW5kaWNhdG9yIHdpdGggdGhlIFBhZ2VzIFJvdXRlci4gKi9cbmV4cG9ydCBjb25zdCBpbml0aWFsaXplRGV2QnVpbGRJbmRpY2F0b3JGb3JQYWdlUm91dGVyID0gKCkgPT4ge1xuICBpZiAoIXByb2Nlc3MuZW52Ll9fTkVYVF9ERVZfSU5ESUNBVE9SKSB7XG4gICAgcmV0dXJuXG4gIH1cblxuICBkZXZCdWlsZEluZGljYXRvci5pbml0aWFsaXplKClcblxuICAvLyBBZGQgbWVzc2FnZSBsaXN0ZW5lciBzcGVjaWZpY2FsbHkgZm9yIFBhZ2VzIFJvdXRlciB0byBoYW5kbGUgbGlmZWN5Y2xlIGV2ZW50c1xuICAvLyByZWxhdGVkIHRvIGRldiBidWlsZHMgKGJ1aWxkaW5nLCBidWlsdCwgc3luYylcbiAgYWRkTWVzc2FnZUxpc3RlbmVyKGhhbmRsZURldkJ1aWxkSW5kaWNhdG9ySG1yRXZlbnRzKVxufVxuIl0sIm5hbWVzIjpbImluaXRpYWxpemVEZXZCdWlsZEluZGljYXRvckZvclBhZ2VSb3V0ZXIiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX0RFVl9JTkRJQ0FUT1IiLCJkZXZCdWlsZEluZGljYXRvciIsImluaXRpYWxpemUiLCJhZGRNZXNzYWdlTGlzdGVuZXIiLCJoYW5kbGVEZXZCdWlsZEluZGljYXRvckhtckV2ZW50cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-page-router.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"devBuildIndicator\", ({\n    enumerable: true,\n    get: function() {\n        return devBuildIndicator;\n    }\n}));\nconst _initialize = __webpack_require__(/*! ./initialize */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst NOOP = ()=>{};\n_c = NOOP;\nconst devBuildIndicator = {\n    /** Shows build indicator when Next.js is compiling. Requires initialize() first. */ show: NOOP,\n    /** Hides build indicator when Next.js finishes compiling. Requires initialize() first. */ hide: NOOP,\n    /** Sets up the build indicator UI component. Call this before using show/hide. */ initialize: _initialize.initialize\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-build-indicator.js.map\nvar _c;\n$RefreshReg$(_c, \"NOOP\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2ludGVybmFsL2Rldi1idWlsZC1pbmRpY2F0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFJYUE7OztlQUFBQTs7O3dDQUpjO0FBRTNCLGFBQWEsS0FBTztLQUFkQztBQUVDLE1BQU1ELG9CQUFvQjtJQUMvQixrRkFBa0YsR0FDbEZFLE1BQU1EO0lBQ04sd0ZBQXdGLEdBQ3hGRSxNQUFNRjtJQUNOLGdGQUFnRixHQUNoRkcsWUFBQUEsWUFBQUEsVUFBVTtBQUNaIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcZGV2LWJ1aWxkLWluZGljYXRvclxcaW50ZXJuYWxcXGRldi1idWlsZC1pbmRpY2F0b3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW5pdGlhbGl6ZSB9IGZyb20gJy4vaW5pdGlhbGl6ZSdcblxuY29uc3QgTk9PUCA9ICgpID0+IHt9XG5cbmV4cG9ydCBjb25zdCBkZXZCdWlsZEluZGljYXRvciA9IHtcbiAgLyoqIFNob3dzIGJ1aWxkIGluZGljYXRvciB3aGVuIE5leHQuanMgaXMgY29tcGlsaW5nLiBSZXF1aXJlcyBpbml0aWFsaXplKCkgZmlyc3QuICovXG4gIHNob3c6IE5PT1AsXG4gIC8qKiBIaWRlcyBidWlsZCBpbmRpY2F0b3Igd2hlbiBOZXh0LmpzIGZpbmlzaGVzIGNvbXBpbGluZy4gUmVxdWlyZXMgaW5pdGlhbGl6ZSgpIGZpcnN0LiAqL1xuICBoaWRlOiBOT09QLFxuICAvKiogU2V0cyB1cCB0aGUgYnVpbGQgaW5kaWNhdG9yIFVJIGNvbXBvbmVudC4gQ2FsbCB0aGlzIGJlZm9yZSB1c2luZyBzaG93L2hpZGUuICovXG4gIGluaXRpYWxpemUsXG59XG4iXSwibmFtZXMiOlsiZGV2QnVpbGRJbmRpY2F0b3IiLCJOT09QIiwic2hvdyIsImhpZGUiLCJpbml0aWFsaXplIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js ***!
  \*****************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleDevBuildIndicatorHmrEvents\", ({\n    enumerable: true,\n    get: function() {\n        return handleDevBuildIndicatorHmrEvents;\n    }\n}));\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(pages-dir-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst handleDevBuildIndicatorHmrEvents = (obj)=>{\n    try {\n        if (!('action' in obj)) {\n            return;\n        }\n        // eslint-disable-next-line default-case\n        switch(obj.action){\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n                _devbuildindicator.devBuildIndicator.show();\n                break;\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n                _devbuildindicator.devBuildIndicator.hide();\n                break;\n        }\n    } catch (e) {}\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-dev-build-indicator-hmr-events.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    initialize: function() {\n        return initialize;\n    },\n    useIsDevBuilding: function() {\n        return useIsDevBuilding;\n    }\n});\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nlet isVisible = false;\nlet listeners = [];\nconst subscribe = (listener)=>{\n    listeners.push(listener);\n    return ()=>{\n        listeners = listeners.filter((l)=>l !== listener);\n    };\n};\nconst getSnapshot = ()=>isVisible;\nfunction useIsDevBuilding() {\n    return (0, _react.useSyncExternalStore)(subscribe, getSnapshot);\n}\nfunction initialize() {\n    _devbuildindicator.devBuildIndicator.show = ()=>{\n        isVisible = true;\n        listeners.forEach((listener)=>listener());\n    };\n    _devbuildindicator.devBuildIndicator.hide = ()=>{\n        isVisible = false;\n        listeners.forEach((listener)=>listener());\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/fouc.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/client/dev/fouc.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This wrapper function is used to safely select the best available function\n// to schedule removal of the no-FOUC styles workaround. requestAnimationFrame\n// is the ideal choice, but when used in iframes, there are no guarantees that\n// the callback will actually be called, which could stall the promise returned\n// from displayContent.\n//\n// See: https://www.vector-logic.com/blog/posts/on-request-animation-frame-and-embedded-iframes\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"displayContent\", ({\n    enumerable: true,\n    get: function() {\n        return displayContent;\n    }\n}));\nconst safeCallbackQueue = (callback)=>{\n    if (window.requestAnimationFrame && window.self === window.top) {\n        window.requestAnimationFrame(callback);\n    } else {\n        window.setTimeout(callback);\n    }\n};\nfunction displayContent() {\n    return new Promise((resolve)=>{\n        safeCallbackQueue(function() {\n            for(var x = document.querySelectorAll('[data-next-hide-fouc]'), i = x.length; i--;){\n                x[i].parentNode.removeChild(x[i]);\n            }\n            resolve();\n        });\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fouc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/fouc.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/hot-middleware-client.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/hot-middleware-client.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _hotreloaderclient = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../components/react-dev-overlay/pages/hot-reloader-client */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js\"));\nconst _websocket = __webpack_require__(/*! ../components/react-dev-overlay/pages/websocket */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nlet reloading = false;\nconst _default = (mode)=>{\n    const devClient = (0, _hotreloaderclient.default)(mode);\n    devClient.subscribeToHmrEvent((obj)=>{\n        var _window_next;\n        if (reloading) return;\n        // Retrieve the router if it's available\n        const router = (_window_next = window.next) == null ? void 0 : _window_next.router;\n        // Determine if we're on an error page or the router is not initialized\n        const isOnErrorPage = !router || router.pathname === '/404' || router.pathname === '/_error';\n        switch(obj.action){\n            case 'reloadPage':\n                {\n                    (0, _websocket.sendMessage)(JSON.stringify({\n                        event: 'client-reload-page',\n                        clientId: window.__nextDevClientId\n                    }));\n                    reloading = true;\n                    return window.location.reload();\n                }\n            case 'removedPage':\n                {\n                    const [page] = obj.data;\n                    // Check if the removed page is the current page\n                    const isCurrentPage = page === (router == null ? void 0 : router.pathname);\n                    // We enter here if the removed page is currently being viewed\n                    // or if we happen to be on an error page.\n                    if (isCurrentPage || isOnErrorPage) {\n                        (0, _websocket.sendMessage)(JSON.stringify({\n                            event: 'client-removed-page',\n                            clientId: window.__nextDevClientId,\n                            page\n                        }));\n                        return window.location.reload();\n                    }\n                    return;\n                }\n            case 'addedPage':\n                {\n                    var _router_components;\n                    const [page] = obj.data;\n                    // Check if the added page is the current page\n                    const isCurrentPage = page === (router == null ? void 0 : router.pathname);\n                    // Check if the page component is not yet loaded\n                    const isPageNotLoaded = typeof (router == null ? void 0 : (_router_components = router.components) == null ? void 0 : _router_components[page]) === 'undefined';\n                    // We enter this block if the newly added page is the one currently being viewed\n                    // but hasn't been loaded yet, or if we're on an error page.\n                    if (isCurrentPage && isPageNotLoaded || isOnErrorPage) {\n                        (0, _websocket.sendMessage)(JSON.stringify({\n                            event: 'client-added-page',\n                            clientId: window.__nextDevClientId,\n                            page\n                        }));\n                        return window.location.reload();\n                    }\n                    return;\n                }\n            case 'serverError':\n            case 'devPagesManifestUpdate':\n            case 'isrManifest':\n            case 'building':\n            case 'finishBuilding':\n                {\n                    return;\n                }\n            default:\n                {\n                    throw Object.defineProperty(new Error('Unexpected action ' + obj.action), \"__NEXT_ERROR_CODE\", {\n                        value: \"E59\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n        }\n    });\n    return devClient;\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-middleware-client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/hot-middleware-client.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/on-demand-entries-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/on-demand-entries-client.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _router = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../router */ \"(pages-dir-browser)/./node_modules/next/dist/client/router.js\"));\nconst _websocket = __webpack_require__(/*! ../components/react-dev-overlay/pages/websocket */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _default = async (page)=>{\n    // Never send pings when using Turbopack as it's not used.\n    // Pings were originally used to keep track of active routes in on-demand-entries with webpack.\n    if (false) {}\n    if (page) {\n        // in AMP the router isn't initialized on the client and\n        // client-transitions don't occur so ping initial page\n        setInterval(()=>{\n            (0, _websocket.sendMessage)(JSON.stringify({\n                event: 'ping',\n                page\n            }));\n        }, 2500);\n    } else {\n        _router.default.ready(()=>{\n            setInterval(()=>{\n                // when notFound: true is returned we should use the notFoundPage\n                // as the Router.pathname will point to the 404 page but we want\n                // to ping the source page that returned notFound: true instead\n                const notFoundSrcPage = self.__NEXT_DATA__.notFoundSrcPage;\n                const pathname = (_router.default.pathname === '/404' || _router.default.pathname === '/_error') && notFoundSrcPage ? notFoundSrcPage : _router.default.pathname;\n                (0, _websocket.sendMessage)(JSON.stringify({\n                    event: 'ping',\n                    page: pathname\n                }));\n            }, 2500);\n        });\n    }\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-demand-entries-client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/on-demand-entries-client.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/has-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/has-base-path.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hasBasePath\", ({\n    enumerable: true,\n    get: function() {\n        return hasBasePath;\n    }\n}));\nconst _pathhasprefix = __webpack_require__(/*! ../shared/lib/router/utils/path-has-prefix */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/path-has-prefix.js\");\nconst basePath =  false || '';\nfunction hasBasePath(path) {\n    return (0, _pathhasprefix.pathHasPrefix)(path, basePath);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-base-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2hhcy1iYXNlLXBhdGguanMiLCJtYXBwaW5ncyI6Ijs7OzsrQ0FJZ0JBOzs7ZUFBQUE7OzsyQ0FKYztBQUU5QixNQUFNQyxXQUFZQyxNQUFrQyxJQUFlO0FBRTVELFNBQVNGLFlBQVlLLElBQVk7SUFDdEMsT0FBT0MsQ0FBQUEsR0FBQUEsZUFBQUEsYUFBQUEsRUFBY0QsTUFBTUo7QUFDN0IiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHNyY1xcY2xpZW50XFxoYXMtYmFzZS1wYXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBhdGhIYXNQcmVmaXggfSBmcm9tICcuLi9zaGFyZWQvbGliL3JvdXRlci91dGlscy9wYXRoLWhhcy1wcmVmaXgnXG5cbmNvbnN0IGJhc2VQYXRoID0gKHByb2Nlc3MuZW52Ll9fTkVYVF9ST1VURVJfQkFTRVBBVEggYXMgc3RyaW5nKSB8fCAnJ1xuXG5leHBvcnQgZnVuY3Rpb24gaGFzQmFzZVBhdGgocGF0aDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBwYXRoSGFzUHJlZml4KHBhdGgsIGJhc2VQYXRoKVxufVxuIl0sIm5hbWVzIjpbImhhc0Jhc2VQYXRoIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJwYXRoSGFzUHJlZml4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/has-base-path.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return initHeadManager;\n    },\n    isEqualNode: function() {\n        return isEqualNode;\n    }\n});\nconst _setattributesfromprops = __webpack_require__(/*! ./set-attributes-from-props */ \"(pages-dir-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\");\nfunction reactElementToDOM(param) {\n    let { type, props } = param;\n    const el = document.createElement(type);\n    (0, _setattributesfromprops.setAttributesFromProps)(el, props);\n    const { children, dangerouslySetInnerHTML } = props;\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n    }\n    return el;\n}\nfunction isEqualNode(oldTag, newTag) {\n    if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n        const nonce = newTag.getAttribute('nonce');\n        // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n        // be stripped if there is no content security policy response header that includes a nonce.\n        if (nonce && !oldTag.getAttribute('nonce')) {\n            const cloneTag = newTag.cloneNode(true);\n            cloneTag.setAttribute('nonce', '');\n            cloneTag.nonce = nonce;\n            return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag);\n        }\n    }\n    return oldTag.isEqualNode(newTag);\n}\nlet updateElements;\nif (true) {\n    updateElements = (type, components)=>{\n        const headEl = document.querySelector('head');\n        if (!headEl) return;\n        const oldTags = new Set(headEl.querySelectorAll(\"\" + type + \"[data-next-head]\"));\n        if (type === 'meta') {\n            const metaCharset = headEl.querySelector('meta[charset]');\n            if (metaCharset !== null) {\n                oldTags.add(metaCharset);\n            }\n        }\n        const newTags = [];\n        for(let i = 0; i < components.length; i++){\n            const component = components[i];\n            const newTag = reactElementToDOM(component);\n            newTag.setAttribute('data-next-head', '');\n            let isNew = true;\n            for (const oldTag of oldTags){\n                if (isEqualNode(oldTag, newTag)) {\n                    oldTags.delete(oldTag);\n                    isNew = false;\n                    break;\n                }\n            }\n            if (isNew) {\n                newTags.push(newTag);\n            }\n        }\n        for (const oldTag of oldTags){\n            var _oldTag_parentNode;\n            (_oldTag_parentNode = oldTag.parentNode) == null ? void 0 : _oldTag_parentNode.removeChild(oldTag);\n        }\n        for (const newTag of newTags){\n            // meta[charset] must be first element so special case\n            if (newTag.tagName.toLowerCase() === 'meta' && newTag.getAttribute('charset') !== null) {\n                headEl.prepend(newTag);\n            }\n            headEl.appendChild(newTag);\n        }\n    };\n} else {}\nfunction initHeadManager() {\n    return {\n        mountedInstances: new Set(),\n        updateHead: (head)=>{\n            const tags = {};\n            head.forEach((h)=>{\n                if (// it won't be inlined. In this case revert to the original behavior\n                h.type === 'link' && h.props['data-optimized-fonts']) {\n                    if (document.querySelector('style[data-href=\"' + h.props['data-href'] + '\"]')) {\n                        return;\n                    } else {\n                        h.props.href = h.props['data-href'];\n                        h.props['data-href'] = undefined;\n                    }\n                }\n                const components = tags[h.type] || [];\n                components.push(h);\n                tags[h.type] = components;\n            });\n            const titleComponent = tags.title ? tags.title[0] : null;\n            let title = '';\n            if (titleComponent) {\n                const { children } = titleComponent.props;\n                title = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n            }\n            if (title !== document.title) document.title = title;\n            [\n                'meta',\n                'base',\n                'link',\n                'style',\n                'script'\n            ].forEach((type)=>{\n                updateElements(type, tags[type] || []);\n            });\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head-manager.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2hlYWQtbWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF5SkEsT0FnREM7ZUFoRHVCQTs7SUFySFJDLFdBQVc7ZUFBWEE7OztvREFwQ3VCO0FBSXZDLFNBQVNDLGtCQUFrQixLQUE0QjtJQUE1QixNQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBZSxHQUE1QjtJQUN6QixNQUFNQyxLQUFrQkMsU0FBU0MsYUFBYSxDQUFDSjtJQUMvQ0ssQ0FBQUEsR0FBQUEsd0JBQUFBLHNCQUFzQixFQUFDSCxJQUFJRDtJQUUzQixNQUFNLEVBQUVLLFFBQVEsRUFBRUMsdUJBQXVCLEVBQUUsR0FBR047SUFDOUMsSUFBSU0seUJBQXlCO1FBQzNCTCxHQUFHTSxTQUFTLEdBQUdELHdCQUF3QkUsTUFBTSxJQUFJO0lBQ25ELE9BQU8sSUFBSUgsVUFBVTtRQUNuQkosR0FBR1EsV0FBVyxHQUNaLE9BQU9KLGFBQWEsV0FDaEJBLFdBQ0FLLE1BQU1DLE9BQU8sQ0FBQ04sWUFDWkEsU0FBU08sSUFBSSxDQUFDLE1BQ2Q7SUFDVjtJQUNBLE9BQU9YO0FBQ1Q7QUFnQk8sU0FBU0osWUFBWWdCLE1BQWUsRUFBRUMsTUFBZTtJQUMxRCxJQUFJRCxrQkFBa0JFLGVBQWVELGtCQUFrQkMsYUFBYTtRQUNsRSxNQUFNQyxRQUFRRixPQUFPRyxZQUFZLENBQUM7UUFDbEMsOEZBQThGO1FBQzlGLDRGQUE0RjtRQUM1RixJQUFJRCxTQUFTLENBQUNILE9BQU9JLFlBQVksQ0FBQyxVQUFVO1lBQzFDLE1BQU1DLFdBQVdKLE9BQU9LLFNBQVMsQ0FBQztZQUNsQ0QsU0FBU0UsWUFBWSxDQUFDLFNBQVM7WUFDL0JGLFNBQVNGLEtBQUssR0FBR0E7WUFDakIsT0FBT0EsVUFBVUgsT0FBT0csS0FBSyxJQUFJSCxPQUFPaEIsV0FBVyxDQUFDcUI7UUFDdEQ7SUFDRjtJQUVBLE9BQU9MLE9BQU9oQixXQUFXLENBQUNpQjtBQUM1QjtBQUVBLElBQUlPO0FBRUosSUFBSUMsSUFBbUMsRUFBRTtJQUN2Q0QsaUJBQWlCLENBQUN0QixNQUFNMEI7UUFDdEIsTUFBTUMsU0FBU3hCLFNBQVN5QixhQUFhLENBQUM7UUFDdEMsSUFBSSxDQUFDRCxRQUFRO1FBRWIsTUFBTUUsVUFBVSxJQUFJQyxJQUFJSCxPQUFPSSxnQkFBZ0IsQ0FBRSxLQUFFL0IsT0FBSztRQUV4RCxJQUFJQSxTQUFTLFFBQVE7WUFDbkIsTUFBTWdDLGNBQWNMLE9BQU9DLGFBQWEsQ0FBQztZQUN6QyxJQUFJSSxnQkFBZ0IsTUFBTTtnQkFDeEJILFFBQVFJLEdBQUcsQ0FBQ0Q7WUFDZDtRQUNGO1FBRUEsTUFBTUUsVUFBcUIsRUFBRTtRQUM3QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSVQsV0FBV1UsTUFBTSxFQUFFRCxJQUFLO1lBQzFDLE1BQU1FLFlBQVlYLFVBQVUsQ0FBQ1MsRUFBRTtZQUMvQixNQUFNcEIsU0FBU2hCLGtCQUFrQnNDO1lBQ2pDdEIsT0FBT00sWUFBWSxDQUFDLGtCQUFrQjtZQUV0QyxJQUFJaUIsUUFBUTtZQUNaLEtBQUssTUFBTXhCLFVBQVVlLFFBQVM7Z0JBQzVCLElBQUkvQixZQUFZZ0IsUUFBUUMsU0FBUztvQkFDL0JjLFFBQVFVLE1BQU0sQ0FBQ3pCO29CQUNmd0IsUUFBUTtvQkFDUjtnQkFDRjtZQUNGO1lBRUEsSUFBSUEsT0FBTztnQkFDVEosUUFBUU0sSUFBSSxDQUFDekI7WUFDZjtRQUNGO1FBRUEsS0FBSyxNQUFNRCxVQUFVZSxRQUFTO2dCQUM1QmY7YUFBQUEscUJBQUFBLE9BQU8yQixVQUFBQSxLQUFVLGdCQUFqQjNCLG1CQUFtQjRCLFdBQVcsQ0FBQzVCO1FBQ2pDO1FBRUEsS0FBSyxNQUFNQyxVQUFVbUIsUUFBUztZQUM1QixzREFBc0Q7WUFDdEQsSUFDRW5CLE9BQU80QixPQUFPLENBQUNDLFdBQVcsT0FBTyxVQUNqQzdCLE9BQU9HLFlBQVksQ0FBQyxlQUFlLE1BQ25DO2dCQUNBUyxPQUFPa0IsT0FBTyxDQUFDOUI7WUFDakI7WUFDQVksT0FBT21CLFdBQVcsQ0FBQy9CO1FBQ3JCO0lBQ0Y7QUFDRixPQUFPLEVBZ0ROO0FBRWMsU0FBU2xCO0lBSXRCLE9BQU87UUFDTHFFLGtCQUFrQixJQUFJcEM7UUFDdEJxQyxZQUFZLENBQUNDO1lBQ1gsTUFBTUMsT0FBc0MsQ0FBQztZQUU3Q0QsS0FBS04sT0FBTyxDQUFDLENBQUNRO2dCQUNaLElBQ0Usc0RBQXNELGNBQ2M7Z0JBQ3BFQSxFQUFFdEUsSUFBSSxLQUFLLFVBQ1hzRSxFQUFFckUsS0FBSyxDQUFDLHVCQUF1QixFQUMvQjtvQkFDQSxJQUNFRSxTQUFTeUIsYUFBYSxDQUFFLHNCQUFtQjBDLEVBQUVyRSxLQUFLLENBQUMsWUFBWSxHQUFDLE9BQ2hFO3dCQUNBO29CQUNGLE9BQU87d0JBQ0xxRSxFQUFFckUsS0FBSyxDQUFDc0UsSUFBSSxHQUFHRCxFQUFFckUsS0FBSyxDQUFDLFlBQVk7d0JBQ25DcUUsRUFBRXJFLEtBQUssQ0FBQyxZQUFZLEdBQUd1RTtvQkFDekI7Z0JBQ0Y7Z0JBRUEsTUFBTTlDLGFBQWEyQyxJQUFJLENBQUNDLEVBQUV0RSxJQUFJLENBQUMsSUFBSSxFQUFFO2dCQUNyQzBCLFdBQVdjLElBQUksQ0FBQzhCO2dCQUNoQkQsSUFBSSxDQUFDQyxFQUFFdEUsSUFBSSxDQUFDLEdBQUcwQjtZQUNqQjtZQUVBLE1BQU0rQyxpQkFBaUJKLEtBQUtLLEtBQUssR0FBR0wsS0FBS0ssS0FBSyxDQUFDLEVBQUUsR0FBRztZQUNwRCxJQUFJQSxRQUFRO1lBQ1osSUFBSUQsZ0JBQWdCO2dCQUNsQixNQUFNLEVBQUVuRSxRQUFRLEVBQUUsR0FBR21FLGVBQWV4RSxLQUFLO2dCQUN6Q3lFLFFBQ0UsT0FBT3BFLGFBQWEsV0FDaEJBLFdBQ0FLLE1BQU1DLE9BQU8sQ0FBQ04sWUFDWkEsU0FBU08sSUFBSSxDQUFDLE1BQ2Q7WUFDVjtZQUNBLElBQUk2RCxVQUFVdkUsU0FBU3VFLEtBQUssRUFBRXZFLFNBQVN1RSxLQUFLLEdBQUdBO1lBQzlDO2dCQUFDO2dCQUFRO2dCQUFRO2dCQUFRO2dCQUFTO2FBQVMsQ0FBQ1osT0FBTyxDQUFDLENBQUM5RDtnQkFDcERzQixlQUFldEIsTUFBTXFFLElBQUksQ0FBQ3JFLEtBQUssSUFBSSxFQUFFO1lBQ3ZDO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJFOlxcYm90XFxzcmNcXGNsaWVudFxcaGVhZC1tYW5hZ2VyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNldEF0dHJpYnV0ZXNGcm9tUHJvcHMgfSBmcm9tICcuL3NldC1hdHRyaWJ1dGVzLWZyb20tcHJvcHMnXG5cbmltcG9ydCB0eXBlIHsgSlNYIH0gZnJvbSAncmVhY3QnXG5cbmZ1bmN0aW9uIHJlYWN0RWxlbWVudFRvRE9NKHsgdHlwZSwgcHJvcHMgfTogSlNYLkVsZW1lbnQpOiBIVE1MRWxlbWVudCB7XG4gIGNvbnN0IGVsOiBIVE1MRWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQodHlwZSlcbiAgc2V0QXR0cmlidXRlc0Zyb21Qcm9wcyhlbCwgcHJvcHMpXG5cbiAgY29uc3QgeyBjaGlsZHJlbiwgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgfSA9IHByb3BzXG4gIGlmIChkYW5nZXJvdXNseVNldElubmVySFRNTCkge1xuICAgIGVsLmlubmVySFRNTCA9IGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MLl9faHRtbCB8fCAnJ1xuICB9IGVsc2UgaWYgKGNoaWxkcmVuKSB7XG4gICAgZWwudGV4dENvbnRlbnQgPVxuICAgICAgdHlwZW9mIGNoaWxkcmVuID09PSAnc3RyaW5nJ1xuICAgICAgICA/IGNoaWxkcmVuXG4gICAgICAgIDogQXJyYXkuaXNBcnJheShjaGlsZHJlbilcbiAgICAgICAgICA/IGNoaWxkcmVuLmpvaW4oJycpXG4gICAgICAgICAgOiAnJ1xuICB9XG4gIHJldHVybiBlbFxufVxuXG4vKipcbiAqIFdoZW4gYSBgbm9uY2VgIGlzIHByZXNlbnQgb24gYW4gZWxlbWVudCwgYnJvd3NlcnMgc3VjaCBhcyBDaHJvbWUgYW5kIEZpcmVmb3ggc3RyaXAgaXQgb3V0IG9mIHRoZVxuICogYWN0dWFsIEhUTUwgYXR0cmlidXRlcyBmb3Igc2VjdXJpdHkgcmVhc29ucyAqd2hlbiB0aGUgZWxlbWVudCBpcyBhZGRlZCB0byB0aGUgZG9jdW1lbnQqLiBUaHVzLFxuICogZ2l2ZW4gdHdvIGVxdWl2YWxlbnQgZWxlbWVudHMgdGhhdCBoYXZlIG5vbmNlcywgYEVsZW1lbnQsaXNFcXVhbE5vZGUoKWAgd2lsbCByZXR1cm4gZmFsc2UgaWYgb25lXG4gKiBvZiB0aG9zZSBlbGVtZW50cyBnZXRzIGFkZGVkIHRvIHRoZSBkb2N1bWVudC4gQWx0aG91Z2ggdGhlIGBlbGVtZW50Lm5vbmNlYCBwcm9wZXJ0eSB3aWxsIGJlIHRoZVxuICogc2FtZSBmb3IgYm90aCBlbGVtZW50cywgdGhlIG9uZSB0aGF0IHdhcyBhZGRlZCB0byB0aGUgZG9jdW1lbnQgd2lsbCByZXR1cm4gYW4gZW1wdHkgc3RyaW5nIGZvclxuICogaXRzIG5vbmNlIEhUTUwgYXR0cmlidXRlIHZhbHVlLlxuICpcbiAqIFRoaXMgY3VzdG9tIGBpc0VxdWFsTm9kZSgpYCBmdW5jdGlvbiB0aGVyZWZvcmUgcmVtb3ZlcyB0aGUgbm9uY2UgdmFsdWUgZnJvbSB0aGUgYG5ld1RhZ2AgYmVmb3JlXG4gKiBjb21wYXJpbmcgaXQgdG8gYG9sZFRhZ2AsIHJlc3RvcmluZyBpdCBhZnRlcndhcmRzLlxuICpcbiAqIEZvciBtb3JlIGluZm9ybWF0aW9uLCBzZWU6XG4gKiBodHRwczovL2J1Z3MuY2hyb21pdW0ub3JnL3AvY2hyb21pdW0vaXNzdWVzL2RldGFpbD9pZD0xMjExNDcxI2MxMlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNFcXVhbE5vZGUob2xkVGFnOiBFbGVtZW50LCBuZXdUYWc6IEVsZW1lbnQpIHtcbiAgaWYgKG9sZFRhZyBpbnN0YW5jZW9mIEhUTUxFbGVtZW50ICYmIG5ld1RhZyBpbnN0YW5jZW9mIEhUTUxFbGVtZW50KSB7XG4gICAgY29uc3Qgbm9uY2UgPSBuZXdUYWcuZ2V0QXR0cmlidXRlKCdub25jZScpXG4gICAgLy8gT25seSBzdHJpcCB0aGUgbm9uY2UgaWYgYG9sZFRhZ2AgaGFzIGhhZCBpdCBzdHJpcHBlZC4gQW4gZWxlbWVudCdzIG5vbmNlIGF0dHJpYnV0ZSB3aWxsIG5vdFxuICAgIC8vIGJlIHN0cmlwcGVkIGlmIHRoZXJlIGlzIG5vIGNvbnRlbnQgc2VjdXJpdHkgcG9saWN5IHJlc3BvbnNlIGhlYWRlciB0aGF0IGluY2x1ZGVzIGEgbm9uY2UuXG4gICAgaWYgKG5vbmNlICYmICFvbGRUYWcuZ2V0QXR0cmlidXRlKCdub25jZScpKSB7XG4gICAgICBjb25zdCBjbG9uZVRhZyA9IG5ld1RhZy5jbG9uZU5vZGUodHJ1ZSkgYXMgdHlwZW9mIG5ld1RhZ1xuICAgICAgY2xvbmVUYWcuc2V0QXR0cmlidXRlKCdub25jZScsICcnKVxuICAgICAgY2xvbmVUYWcubm9uY2UgPSBub25jZVxuICAgICAgcmV0dXJuIG5vbmNlID09PSBvbGRUYWcubm9uY2UgJiYgb2xkVGFnLmlzRXF1YWxOb2RlKGNsb25lVGFnKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBvbGRUYWcuaXNFcXVhbE5vZGUobmV3VGFnKVxufVxuXG5sZXQgdXBkYXRlRWxlbWVudHM6ICh0eXBlOiBzdHJpbmcsIGNvbXBvbmVudHM6IEpTWC5FbGVtZW50W10pID0+IHZvaWRcblxuaWYgKHByb2Nlc3MuZW52Ll9fTkVYVF9TVFJJQ1RfTkVYVF9IRUFEKSB7XG4gIHVwZGF0ZUVsZW1lbnRzID0gKHR5cGUsIGNvbXBvbmVudHMpID0+IHtcbiAgICBjb25zdCBoZWFkRWwgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdoZWFkJylcbiAgICBpZiAoIWhlYWRFbCkgcmV0dXJuXG5cbiAgICBjb25zdCBvbGRUYWdzID0gbmV3IFNldChoZWFkRWwucXVlcnlTZWxlY3RvckFsbChgJHt0eXBlfVtkYXRhLW5leHQtaGVhZF1gKSlcblxuICAgIGlmICh0eXBlID09PSAnbWV0YScpIHtcbiAgICAgIGNvbnN0IG1ldGFDaGFyc2V0ID0gaGVhZEVsLnF1ZXJ5U2VsZWN0b3IoJ21ldGFbY2hhcnNldF0nKVxuICAgICAgaWYgKG1ldGFDaGFyc2V0ICE9PSBudWxsKSB7XG4gICAgICAgIG9sZFRhZ3MuYWRkKG1ldGFDaGFyc2V0KVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IG5ld1RhZ3M6IEVsZW1lbnRbXSA9IFtdXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb21wb25lbnRzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBjb21wb25lbnQgPSBjb21wb25lbnRzW2ldXG4gICAgICBjb25zdCBuZXdUYWcgPSByZWFjdEVsZW1lbnRUb0RPTShjb21wb25lbnQpXG4gICAgICBuZXdUYWcuc2V0QXR0cmlidXRlKCdkYXRhLW5leHQtaGVhZCcsICcnKVxuXG4gICAgICBsZXQgaXNOZXcgPSB0cnVlXG4gICAgICBmb3IgKGNvbnN0IG9sZFRhZyBvZiBvbGRUYWdzKSB7XG4gICAgICAgIGlmIChpc0VxdWFsTm9kZShvbGRUYWcsIG5ld1RhZykpIHtcbiAgICAgICAgICBvbGRUYWdzLmRlbGV0ZShvbGRUYWcpXG4gICAgICAgICAgaXNOZXcgPSBmYWxzZVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKGlzTmV3KSB7XG4gICAgICAgIG5ld1RhZ3MucHVzaChuZXdUYWcpXG4gICAgICB9XG4gICAgfVxuXG4gICAgZm9yIChjb25zdCBvbGRUYWcgb2Ygb2xkVGFncykge1xuICAgICAgb2xkVGFnLnBhcmVudE5vZGU/LnJlbW92ZUNoaWxkKG9sZFRhZylcbiAgICB9XG5cbiAgICBmb3IgKGNvbnN0IG5ld1RhZyBvZiBuZXdUYWdzKSB7XG4gICAgICAvLyBtZXRhW2NoYXJzZXRdIG11c3QgYmUgZmlyc3QgZWxlbWVudCBzbyBzcGVjaWFsIGNhc2VcbiAgICAgIGlmIChcbiAgICAgICAgbmV3VGFnLnRhZ05hbWUudG9Mb3dlckNhc2UoKSA9PT0gJ21ldGEnICYmXG4gICAgICAgIG5ld1RhZy5nZXRBdHRyaWJ1dGUoJ2NoYXJzZXQnKSAhPT0gbnVsbFxuICAgICAgKSB7XG4gICAgICAgIGhlYWRFbC5wcmVwZW5kKG5ld1RhZylcbiAgICAgIH1cbiAgICAgIGhlYWRFbC5hcHBlbmRDaGlsZChuZXdUYWcpXG4gICAgfVxuICB9XG59IGVsc2Uge1xuICB1cGRhdGVFbGVtZW50cyA9ICh0eXBlLCBjb21wb25lbnRzKSA9PiB7XG4gICAgY29uc3QgaGVhZEVsID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeVRhZ05hbWUoJ2hlYWQnKVswXVxuICAgIGNvbnN0IGhlYWRDb3VudEVsOiBIVE1MTWV0YUVsZW1lbnQgPSBoZWFkRWwucXVlcnlTZWxlY3RvcihcbiAgICAgICdtZXRhW25hbWU9bmV4dC1oZWFkLWNvdW50XSdcbiAgICApIGFzIEhUTUxNZXRhRWxlbWVudFxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICBpZiAoIWhlYWRDb3VudEVsKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgJ1dhcm5pbmc6IG5leHQtaGVhZC1jb3VudCBpcyBtaXNzaW5nLiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9uZXh0LWhlYWQtY291bnQtbWlzc2luZydcbiAgICAgICAgKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBoZWFkQ291bnQgPSBOdW1iZXIoaGVhZENvdW50RWwuY29udGVudClcbiAgICBjb25zdCBvbGRUYWdzOiBFbGVtZW50W10gPSBbXVxuXG4gICAgZm9yIChcbiAgICAgIGxldCBpID0gMCwgaiA9IGhlYWRDb3VudEVsLnByZXZpb3VzRWxlbWVudFNpYmxpbmc7XG4gICAgICBpIDwgaGVhZENvdW50O1xuICAgICAgaSsrLCBqID0gaj8ucHJldmlvdXNFbGVtZW50U2libGluZyB8fCBudWxsXG4gICAgKSB7XG4gICAgICBpZiAoaj8udGFnTmFtZT8udG9Mb3dlckNhc2UoKSA9PT0gdHlwZSkge1xuICAgICAgICBvbGRUYWdzLnB1c2goailcbiAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgbmV3VGFncyA9IChjb21wb25lbnRzLm1hcChyZWFjdEVsZW1lbnRUb0RPTSkgYXMgSFRNTEVsZW1lbnRbXSkuZmlsdGVyKFxuICAgICAgKG5ld1RhZykgPT4ge1xuICAgICAgICBmb3IgKGxldCBrID0gMCwgbGVuID0gb2xkVGFncy5sZW5ndGg7IGsgPCBsZW47IGsrKykge1xuICAgICAgICAgIGNvbnN0IG9sZFRhZyA9IG9sZFRhZ3Nba11cbiAgICAgICAgICBpZiAoaXNFcXVhbE5vZGUob2xkVGFnLCBuZXdUYWcpKSB7XG4gICAgICAgICAgICBvbGRUYWdzLnNwbGljZShrLCAxKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cnVlXG4gICAgICB9XG4gICAgKVxuXG4gICAgb2xkVGFncy5mb3JFYWNoKCh0KSA9PiB0LnBhcmVudE5vZGU/LnJlbW92ZUNoaWxkKHQpKVxuICAgIG5ld1RhZ3MuZm9yRWFjaCgodCkgPT4gaGVhZEVsLmluc2VydEJlZm9yZSh0LCBoZWFkQ291bnRFbCkpXG4gICAgaGVhZENvdW50RWwuY29udGVudCA9IChcbiAgICAgIGhlYWRDb3VudCAtXG4gICAgICBvbGRUYWdzLmxlbmd0aCArXG4gICAgICBuZXdUYWdzLmxlbmd0aFxuICAgICkudG9TdHJpbmcoKVxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGluaXRIZWFkTWFuYWdlcigpOiB7XG4gIG1vdW50ZWRJbnN0YW5jZXM6IFNldDx1bmtub3duPlxuICB1cGRhdGVIZWFkOiAoaGVhZDogSlNYLkVsZW1lbnRbXSkgPT4gdm9pZFxufSB7XG4gIHJldHVybiB7XG4gICAgbW91bnRlZEluc3RhbmNlczogbmV3IFNldCgpLFxuICAgIHVwZGF0ZUhlYWQ6IChoZWFkOiBKU1guRWxlbWVudFtdKSA9PiB7XG4gICAgICBjb25zdCB0YWdzOiBSZWNvcmQ8c3RyaW5nLCBKU1guRWxlbWVudFtdPiA9IHt9XG5cbiAgICAgIGhlYWQuZm9yRWFjaCgoaCkgPT4ge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgLy8gSWYgdGhlIGZvbnQgdGFnIGlzIGxvYWRlZCBvbmx5IG9uIGNsaWVudCBuYXZpZ2F0aW9uXG4gICAgICAgICAgLy8gaXQgd29uJ3QgYmUgaW5saW5lZC4gSW4gdGhpcyBjYXNlIHJldmVydCB0byB0aGUgb3JpZ2luYWwgYmVoYXZpb3JcbiAgICAgICAgICBoLnR5cGUgPT09ICdsaW5rJyAmJlxuICAgICAgICAgIGgucHJvcHNbJ2RhdGEtb3B0aW1pemVkLWZvbnRzJ11cbiAgICAgICAgKSB7XG4gICAgICAgICAgaWYgKFxuICAgICAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3Rvcihgc3R5bGVbZGF0YS1ocmVmPVwiJHtoLnByb3BzWydkYXRhLWhyZWYnXX1cIl1gKVxuICAgICAgICAgICkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGgucHJvcHMuaHJlZiA9IGgucHJvcHNbJ2RhdGEtaHJlZiddXG4gICAgICAgICAgICBoLnByb3BzWydkYXRhLWhyZWYnXSA9IHVuZGVmaW5lZFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGNvbXBvbmVudHMgPSB0YWdzW2gudHlwZV0gfHwgW11cbiAgICAgICAgY29tcG9uZW50cy5wdXNoKGgpXG4gICAgICAgIHRhZ3NbaC50eXBlXSA9IGNvbXBvbmVudHNcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IHRpdGxlQ29tcG9uZW50ID0gdGFncy50aXRsZSA/IHRhZ3MudGl0bGVbMF0gOiBudWxsXG4gICAgICBsZXQgdGl0bGUgPSAnJ1xuICAgICAgaWYgKHRpdGxlQ29tcG9uZW50KSB7XG4gICAgICAgIGNvbnN0IHsgY2hpbGRyZW4gfSA9IHRpdGxlQ29tcG9uZW50LnByb3BzXG4gICAgICAgIHRpdGxlID1cbiAgICAgICAgICB0eXBlb2YgY2hpbGRyZW4gPT09ICdzdHJpbmcnXG4gICAgICAgICAgICA/IGNoaWxkcmVuXG4gICAgICAgICAgICA6IEFycmF5LmlzQXJyYXkoY2hpbGRyZW4pXG4gICAgICAgICAgICAgID8gY2hpbGRyZW4uam9pbignJylcbiAgICAgICAgICAgICAgOiAnJ1xuICAgICAgfVxuICAgICAgaWYgKHRpdGxlICE9PSBkb2N1bWVudC50aXRsZSkgZG9jdW1lbnQudGl0bGUgPSB0aXRsZVxuICAgICAgO1snbWV0YScsICdiYXNlJywgJ2xpbmsnLCAnc3R5bGUnLCAnc2NyaXB0J10uZm9yRWFjaCgodHlwZSkgPT4ge1xuICAgICAgICB1cGRhdGVFbGVtZW50cyh0eXBlLCB0YWdzW3R5cGVdIHx8IFtdKVxuICAgICAgfSlcbiAgICB9LFxuICB9XG59XG4iXSwibmFtZXMiOlsiaW5pdEhlYWRNYW5hZ2VyIiwiaXNFcXVhbE5vZGUiLCJyZWFjdEVsZW1lbnRUb0RPTSIsInR5cGUiLCJwcm9wcyIsImVsIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic2V0QXR0cmlidXRlc0Zyb21Qcm9wcyIsImNoaWxkcmVuIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJpbm5lckhUTUwiLCJfX2h0bWwiLCJ0ZXh0Q29udGVudCIsIkFycmF5IiwiaXNBcnJheSIsImpvaW4iLCJvbGRUYWciLCJuZXdUYWciLCJIVE1MRWxlbWVudCIsIm5vbmNlIiwiZ2V0QXR0cmlidXRlIiwiY2xvbmVUYWciLCJjbG9uZU5vZGUiLCJzZXRBdHRyaWJ1dGUiLCJ1cGRhdGVFbGVtZW50cyIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfU1RSSUNUX05FWFRfSEVBRCIsImNvbXBvbmVudHMiLCJoZWFkRWwiLCJxdWVyeVNlbGVjdG9yIiwib2xkVGFncyIsIlNldCIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJtZXRhQ2hhcnNldCIsImFkZCIsIm5ld1RhZ3MiLCJpIiwibGVuZ3RoIiwiY29tcG9uZW50IiwiaXNOZXciLCJkZWxldGUiLCJwdXNoIiwicGFyZW50Tm9kZSIsInJlbW92ZUNoaWxkIiwidGFnTmFtZSIsInRvTG93ZXJDYXNlIiwicHJlcGVuZCIsImFwcGVuZENoaWxkIiwiZ2V0RWxlbWVudHNCeVRhZ05hbWUiLCJoZWFkQ291bnRFbCIsIk5PREVfRU5WIiwiY29uc29sZSIsImVycm9yIiwiaGVhZENvdW50IiwiTnVtYmVyIiwiY29udGVudCIsImoiLCJwcmV2aW91c0VsZW1lbnRTaWJsaW5nIiwibWFwIiwiZmlsdGVyIiwiayIsImxlbiIsInNwbGljZSIsImZvckVhY2giLCJ0IiwiaW5zZXJ0QmVmb3JlIiwidG9TdHJpbmciLCJtb3VudGVkSW5zdGFuY2VzIiwidXBkYXRlSGVhZCIsImhlYWQiLCJ0YWdzIiwiaCIsImhyZWYiLCJ1bmRlZmluZWQiLCJ0aXRsZUNvbXBvbmVudCIsInRpdGxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/head-manager.js\n"));

/***/ })

}]);