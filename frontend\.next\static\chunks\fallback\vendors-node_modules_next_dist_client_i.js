"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_i"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/lib/console.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/lib/console.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatConsoleArgs: function() {\n        return formatConsoleArgs;\n    },\n    parseConsoleArgs: function() {\n        return parseConsoleArgs;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nfunction formatObject(arg, depth) {\n    switch(typeof arg){\n        case 'object':\n            if (arg === null) {\n                return 'null';\n            } else if (Array.isArray(arg)) {\n                let result = '[';\n                if (depth < 1) {\n                    for(let i = 0; i < arg.length; i++){\n                        if (result !== '[') {\n                            result += ',';\n                        }\n                        if (Object.prototype.hasOwnProperty.call(arg, i)) {\n                            result += formatObject(arg[i], depth + 1);\n                        }\n                    }\n                } else {\n                    result += arg.length > 0 ? '...' : '';\n                }\n                result += ']';\n                return result;\n            } else if (arg instanceof Error) {\n                return arg + '';\n            } else {\n                const keys = Object.keys(arg);\n                let result = '{';\n                if (depth < 1) {\n                    for(let i = 0; i < keys.length; i++){\n                        const key = keys[i];\n                        const desc = Object.getOwnPropertyDescriptor(arg, 'key');\n                        if (desc && !desc.get && !desc.set) {\n                            const jsonKey = JSON.stringify(key);\n                            if (jsonKey !== '\"' + key + '\"') {\n                                result += jsonKey + ': ';\n                            } else {\n                                result += key + ': ';\n                            }\n                            result += formatObject(desc.value, depth + 1);\n                        }\n                    }\n                } else {\n                    result += keys.length > 0 ? '...' : '';\n                }\n                result += '}';\n                return result;\n            }\n        case 'string':\n            return JSON.stringify(arg);\n        default:\n            return String(arg);\n    }\n}\nfunction formatConsoleArgs(args) {\n    let message;\n    let idx;\n    if (typeof args[0] === 'string') {\n        message = args[0];\n        idx = 1;\n    } else {\n        message = '';\n        idx = 0;\n    }\n    let result = '';\n    let startQuote = false;\n    for(let i = 0; i < message.length; ++i){\n        const char = message[i];\n        if (char !== '%' || i === message.length - 1 || idx >= args.length) {\n            result += char;\n            continue;\n        }\n        const code = message[++i];\n        switch(code){\n            case 'c':\n                {\n                    // TODO: We should colorize with HTML instead of turning into a string.\n                    // Ignore for now.\n                    result = startQuote ? \"\" + result + \"]\" : \"[\" + result;\n                    startQuote = !startQuote;\n                    idx++;\n                    break;\n                }\n            case 'O':\n            case 'o':\n                {\n                    result += formatObject(args[idx++], 0);\n                    break;\n                }\n            case 'd':\n            case 'i':\n                {\n                    result += parseInt(args[idx++], 10);\n                    break;\n                }\n            case 'f':\n                {\n                    result += parseFloat(args[idx++]);\n                    break;\n                }\n            case 's':\n                {\n                    result += String(args[idx++]);\n                    break;\n                }\n            default:\n                result += '%' + code;\n        }\n    }\n    for(; idx < args.length; idx++){\n        result += (idx > 0 ? ' ' : '') + formatObject(args[idx], 0);\n    }\n    return result;\n}\nfunction parseConsoleArgs(args) {\n    // See\n    // https://github.com/facebook/react/blob/65a56d0e99261481c721334a3ec4561d173594cd/packages/react-devtools-shared/src/backend/flight/renderer.js#L88-L93\n    //\n    // Logs replayed from the server look like this:\n    // [\n    //   \"%c%s%c %o\\n\\n%s\\n\\n%s\\n\",\n    //   \"background: #e6e6e6; ...\",\n    //   \" Server \", // can also be e.g. \" Prerender \"\n    //   \"\",\n    //   Error,\n    //   \"The above error occurred in the <Page> component.\",\n    //   ...\n    // ]\n    if (args.length > 3 && typeof args[0] === 'string' && args[0].startsWith('%c%s%c ') && typeof args[1] === 'string' && typeof args[2] === 'string' && typeof args[3] === 'string') {\n        const environmentName = args[2];\n        const maybeError = args[4];\n        return {\n            environmentName: environmentName.trim(),\n            error: (0, _iserror.default)(maybeError) ? maybeError : null\n        };\n    }\n    return {\n        environmentName: null,\n        error: null\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=console.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"shouldRenderRootLevelErrorOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return shouldRenderRootLevelErrorOverlay;\n    }\n}));\nconst shouldRenderRootLevelErrorOverlay = ()=>{\n    var _window___next_root_layout_missing_tags;\n    return !!((_window___next_root_layout_missing_tags = window.__next_root_layout_missing_tags) == null ? void 0 : _window___next_root_layout_missing_tags.length);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=is-error-thrown-while-rendering-rsc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpYi9pcy1lcnJvci10aHJvd24td2hpbGUtcmVuZGVyaW5nLXJzYy5qcyIsIm1hcHBpbmdzIjoiOzs7O3FFQUFhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxvQ0FBb0M7UUFDdENDO0lBQVQsT0FBTyxDQUFDLEdBQUNBLDBDQUFBQSxPQUFPQywrQkFBQUEsS0FBK0IsZ0JBQXRDRCx3Q0FBd0NFLE1BQUFBO0FBQ25EIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGxpYlxcaXMtZXJyb3ItdGhyb3duLXdoaWxlLXJlbmRlcmluZy1yc2MudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHNob3VsZFJlbmRlclJvb3RMZXZlbEVycm9yT3ZlcmxheSA9ICgpID0+IHtcbiAgcmV0dXJuICEhd2luZG93Ll9fbmV4dF9yb290X2xheW91dF9taXNzaW5nX3RhZ3M/Lmxlbmd0aFxufVxuIl0sIm5hbWVzIjpbInNob3VsZFJlbmRlclJvb3RMZXZlbEVycm9yT3ZlcmxheSIsIndpbmRvdyIsIl9fbmV4dF9yb290X2xheW91dF9taXNzaW5nX3RhZ3MiLCJsZW5ndGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/index.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/client/index.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* global location */ // imports polyfill from `@next/polyfill-module` after build.\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    emitter: function() {\n        return emitter;\n    },\n    hydrate: function() {\n        return hydrate;\n    },\n    initialize: function() {\n        return initialize;\n    },\n    router: function() {\n        return router;\n    },\n    version: function() {\n        return version;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(pages-dir-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(pages-dir-browser)/./node_modules/react-dom/client.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/mitt */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../shared/lib/router/utils/handle-smooth-scroll */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _isdynamic = __webpack_require__(/*! ../shared/lib/router/utils/is-dynamic */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _querystring = __webpack_require__(/*! ../shared/lib/router/utils/querystring */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\");\nconst _runtimeconfigexternal = __webpack_require__(/*! ../shared/lib/runtime-config.external */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/runtime-config.external.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _portal = __webpack_require__(/*! ./portal */ \"(pages-dir-browser)/./node_modules/next/dist/client/portal/index.js\");\nconst _headmanager = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./head-manager */ \"(pages-dir-browser)/./node_modules/next/dist/client/head-manager.js\"));\nconst _pageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./page-loader */ \"(pages-dir-browser)/./node_modules/next/dist/client/page-loader.js\"));\nconst _routeannouncer = __webpack_require__(/*! ./route-announcer */ \"(pages-dir-browser)/./node_modules/next/dist/client/route-announcer.js\");\nconst _router = __webpack_require__(/*! ./router */ \"(pages-dir-browser)/./node_modules/next/dist/client/router.js\");\nconst _iserror = __webpack_require__(/*! ../lib/is-error */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-error.js\");\nconst _imageconfigcontextsharedruntime = __webpack_require__(/*! ../shared/lib/image-config-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\");\nconst _removebasepath = __webpack_require__(/*! ./remove-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ./has-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _adapters = __webpack_require__(/*! ../shared/lib/router/adapters */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/adapters.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../shared/lib/hooks-client-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _tracer = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./tracing/tracer */ \"(pages-dir-browser)/./node_modules/next/dist/client/tracing/tracer.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ./components/is-next-router-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst version = \"15.2.3\";\nlet router;\nconst emitter = (0, _mitt.default)();\nconst looseToArray = (input)=>[].slice.call(input);\nlet initialData;\nlet defaultLocale = undefined;\nlet asPath;\nlet pageLoader;\nlet appElement;\nlet headManager;\nlet initialMatchesMiddleware = false;\nlet lastAppProps;\nlet lastRenderReject;\nlet devClient;\nlet CachedApp, onPerfEntry;\nlet CachedComponent;\nclass Container extends _react.default.Component {\n    componentDidCatch(componentErr, info) {\n        this.props.fn(componentErr, info);\n    }\n    componentDidMount() {\n        this.scrollToHash();\n        // We need to replace the router state if:\n        // - the page was (auto) exported and has a query string or search (hash)\n        // - it was auto exported and is a dynamic route (to provide params)\n        // - if it is a client-side skeleton (fallback render)\n        // - if middleware matches the current page (may have rewrite params)\n        // - if rewrites in next.config.js match (may have rewrite params)\n        if (router.isSsr && (initialData.isFallback || initialData.nextExport && ((0, _isdynamic.isDynamicRoute)(router.pathname) || location.search || false || initialMatchesMiddleware) || initialData.props && initialData.props.__N_SSG && (location.search || false || initialMatchesMiddleware))) {\n            // update query on mount for exported pages\n            router.replace(router.pathname + '?' + String((0, _querystring.assign)((0, _querystring.urlQueryToSearchParams)(router.query), new URLSearchParams(location.search))), asPath, {\n                // @ts-ignore\n                // WARNING: `_h` is an internal option for handing Next.js\n                // client-side hydration. Your app should _never_ use this property.\n                // It may change at any time without notice.\n                _h: 1,\n                // Fallback pages must trigger the data fetch, so the transition is\n                // not shallow.\n                // Other pages (strictly updating query) happens shallowly, as data\n                // requirements would already be present.\n                shallow: !initialData.isFallback && !initialMatchesMiddleware\n            }).catch((err)=>{\n                if (!err.cancelled) throw err;\n            });\n        }\n    }\n    componentDidUpdate() {\n        this.scrollToHash();\n    }\n    scrollToHash() {\n        let { hash } = location;\n        hash = hash && hash.substring(1);\n        if (!hash) return;\n        const el = document.getElementById(hash);\n        if (!el) return;\n        // If we call scrollIntoView() in here without a setTimeout\n        // it won't scroll properly.\n        setTimeout(()=>el.scrollIntoView(), 0);\n    }\n    render() {\n        if (false) {} else {\n            const { PagesDevOverlay } = __webpack_require__(/*! ./components/react-dev-overlay/pages/pages-dev-overlay */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.js\");\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(PagesDevOverlay, {\n                children: this.props.children\n            });\n        }\n    }\n}\nasync function initialize(opts) {\n    if (opts === void 0) opts = {};\n    // This makes sure this specific lines are removed in production\n    if (true) {\n        _tracer.default.onSpanEnd((__webpack_require__(/*! ./tracing/report-to-socket */ \"(pages-dir-browser)/./node_modules/next/dist/client/tracing/report-to-socket.js\")[\"default\"]));\n        devClient = opts.devClient;\n    }\n    initialData = JSON.parse(document.getElementById('__NEXT_DATA__').textContent);\n    window.__NEXT_DATA__ = initialData;\n    defaultLocale = initialData.defaultLocale;\n    const prefix = initialData.assetPrefix || '';\n    self.__next_set_public_path__(\"\" + prefix + \"/_next/\") //eslint-disable-line\n    ;\n    // Initialize next/config with the environment configuration\n    (0, _runtimeconfigexternal.setConfig)({\n        serverRuntimeConfig: {},\n        publicRuntimeConfig: initialData.runtimeConfig || {}\n    });\n    asPath = (0, _utils.getURL)();\n    // make sure not to attempt stripping basePath for 404s\n    if ((0, _hasbasepath.hasBasePath)(asPath)) {\n        asPath = (0, _removebasepath.removeBasePath)(asPath);\n    }\n    if (false) {}\n    if (initialData.scriptLoader) {\n        const { initScriptLoader } = __webpack_require__(/*! ./script */ \"(pages-dir-browser)/./node_modules/next/dist/client/script.js\");\n        initScriptLoader(initialData.scriptLoader);\n    }\n    pageLoader = new _pageloader.default(initialData.buildId, prefix);\n    const register = (param)=>{\n        let [r, f] = param;\n        return pageLoader.routeLoader.onEntrypoint(r, f);\n    };\n    if (window.__NEXT_P) {\n        // Defer page registration for another tick. This will increase the overall\n        // latency in hydrating the page, but reduce the total blocking time.\n        window.__NEXT_P.map((p)=>setTimeout(()=>register(p), 0));\n    }\n    window.__NEXT_P = [];\n    window.__NEXT_P.push = register;\n    headManager = (0, _headmanager.default)();\n    headManager.getIsSsr = ()=>{\n        return router.isSsr;\n    };\n    appElement = document.getElementById('__next');\n    return {\n        assetPrefix: prefix\n    };\n}\nfunction renderApp(App, appProps) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(App, {\n        ...appProps\n    });\n}\nfunction AppContainer(param) {\n    _s();\n    let { children } = param;\n    // Create a memoized value for next/navigation router context.\n    const adaptedForAppRouter = _react.default.useMemo({\n        \"AppContainer.useMemo[adaptedForAppRouter]\": ()=>{\n            return (0, _adapters.adaptForAppRouterInstance)(router);\n        }\n    }[\"AppContainer.useMemo[adaptedForAppRouter]\"], []);\n    var _self___NEXT_DATA___autoExport;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Container, {\n        fn: (error)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            renderError({\n                App: CachedApp,\n                err: error\n            }).catch((err)=>console.error('Error rendering page: ', err)),\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n            value: adaptedForAppRouter,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                value: (0, _adapters.adaptForSearchParams)(router),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_adapters.PathnameContextProviderAdapter, {\n                    router: router,\n                    isAutoExport: (_self___NEXT_DATA___autoExport = self.__NEXT_DATA__.autoExport) != null ? _self___NEXT_DATA___autoExport : false,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                        value: (0, _adapters.adaptForPathParams)(router),\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_routercontextsharedruntime.RouterContext.Provider, {\n                            value: (0, _router.makePublicRouterInstance)(router),\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n                                value: headManager,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_imageconfigcontextsharedruntime.ImageConfigContext.Provider, {\n                                    value: {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"i.imgur.com\",\"port\":\"\",\"pathname\":\"/**\"}],\"output\":\"standalone\"},\n                                    children: children\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        })\n    });\n}\n_s(AppContainer, \"F6BSfrFQNeqenuPnUMVY/6gI8uE=\");\n_c = AppContainer;\nconst wrapApp = (App)=>(wrappedAppProps)=>{\n        const appProps = {\n            ...wrappedAppProps,\n            Component: CachedComponent,\n            err: initialData.err,\n            router\n        };\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(AppContainer, {\n            children: renderApp(App, appProps)\n        });\n    };\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps) {\n    let { App, err } = renderErrorProps;\n    // In development runtime errors are caught by our overlay\n    // In production we catch runtime errors using componentDidCatch which will trigger renderError\n    if (true) {\n        // A Next.js rendering runtime error is always unrecoverable\n        // FIXME: let's make this recoverable (error in GIP client-transition)\n        devClient.onUnrecoverableError();\n        // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n        // render itself.\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        return doRender({\n            App: ()=>null,\n            props: {},\n            Component: ()=>null,\n            styleSheets: []\n        });\n    }\n    // Make sure we log the error to the console, otherwise users can't track down issues.\n    console.error(err);\n    console.error(\"A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred\");\n    return pageLoader.loadPage('/_error').then((param)=>{\n        let { page: ErrorComponent, styleSheets } = param;\n        return (lastAppProps == null ? void 0 : lastAppProps.Component) === ErrorComponent ? Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_next_dist_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_l\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_router-reducer_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_lib_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_p\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_ro\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_seg\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_s\"), __webpack_require__.e(\"vendors-node_modules_sc\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! ../pages/_error */ \"(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\", 23)).then((errorModule)=>{\n            return Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_next_dist_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_l\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_router-reducer_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_lib_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_p\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_ro\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_seg\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_s\"), __webpack_require__.e(\"vendors-node_modules_sc\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! ../pages/_app */ \"(pages-dir-browser)/./node_modules/next/dist/pages/_app.js\", 23)).then((appModule)=>{\n                App = appModule.default;\n                renderErrorProps.App = App;\n                return errorModule;\n            });\n        }).then((m)=>({\n                ErrorComponent: m.default,\n                styleSheets: []\n            })) : {\n            ErrorComponent,\n            styleSheets\n        };\n    }).then((param)=>{\n        let { ErrorComponent, styleSheets } = param;\n        var _renderErrorProps_props;\n        // In production we do a normal render with the `ErrorComponent` as component.\n        // If we've gotten here upon initial render, we can use the props from the server.\n        // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n        const AppTree = wrapApp(App);\n        const appCtx = {\n            Component: ErrorComponent,\n            AppTree,\n            router,\n            ctx: {\n                err,\n                pathname: initialData.page,\n                query: initialData.query,\n                asPath,\n                AppTree\n            }\n        };\n        return Promise.resolve(((_renderErrorProps_props = renderErrorProps.props) == null ? void 0 : _renderErrorProps_props.err) ? renderErrorProps.props : (0, _utils.loadGetInitialProps)(App, appCtx)).then((initProps)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            doRender({\n                ...renderErrorProps,\n                err,\n                Component: ErrorComponent,\n                styleSheets,\n                props: initProps\n            }));\n    });\n}\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head(param) {\n    _s1();\n    let { callback } = param;\n    // We use `useLayoutEffect` to guarantee the callback is executed\n    // as soon as React flushes the update.\n    _react.default.useLayoutEffect({\n        \"Head.useLayoutEffect\": ()=>callback()\n    }[\"Head.useLayoutEffect\"], [\n        callback\n    ]);\n    return null;\n}\n_s1(Head, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c1 = Head;\nconst performanceMarks = {\n    navigationStart: 'navigationStart',\n    beforeRender: 'beforeRender',\n    afterRender: 'afterRender',\n    afterHydrate: 'afterHydrate',\n    routeChange: 'routeChange'\n};\nconst performanceMeasures = {\n    hydration: 'Next.js-hydration',\n    beforeHydration: 'Next.js-before-hydration',\n    routeChangeToRender: 'Next.js-route-change-to-render',\n    render: 'Next.js-render'\n};\nlet reactRoot = null;\n// On initial render a hydrate should always happen\nlet shouldHydrate = true;\nfunction clearMarks() {\n    ;\n    [\n        performanceMarks.beforeRender,\n        performanceMarks.afterHydrate,\n        performanceMarks.afterRender,\n        performanceMarks.routeChange\n    ].forEach((mark)=>performance.clearMarks(mark));\n}\nfunction markHydrateComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n    ;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, 'mark').length;\n    if (hasBeforeRenderMark) {\n        const beforeHydrationMeasure = performance.measure(performanceMeasures.beforeHydration, performanceMarks.navigationStart, performanceMarks.beforeRender);\n        const hydrationMeasure = performance.measure(performanceMeasures.hydration, performanceMarks.beforeRender, performanceMarks.afterHydrate);\n        if ( true && // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n        beforeHydrationMeasure && hydrationMeasure) {\n            _tracer.default.startSpan('navigation-to-hydration', {\n                startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n                attributes: {\n                    pathname: location.pathname,\n                    query: location.search\n                }\n            }).end(performance.timeOrigin + hydrationMeasure.startTime + hydrationMeasure.duration);\n        }\n    }\n    if (onPerfEntry) {\n        performance.getEntriesByName(performanceMeasures.hydration).forEach(onPerfEntry);\n    }\n    clearMarks();\n}\nfunction markRenderComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterRender) // mark end of render\n    ;\n    const navStartEntries = performance.getEntriesByName(performanceMarks.routeChange, 'mark');\n    if (!navStartEntries.length) return;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, 'mark').length;\n    if (hasBeforeRenderMark) {\n        performance.measure(performanceMeasures.routeChangeToRender, navStartEntries[0].name, performanceMarks.beforeRender);\n        performance.measure(performanceMeasures.render, performanceMarks.beforeRender, performanceMarks.afterRender);\n        if (onPerfEntry) {\n            performance.getEntriesByName(performanceMeasures.render).forEach(onPerfEntry);\n            performance.getEntriesByName(performanceMeasures.routeChangeToRender).forEach(onPerfEntry);\n        }\n    }\n    clearMarks();\n    [\n        performanceMeasures.routeChangeToRender,\n        performanceMeasures.render\n    ].forEach((measure)=>performance.clearMeasures(measure));\n}\nfunction renderReactElement(domEl, fn) {\n    // mark start of hydrate/render\n    if (_utils.ST) {\n        performance.mark(performanceMarks.beforeRender);\n    }\n    const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete);\n    if (!reactRoot) {\n        // Unlike with createRoot, you don't need a separate root.render() call here\n        reactRoot = _client.default.hydrateRoot(domEl, reactEl, {\n            onRecoverableError: _onrecoverableerror.onRecoverableError\n        });\n        // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n        shouldHydrate = false;\n    } else {\n        const startTransition = _react.default.startTransition;\n        startTransition(()=>{\n            reactRoot.render(reactEl);\n        });\n    }\n}\nfunction Root(param) {\n    _s2();\n    let { callbacks, children } = param;\n    // We use `useLayoutEffect` to guarantee the callbacks are executed\n    // as soon as React flushes the update\n    _react.default.useLayoutEffect({\n        \"Root.useLayoutEffect\": ()=>callbacks.forEach({\n                \"Root.useLayoutEffect\": (callback)=>callback()\n            }[\"Root.useLayoutEffect\"])\n    }[\"Root.useLayoutEffect\"], [\n        callbacks\n    ]);\n    if (false) {}\n    return children;\n}\n_s2(Root, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c2 = Root;\nfunction doRender(input) {\n    let { App, Component, props, err } = input;\n    let styleSheets = 'initial' in input ? undefined : input.styleSheets;\n    Component = Component || lastAppProps.Component;\n    props = props || lastAppProps.props;\n    const appProps = {\n        ...props,\n        Component,\n        err,\n        router\n    };\n    // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n    lastAppProps = appProps;\n    let canceled = false;\n    let resolvePromise;\n    const renderPromise = new Promise((resolve, reject)=>{\n        if (lastRenderReject) {\n            lastRenderReject();\n        }\n        resolvePromise = ()=>{\n            lastRenderReject = null;\n            resolve();\n        };\n        lastRenderReject = ()=>{\n            canceled = true;\n            lastRenderReject = null;\n            const error = Object.defineProperty(new Error('Cancel rendering route'), \"__NEXT_ERROR_CODE\", {\n                value: \"E503\",\n                enumerable: false,\n                configurable: true\n            });\n            error.cancelled = true;\n            reject(error);\n        };\n    });\n    // This function has a return type to ensure it doesn't start returning a\n    // Promise. It should remain synchronous.\n    function onStart() {\n        if (!styleSheets || // We use `style-loader` in development, so we don't need to do anything\n        // unless we're in production:\n        \"development\" !== 'production') {\n            return false;\n        }\n        const currentStyleTags = looseToArray(document.querySelectorAll('style[data-n-href]'));\n        const currentHrefs = new Set(currentStyleTags.map((tag)=>tag.getAttribute('data-n-href')));\n        const noscript = document.querySelector('noscript[data-n-css]');\n        const nonce = noscript == null ? void 0 : noscript.getAttribute('data-n-css');\n        styleSheets.forEach((param)=>{\n            let { href, text } = param;\n            if (!currentHrefs.has(href)) {\n                const styleTag = document.createElement('style');\n                styleTag.setAttribute('data-n-href', href);\n                styleTag.setAttribute('media', 'x');\n                if (nonce) {\n                    styleTag.setAttribute('nonce', nonce);\n                }\n                document.head.appendChild(styleTag);\n                styleTag.appendChild(document.createTextNode(text));\n            }\n        });\n        return true;\n    }\n    function onHeadCommit() {\n        if (false) {}\n        if (input.scroll) {\n            const { x, y } = input.scroll;\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                window.scrollTo(x, y);\n            });\n        }\n    }\n    function onRootCommit() {\n        resolvePromise();\n    }\n    onStart();\n    const elem = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n                callback: onHeadCommit\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(AppContainer, {\n                children: [\n                    renderApp(App, appProps),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_portal.Portal, {\n                        type: \"next-route-announcer\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_routeannouncer.RouteAnnouncer, {})\n                    })\n                ]\n            })\n        ]\n    });\n    // We catch runtime errors using componentDidCatch which will trigger renderError\n    renderReactElement(appElement, (callback)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n            callbacks: [\n                callback,\n                onRootCommit\n            ],\n            children:  true ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.StrictMode, {\n                children: elem\n            }) : 0\n        }));\n    return renderPromise;\n}\nasync function render(renderingProps) {\n    // if an error occurs in a server-side page (e.g. in getInitialProps),\n    // skip re-rendering the error page client-side as data-fetching operations\n    // will already have been done on the server and NEXT_DATA contains the correct\n    // data for straight-forward hydration of the error page\n    if (renderingProps.err && // renderingProps.Component might be undefined if there is a top/module-level error\n    (typeof renderingProps.Component === 'undefined' || !renderingProps.isHydratePass)) {\n        await renderError(renderingProps);\n        return;\n    }\n    try {\n        await doRender(renderingProps);\n    } catch (err) {\n        const renderErr = (0, _iserror.getProperError)(err);\n        // bubble up cancelation errors\n        if (renderErr.cancelled) {\n            throw renderErr;\n        }\n        if (true) {\n            // Ensure this error is displayed in the overlay in development\n            setTimeout(()=>{\n                throw renderErr;\n            });\n        }\n        await renderError({\n            ...renderingProps,\n            err: renderErr\n        });\n    }\n}\nasync function hydrate(opts) {\n    let initialErr = initialData.err;\n    try {\n        const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint('/_app');\n        if ('error' in appEntrypoint) {\n            throw appEntrypoint.error;\n        }\n        const { component: app, exports: mod } = appEntrypoint;\n        CachedApp = app;\n        if (mod && mod.reportWebVitals) {\n            onPerfEntry = (param)=>{\n                let { id, name, startTime, value, duration, entryType, entries, attribution } = param;\n                // Combines timestamp with random number for unique ID\n                const uniqueID = Date.now() + \"-\" + (Math.floor(Math.random() * (9e12 - 1)) + 1e12);\n                let perfStartEntry;\n                if (entries && entries.length) {\n                    perfStartEntry = entries[0].startTime;\n                }\n                const webVitals = {\n                    id: id || uniqueID,\n                    name,\n                    startTime: startTime || perfStartEntry,\n                    value: value == null ? duration : value,\n                    label: entryType === 'mark' || entryType === 'measure' ? 'custom' : 'web-vital'\n                };\n                if (attribution) {\n                    webVitals.attribution = attribution;\n                }\n                mod.reportWebVitals(webVitals);\n            };\n        }\n        const pageEntrypoint = // error, so we need to skip waiting for the entrypoint.\n         true && initialData.err ? {\n            error: initialData.err\n        } : await pageLoader.routeLoader.whenEntrypoint(initialData.page);\n        if ('error' in pageEntrypoint) {\n            throw pageEntrypoint.error;\n        }\n        CachedComponent = pageEntrypoint.component;\n        if (true) {\n            const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/react-is/index.js\");\n            if (!isValidElementType(CachedComponent)) {\n                throw Object.defineProperty(new Error('The default export is not a React Component in page: \"' + initialData.page + '\"'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E286\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    } catch (error) {\n        // This catches errors like throwing in the top level of a module\n        initialErr = (0, _iserror.getProperError)(error);\n    }\n    if (true) {\n        const getServerError = (__webpack_require__(/*! ./components/react-dev-overlay/pages/client */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\").getServerError);\n        // Server-side runtime errors need to be re-thrown on the client-side so\n        // that the overlay is rendered.\n        if (initialErr) {\n            if (initialErr === initialData.err) {\n                setTimeout(()=>{\n                    let error;\n                    try {\n                        // Generate a new error object. We `throw` it because some browsers\n                        // will set the `stack` when thrown, and we want to ensure ours is\n                        // not overridden when we re-throw it below.\n                        throw Object.defineProperty(new Error(initialErr.message), \"__NEXT_ERROR_CODE\", {\n                            value: \"E394\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    } catch (e) {\n                        error = e;\n                    }\n                    error.name = initialErr.name;\n                    error.stack = initialErr.stack;\n                    const errSource = initialErr.source;\n                    // In development, error the navigation API usage in runtime,\n                    // since it's not allowed to be used in pages router as it doesn't contain error boundary like app router.\n                    if ((0, _isnextroutererror.isNextRouterError)(initialErr)) {\n                        error.message = 'Next.js navigation API is not allowed to be used in Pages Router.';\n                    }\n                    throw getServerError(error, errSource);\n                });\n            } else {\n                setTimeout(()=>{\n                    throw initialErr;\n                });\n            }\n        }\n    }\n    if (window.__NEXT_PRELOADREADY) {\n        await window.__NEXT_PRELOADREADY(initialData.dynamicIds);\n    }\n    router = (0, _router.createRouter)(initialData.page, initialData.query, asPath, {\n        initialProps: initialData.props,\n        pageLoader,\n        App: CachedApp,\n        Component: CachedComponent,\n        wrapApp,\n        err: initialErr,\n        isFallback: Boolean(initialData.isFallback),\n        subscription: (info, App, scroll)=>render(Object.assign({}, info, {\n                App,\n                scroll\n            })),\n        locale: initialData.locale,\n        locales: initialData.locales,\n        defaultLocale,\n        domainLocales: initialData.domainLocales,\n        isPreview: initialData.isPreview\n    });\n    initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise;\n    const renderCtx = {\n        App: CachedApp,\n        initial: true,\n        Component: CachedComponent,\n        props: initialData.props,\n        err: initialErr,\n        isHydratePass: true\n    };\n    if (opts == null ? void 0 : opts.beforeRender) {\n        await opts.beforeRender();\n    }\n    render(renderCtx);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/index.js\n"));

/***/ })

}]);