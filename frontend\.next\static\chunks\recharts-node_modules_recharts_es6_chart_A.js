"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["recharts-node_modules_recharts_es6_chart_A"],{

/***/ "(app-pages-browser)/./node_modules/recharts/es6/chart/AccessibilityManager.js":
/*!*****************************************************************!*\
  !*** ./node_modules/recharts/es6/chart/AccessibilityManager.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessibilityManager: () => (/* binding */ AccessibilityManager)\n/* harmony export */ });\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nvar AccessibilityManager = /*#__PURE__*/ function() {\n    function AccessibilityManager() {\n        _classCallCheck(this, AccessibilityManager);\n        _defineProperty(this, \"activeIndex\", 0);\n        _defineProperty(this, \"coordinateList\", []);\n        _defineProperty(this, \"layout\", 'horizontal');\n    }\n    return _createClass(AccessibilityManager, [\n        {\n            key: \"setDetails\",\n            value: function setDetails(_ref) {\n                var _ref2;\n                var _ref$coordinateList = _ref.coordinateList, coordinateList = _ref$coordinateList === void 0 ? null : _ref$coordinateList, _ref$container = _ref.container, container = _ref$container === void 0 ? null : _ref$container, _ref$layout = _ref.layout, layout = _ref$layout === void 0 ? null : _ref$layout, _ref$offset = _ref.offset, offset = _ref$offset === void 0 ? null : _ref$offset, _ref$mouseHandlerCall = _ref.mouseHandlerCallback, mouseHandlerCallback = _ref$mouseHandlerCall === void 0 ? null : _ref$mouseHandlerCall;\n                this.coordinateList = (_ref2 = coordinateList !== null && coordinateList !== void 0 ? coordinateList : this.coordinateList) !== null && _ref2 !== void 0 ? _ref2 : [];\n                this.container = container !== null && container !== void 0 ? container : this.container;\n                this.layout = layout !== null && layout !== void 0 ? layout : this.layout;\n                this.offset = offset !== null && offset !== void 0 ? offset : this.offset;\n                this.mouseHandlerCallback = mouseHandlerCallback !== null && mouseHandlerCallback !== void 0 ? mouseHandlerCallback : this.mouseHandlerCallback;\n                // Keep activeIndex in the bounds between 0 and the last coordinate index\n                this.activeIndex = Math.min(Math.max(this.activeIndex, 0), this.coordinateList.length - 1);\n            }\n        },\n        {\n            key: \"focus\",\n            value: function focus() {\n                this.spoofMouse();\n            }\n        },\n        {\n            key: \"keyboardEvent\",\n            value: function keyboardEvent(e) {\n                // The AccessibilityManager relies on the Tooltip component. When tooltips suddenly stop existing,\n                // it can cause errors. We use this function to check. We don't want arrow keys to be processed\n                // if there are no tooltips, since that will cause unexpected behavior of users.\n                if (this.coordinateList.length === 0) {\n                    return;\n                }\n                switch(e.key){\n                    case 'ArrowRight':\n                        {\n                            if (this.layout !== 'horizontal') {\n                                return;\n                            }\n                            this.activeIndex = Math.min(this.activeIndex + 1, this.coordinateList.length - 1);\n                            this.spoofMouse();\n                            break;\n                        }\n                    case 'ArrowLeft':\n                        {\n                            if (this.layout !== 'horizontal') {\n                                return;\n                            }\n                            this.activeIndex = Math.max(this.activeIndex - 1, 0);\n                            this.spoofMouse();\n                            break;\n                        }\n                    default:\n                        {\n                            break;\n                        }\n                }\n            }\n        },\n        {\n            key: \"setIndex\",\n            value: function setIndex(newIndex) {\n                this.activeIndex = newIndex;\n            }\n        },\n        {\n            key: \"spoofMouse\",\n            value: function spoofMouse() {\n                var _window, _window2;\n                if (this.layout !== 'horizontal') {\n                    return;\n                }\n                // This can happen when the tooltips suddenly stop existing as children of the component\n                // That update doesn't otherwise fire events, so we have to double check here.\n                if (this.coordinateList.length === 0) {\n                    return;\n                }\n                var _this$container$getBo = this.container.getBoundingClientRect(), x = _this$container$getBo.x, y = _this$container$getBo.y, height = _this$container$getBo.height;\n                var coordinate = this.coordinateList[this.activeIndex].coordinate;\n                var scrollOffsetX = ((_window = window) === null || _window === void 0 ? void 0 : _window.scrollX) || 0;\n                var scrollOffsetY = ((_window2 = window) === null || _window2 === void 0 ? void 0 : _window2.scrollY) || 0;\n                var pageX = x + coordinate + scrollOffsetX;\n                var pageY = y + this.offset.top + height / 2 + scrollOffsetY;\n                this.mouseHandlerCallback({\n                    pageX: pageX,\n                    pageY: pageY\n                });\n            }\n        }\n    ]);\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/chart/AccessibilityManager.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js":
/*!******************************************************!*\
  !*** ./node_modules/recharts/es6/chart/LineChart.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LineChart: () => (/* binding */ LineChart)\n/* harmony export */ });\n/* harmony import */ var _generateCategoricalChart__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./generateCategoricalChart */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/generateCategoricalChart.js\");\n/* harmony import */ var _cartesian_Line__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../cartesian/Line */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cartesian/XAxis */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cartesian/YAxis */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _util_CartesianUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/CartesianUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/CartesianUtils.js\");\n/**\n * @fileOverview Line Chart\n */ \n\n\n\n\nvar LineChart = (0,_generateCategoricalChart__WEBPACK_IMPORTED_MODULE_0__.generateCategoricalChart)({\n    chartName: 'LineChart',\n    GraphicalChild: _cartesian_Line__WEBPACK_IMPORTED_MODULE_1__.Line,\n    axisComponents: [\n        {\n            axisType: 'xAxis',\n            AxisComp: _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_2__.XAxis\n        },\n        {\n            axisType: 'yAxis',\n            AxisComp: _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_3__.YAxis\n        }\n    ],\n    formatAxisMap: _util_CartesianUtils__WEBPACK_IMPORTED_MODULE_4__.formatAxisMap\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvY2hhcnQvTGluZUNoYXJ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBOztDQUVDLEdBQ3FFO0FBQzdCO0FBQ0U7QUFDQTtBQUNZO0FBQ2hELElBQUlLLFlBQVlMLG1GQUF3QkEsQ0FBQztJQUM5Q00sV0FBVztJQUNYQyxnQkFBZ0JOLGlEQUFJQTtJQUNwQk8sZ0JBQWdCO1FBQUM7WUFDZkMsVUFBVTtZQUNWQyxVQUFVUixtREFBS0E7UUFDakI7UUFBRztZQUNETyxVQUFVO1lBQ1ZDLFVBQVVQLG1EQUFLQTtRQUNqQjtLQUFFO0lBQ0ZDLGVBQWVBLCtEQUFhQTtBQUM5QixHQUFHIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWNoYXJ0c1xcZXM2XFxjaGFydFxcTGluZUNoYXJ0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGZpbGVPdmVydmlldyBMaW5lIENoYXJ0XG4gKi9cbmltcG9ydCB7IGdlbmVyYXRlQ2F0ZWdvcmljYWxDaGFydCB9IGZyb20gJy4vZ2VuZXJhdGVDYXRlZ29yaWNhbENoYXJ0JztcbmltcG9ydCB7IExpbmUgfSBmcm9tICcuLi9jYXJ0ZXNpYW4vTGluZSc7XG5pbXBvcnQgeyBYQXhpcyB9IGZyb20gJy4uL2NhcnRlc2lhbi9YQXhpcyc7XG5pbXBvcnQgeyBZQXhpcyB9IGZyb20gJy4uL2NhcnRlc2lhbi9ZQXhpcyc7XG5pbXBvcnQgeyBmb3JtYXRBeGlzTWFwIH0gZnJvbSAnLi4vdXRpbC9DYXJ0ZXNpYW5VdGlscyc7XG5leHBvcnQgdmFyIExpbmVDaGFydCA9IGdlbmVyYXRlQ2F0ZWdvcmljYWxDaGFydCh7XG4gIGNoYXJ0TmFtZTogJ0xpbmVDaGFydCcsXG4gIEdyYXBoaWNhbENoaWxkOiBMaW5lLFxuICBheGlzQ29tcG9uZW50czogW3tcbiAgICBheGlzVHlwZTogJ3hBeGlzJyxcbiAgICBBeGlzQ29tcDogWEF4aXNcbiAgfSwge1xuICAgIGF4aXNUeXBlOiAneUF4aXMnLFxuICAgIEF4aXNDb21wOiBZQXhpc1xuICB9XSxcbiAgZm9ybWF0QXhpc01hcDogZm9ybWF0QXhpc01hcFxufSk7Il0sIm5hbWVzIjpbImdlbmVyYXRlQ2F0ZWdvcmljYWxDaGFydCIsIkxpbmUiLCJYQXhpcyIsIllBeGlzIiwiZm9ybWF0QXhpc01hcCIsIkxpbmVDaGFydCIsImNoYXJ0TmFtZSIsIkdyYXBoaWNhbENoaWxkIiwiYXhpc0NvbXBvbmVudHMiLCJheGlzVHlwZSIsIkF4aXNDb21wIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/chart/generateCategoricalChart.js":
/*!*********************************************************************!*\
  !*** ./node_modules/recharts/es6/chart/generateCategoricalChart.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDefaultState: () => (/* binding */ createDefaultState),\n/* harmony export */   generateCategoricalChart: () => (/* binding */ generateCategoricalChart),\n/* harmony export */   getAxisMapByAxes: () => (/* binding */ getAxisMapByAxes)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/isNil */ \"(app-pages-browser)/./node_modules/lodash/isNil.js\");\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_isNil__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isFunction */ \"(app-pages-browser)/./node_modules/lodash/isFunction.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isFunction__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_range__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/range */ \"(app-pages-browser)/./node_modules/lodash/range.js\");\n/* harmony import */ var lodash_range__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_range__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_get__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/get */ \"(app-pages-browser)/./node_modules/lodash/get.js\");\n/* harmony import */ var lodash_get__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_get__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_sortBy__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/sortBy */ \"(app-pages-browser)/./node_modules/lodash/sortBy.js\");\n/* harmony import */ var lodash_sortBy__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_sortBy__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var _container_Surface__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../container/Surface */ \"(app-pages-browser)/./node_modules/recharts/es6/container/Surface.js\");\n/* harmony import */ var _container_Layer__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ../container/Layer */ \"(app-pages-browser)/./node_modules/recharts/es6/container/Layer.js\");\n/* harmony import */ var _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../component/Tooltip */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _component_Legend__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../component/Legend */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _shape_Dot__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ../shape/Dot */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Dot.js\");\n/* harmony import */ var _shape_Rectangle__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../shape/Rectangle */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Rectangle.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\n/* harmony import */ var _cartesian_Brush__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../cartesian/Brush */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Brush.js\");\n/* harmony import */ var _util_DOMUtils__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../util/DOMUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DOMUtils.js\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../util/ChartUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ChartUtils.js\");\n/* harmony import */ var _util_ChartUtils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../util/ChartUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/getLegendProps.js\");\n/* harmony import */ var _util_DetectReferenceElementsDomain__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../util/DetectReferenceElementsDomain */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DetectReferenceElementsDomain.js\");\n/* harmony import */ var _util_PolarUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../util/PolarUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/PolarUtils.js\");\n/* harmony import */ var _util_ShallowEqual__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ../util/ShallowEqual */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ShallowEqual.js\");\n/* harmony import */ var _util_Events__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../util/Events */ \"(app-pages-browser)/./node_modules/recharts/es6/util/Events.js\");\n/* harmony import */ var _util_types__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../util/types */ \"(app-pages-browser)/./node_modules/recharts/es6/util/types.js\");\n/* harmony import */ var _AccessibilityManager__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./AccessibilityManager */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AccessibilityManager.js\");\n/* harmony import */ var _util_isDomainSpecifiedByUser__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../util/isDomainSpecifiedByUser */ \"(app-pages-browser)/./node_modules/recharts/es6/util/isDomainSpecifiedByUser.js\");\n/* harmony import */ var _util_ActiveShapeUtils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ../util/ActiveShapeUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ActiveShapeUtils.js\");\n/* harmony import */ var _component_Cursor__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../component/Cursor */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cursor.js\");\n/* harmony import */ var _context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ../context/chartLayoutContext */ \"(app-pages-browser)/./node_modules/recharts/es6/context/chartLayoutContext.js\");\nvar _excluded = [\n    \"item\"\n], _excluded2 = [\n    \"children\",\n    \"className\",\n    \"width\",\n    \"height\",\n    \"style\",\n    \"compact\",\n    \"title\",\n    \"desc\"\n];\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n        var e, n, i, u, a = [], f = !0, o = !1;\n        try {\n            if (i = (t = t.call(r)).next, 0 === l) {\n                if (Object(t) !== t) return;\n                f = !1;\n            } else for(; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n        } catch (r) {\n            o = !0, n = r;\n        } finally{\n            try {\n                if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n            } finally{\n                if (o) throw n;\n            }\n        }\n        return a;\n    }\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n    if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n        return call;\n    } else if (call !== void 0) {\n        throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n            value: subClass,\n            writable: true,\n            configurable: true\n        }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n        writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n\n\n\n\n\n\n\n\n// eslint-disable-next-line no-restricted-imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ORIENT_MAP = {\n    xAxis: [\n        'bottom',\n        'top'\n    ],\n    yAxis: [\n        'left',\n        'right'\n    ]\n};\nvar FULL_WIDTH_AND_HEIGHT = {\n    width: '100%',\n    height: '100%'\n};\nvar originCoordinate = {\n    x: 0,\n    y: 0\n};\n/**\n * This function exists as a temporary workaround.\n *\n * Why? generateCategoricalChart does not render `{children}` directly;\n * instead it passes them through `renderByOrder` function which reads their handlers.\n *\n * So, this is a handler that does nothing.\n * Once we get rid of `renderByOrder` and switch to JSX only, we can get rid of this handler too.\n *\n * @param {JSX} element as is in JSX\n * @returns {JSX} the same element\n */ function renderAsIs(element) {\n    return element;\n}\nvar calculateTooltipPos = function calculateTooltipPos(rangeObj, layout) {\n    if (layout === 'horizontal') {\n        return rangeObj.x;\n    }\n    if (layout === 'vertical') {\n        return rangeObj.y;\n    }\n    if (layout === 'centric') {\n        return rangeObj.angle;\n    }\n    return rangeObj.radius;\n};\nvar getActiveCoordinate = function getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj) {\n    var entry = tooltipTicks.find(function(tick) {\n        return tick && tick.index === activeIndex;\n    });\n    if (entry) {\n        if (layout === 'horizontal') {\n            return {\n                x: entry.coordinate,\n                y: rangeObj.y\n            };\n        }\n        if (layout === 'vertical') {\n            return {\n                x: rangeObj.x,\n                y: entry.coordinate\n            };\n        }\n        if (layout === 'centric') {\n            var _angle = entry.coordinate;\n            var _radius = rangeObj.radius;\n            return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_9__.polarToCartesian)(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n                angle: _angle,\n                radius: _radius\n            });\n        }\n        var radius = entry.coordinate;\n        var angle = rangeObj.angle;\n        return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_9__.polarToCartesian)(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n            angle: angle,\n            radius: radius\n        });\n    }\n    return originCoordinate;\n};\nvar getDisplayedData = function getDisplayedData(data, _ref) {\n    var graphicalItems = _ref.graphicalItems, dataStartIndex = _ref.dataStartIndex, dataEndIndex = _ref.dataEndIndex;\n    var itemsData = (graphicalItems !== null && graphicalItems !== void 0 ? graphicalItems : []).reduce(function(result, child) {\n        var itemData = child.props.data;\n        if (itemData && itemData.length) {\n            return [].concat(_toConsumableArray(result), _toConsumableArray(itemData));\n        }\n        return result;\n    }, []);\n    if (itemsData.length > 0) {\n        return itemsData;\n    }\n    if (data && data.length && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.isNumber)(dataStartIndex) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.isNumber)(dataEndIndex)) {\n        return data.slice(dataStartIndex, dataEndIndex + 1);\n    }\n    return [];\n};\nfunction getDefaultDomainByAxisType(axisType) {\n    return axisType === 'number' ? [\n        0,\n        'auto'\n    ] : undefined;\n}\n/**\n * Get the content to be displayed in the tooltip\n * @param  {Object} state          Current state\n * @param  {Array}  chartData      The data defined in chart\n * @param  {Number} activeIndex    Active index of data\n * @param  {String} activeLabel    Active label of data\n * @return {Array}                 The content of tooltip\n */ var getTooltipContent = function getTooltipContent(state, chartData, activeIndex, activeLabel) {\n    var graphicalItems = state.graphicalItems, tooltipAxis = state.tooltipAxis;\n    var displayedData = getDisplayedData(chartData, state);\n    if (activeIndex < 0 || !graphicalItems || !graphicalItems.length || activeIndex >= displayedData.length) {\n        return null;\n    }\n    // get data by activeIndex when the axis don't allow duplicated category\n    return graphicalItems.reduce(function(result, child) {\n        var _child$props$data;\n        /**\n     * Fixes: https://github.com/recharts/recharts/issues/3669\n     * Defaulting to chartData below to fix an edge case where the tooltip does not include data from all charts\n     * when a separate dataset is passed to chart prop data and specified on Line/Area/etc prop data\n     */ var data = (_child$props$data = child.props.data) !== null && _child$props$data !== void 0 ? _child$props$data : chartData;\n        if (data && state.dataStartIndex + state.dataEndIndex !== 0 && // https://github.com/recharts/recharts/issues/4717\n        // The data is sliced only when the active index is within the start/end index range.\n        state.dataEndIndex - state.dataStartIndex >= activeIndex) {\n            data = data.slice(state.dataStartIndex, state.dataEndIndex + 1);\n        }\n        var payload;\n        if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n            // graphic child has data props\n            var entries = data === undefined ? displayedData : data;\n            payload = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.findEntryInArray)(entries, tooltipAxis.dataKey, activeLabel);\n        } else {\n            payload = data && data[activeIndex] || displayedData[activeIndex];\n        }\n        if (!payload) {\n            return result;\n        }\n        return [].concat(_toConsumableArray(result), [\n            (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getTooltipItem)(child, payload)\n        ]);\n    }, []);\n};\n/**\n * Returns tooltip data based on a mouse position (as a parameter or in state)\n * @param  {Object} state     current state\n * @param  {Array}  chartData the data defined in chart\n * @param  {String} layout     The layout type of chart\n * @param  {Object} rangeObj  { x, y } coordinates\n * @return {Object}           Tooltip data data\n */ var getTooltipData = function getTooltipData(state, chartData, layout, rangeObj) {\n    var rangeData = rangeObj || {\n        x: state.chartX,\n        y: state.chartY\n    };\n    var pos = calculateTooltipPos(rangeData, layout);\n    var ticks = state.orderedTooltipTicks, axis = state.tooltipAxis, tooltipTicks = state.tooltipTicks;\n    var activeIndex = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.calculateActiveTickIndex)(pos, ticks, tooltipTicks, axis);\n    if (activeIndex >= 0 && tooltipTicks) {\n        var activeLabel = tooltipTicks[activeIndex] && tooltipTicks[activeIndex].value;\n        var activePayload = getTooltipContent(state, chartData, activeIndex, activeLabel);\n        var activeCoordinate = getActiveCoordinate(layout, ticks, activeIndex, rangeData);\n        return {\n            activeTooltipIndex: activeIndex,\n            activeLabel: activeLabel,\n            activePayload: activePayload,\n            activeCoordinate: activeCoordinate\n        };\n    }\n    return null;\n};\n/**\n * Get the configuration of axis by the options of axis instance\n * @param  {Object} props         Latest props\n * @param {Array}  axes           The instance of axes\n * @param  {Array} graphicalItems The instances of item\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}      Configuration\n */ var getAxisMapByAxes = function getAxisMapByAxes(props, _ref2) {\n    var axes = _ref2.axes, graphicalItems = _ref2.graphicalItems, axisType = _ref2.axisType, axisIdKey = _ref2.axisIdKey, stackGroups = _ref2.stackGroups, dataStartIndex = _ref2.dataStartIndex, dataEndIndex = _ref2.dataEndIndex;\n    var layout = props.layout, children = props.children, stackOffset = props.stackOffset;\n    var isCategorical = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.isCategoricalAxis)(layout, axisType);\n    // Eliminate duplicated axes\n    return axes.reduce(function(result, child) {\n        var _childProps$domain2;\n        var childProps = child.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, child.type.defaultProps), child.props) : child.props;\n        var type = childProps.type, dataKey = childProps.dataKey, allowDataOverflow = childProps.allowDataOverflow, allowDuplicatedCategory = childProps.allowDuplicatedCategory, scale = childProps.scale, ticks = childProps.ticks, includeHidden = childProps.includeHidden;\n        var axisId = childProps[axisIdKey];\n        if (result[axisId]) {\n            return result;\n        }\n        var displayedData = getDisplayedData(props.data, {\n            graphicalItems: graphicalItems.filter(function(item) {\n                var _defaultProps;\n                var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps = item.type.defaultProps) === null || _defaultProps === void 0 ? void 0 : _defaultProps[axisIdKey];\n                return itemAxisId === axisId;\n            }),\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n        });\n        var len = displayedData.length;\n        var domain, duplicateDomain, categoricalDomain;\n        /*\n     * This is a hack to short-circuit the domain creation here to enhance performance.\n     * Usually, the data is used to determine the domain, but when the user specifies\n     * a domain upfront (via props), there is no need to calculate the domain start and end,\n     * which is very expensive for a larger amount of data.\n     * The only thing that would prohibit short-circuiting is when the user doesn't allow data overflow,\n     * because the axis is supposed to ignore the specified domain that way.\n     */ if ((0,_util_isDomainSpecifiedByUser__WEBPACK_IMPORTED_MODULE_12__.isDomainSpecifiedByUser)(childProps.domain, allowDataOverflow, type)) {\n            domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.parseSpecifiedDomain)(childProps.domain, null, allowDataOverflow);\n            /* The chart can be categorical and have the domain specified in numbers\n       * we still need to calculate the categorical domain\n       * TODO: refactor this more\n       */ if (isCategorical && (type === 'number' || scale !== 'auto')) {\n                categoricalDomain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfDataByKey)(displayedData, dataKey, 'category');\n            }\n        }\n        // if the domain is defaulted we need this for `originalDomain` as well\n        var defaultDomain = getDefaultDomainByAxisType(type);\n        // we didn't create the domain from user's props above, so we need to calculate it\n        if (!domain || domain.length === 0) {\n            var _childProps$domain;\n            var childDomain = (_childProps$domain = childProps.domain) !== null && _childProps$domain !== void 0 ? _childProps$domain : defaultDomain;\n            if (dataKey) {\n                // has dataKey in <Axis />\n                domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfDataByKey)(displayedData, dataKey, type);\n                if (type === 'category' && isCategorical) {\n                    // the field type is category data and this axis is categorical axis\n                    var duplicate = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.hasDuplicate)(domain);\n                    if (allowDuplicatedCategory && duplicate) {\n                        duplicateDomain = domain;\n                        // When category axis has duplicated text, serial numbers are used to generate scale\n                        domain = lodash_range__WEBPACK_IMPORTED_MODULE_3___default()(0, len);\n                    } else if (!allowDuplicatedCategory) {\n                        // remove duplicated category\n                        domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.parseDomainOfCategoryAxis)(childDomain, domain, child).reduce(function(finalDomain, entry) {\n                            return finalDomain.indexOf(entry) >= 0 ? finalDomain : [].concat(_toConsumableArray(finalDomain), [\n                                entry\n                            ]);\n                        }, []);\n                    }\n                } else if (type === 'category') {\n                    // the field type is category data and this axis is numerical axis\n                    if (!allowDuplicatedCategory) {\n                        domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.parseDomainOfCategoryAxis)(childDomain, domain, child).reduce(function(finalDomain, entry) {\n                            return finalDomain.indexOf(entry) >= 0 || entry === '' || lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(entry) ? finalDomain : [].concat(_toConsumableArray(finalDomain), [\n                                entry\n                            ]);\n                        }, []);\n                    } else {\n                        // eliminate undefined or null or empty string\n                        domain = domain.filter(function(entry) {\n                            return entry !== '' && !lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(entry);\n                        });\n                    }\n                } else if (type === 'number') {\n                    // the field type is numerical\n                    var errorBarsDomain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.parseErrorBarsOfAxis)(displayedData, graphicalItems.filter(function(item) {\n                        var _defaultProps2, _defaultProps3;\n                        var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps2 = item.type.defaultProps) === null || _defaultProps2 === void 0 ? void 0 : _defaultProps2[axisIdKey];\n                        var itemHide = 'hide' in item.props ? item.props.hide : (_defaultProps3 = item.type.defaultProps) === null || _defaultProps3 === void 0 ? void 0 : _defaultProps3.hide;\n                        return itemAxisId === axisId && (includeHidden || !itemHide);\n                    }), dataKey, axisType, layout);\n                    if (errorBarsDomain) {\n                        domain = errorBarsDomain;\n                    }\n                }\n                if (isCategorical && (type === 'number' || scale !== 'auto')) {\n                    categoricalDomain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfDataByKey)(displayedData, dataKey, 'category');\n                }\n            } else if (isCategorical) {\n                // the axis is a categorical axis\n                domain = lodash_range__WEBPACK_IMPORTED_MODULE_3___default()(0, len);\n            } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack && type === 'number') {\n                // when stackOffset is 'expand', the domain may be calculated as [0, 1.000000000002]\n                domain = stackOffset === 'expand' ? [\n                    0,\n                    1\n                ] : (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfStackGroups)(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n            } else {\n                domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfItemsWithSameAxis)(displayedData, graphicalItems.filter(function(item) {\n                    var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : item.type.defaultProps[axisIdKey];\n                    var itemHide = 'hide' in item.props ? item.props.hide : item.type.defaultProps.hide;\n                    return itemAxisId === axisId && (includeHidden || !itemHide);\n                }), type, layout, true);\n            }\n            if (type === 'number') {\n                // To detect wether there is any reference lines whose props alwaysShow is true\n                domain = (0,_util_DetectReferenceElementsDomain__WEBPACK_IMPORTED_MODULE_13__.detectReferenceElementsDomain)(children, domain, axisId, axisType, ticks);\n                if (childDomain) {\n                    domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.parseSpecifiedDomain)(childDomain, domain, allowDataOverflow);\n                }\n            } else if (type === 'category' && childDomain) {\n                var axisDomain = childDomain;\n                var isDomainValid = domain.every(function(entry) {\n                    return axisDomain.indexOf(entry) >= 0;\n                });\n                if (isDomainValid) {\n                    domain = axisDomain;\n                }\n            }\n        }\n        return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({}, childProps), {}, {\n            axisType: axisType,\n            domain: domain,\n            categoricalDomain: categoricalDomain,\n            duplicateDomain: duplicateDomain,\n            originalDomain: (_childProps$domain2 = childProps.domain) !== null && _childProps$domain2 !== void 0 ? _childProps$domain2 : defaultDomain,\n            isCategorical: isCategorical,\n            layout: layout\n        })));\n    }, {});\n};\n/**\n * Get the configuration of axis by the options of item,\n * this kind of axis does not display in chart\n * @param  {Object} props         Latest props\n * @param  {Array} graphicalItems The instances of item\n * @param  {ReactElement} Axis    Axis Component\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}               Configuration\n */ var getAxisMapByItems = function getAxisMapByItems(props, _ref3) {\n    var graphicalItems = _ref3.graphicalItems, Axis = _ref3.Axis, axisType = _ref3.axisType, axisIdKey = _ref3.axisIdKey, stackGroups = _ref3.stackGroups, dataStartIndex = _ref3.dataStartIndex, dataEndIndex = _ref3.dataEndIndex;\n    var layout = props.layout, children = props.children;\n    var displayedData = getDisplayedData(props.data, {\n        graphicalItems: graphicalItems,\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n    });\n    var len = displayedData.length;\n    var isCategorical = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.isCategoricalAxis)(layout, axisType);\n    var index = -1;\n    // The default type of x-axis is category axis,\n    // The default contents of x-axis is the serial numbers of data\n    // The default type of y-axis is number axis\n    // The default contents of y-axis is the domain of data\n    return graphicalItems.reduce(function(result, child) {\n        var childProps = child.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, child.type.defaultProps), child.props) : child.props;\n        var axisId = childProps[axisIdKey];\n        var originalDomain = getDefaultDomainByAxisType('number');\n        if (!result[axisId]) {\n            index++;\n            var domain;\n            if (isCategorical) {\n                domain = lodash_range__WEBPACK_IMPORTED_MODULE_3___default()(0, len);\n            } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack) {\n                domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfStackGroups)(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n                domain = (0,_util_DetectReferenceElementsDomain__WEBPACK_IMPORTED_MODULE_13__.detectReferenceElementsDomain)(children, domain, axisId, axisType);\n            } else {\n                domain = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.parseSpecifiedDomain)(originalDomain, (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getDomainOfItemsWithSameAxis)(displayedData, graphicalItems.filter(function(item) {\n                    var _defaultProps4, _defaultProps5;\n                    var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps4 = item.type.defaultProps) === null || _defaultProps4 === void 0 ? void 0 : _defaultProps4[axisIdKey];\n                    var itemHide = 'hide' in item.props ? item.props.hide : (_defaultProps5 = item.type.defaultProps) === null || _defaultProps5 === void 0 ? void 0 : _defaultProps5.hide;\n                    return itemAxisId === axisId && !itemHide;\n                }), 'number', layout), Axis.defaultProps.allowDataOverflow);\n                domain = (0,_util_DetectReferenceElementsDomain__WEBPACK_IMPORTED_MODULE_13__.detectReferenceElementsDomain)(children, domain, axisId, axisType);\n            }\n            return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({\n                axisType: axisType\n            }, Axis.defaultProps), {}, {\n                hide: true,\n                orientation: lodash_get__WEBPACK_IMPORTED_MODULE_4___default()(ORIENT_MAP, \"\".concat(axisType, \".\").concat(index % 2), null),\n                domain: domain,\n                originalDomain: originalDomain,\n                isCategorical: isCategorical,\n                layout: layout\n            })));\n        }\n        return result;\n    }, {});\n};\n/**\n * Get the configuration of all x-axis or y-axis\n * @param  {Object} props          Latest props\n * @param  {String} axisType       The type of axis\n * @param  {React.ComponentType}  [AxisComp]      Axis Component\n * @param  {Array}  graphicalItems The instances of item\n * @param  {Object} stackGroups    The items grouped by axisId and stackId\n * @param {Number} dataStartIndex  The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex    The end index of the data series when a brush is applied\n * @return {Object}          Configuration\n */ var getAxisMap = function getAxisMap(props, _ref4) {\n    var _ref4$axisType = _ref4.axisType, axisType = _ref4$axisType === void 0 ? 'xAxis' : _ref4$axisType, AxisComp = _ref4.AxisComp, graphicalItems = _ref4.graphicalItems, stackGroups = _ref4.stackGroups, dataStartIndex = _ref4.dataStartIndex, dataEndIndex = _ref4.dataEndIndex;\n    var children = props.children;\n    var axisIdKey = \"\".concat(axisType, \"Id\");\n    // Get all the instance of Axis\n    var axes = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findAllByType)(children, AxisComp);\n    var axisMap = {};\n    if (axes && axes.length) {\n        axisMap = getAxisMapByAxes(props, {\n            axes: axes,\n            graphicalItems: graphicalItems,\n            axisType: axisType,\n            axisIdKey: axisIdKey,\n            stackGroups: stackGroups,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n        });\n    } else if (graphicalItems && graphicalItems.length) {\n        axisMap = getAxisMapByItems(props, {\n            Axis: AxisComp,\n            graphicalItems: graphicalItems,\n            axisType: axisType,\n            axisIdKey: axisIdKey,\n            stackGroups: stackGroups,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n        });\n    }\n    return axisMap;\n};\nvar tooltipTicksGenerator = function tooltipTicksGenerator(axisMap) {\n    var axis = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.getAnyElementOfObject)(axisMap);\n    var tooltipTicks = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getTicksOfAxis)(axis, false, true);\n    return {\n        tooltipTicks: tooltipTicks,\n        orderedTooltipTicks: lodash_sortBy__WEBPACK_IMPORTED_MODULE_5___default()(tooltipTicks, function(o) {\n            return o.coordinate;\n        }),\n        tooltipAxis: axis,\n        tooltipAxisBandSize: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getBandSizeOfAxis)(axis, tooltipTicks)\n    };\n};\n/**\n * Returns default, reset state for the categorical chart.\n * @param {Object} props Props object to use when creating the default state\n * @return {Object} Whole new state\n */ var createDefaultState = function createDefaultState(props) {\n    var children = props.children, defaultShowTooltip = props.defaultShowTooltip;\n    var brushItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _cartesian_Brush__WEBPACK_IMPORTED_MODULE_15__.Brush);\n    var startIndex = 0;\n    var endIndex = 0;\n    if (props.data && props.data.length !== 0) {\n        endIndex = props.data.length - 1;\n    }\n    if (brushItem && brushItem.props) {\n        if (brushItem.props.startIndex >= 0) {\n            startIndex = brushItem.props.startIndex;\n        }\n        if (brushItem.props.endIndex >= 0) {\n            endIndex = brushItem.props.endIndex;\n        }\n    }\n    return {\n        chartX: 0,\n        chartY: 0,\n        dataStartIndex: startIndex,\n        dataEndIndex: endIndex,\n        activeTooltipIndex: -1,\n        isTooltipActive: Boolean(defaultShowTooltip)\n    };\n};\nvar hasGraphicalBarItem = function hasGraphicalBarItem(graphicalItems) {\n    if (!graphicalItems || !graphicalItems.length) {\n        return false;\n    }\n    return graphicalItems.some(function(item) {\n        var name = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.getDisplayName)(item && item.type);\n        return name && name.indexOf('Bar') >= 0;\n    });\n};\nvar getAxisNameByLayout = function getAxisNameByLayout(layout) {\n    if (layout === 'horizontal') {\n        return {\n            numericAxisName: 'yAxis',\n            cateAxisName: 'xAxis'\n        };\n    }\n    if (layout === 'vertical') {\n        return {\n            numericAxisName: 'xAxis',\n            cateAxisName: 'yAxis'\n        };\n    }\n    if (layout === 'centric') {\n        return {\n            numericAxisName: 'radiusAxis',\n            cateAxisName: 'angleAxis'\n        };\n    }\n    return {\n        numericAxisName: 'angleAxis',\n        cateAxisName: 'radiusAxis'\n    };\n};\n/**\n * Calculate the offset of main part in the svg element\n * @param  {Object} params.props          Latest props\n * @param  {Array}  params.graphicalItems The instances of item\n * @param  {Object} params.xAxisMap       The configuration of x-axis\n * @param  {Object} params.yAxisMap       The configuration of y-axis\n * @param  {Object} prevLegendBBox        The boundary box of legend\n * @return {Object} The offset of main part in the svg element\n */ var calculateOffset = function calculateOffset(_ref5, prevLegendBBox) {\n    var props = _ref5.props, graphicalItems = _ref5.graphicalItems, _ref5$xAxisMap = _ref5.xAxisMap, xAxisMap = _ref5$xAxisMap === void 0 ? {} : _ref5$xAxisMap, _ref5$yAxisMap = _ref5.yAxisMap, yAxisMap = _ref5$yAxisMap === void 0 ? {} : _ref5$yAxisMap;\n    var width = props.width, height = props.height, children = props.children;\n    var margin = props.margin || {};\n    var brushItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _cartesian_Brush__WEBPACK_IMPORTED_MODULE_15__.Brush);\n    var legendItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _component_Legend__WEBPACK_IMPORTED_MODULE_16__.Legend);\n    var offsetH = Object.keys(yAxisMap).reduce(function(result, id) {\n        var entry = yAxisMap[id];\n        var orientation = entry.orientation;\n        if (!entry.mirror && !entry.hide) {\n            return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, result[orientation] + entry.width));\n        }\n        return result;\n    }, {\n        left: margin.left || 0,\n        right: margin.right || 0\n    });\n    var offsetV = Object.keys(xAxisMap).reduce(function(result, id) {\n        var entry = xAxisMap[id];\n        var orientation = entry.orientation;\n        if (!entry.mirror && !entry.hide) {\n            return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, lodash_get__WEBPACK_IMPORTED_MODULE_4___default()(result, \"\".concat(orientation)) + entry.height));\n        }\n        return result;\n    }, {\n        top: margin.top || 0,\n        bottom: margin.bottom || 0\n    });\n    var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n    var brushBottom = offset.bottom;\n    if (brushItem) {\n        offset.bottom += brushItem.props.height || _cartesian_Brush__WEBPACK_IMPORTED_MODULE_15__.Brush.defaultProps.height;\n    }\n    if (legendItem && prevLegendBBox) {\n        // @ts-expect-error margin is optional in props but required in appendOffsetOfLegend\n        offset = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.appendOffsetOfLegend)(offset, graphicalItems, props, prevLegendBBox);\n    }\n    var offsetWidth = width - offset.left - offset.right;\n    var offsetHeight = height - offset.top - offset.bottom;\n    return _objectSpread(_objectSpread({\n        brushBottom: brushBottom\n    }, offset), {}, {\n        // never return negative values for height and width\n        width: Math.max(offsetWidth, 0),\n        height: Math.max(offsetHeight, 0)\n    });\n};\n// Determine the size of the axis, used for calculation of relative bar sizes\nvar getCartesianAxisSize = function getCartesianAxisSize(axisObj, axisName) {\n    if (axisName === 'xAxis') {\n        return axisObj[axisName].width;\n    }\n    if (axisName === 'yAxis') {\n        return axisObj[axisName].height;\n    }\n    // This is only supported for Bar charts (i.e. charts with cartesian axes), so we should never get here\n    return undefined;\n};\nvar generateCategoricalChart = function generateCategoricalChart(_ref6) {\n    var chartName = _ref6.chartName, GraphicalChild = _ref6.GraphicalChild, _ref6$defaultTooltipE = _ref6.defaultTooltipEventType, defaultTooltipEventType = _ref6$defaultTooltipE === void 0 ? 'axis' : _ref6$defaultTooltipE, _ref6$validateTooltip = _ref6.validateTooltipEventTypes, validateTooltipEventTypes = _ref6$validateTooltip === void 0 ? [\n        'axis'\n    ] : _ref6$validateTooltip, axisComponents = _ref6.axisComponents, legendContent = _ref6.legendContent, formatAxisMap = _ref6.formatAxisMap, defaultProps = _ref6.defaultProps;\n    var getFormatItems = function getFormatItems(props, currentState) {\n        var graphicalItems = currentState.graphicalItems, stackGroups = currentState.stackGroups, offset = currentState.offset, updateId = currentState.updateId, dataStartIndex = currentState.dataStartIndex, dataEndIndex = currentState.dataEndIndex;\n        var barSize = props.barSize, layout = props.layout, barGap = props.barGap, barCategoryGap = props.barCategoryGap, globalMaxBarSize = props.maxBarSize;\n        var _getAxisNameByLayout = getAxisNameByLayout(layout), numericAxisName = _getAxisNameByLayout.numericAxisName, cateAxisName = _getAxisNameByLayout.cateAxisName;\n        var hasBar = hasGraphicalBarItem(graphicalItems);\n        var formattedItems = [];\n        graphicalItems.forEach(function(item, index) {\n            var displayedData = getDisplayedData(props.data, {\n                graphicalItems: [\n                    item\n                ],\n                dataStartIndex: dataStartIndex,\n                dataEndIndex: dataEndIndex\n            });\n            var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n            var dataKey = itemProps.dataKey, childMaxBarSize = itemProps.maxBarSize;\n            // axisId of the numerical axis\n            var numericAxisId = itemProps[\"\".concat(numericAxisName, \"Id\")];\n            // axisId of the categorical axis\n            var cateAxisId = itemProps[\"\".concat(cateAxisName, \"Id\")];\n            var axisObjInitialValue = {};\n            var axisObj = axisComponents.reduce(function(result, entry) {\n                var _item$type$displayNam, _item$type;\n                // map of axisId to axis for a specific axis type\n                var axisMap = currentState[\"\".concat(entry.axisType, \"Map\")];\n                // axisId of axis we are currently computing\n                var id = itemProps[\"\".concat(entry.axisType, \"Id\")];\n                /**\n         * tell the user in dev mode that their configuration is incorrect if we cannot find a match between\n         * axisId on the chart and axisId on the axis. zAxis does not get passed in the map for ComposedChart,\n         * leave it out of the check for now.\n         */ !(axisMap && axisMap[id] || entry.axisType === 'zAxis') ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(false, \"Specifying a(n) \".concat(entry.axisType, \"Id requires a corresponding \").concat(entry.axisType, \"Id on the targeted graphical component \").concat((_item$type$displayNam = item === null || item === void 0 || (_item$type = item.type) === null || _item$type === void 0 ? void 0 : _item$type.displayName) !== null && _item$type$displayNam !== void 0 ? _item$type$displayNam : '')) : 0 : void 0;\n                // the axis we are currently formatting\n                var axis = axisMap[id];\n                return _objectSpread(_objectSpread({}, result), {}, _defineProperty(_defineProperty({}, entry.axisType, axis), \"\".concat(entry.axisType, \"Ticks\"), (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getTicksOfAxis)(axis)));\n            }, axisObjInitialValue);\n            var cateAxis = axisObj[cateAxisName];\n            var cateTicks = axisObj[\"\".concat(cateAxisName, \"Ticks\")];\n            var stackedData = stackGroups && stackGroups[numericAxisId] && stackGroups[numericAxisId].hasStack && (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getStackedDataOfItem)(item, stackGroups[numericAxisId].stackGroups);\n            var itemIsBar = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.getDisplayName)(item.type).indexOf('Bar') >= 0;\n            var bandSize = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getBandSizeOfAxis)(cateAxis, cateTicks);\n            var barPosition = [];\n            var sizeList = hasBar && (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getBarSizeList)({\n                barSize: barSize,\n                stackGroups: stackGroups,\n                totalSize: getCartesianAxisSize(axisObj, cateAxisName)\n            });\n            if (itemIsBar) {\n                var _ref7, _getBandSizeOfAxis;\n                // If it is bar, calculate the position of bar\n                var maxBarSize = lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n                var barBandSize = (_ref7 = (_getBandSizeOfAxis = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getBandSizeOfAxis)(cateAxis, cateTicks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref7 !== void 0 ? _ref7 : 0;\n                barPosition = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getBarPosition)({\n                    barGap: barGap,\n                    barCategoryGap: barCategoryGap,\n                    bandSize: barBandSize !== bandSize ? barBandSize : bandSize,\n                    sizeList: sizeList[cateAxisId],\n                    maxBarSize: maxBarSize\n                });\n                if (barBandSize !== bandSize) {\n                    barPosition = barPosition.map(function(pos) {\n                        return _objectSpread(_objectSpread({}, pos), {}, {\n                            position: _objectSpread(_objectSpread({}, pos.position), {}, {\n                                offset: pos.position.offset - barBandSize / 2\n                            })\n                        });\n                    });\n                }\n            }\n            // @ts-expect-error we should stop reading data from ReactElements\n            var composedFn = item && item.type && item.type.getComposedData;\n            if (composedFn) {\n                formattedItems.push({\n                    props: _objectSpread(_objectSpread({}, composedFn(_objectSpread(_objectSpread({}, axisObj), {}, {\n                        displayedData: displayedData,\n                        props: props,\n                        dataKey: dataKey,\n                        item: item,\n                        bandSize: bandSize,\n                        barPosition: barPosition,\n                        offset: offset,\n                        stackedData: stackedData,\n                        layout: layout,\n                        dataStartIndex: dataStartIndex,\n                        dataEndIndex: dataEndIndex\n                    }))), {}, _defineProperty(_defineProperty(_defineProperty({\n                        key: item.key || \"item-\".concat(index)\n                    }, numericAxisName, axisObj[numericAxisName]), cateAxisName, axisObj[cateAxisName]), \"animationId\", updateId)),\n                    childIndex: (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.parseChildIndex)(item, props.children),\n                    item: item\n                });\n            }\n        });\n        return formattedItems;\n    };\n    /**\n   * The AxisMaps are expensive to render on large data sets\n   * so provide the ability to store them in state and only update them when necessary\n   * they are dependent upon the start and end index of\n   * the brush so it's important that this method is called _after_\n   * the state is updated with any new start/end indices\n   *\n   * @param {Object} props          The props object to be used for updating the axismaps\n   * dataStartIndex: The start index of the data series when a brush is applied\n   * dataEndIndex: The end index of the data series when a brush is applied\n   * updateId: The update id\n   * @param {Object} prevState      Prev state\n   * @return {Object} state New state to set\n   */ var updateStateOfAxisMapsOffsetAndStackGroups = function updateStateOfAxisMapsOffsetAndStackGroups(_ref8, prevState) {\n        var props = _ref8.props, dataStartIndex = _ref8.dataStartIndex, dataEndIndex = _ref8.dataEndIndex, updateId = _ref8.updateId;\n        if (!(0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.validateWidthHeight)({\n            props: props\n        })) {\n            return null;\n        }\n        var children = props.children, layout = props.layout, stackOffset = props.stackOffset, data = props.data, reverseStackOrder = props.reverseStackOrder;\n        var _getAxisNameByLayout2 = getAxisNameByLayout(layout), numericAxisName = _getAxisNameByLayout2.numericAxisName, cateAxisName = _getAxisNameByLayout2.cateAxisName;\n        var graphicalItems = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findAllByType)(children, GraphicalChild);\n        var stackGroups = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getStackGroupsByAxisId)(data, graphicalItems, \"\".concat(numericAxisName, \"Id\"), \"\".concat(cateAxisName, \"Id\"), stackOffset, reverseStackOrder);\n        var axisObj = axisComponents.reduce(function(result, entry) {\n            var name = \"\".concat(entry.axisType, \"Map\");\n            return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, name, getAxisMap(props, _objectSpread(_objectSpread({}, entry), {}, {\n                graphicalItems: graphicalItems,\n                stackGroups: entry.axisType === numericAxisName && stackGroups,\n                dataStartIndex: dataStartIndex,\n                dataEndIndex: dataEndIndex\n            }))));\n        }, {});\n        var offset = calculateOffset(_objectSpread(_objectSpread({}, axisObj), {}, {\n            props: props,\n            graphicalItems: graphicalItems\n        }), prevState === null || prevState === void 0 ? void 0 : prevState.legendBBox);\n        Object.keys(axisObj).forEach(function(key) {\n            axisObj[key] = formatAxisMap(props, axisObj[key], offset, key.replace('Map', ''), chartName);\n        });\n        var cateAxisMap = axisObj[\"\".concat(cateAxisName, \"Map\")];\n        var ticksObj = tooltipTicksGenerator(cateAxisMap);\n        var formattedGraphicalItems = getFormatItems(props, _objectSpread(_objectSpread({}, axisObj), {}, {\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId,\n            graphicalItems: graphicalItems,\n            stackGroups: stackGroups,\n            offset: offset\n        }));\n        return _objectSpread(_objectSpread({\n            formattedGraphicalItems: formattedGraphicalItems,\n            graphicalItems: graphicalItems,\n            offset: offset,\n            stackGroups: stackGroups\n        }, ticksObj), axisObj);\n    };\n    var CategoricalChartWrapper = /*#__PURE__*/ function(_Component) {\n        function CategoricalChartWrapper(_props) {\n            var _props$id, _props$throttleDelay;\n            var _this;\n            _classCallCheck(this, CategoricalChartWrapper);\n            _this = _callSuper(this, CategoricalChartWrapper, [\n                _props\n            ]);\n            _defineProperty(_this, \"eventEmitterSymbol\", Symbol('rechartsEventEmitter'));\n            _defineProperty(_this, \"accessibilityManager\", new _AccessibilityManager__WEBPACK_IMPORTED_MODULE_17__.AccessibilityManager());\n            _defineProperty(_this, \"handleLegendBBoxUpdate\", function(box) {\n                if (box) {\n                    var _this$state = _this.state, dataStartIndex = _this$state.dataStartIndex, dataEndIndex = _this$state.dataEndIndex, updateId = _this$state.updateId;\n                    _this.setState(_objectSpread({\n                        legendBBox: box\n                    }, updateStateOfAxisMapsOffsetAndStackGroups({\n                        props: _this.props,\n                        dataStartIndex: dataStartIndex,\n                        dataEndIndex: dataEndIndex,\n                        updateId: updateId\n                    }, _objectSpread(_objectSpread({}, _this.state), {}, {\n                        legendBBox: box\n                    }))));\n                }\n            });\n            _defineProperty(_this, \"handleReceiveSyncEvent\", function(cId, data, emitter) {\n                if (_this.props.syncId === cId) {\n                    if (emitter === _this.eventEmitterSymbol && typeof _this.props.syncMethod !== 'function') {\n                        return;\n                    }\n                    _this.applySyncEvent(data);\n                }\n            });\n            _defineProperty(_this, \"handleBrushChange\", function(_ref9) {\n                var startIndex = _ref9.startIndex, endIndex = _ref9.endIndex;\n                // Only trigger changes if the extents of the brush have actually changed\n                if (startIndex !== _this.state.dataStartIndex || endIndex !== _this.state.dataEndIndex) {\n                    var updateId = _this.state.updateId;\n                    _this.setState(function() {\n                        return _objectSpread({\n                            dataStartIndex: startIndex,\n                            dataEndIndex: endIndex\n                        }, updateStateOfAxisMapsOffsetAndStackGroups({\n                            props: _this.props,\n                            dataStartIndex: startIndex,\n                            dataEndIndex: endIndex,\n                            updateId: updateId\n                        }, _this.state));\n                    });\n                    _this.triggerSyncEvent({\n                        dataStartIndex: startIndex,\n                        dataEndIndex: endIndex\n                    });\n                }\n            });\n            /**\n       * The handler of mouse entering chart\n       * @param  {Object} e              Event object\n       * @return {Null}                  null\n       */ _defineProperty(_this, \"handleMouseEnter\", function(e) {\n                var mouse = _this.getMouseInfo(e);\n                if (mouse) {\n                    var _nextState = _objectSpread(_objectSpread({}, mouse), {}, {\n                        isTooltipActive: true\n                    });\n                    _this.setState(_nextState);\n                    _this.triggerSyncEvent(_nextState);\n                    var onMouseEnter = _this.props.onMouseEnter;\n                    if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onMouseEnter)) {\n                        onMouseEnter(_nextState, e);\n                    }\n                }\n            });\n            _defineProperty(_this, \"triggeredAfterMouseMove\", function(e) {\n                var mouse = _this.getMouseInfo(e);\n                var nextState = mouse ? _objectSpread(_objectSpread({}, mouse), {}, {\n                    isTooltipActive: true\n                }) : {\n                    isTooltipActive: false\n                };\n                _this.setState(nextState);\n                _this.triggerSyncEvent(nextState);\n                var onMouseMove = _this.props.onMouseMove;\n                if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onMouseMove)) {\n                    onMouseMove(nextState, e);\n                }\n            });\n            /**\n       * The handler of mouse entering a scatter\n       * @param {Object} el The active scatter\n       * @return {Object} no return\n       */ _defineProperty(_this, \"handleItemMouseEnter\", function(el) {\n                _this.setState(function() {\n                    return {\n                        isTooltipActive: true,\n                        activeItem: el,\n                        activePayload: el.tooltipPayload,\n                        activeCoordinate: el.tooltipPosition || {\n                            x: el.cx,\n                            y: el.cy\n                        }\n                    };\n                });\n            });\n            /**\n       * The handler of mouse leaving a scatter\n       * @return {Object} no return\n       */ _defineProperty(_this, \"handleItemMouseLeave\", function() {\n                _this.setState(function() {\n                    return {\n                        isTooltipActive: false\n                    };\n                });\n            });\n            /**\n       * The handler of mouse moving in chart\n       * @param  {React.MouseEvent} e        Event object\n       * @return {void} no return\n       */ _defineProperty(_this, \"handleMouseMove\", function(e) {\n                e.persist();\n                _this.throttleTriggeredAfterMouseMove(e);\n            });\n            /**\n       * The handler if mouse leaving chart\n       * @param {Object} e Event object\n       * @return {Null} no return\n       */ _defineProperty(_this, \"handleMouseLeave\", function(e) {\n                _this.throttleTriggeredAfterMouseMove.cancel();\n                var nextState = {\n                    isTooltipActive: false\n                };\n                _this.setState(nextState);\n                _this.triggerSyncEvent(nextState);\n                var onMouseLeave = _this.props.onMouseLeave;\n                if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onMouseLeave)) {\n                    onMouseLeave(nextState, e);\n                }\n            });\n            _defineProperty(_this, \"handleOuterEvent\", function(e) {\n                var eventName = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.getReactEventByType)(e);\n                var event = lodash_get__WEBPACK_IMPORTED_MODULE_4___default()(_this.props, \"\".concat(eventName));\n                if (eventName && lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(event)) {\n                    var _mouse;\n                    var mouse;\n                    if (/.*touch.*/i.test(eventName)) {\n                        mouse = _this.getMouseInfo(e.changedTouches[0]);\n                    } else {\n                        mouse = _this.getMouseInfo(e);\n                    }\n                    event((_mouse = mouse) !== null && _mouse !== void 0 ? _mouse : {}, e);\n                }\n            });\n            _defineProperty(_this, \"handleClick\", function(e) {\n                var mouse = _this.getMouseInfo(e);\n                if (mouse) {\n                    var _nextState2 = _objectSpread(_objectSpread({}, mouse), {}, {\n                        isTooltipActive: true\n                    });\n                    _this.setState(_nextState2);\n                    _this.triggerSyncEvent(_nextState2);\n                    var onClick = _this.props.onClick;\n                    if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onClick)) {\n                        onClick(_nextState2, e);\n                    }\n                }\n            });\n            _defineProperty(_this, \"handleMouseDown\", function(e) {\n                var onMouseDown = _this.props.onMouseDown;\n                if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onMouseDown)) {\n                    var _nextState3 = _this.getMouseInfo(e);\n                    onMouseDown(_nextState3, e);\n                }\n            });\n            _defineProperty(_this, \"handleMouseUp\", function(e) {\n                var onMouseUp = _this.props.onMouseUp;\n                if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onMouseUp)) {\n                    var _nextState4 = _this.getMouseInfo(e);\n                    onMouseUp(_nextState4, e);\n                }\n            });\n            _defineProperty(_this, \"handleTouchMove\", function(e) {\n                if (e.changedTouches != null && e.changedTouches.length > 0) {\n                    _this.throttleTriggeredAfterMouseMove(e.changedTouches[0]);\n                }\n            });\n            _defineProperty(_this, \"handleTouchStart\", function(e) {\n                if (e.changedTouches != null && e.changedTouches.length > 0) {\n                    _this.handleMouseDown(e.changedTouches[0]);\n                }\n            });\n            _defineProperty(_this, \"handleTouchEnd\", function(e) {\n                if (e.changedTouches != null && e.changedTouches.length > 0) {\n                    _this.handleMouseUp(e.changedTouches[0]);\n                }\n            });\n            _defineProperty(_this, \"handleDoubleClick\", function(e) {\n                var onDoubleClick = _this.props.onDoubleClick;\n                if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onDoubleClick)) {\n                    var _nextState5 = _this.getMouseInfo(e);\n                    onDoubleClick(_nextState5, e);\n                }\n            });\n            _defineProperty(_this, \"handleContextMenu\", function(e) {\n                var onContextMenu = _this.props.onContextMenu;\n                if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(onContextMenu)) {\n                    var _nextState6 = _this.getMouseInfo(e);\n                    onContextMenu(_nextState6, e);\n                }\n            });\n            _defineProperty(_this, \"triggerSyncEvent\", function(data) {\n                if (_this.props.syncId !== undefined) {\n                    _util_Events__WEBPACK_IMPORTED_MODULE_18__.eventCenter.emit(_util_Events__WEBPACK_IMPORTED_MODULE_18__.SYNC_EVENT, _this.props.syncId, data, _this.eventEmitterSymbol);\n                }\n            });\n            _defineProperty(_this, \"applySyncEvent\", function(data) {\n                var _this$props = _this.props, layout = _this$props.layout, syncMethod = _this$props.syncMethod;\n                var updateId = _this.state.updateId;\n                var dataStartIndex = data.dataStartIndex, dataEndIndex = data.dataEndIndex;\n                if (data.dataStartIndex !== undefined || data.dataEndIndex !== undefined) {\n                    _this.setState(_objectSpread({\n                        dataStartIndex: dataStartIndex,\n                        dataEndIndex: dataEndIndex\n                    }, updateStateOfAxisMapsOffsetAndStackGroups({\n                        props: _this.props,\n                        dataStartIndex: dataStartIndex,\n                        dataEndIndex: dataEndIndex,\n                        updateId: updateId\n                    }, _this.state)));\n                } else if (data.activeTooltipIndex !== undefined) {\n                    var chartX = data.chartX, chartY = data.chartY;\n                    var activeTooltipIndex = data.activeTooltipIndex;\n                    var _this$state2 = _this.state, offset = _this$state2.offset, tooltipTicks = _this$state2.tooltipTicks;\n                    if (!offset) {\n                        return;\n                    }\n                    if (typeof syncMethod === 'function') {\n                        // Call a callback function. If there is an application specific algorithm\n                        activeTooltipIndex = syncMethod(tooltipTicks, data);\n                    } else if (syncMethod === 'value') {\n                        // Set activeTooltipIndex to the index with the same value as data.activeLabel\n                        // For loop instead of findIndex because the latter is very slow in some browsers\n                        activeTooltipIndex = -1; // in case we cannot find the element\n                        for(var i = 0; i < tooltipTicks.length; i++){\n                            if (tooltipTicks[i].value === data.activeLabel) {\n                                activeTooltipIndex = i;\n                                break;\n                            }\n                        }\n                    }\n                    var viewBox = _objectSpread(_objectSpread({}, offset), {}, {\n                        x: offset.left,\n                        y: offset.top\n                    });\n                    // When a categorical chart is combined with another chart, the value of chartX\n                    // and chartY may beyond the boundaries.\n                    var validateChartX = Math.min(chartX, viewBox.x + viewBox.width);\n                    var validateChartY = Math.min(chartY, viewBox.y + viewBox.height);\n                    var activeLabel = tooltipTicks[activeTooltipIndex] && tooltipTicks[activeTooltipIndex].value;\n                    var activePayload = getTooltipContent(_this.state, _this.props.data, activeTooltipIndex);\n                    var activeCoordinate = tooltipTicks[activeTooltipIndex] ? {\n                        x: layout === 'horizontal' ? tooltipTicks[activeTooltipIndex].coordinate : validateChartX,\n                        y: layout === 'horizontal' ? validateChartY : tooltipTicks[activeTooltipIndex].coordinate\n                    } : originCoordinate;\n                    _this.setState(_objectSpread(_objectSpread({}, data), {}, {\n                        activeLabel: activeLabel,\n                        activeCoordinate: activeCoordinate,\n                        activePayload: activePayload,\n                        activeTooltipIndex: activeTooltipIndex\n                    }));\n                } else {\n                    _this.setState(data);\n                }\n            });\n            _defineProperty(_this, \"renderCursor\", function(element) {\n                var _element$props$active;\n                var _this$state3 = _this.state, isTooltipActive = _this$state3.isTooltipActive, activeCoordinate = _this$state3.activeCoordinate, activePayload = _this$state3.activePayload, offset = _this$state3.offset, activeTooltipIndex = _this$state3.activeTooltipIndex, tooltipAxisBandSize = _this$state3.tooltipAxisBandSize;\n                var tooltipEventType = _this.getTooltipEventType();\n                // The cursor is a part of the Tooltip, and it should be shown (by default) when the Tooltip is active.\n                var isActive = (_element$props$active = element.props.active) !== null && _element$props$active !== void 0 ? _element$props$active : isTooltipActive;\n                var layout = _this.props.layout;\n                var key = element.key || '_recharts-cursor';\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_component_Cursor__WEBPACK_IMPORTED_MODULE_19__.Cursor, {\n                    key: key,\n                    activeCoordinate: activeCoordinate,\n                    activePayload: activePayload,\n                    activeTooltipIndex: activeTooltipIndex,\n                    chartName: chartName,\n                    element: element,\n                    isActive: isActive,\n                    layout: layout,\n                    offset: offset,\n                    tooltipAxisBandSize: tooltipAxisBandSize,\n                    tooltipEventType: tooltipEventType\n                });\n            });\n            _defineProperty(_this, \"renderPolarAxis\", function(element, displayName, index) {\n                var axisType = lodash_get__WEBPACK_IMPORTED_MODULE_4___default()(element, 'type.axisType');\n                var axisMap = lodash_get__WEBPACK_IMPORTED_MODULE_4___default()(_this.state, \"\".concat(axisType, \"Map\"));\n                var elementDefaultProps = element.type.defaultProps;\n                var elementProps = elementDefaultProps !== undefined ? _objectSpread(_objectSpread({}, elementDefaultProps), element.props) : element.props;\n                var axisOption = axisMap && axisMap[elementProps[\"\".concat(axisType, \"Id\")]];\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, _objectSpread(_objectSpread({}, axisOption), {}, {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(axisType, axisOption.className),\n                    key: element.key || \"\".concat(displayName, \"-\").concat(index),\n                    ticks: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getTicksOfAxis)(axisOption, true)\n                }));\n            });\n            _defineProperty(_this, \"renderPolarGrid\", function(element) {\n                var _element$props = element.props, radialLines = _element$props.radialLines, polarAngles = _element$props.polarAngles, polarRadius = _element$props.polarRadius;\n                var _this$state4 = _this.state, radiusAxisMap = _this$state4.radiusAxisMap, angleAxisMap = _this$state4.angleAxisMap;\n                var radiusAxis = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.getAnyElementOfObject)(radiusAxisMap);\n                var angleAxis = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.getAnyElementOfObject)(angleAxisMap);\n                var cx = angleAxis.cx, cy = angleAxis.cy, innerRadius = angleAxis.innerRadius, outerRadius = angleAxis.outerRadius;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, {\n                    polarAngles: Array.isArray(polarAngles) ? polarAngles : (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getTicksOfAxis)(angleAxis, true).map(function(entry) {\n                        return entry.coordinate;\n                    }),\n                    polarRadius: Array.isArray(polarRadius) ? polarRadius : (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getTicksOfAxis)(radiusAxis, true).map(function(entry) {\n                        return entry.coordinate;\n                    }),\n                    cx: cx,\n                    cy: cy,\n                    innerRadius: innerRadius,\n                    outerRadius: outerRadius,\n                    key: element.key || 'polar-grid',\n                    radialLines: radialLines\n                });\n            });\n            /**\n       * Draw legend\n       * @return {ReactElement}            The instance of Legend\n       */ _defineProperty(_this, \"renderLegend\", function() {\n                var formattedGraphicalItems = _this.state.formattedGraphicalItems;\n                var _this$props2 = _this.props, children = _this$props2.children, width = _this$props2.width, height = _this$props2.height;\n                var margin = _this.props.margin || {};\n                var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n                var props = (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_20__.getLegendProps)({\n                    children: children,\n                    formattedGraphicalItems: formattedGraphicalItems,\n                    legendWidth: legendWidth,\n                    legendContent: legendContent\n                });\n                if (!props) {\n                    return null;\n                }\n                var item = props.item, otherProps = _objectWithoutProperties(props, _excluded);\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(item, _objectSpread(_objectSpread({}, otherProps), {}, {\n                    chartWidth: width,\n                    chartHeight: height,\n                    margin: margin,\n                    onBBoxUpdate: _this.handleLegendBBoxUpdate\n                }));\n            });\n            /**\n       * Draw Tooltip\n       * @return {ReactElement}  The instance of Tooltip\n       */ _defineProperty(_this, \"renderTooltip\", function() {\n                var _tooltipItem$props$ac;\n                var _this$props3 = _this.props, children = _this$props3.children, accessibilityLayer = _this$props3.accessibilityLayer;\n                var tooltipItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip);\n                if (!tooltipItem) {\n                    return null;\n                }\n                var _this$state5 = _this.state, isTooltipActive = _this$state5.isTooltipActive, activeCoordinate = _this$state5.activeCoordinate, activePayload = _this$state5.activePayload, activeLabel = _this$state5.activeLabel, offset = _this$state5.offset;\n                // The user can set isActive on the Tooltip,\n                // and we respect the user to enable customisation.\n                // The Tooltip is active if the user has set isActive, or if the tooltip is active due to a mouse event.\n                var isActive = (_tooltipItem$props$ac = tooltipItem.props.active) !== null && _tooltipItem$props$ac !== void 0 ? _tooltipItem$props$ac : isTooltipActive;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(tooltipItem, {\n                    viewBox: _objectSpread(_objectSpread({}, offset), {}, {\n                        x: offset.left,\n                        y: offset.top\n                    }),\n                    active: isActive,\n                    label: activeLabel,\n                    payload: isActive ? activePayload : [],\n                    coordinate: activeCoordinate,\n                    accessibilityLayer: accessibilityLayer\n                });\n            });\n            _defineProperty(_this, \"renderBrush\", function(element) {\n                var _this$props4 = _this.props, margin = _this$props4.margin, data = _this$props4.data;\n                var _this$state6 = _this.state, offset = _this$state6.offset, dataStartIndex = _this$state6.dataStartIndex, dataEndIndex = _this$state6.dataEndIndex, updateId = _this$state6.updateId;\n                // TODO: update brush when children update\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, {\n                    key: element.key || '_recharts-brush',\n                    onChange: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.combineEventHandlers)(_this.handleBrushChange, element.props.onChange),\n                    data: data,\n                    x: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.isNumber)(element.props.x) ? element.props.x : offset.left,\n                    y: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.isNumber)(element.props.y) ? element.props.y : offset.top + offset.height + offset.brushBottom - (margin.bottom || 0),\n                    width: (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.isNumber)(element.props.width) ? element.props.width : offset.width,\n                    startIndex: dataStartIndex,\n                    endIndex: dataEndIndex,\n                    updateId: \"brush-\".concat(updateId)\n                });\n            });\n            _defineProperty(_this, \"renderReferenceElement\", function(element, displayName, index) {\n                if (!element) {\n                    return null;\n                }\n                var _this2 = _this, clipPathId = _this2.clipPathId;\n                var _this$state7 = _this.state, xAxisMap = _this$state7.xAxisMap, yAxisMap = _this$state7.yAxisMap, offset = _this$state7.offset;\n                var elementDefaultProps = element.type.defaultProps || {};\n                var _element$props2 = element.props, _element$props2$xAxis = _element$props2.xAxisId, xAxisId = _element$props2$xAxis === void 0 ? elementDefaultProps.xAxisId : _element$props2$xAxis, _element$props2$yAxis = _element$props2.yAxisId, yAxisId = _element$props2$yAxis === void 0 ? elementDefaultProps.yAxisId : _element$props2$yAxis;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, {\n                    key: element.key || \"\".concat(displayName, \"-\").concat(index),\n                    xAxis: xAxisMap[xAxisId],\n                    yAxis: yAxisMap[yAxisId],\n                    viewBox: {\n                        x: offset.left,\n                        y: offset.top,\n                        width: offset.width,\n                        height: offset.height\n                    },\n                    clipPathId: clipPathId\n                });\n            });\n            _defineProperty(_this, \"renderActivePoints\", function(_ref10) {\n                var item = _ref10.item, activePoint = _ref10.activePoint, basePoint = _ref10.basePoint, childIndex = _ref10.childIndex, isRange = _ref10.isRange;\n                var result = [];\n                // item is not a React Element so we don't need to resolve defaultProps.\n                var key = item.props.key;\n                var itemItemProps = item.item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.item.type.defaultProps), item.item.props) : item.item.props;\n                var activeDot = itemItemProps.activeDot, dataKey = itemItemProps.dataKey;\n                var dotProps = _objectSpread(_objectSpread({\n                    index: childIndex,\n                    dataKey: dataKey,\n                    cx: activePoint.x,\n                    cy: activePoint.y,\n                    r: 4,\n                    fill: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.getMainColorOfGraphicItem)(item.item),\n                    strokeWidth: 2,\n                    stroke: '#fff',\n                    payload: activePoint.payload,\n                    value: activePoint.value\n                }, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.filterProps)(activeDot, false)), (0,_util_types__WEBPACK_IMPORTED_MODULE_22__.adaptEventHandlers)(activeDot));\n                result.push(CategoricalChartWrapper.renderActiveDot(activeDot, dotProps, \"\".concat(key, \"-activePoint-\").concat(childIndex)));\n                if (basePoint) {\n                    result.push(CategoricalChartWrapper.renderActiveDot(activeDot, _objectSpread(_objectSpread({}, dotProps), {}, {\n                        cx: basePoint.x,\n                        cy: basePoint.y\n                    }), \"\".concat(key, \"-basePoint-\").concat(childIndex)));\n                } else if (isRange) {\n                    result.push(null);\n                }\n                return result;\n            });\n            _defineProperty(_this, \"renderGraphicChild\", function(element, displayName, index) {\n                var item = _this.filterFormatItem(element, displayName, index);\n                if (!item) {\n                    return null;\n                }\n                var tooltipEventType = _this.getTooltipEventType();\n                var _this$state8 = _this.state, isTooltipActive = _this$state8.isTooltipActive, tooltipAxis = _this$state8.tooltipAxis, activeTooltipIndex = _this$state8.activeTooltipIndex, activeLabel = _this$state8.activeLabel;\n                var children = _this.props.children;\n                var tooltipItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip);\n                // item is not a React Element so we don't need to resolve defaultProps\n                var _item$props = item.props, points = _item$props.points, isRange = _item$props.isRange, baseLine = _item$props.baseLine;\n                var itemItemProps = item.item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.item.type.defaultProps), item.item.props) : item.item.props;\n                var activeDot = itemItemProps.activeDot, hide = itemItemProps.hide, activeBar = itemItemProps.activeBar, activeShape = itemItemProps.activeShape;\n                var hasActive = Boolean(!hide && isTooltipActive && tooltipItem && (activeDot || activeBar || activeShape));\n                var itemEvents = {};\n                if (tooltipEventType !== 'axis' && tooltipItem && tooltipItem.props.trigger === 'click') {\n                    itemEvents = {\n                        onClick: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.combineEventHandlers)(_this.handleItemMouseEnter, element.props.onClick)\n                    };\n                } else if (tooltipEventType !== 'axis') {\n                    itemEvents = {\n                        onMouseLeave: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.combineEventHandlers)(_this.handleItemMouseLeave, element.props.onMouseLeave),\n                        onMouseEnter: (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_11__.combineEventHandlers)(_this.handleItemMouseEnter, element.props.onMouseEnter)\n                    };\n                }\n                var graphicalItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, _objectSpread(_objectSpread({}, item.props), itemEvents));\n                function findWithPayload(entry) {\n                    // TODO needs to verify dataKey is Function\n                    return typeof tooltipAxis.dataKey === 'function' ? tooltipAxis.dataKey(entry.payload) : null;\n                }\n                if (hasActive) {\n                    if (activeTooltipIndex >= 0) {\n                        var activePoint, basePoint;\n                        if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n                            // number transform to string\n                            var specifiedKey = typeof tooltipAxis.dataKey === 'function' ? findWithPayload : 'payload.'.concat(tooltipAxis.dataKey.toString());\n                            activePoint = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.findEntryInArray)(points, specifiedKey, activeLabel);\n                            basePoint = isRange && baseLine && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.findEntryInArray)(baseLine, specifiedKey, activeLabel);\n                        } else {\n                            activePoint = points === null || points === void 0 ? void 0 : points[activeTooltipIndex];\n                            basePoint = isRange && baseLine && baseLine[activeTooltipIndex];\n                        }\n                        if (activeShape || activeBar) {\n                            var activeIndex = element.props.activeIndex !== undefined ? element.props.activeIndex : activeTooltipIndex;\n                            return [\n                                /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, _objectSpread(_objectSpread(_objectSpread({}, item.props), itemEvents), {}, {\n                                    activeIndex: activeIndex\n                                })),\n                                null,\n                                null\n                            ];\n                        }\n                        if (!lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(activePoint)) {\n                            return [\n                                graphicalItem\n                            ].concat(_toConsumableArray(_this.renderActivePoints({\n                                item: item,\n                                activePoint: activePoint,\n                                basePoint: basePoint,\n                                childIndex: activeTooltipIndex,\n                                isRange: isRange\n                            })));\n                        }\n                    } else {\n                        var _this$getItemByXY;\n                        /**\n             * We hit this block if consumer uses a Tooltip without XAxis and/or YAxis.\n             * In which case, this.state.activeTooltipIndex never gets set\n             * because the mouse events that trigger that value getting set never get trigged without the axis components.\n             *\n             * An example usage case is a FunnelChart\n             */ var _ref11 = (_this$getItemByXY = _this.getItemByXY(_this.state.activeCoordinate)) !== null && _this$getItemByXY !== void 0 ? _this$getItemByXY : {\n                            graphicalItem: graphicalItem\n                        }, _ref11$graphicalItem = _ref11.graphicalItem, _ref11$graphicalItem$ = _ref11$graphicalItem.item, xyItem = _ref11$graphicalItem$ === void 0 ? element : _ref11$graphicalItem$, childIndex = _ref11$graphicalItem.childIndex;\n                        var elementProps = _objectSpread(_objectSpread(_objectSpread({}, item.props), itemEvents), {}, {\n                            activeIndex: childIndex\n                        });\n                        return [\n                            /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(xyItem, elementProps),\n                            null,\n                            null\n                        ];\n                    }\n                }\n                if (isRange) {\n                    return [\n                        graphicalItem,\n                        null,\n                        null\n                    ];\n                }\n                return [\n                    graphicalItem,\n                    null\n                ];\n            });\n            _defineProperty(_this, \"renderCustomized\", function(element, displayName, index) {\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, _objectSpread(_objectSpread({\n                    key: \"recharts-customized-\".concat(index)\n                }, _this.props), _this.state));\n            });\n            _defineProperty(_this, \"renderMap\", {\n                CartesianGrid: {\n                    handler: renderAsIs,\n                    once: true\n                },\n                ReferenceArea: {\n                    handler: _this.renderReferenceElement\n                },\n                ReferenceLine: {\n                    handler: renderAsIs\n                },\n                ReferenceDot: {\n                    handler: _this.renderReferenceElement\n                },\n                XAxis: {\n                    handler: renderAsIs\n                },\n                YAxis: {\n                    handler: renderAsIs\n                },\n                Brush: {\n                    handler: _this.renderBrush,\n                    once: true\n                },\n                Bar: {\n                    handler: _this.renderGraphicChild\n                },\n                Line: {\n                    handler: _this.renderGraphicChild\n                },\n                Area: {\n                    handler: _this.renderGraphicChild\n                },\n                Radar: {\n                    handler: _this.renderGraphicChild\n                },\n                RadialBar: {\n                    handler: _this.renderGraphicChild\n                },\n                Scatter: {\n                    handler: _this.renderGraphicChild\n                },\n                Pie: {\n                    handler: _this.renderGraphicChild\n                },\n                Funnel: {\n                    handler: _this.renderGraphicChild\n                },\n                Tooltip: {\n                    handler: _this.renderCursor,\n                    once: true\n                },\n                PolarGrid: {\n                    handler: _this.renderPolarGrid,\n                    once: true\n                },\n                PolarAngleAxis: {\n                    handler: _this.renderPolarAxis\n                },\n                PolarRadiusAxis: {\n                    handler: _this.renderPolarAxis\n                },\n                Customized: {\n                    handler: _this.renderCustomized\n                }\n            });\n            _this.clipPathId = \"\".concat((_props$id = _props.id) !== null && _props$id !== void 0 ? _props$id : (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.uniqueId)('recharts'), \"-clip\");\n            // trigger 60fps\n            _this.throttleTriggeredAfterMouseMove = lodash_throttle__WEBPACK_IMPORTED_MODULE_6___default()(_this.triggeredAfterMouseMove, (_props$throttleDelay = _props.throttleDelay) !== null && _props$throttleDelay !== void 0 ? _props$throttleDelay : 1000 / 60);\n            _this.state = {};\n            return _this;\n        }\n        _inherits(CategoricalChartWrapper, _Component);\n        return _createClass(CategoricalChartWrapper, [\n            {\n                key: \"componentDidMount\",\n                value: function componentDidMount() {\n                    var _this$props$margin$le, _this$props$margin$to;\n                    this.addListener();\n                    this.accessibilityManager.setDetails({\n                        container: this.container,\n                        offset: {\n                            left: (_this$props$margin$le = this.props.margin.left) !== null && _this$props$margin$le !== void 0 ? _this$props$margin$le : 0,\n                            top: (_this$props$margin$to = this.props.margin.top) !== null && _this$props$margin$to !== void 0 ? _this$props$margin$to : 0\n                        },\n                        coordinateList: this.state.tooltipTicks,\n                        mouseHandlerCallback: this.triggeredAfterMouseMove,\n                        layout: this.props.layout\n                    });\n                    this.displayDefaultTooltip();\n                }\n            },\n            {\n                key: \"displayDefaultTooltip\",\n                value: function displayDefaultTooltip() {\n                    var _this$props5 = this.props, children = _this$props5.children, data = _this$props5.data, height = _this$props5.height, layout = _this$props5.layout;\n                    var tooltipElem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip);\n                    // If the chart doesn't include a <Tooltip /> element, there's no tooltip to display\n                    if (!tooltipElem) {\n                        return;\n                    }\n                    var defaultIndex = tooltipElem.props.defaultIndex;\n                    // Protect against runtime errors\n                    if (typeof defaultIndex !== 'number' || defaultIndex < 0 || defaultIndex > this.state.tooltipTicks.length - 1) {\n                        return;\n                    }\n                    var activeLabel = this.state.tooltipTicks[defaultIndex] && this.state.tooltipTicks[defaultIndex].value;\n                    var activePayload = getTooltipContent(this.state, data, defaultIndex, activeLabel);\n                    var independentAxisCoord = this.state.tooltipTicks[defaultIndex].coordinate;\n                    var dependentAxisCoord = (this.state.offset.top + height) / 2;\n                    var isHorizontal = layout === 'horizontal';\n                    var activeCoordinate = isHorizontal ? {\n                        x: independentAxisCoord,\n                        y: dependentAxisCoord\n                    } : {\n                        y: independentAxisCoord,\n                        x: dependentAxisCoord\n                    };\n                    // Unlike other chart types, scatter plot's tooltip positions rely on both X and Y coordinates. Only the scatter plot\n                    // element knows its own Y coordinates.\n                    // If there's a scatter plot, we'll want to grab that element for an interrogation.\n                    var scatterPlotElement = this.state.formattedGraphicalItems.find(function(_ref12) {\n                        var item = _ref12.item;\n                        return item.type.name === 'Scatter';\n                    });\n                    if (scatterPlotElement) {\n                        activeCoordinate = _objectSpread(_objectSpread({}, activeCoordinate), scatterPlotElement.props.points[defaultIndex].tooltipPosition);\n                        activePayload = scatterPlotElement.props.points[defaultIndex].tooltipPayload;\n                    }\n                    var nextState = {\n                        activeTooltipIndex: defaultIndex,\n                        isTooltipActive: true,\n                        activeLabel: activeLabel,\n                        activePayload: activePayload,\n                        activeCoordinate: activeCoordinate\n                    };\n                    this.setState(nextState);\n                    this.renderCursor(tooltipElem);\n                    // Make sure that anyone who keyboard-only users who tab to the chart will start their\n                    // cursors at defaultIndex\n                    this.accessibilityManager.setIndex(defaultIndex);\n                }\n            },\n            {\n                key: \"getSnapshotBeforeUpdate\",\n                value: function getSnapshotBeforeUpdate(prevProps, prevState) {\n                    if (!this.props.accessibilityLayer) {\n                        return null;\n                    }\n                    if (this.state.tooltipTicks !== prevState.tooltipTicks) {\n                        this.accessibilityManager.setDetails({\n                            coordinateList: this.state.tooltipTicks\n                        });\n                    }\n                    if (this.props.layout !== prevProps.layout) {\n                        this.accessibilityManager.setDetails({\n                            layout: this.props.layout\n                        });\n                    }\n                    if (this.props.margin !== prevProps.margin) {\n                        var _this$props$margin$le2, _this$props$margin$to2;\n                        this.accessibilityManager.setDetails({\n                            offset: {\n                                left: (_this$props$margin$le2 = this.props.margin.left) !== null && _this$props$margin$le2 !== void 0 ? _this$props$margin$le2 : 0,\n                                top: (_this$props$margin$to2 = this.props.margin.top) !== null && _this$props$margin$to2 !== void 0 ? _this$props$margin$to2 : 0\n                            }\n                        });\n                    }\n                    // Something has to be returned for getSnapshotBeforeUpdate\n                    return null;\n                }\n            },\n            {\n                key: \"componentDidUpdate\",\n                value: function componentDidUpdate(prevProps) {\n                    // Check to see if the Tooltip updated. If so, re-check default tooltip position\n                    if (!(0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.isChildrenEqual)([\n                        (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(prevProps.children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip)\n                    ], [\n                        (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(this.props.children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip)\n                    ])) {\n                        this.displayDefaultTooltip();\n                    }\n                }\n            },\n            {\n                key: \"componentWillUnmount\",\n                value: function componentWillUnmount() {\n                    this.removeListener();\n                    this.throttleTriggeredAfterMouseMove.cancel();\n                }\n            },\n            {\n                key: \"getTooltipEventType\",\n                value: function getTooltipEventType() {\n                    var tooltipItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(this.props.children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip);\n                    if (tooltipItem && typeof tooltipItem.props.shared === 'boolean') {\n                        var eventType = tooltipItem.props.shared ? 'axis' : 'item';\n                        return validateTooltipEventTypes.indexOf(eventType) >= 0 ? eventType : defaultTooltipEventType;\n                    }\n                    return defaultTooltipEventType;\n                }\n            },\n            {\n                key: \"getMouseInfo\",\n                value: function getMouseInfo(event) {\n                    if (!this.container) {\n                        return null;\n                    }\n                    var element = this.container;\n                    var boundingRect = element.getBoundingClientRect();\n                    var containerOffset = (0,_util_DOMUtils__WEBPACK_IMPORTED_MODULE_23__.getOffset)(boundingRect);\n                    var e = {\n                        chartX: Math.round(event.pageX - containerOffset.left),\n                        chartY: Math.round(event.pageY - containerOffset.top)\n                    };\n                    var scale = boundingRect.width / element.offsetWidth || 1;\n                    var rangeObj = this.inRange(e.chartX, e.chartY, scale);\n                    if (!rangeObj) {\n                        return null;\n                    }\n                    var _this$state9 = this.state, xAxisMap = _this$state9.xAxisMap, yAxisMap = _this$state9.yAxisMap;\n                    var tooltipEventType = this.getTooltipEventType();\n                    if (tooltipEventType !== 'axis' && xAxisMap && yAxisMap) {\n                        var xScale = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.getAnyElementOfObject)(xAxisMap).scale;\n                        var yScale = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.getAnyElementOfObject)(yAxisMap).scale;\n                        var xValue = xScale && xScale.invert ? xScale.invert(e.chartX) : null;\n                        var yValue = yScale && yScale.invert ? yScale.invert(e.chartY) : null;\n                        return _objectSpread(_objectSpread({}, e), {}, {\n                            xValue: xValue,\n                            yValue: yValue\n                        });\n                    }\n                    var toolTipData = getTooltipData(this.state, this.props.data, this.props.layout, rangeObj);\n                    if (toolTipData) {\n                        return _objectSpread(_objectSpread({}, e), toolTipData);\n                    }\n                    return null;\n                }\n            },\n            {\n                key: \"inRange\",\n                value: function inRange(x, y) {\n                    var scale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n                    var layout = this.props.layout;\n                    var scaledX = x / scale, scaledY = y / scale;\n                    if (layout === 'horizontal' || layout === 'vertical') {\n                        var offset = this.state.offset;\n                        var isInRange = scaledX >= offset.left && scaledX <= offset.left + offset.width && scaledY >= offset.top && scaledY <= offset.top + offset.height;\n                        return isInRange ? {\n                            x: scaledX,\n                            y: scaledY\n                        } : null;\n                    }\n                    var _this$state10 = this.state, angleAxisMap = _this$state10.angleAxisMap, radiusAxisMap = _this$state10.radiusAxisMap;\n                    if (angleAxisMap && radiusAxisMap) {\n                        var angleAxis = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_10__.getAnyElementOfObject)(angleAxisMap);\n                        return (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_9__.inRangeOfSector)({\n                            x: scaledX,\n                            y: scaledY\n                        }, angleAxis);\n                    }\n                    return null;\n                }\n            },\n            {\n                key: \"parseEventsOfWrapper\",\n                value: function parseEventsOfWrapper() {\n                    var children = this.props.children;\n                    var tooltipEventType = this.getTooltipEventType();\n                    var tooltipItem = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _component_Tooltip__WEBPACK_IMPORTED_MODULE_21__.Tooltip);\n                    var tooltipEvents = {};\n                    if (tooltipItem && tooltipEventType === 'axis') {\n                        if (tooltipItem.props.trigger === 'click') {\n                            tooltipEvents = {\n                                onClick: this.handleClick\n                            };\n                        } else {\n                            tooltipEvents = {\n                                onMouseEnter: this.handleMouseEnter,\n                                onDoubleClick: this.handleDoubleClick,\n                                onMouseMove: this.handleMouseMove,\n                                onMouseLeave: this.handleMouseLeave,\n                                onTouchMove: this.handleTouchMove,\n                                onTouchStart: this.handleTouchStart,\n                                onTouchEnd: this.handleTouchEnd,\n                                onContextMenu: this.handleContextMenu\n                            };\n                        }\n                    }\n                    // @ts-expect-error adaptEventHandlers expects DOM Event but generateCategoricalChart works with React UIEvents\n                    var outerEvents = (0,_util_types__WEBPACK_IMPORTED_MODULE_22__.adaptEventHandlers)(this.props, this.handleOuterEvent);\n                    return _objectSpread(_objectSpread({}, outerEvents), tooltipEvents);\n                }\n            },\n            {\n                key: \"addListener\",\n                value: function addListener() {\n                    _util_Events__WEBPACK_IMPORTED_MODULE_18__.eventCenter.on(_util_Events__WEBPACK_IMPORTED_MODULE_18__.SYNC_EVENT, this.handleReceiveSyncEvent);\n                }\n            },\n            {\n                key: \"removeListener\",\n                value: function removeListener() {\n                    _util_Events__WEBPACK_IMPORTED_MODULE_18__.eventCenter.removeListener(_util_Events__WEBPACK_IMPORTED_MODULE_18__.SYNC_EVENT, this.handleReceiveSyncEvent);\n                }\n            },\n            {\n                key: \"filterFormatItem\",\n                value: function filterFormatItem(item, displayName, childIndex) {\n                    var formattedGraphicalItems = this.state.formattedGraphicalItems;\n                    for(var i = 0, len = formattedGraphicalItems.length; i < len; i++){\n                        var entry = formattedGraphicalItems[i];\n                        if (entry.item === item || entry.props.key === item.key || displayName === (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.getDisplayName)(entry.item.type) && childIndex === entry.childIndex) {\n                            return entry;\n                        }\n                    }\n                    return null;\n                }\n            },\n            {\n                key: \"renderClipPath\",\n                value: function renderClipPath() {\n                    var clipPathId = this.clipPathId;\n                    var _this$state$offset = this.state.offset, left = _this$state$offset.left, top = _this$state$offset.top, height = _this$state$offset.height, width = _this$state$offset.width;\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"clipPath\", {\n                        id: clipPathId\n                    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"rect\", {\n                        x: left,\n                        y: top,\n                        height: height,\n                        width: width\n                    })));\n                }\n            },\n            {\n                key: \"getXScales\",\n                value: function getXScales() {\n                    var xAxisMap = this.state.xAxisMap;\n                    return xAxisMap ? Object.entries(xAxisMap).reduce(function(res, _ref13) {\n                        var _ref14 = _slicedToArray(_ref13, 2), axisId = _ref14[0], axisProps = _ref14[1];\n                        return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n                    }, {}) : null;\n                }\n            },\n            {\n                key: \"getYScales\",\n                value: function getYScales() {\n                    var yAxisMap = this.state.yAxisMap;\n                    return yAxisMap ? Object.entries(yAxisMap).reduce(function(res, _ref15) {\n                        var _ref16 = _slicedToArray(_ref15, 2), axisId = _ref16[0], axisProps = _ref16[1];\n                        return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n                    }, {}) : null;\n                }\n            },\n            {\n                key: \"getXScaleByAxisId\",\n                value: function getXScaleByAxisId(axisId) {\n                    var _this$state$xAxisMap;\n                    return (_this$state$xAxisMap = this.state.xAxisMap) === null || _this$state$xAxisMap === void 0 || (_this$state$xAxisMap = _this$state$xAxisMap[axisId]) === null || _this$state$xAxisMap === void 0 ? void 0 : _this$state$xAxisMap.scale;\n                }\n            },\n            {\n                key: \"getYScaleByAxisId\",\n                value: function getYScaleByAxisId(axisId) {\n                    var _this$state$yAxisMap;\n                    return (_this$state$yAxisMap = this.state.yAxisMap) === null || _this$state$yAxisMap === void 0 || (_this$state$yAxisMap = _this$state$yAxisMap[axisId]) === null || _this$state$yAxisMap === void 0 ? void 0 : _this$state$yAxisMap.scale;\n                }\n            },\n            {\n                key: \"getItemByXY\",\n                value: function getItemByXY(chartXY) {\n                    var _this$state11 = this.state, formattedGraphicalItems = _this$state11.formattedGraphicalItems, activeItem = _this$state11.activeItem;\n                    if (formattedGraphicalItems && formattedGraphicalItems.length) {\n                        for(var i = 0, len = formattedGraphicalItems.length; i < len; i++){\n                            var graphicalItem = formattedGraphicalItems[i];\n                            // graphicalItem is not a React Element so we don't need to resolve defaultProps\n                            var props = graphicalItem.props, item = graphicalItem.item;\n                            var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n                            var itemDisplayName = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.getDisplayName)(item.type);\n                            if (itemDisplayName === 'Bar') {\n                                var activeBarItem = (props.data || []).find(function(entry) {\n                                    return (0,_shape_Rectangle__WEBPACK_IMPORTED_MODULE_24__.isInRectangle)(chartXY, entry);\n                                });\n                                if (activeBarItem) {\n                                    return {\n                                        graphicalItem: graphicalItem,\n                                        payload: activeBarItem\n                                    };\n                                }\n                            } else if (itemDisplayName === 'RadialBar') {\n                                var _activeBarItem = (props.data || []).find(function(entry) {\n                                    return (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_9__.inRangeOfSector)(chartXY, entry);\n                                });\n                                if (_activeBarItem) {\n                                    return {\n                                        graphicalItem: graphicalItem,\n                                        payload: _activeBarItem\n                                    };\n                                }\n                            } else if ((0,_util_ActiveShapeUtils__WEBPACK_IMPORTED_MODULE_25__.isFunnel)(graphicalItem, activeItem) || (0,_util_ActiveShapeUtils__WEBPACK_IMPORTED_MODULE_25__.isPie)(graphicalItem, activeItem) || (0,_util_ActiveShapeUtils__WEBPACK_IMPORTED_MODULE_25__.isScatter)(graphicalItem, activeItem)) {\n                                var activeIndex = (0,_util_ActiveShapeUtils__WEBPACK_IMPORTED_MODULE_25__.getActiveShapeIndexForTooltip)({\n                                    graphicalItem: graphicalItem,\n                                    activeTooltipItem: activeItem,\n                                    itemData: itemProps.data\n                                });\n                                var childIndex = itemProps.activeIndex === undefined ? activeIndex : itemProps.activeIndex;\n                                return {\n                                    graphicalItem: _objectSpread(_objectSpread({}, graphicalItem), {}, {\n                                        childIndex: childIndex\n                                    }),\n                                    payload: (0,_util_ActiveShapeUtils__WEBPACK_IMPORTED_MODULE_25__.isScatter)(graphicalItem, activeItem) ? itemProps.data[activeIndex] : graphicalItem.props.data[activeIndex]\n                                };\n                            }\n                        }\n                    }\n                    return null;\n                }\n            },\n            {\n                key: \"render\",\n                value: function render() {\n                    var _this3 = this;\n                    if (!(0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.validateWidthHeight)(this)) {\n                        return null;\n                    }\n                    var _this$props6 = this.props, children = _this$props6.children, className = _this$props6.className, width = _this$props6.width, height = _this$props6.height, style = _this$props6.style, compact = _this$props6.compact, title = _this$props6.title, desc = _this$props6.desc, others = _objectWithoutProperties(_this$props6, _excluded2);\n                    var attrs = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.filterProps)(others, false);\n                    // The \"compact\" mode is mainly used as the panorama within Brush\n                    if (compact) {\n                        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_26__.ChartLayoutContextProvider, {\n                            state: this.state,\n                            width: this.props.width,\n                            height: this.props.height,\n                            clipPathId: this.clipPathId\n                        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Surface__WEBPACK_IMPORTED_MODULE_27__.Surface, _extends({}, attrs, {\n                            width: width,\n                            height: height,\n                            title: title,\n                            desc: desc\n                        }), this.renderClipPath(), (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.renderByOrder)(children, this.renderMap)));\n                    }\n                    if (this.props.accessibilityLayer) {\n                        var _this$props$tabIndex, _this$props$role;\n                        // Set tabIndex to 0 by default (can be overwritten)\n                        attrs.tabIndex = (_this$props$tabIndex = this.props.tabIndex) !== null && _this$props$tabIndex !== void 0 ? _this$props$tabIndex : 0;\n                        // Set role to img by default (can be overwritten)\n                        attrs.role = (_this$props$role = this.props.role) !== null && _this$props$role !== void 0 ? _this$props$role : 'application';\n                        attrs.onKeyDown = function(e) {\n                            _this3.accessibilityManager.keyboardEvent(e);\n                        // 'onKeyDown' is not currently a supported prop that can be passed through\n                        // if it's added, this should be added: this.props.onKeyDown(e);\n                        };\n                        attrs.onFocus = function() {\n                            _this3.accessibilityManager.focus();\n                        // 'onFocus' is not currently a supported prop that can be passed through\n                        // if it's added, the focus event should be forwarded to the prop\n                        };\n                    }\n                    var events = this.parseEventsOfWrapper();\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_context_chartLayoutContext__WEBPACK_IMPORTED_MODULE_26__.ChartLayoutContextProvider, {\n                        state: this.state,\n                        width: this.props.width,\n                        height: this.props.height,\n                        clipPathId: this.clipPathId\n                    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", _extends({\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_7__[\"default\"])('recharts-wrapper', className),\n                        style: _objectSpread({\n                            position: 'relative',\n                            cursor: 'default',\n                            width: width,\n                            height: height\n                        }, style)\n                    }, events, {\n                        ref: function ref(node) {\n                            _this3.container = node;\n                        }\n                    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Surface__WEBPACK_IMPORTED_MODULE_27__.Surface, _extends({}, attrs, {\n                        width: width,\n                        height: height,\n                        title: title,\n                        desc: desc,\n                        style: FULL_WIDTH_AND_HEIGHT\n                    }), this.renderClipPath(), (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.renderByOrder)(children, this.renderMap)), this.renderLegend(), this.renderTooltip()));\n                }\n            }\n        ]);\n    }(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n    _defineProperty(CategoricalChartWrapper, \"displayName\", chartName);\n    // todo join specific chart propTypes\n    _defineProperty(CategoricalChartWrapper, \"defaultProps\", _objectSpread({\n        layout: 'horizontal',\n        stackOffset: 'none',\n        barCategoryGap: '10%',\n        barGap: 4,\n        margin: {\n            top: 5,\n            right: 5,\n            bottom: 5,\n            left: 5\n        },\n        reverseStackOrder: false,\n        syncMethod: 'index'\n    }, defaultProps));\n    _defineProperty(CategoricalChartWrapper, \"getDerivedStateFromProps\", function(nextProps, prevState) {\n        var dataKey = nextProps.dataKey, data = nextProps.data, children = nextProps.children, width = nextProps.width, height = nextProps.height, layout = nextProps.layout, stackOffset = nextProps.stackOffset, margin = nextProps.margin;\n        var dataStartIndex = prevState.dataStartIndex, dataEndIndex = prevState.dataEndIndex;\n        if (prevState.updateId === undefined) {\n            var defaultState = createDefaultState(nextProps);\n            return _objectSpread(_objectSpread(_objectSpread({}, defaultState), {}, {\n                updateId: 0\n            }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n                props: nextProps\n            }, defaultState), {}, {\n                updateId: 0\n            }), prevState)), {}, {\n                prevDataKey: dataKey,\n                prevData: data,\n                prevWidth: width,\n                prevHeight: height,\n                prevLayout: layout,\n                prevStackOffset: stackOffset,\n                prevMargin: margin,\n                prevChildren: children\n            });\n        }\n        if (dataKey !== prevState.prevDataKey || data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || layout !== prevState.prevLayout || stackOffset !== prevState.prevStackOffset || !(0,_util_ShallowEqual__WEBPACK_IMPORTED_MODULE_28__.shallowEqual)(margin, prevState.prevMargin)) {\n            var _defaultState = createDefaultState(nextProps);\n            // Fixes https://github.com/recharts/recharts/issues/2143\n            var keepFromPrevState = {\n                // (chartX, chartY) are (0,0) in default state, but we want to keep the last mouse position to avoid\n                // any flickering\n                chartX: prevState.chartX,\n                chartY: prevState.chartY,\n                // The tooltip should stay active when it was active in the previous render. If this is not\n                // the case, the tooltip disappears and immediately re-appears, causing a flickering effect\n                isTooltipActive: prevState.isTooltipActive\n            };\n            var updatesToState = _objectSpread(_objectSpread({}, getTooltipData(prevState, data, layout)), {}, {\n                updateId: prevState.updateId + 1\n            });\n            var newState = _objectSpread(_objectSpread(_objectSpread({}, _defaultState), keepFromPrevState), updatesToState);\n            return _objectSpread(_objectSpread(_objectSpread({}, newState), updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread({\n                props: nextProps\n            }, newState), prevState)), {}, {\n                prevDataKey: dataKey,\n                prevData: data,\n                prevWidth: width,\n                prevHeight: height,\n                prevLayout: layout,\n                prevStackOffset: stackOffset,\n                prevMargin: margin,\n                prevChildren: children\n            });\n        }\n        if (!(0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.isChildrenEqual)(children, prevState.prevChildren)) {\n            var _brush$props$startInd, _brush$props, _brush$props$endIndex, _brush$props2;\n            // specifically check for Brush - if it exists and the start and end indexes are different, re-render with the new ones\n            var brush = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_14__.findChildByType)(children, _cartesian_Brush__WEBPACK_IMPORTED_MODULE_15__.Brush);\n            var startIndex = brush ? (_brush$props$startInd = (_brush$props = brush.props) === null || _brush$props === void 0 ? void 0 : _brush$props.startIndex) !== null && _brush$props$startInd !== void 0 ? _brush$props$startInd : dataStartIndex : dataStartIndex;\n            var endIndex = brush ? (_brush$props$endIndex = (_brush$props2 = brush.props) === null || _brush$props2 === void 0 ? void 0 : _brush$props2.endIndex) !== null && _brush$props$endIndex !== void 0 ? _brush$props$endIndex : dataEndIndex : dataEndIndex;\n            var hasDifferentStartOrEndIndex = startIndex !== dataStartIndex || endIndex !== dataEndIndex;\n            // update configuration in children\n            var hasGlobalData = !lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(data);\n            var newUpdateId = hasGlobalData && !hasDifferentStartOrEndIndex ? prevState.updateId : prevState.updateId + 1;\n            return _objectSpread(_objectSpread({\n                updateId: newUpdateId\n            }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n                props: nextProps\n            }, prevState), {}, {\n                updateId: newUpdateId,\n                dataStartIndex: startIndex,\n                dataEndIndex: endIndex\n            }), prevState)), {}, {\n                prevChildren: children,\n                dataStartIndex: startIndex,\n                dataEndIndex: endIndex\n            });\n        }\n        return null;\n    });\n    _defineProperty(CategoricalChartWrapper, \"renderActiveDot\", function(option, props, key) {\n        var dot;\n        if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(option)) {\n            dot = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(option, props);\n        } else if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(option)) {\n            dot = option(props);\n        } else {\n            dot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shape_Dot__WEBPACK_IMPORTED_MODULE_29__.Dot, props);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_30__.Layer, {\n            className: \"recharts-active-dot\",\n            key: key\n        }, dot);\n    });\n    var CategoricalChart = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function CategoricalChart(props, ref) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(CategoricalChartWrapper, _extends({}, props, {\n            ref: ref\n        }));\n    });\n    CategoricalChart.displayName = CategoricalChartWrapper.displayName;\n    return CategoricalChart;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/chart/generateCategoricalChart.js\n"));

/***/ })

}]);