"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["recharts-node_modules_recharts_es6_com"],{

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js":
/*!*****************************************************!*\
  !*** ./node_modules/recharts/es6/component/Cell.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cell: () => (/* binding */ Cell)\n/* harmony export */ });\n/**\n * @fileOverview Cross\n */ var Cell = function Cell(_props) {\n    return null;\n};\n_c = Cell;\nCell.displayName = 'Cell';\nvar _c;\n$RefreshReg$(_c, \"Cell\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvY29tcG9uZW50L0NlbGwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRU0sSUFBSUEsT0FBTyxTQUFTQSxLQUFLQyxNQUFNO0lBQ3BDLE9BQU87QUFDVCxFQUFFO0tBRlNEO0FBR1hBLEtBQUtFLFdBQVcsR0FBRyIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVjaGFydHNcXGVzNlxcY29tcG9uZW50XFxDZWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGZpbGVPdmVydmlldyBDcm9zc1xuICovXG5cbmV4cG9ydCB2YXIgQ2VsbCA9IGZ1bmN0aW9uIENlbGwoX3Byb3BzKSB7XG4gIHJldHVybiBudWxsO1xufTtcbkNlbGwuZGlzcGxheU5hbWUgPSAnQ2VsbCc7Il0sIm5hbWVzIjpbIkNlbGwiLCJfcHJvcHMiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/Cursor.js":
/*!*******************************************************!*\
  !*** ./node_modules/recharts/es6/component/Cursor.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cursor: () => (/* binding */ Cursor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _shape_Curve__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shape/Curve */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Curve.js\");\n/* harmony import */ var _shape_Cross__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shape/Cross */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Cross.js\");\n/* harmony import */ var _util_cursor_getCursorRectangle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/cursor/getCursorRectangle */ \"(app-pages-browser)/./node_modules/recharts/es6/util/cursor/getCursorRectangle.js\");\n/* harmony import */ var _shape_Rectangle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shape/Rectangle */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Rectangle.js\");\n/* harmony import */ var _util_cursor_getRadialCursorPoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/cursor/getRadialCursorPoints */ \"(app-pages-browser)/./node_modules/recharts/es6/util/cursor/getRadialCursorPoints.js\");\n/* harmony import */ var _shape_Sector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shape/Sector */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Sector.js\");\n/* harmony import */ var _util_cursor_getCursorPoints__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/cursor/getCursorPoints */ \"(app-pages-browser)/./node_modules/recharts/es6/util/cursor/getCursorPoints.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n\n\n\n\n\n\n\n\n\n\n/*\n * Cursor is the background, or a highlight,\n * that shows when user mouses over or activates\n * an area.\n *\n * It usually shows together with a tooltip\n * to emphasise which part of the chart does the tooltip refer to.\n */ function Cursor(props) {\n    var _element$props$cursor, _defaultProps;\n    var element = props.element, tooltipEventType = props.tooltipEventType, isActive = props.isActive, activeCoordinate = props.activeCoordinate, activePayload = props.activePayload, offset = props.offset, activeTooltipIndex = props.activeTooltipIndex, tooltipAxisBandSize = props.tooltipAxisBandSize, layout = props.layout, chartName = props.chartName;\n    var elementPropsCursor = (_element$props$cursor = element.props.cursor) !== null && _element$props$cursor !== void 0 ? _element$props$cursor : (_defaultProps = element.type.defaultProps) === null || _defaultProps === void 0 ? void 0 : _defaultProps.cursor;\n    if (!element || !elementPropsCursor || !isActive || !activeCoordinate || chartName !== 'ScatterChart' && tooltipEventType !== 'axis') {\n        return null;\n    }\n    var restProps;\n    var cursorComp = _shape_Curve__WEBPACK_IMPORTED_MODULE_2__.Curve;\n    if (chartName === 'ScatterChart') {\n        restProps = activeCoordinate;\n        cursorComp = _shape_Cross__WEBPACK_IMPORTED_MODULE_3__.Cross;\n    } else if (chartName === 'BarChart') {\n        restProps = (0,_util_cursor_getCursorRectangle__WEBPACK_IMPORTED_MODULE_4__.getCursorRectangle)(layout, activeCoordinate, offset, tooltipAxisBandSize);\n        cursorComp = _shape_Rectangle__WEBPACK_IMPORTED_MODULE_5__.Rectangle;\n    } else if (layout === 'radial') {\n        var _getRadialCursorPoint = (0,_util_cursor_getRadialCursorPoints__WEBPACK_IMPORTED_MODULE_6__.getRadialCursorPoints)(activeCoordinate), cx = _getRadialCursorPoint.cx, cy = _getRadialCursorPoint.cy, radius = _getRadialCursorPoint.radius, startAngle = _getRadialCursorPoint.startAngle, endAngle = _getRadialCursorPoint.endAngle;\n        restProps = {\n            cx: cx,\n            cy: cy,\n            startAngle: startAngle,\n            endAngle: endAngle,\n            innerRadius: radius,\n            outerRadius: radius\n        };\n        cursorComp = _shape_Sector__WEBPACK_IMPORTED_MODULE_7__.Sector;\n    } else {\n        restProps = {\n            points: (0,_util_cursor_getCursorPoints__WEBPACK_IMPORTED_MODULE_8__.getCursorPoints)(layout, activeCoordinate, offset)\n        };\n        cursorComp = _shape_Curve__WEBPACK_IMPORTED_MODULE_2__.Curve;\n    }\n    var cursorProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n        stroke: '#ccc',\n        pointerEvents: 'none'\n    }, offset), restProps), (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_9__.filterProps)(elementPropsCursor, false)), {}, {\n        payload: activePayload,\n        payloadIndex: activeTooltipIndex,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('recharts-tooltip-cursor', elementPropsCursor.className)\n    });\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(elementPropsCursor) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(elementPropsCursor, cursorProps) : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(cursorComp, cursorProps);\n}\n_c = Cursor;\nvar _c;\n$RefreshReg$(_c, \"Cursor\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/Cursor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/DefaultLegendContent.js":
/*!*********************************************************************!*\
  !*** ./node_modules/recharts/es6/component/DefaultLegendContent.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultLegendContent: () => (/* binding */ DefaultLegendContent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/isFunction */ \"(app-pages-browser)/./node_modules/lodash/isFunction.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_isFunction__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _util_LogUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/LogUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/LogUtils.js\");\n/* harmony import */ var _container_Surface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../container/Surface */ \"(app-pages-browser)/./node_modules/recharts/es6/container/Surface.js\");\n/* harmony import */ var _shape_Symbols__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shape/Symbols */ \"(app-pages-browser)/./node_modules/recharts/es6/shape/Symbols.js\");\n/* harmony import */ var _util_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/types */ \"(app-pages-browser)/./node_modules/recharts/es6/util/types.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n    if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n        return call;\n    } else if (call !== void 0) {\n        throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n            value: subClass,\n            writable: true,\n            configurable: true\n        }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n        writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Default Legend Content\n */ \n\n\n\n\n\n\nvar SIZE = 32;\nvar DefaultLegendContent = /*#__PURE__*/ function(_PureComponent) {\n    function DefaultLegendContent() {\n        _classCallCheck(this, DefaultLegendContent);\n        return _callSuper(this, DefaultLegendContent, arguments);\n    }\n    _inherits(DefaultLegendContent, _PureComponent);\n    return _createClass(DefaultLegendContent, [\n        {\n            key: \"renderIcon\",\n            value: /**\n     * Render the path of icon\n     * @param {Object} data Data of each legend item\n     * @return {String} Path element\n     */ function renderIcon(data) {\n                var inactiveColor = this.props.inactiveColor;\n                var halfSize = SIZE / 2;\n                var sixthSize = SIZE / 6;\n                var thirdSize = SIZE / 3;\n                var color = data.inactive ? inactiveColor : data.color;\n                if (data.type === 'plainline') {\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"line\", {\n                        strokeWidth: 4,\n                        fill: \"none\",\n                        stroke: color,\n                        strokeDasharray: data.payload.strokeDasharray,\n                        x1: 0,\n                        y1: halfSize,\n                        x2: SIZE,\n                        y2: halfSize,\n                        className: \"recharts-legend-icon\"\n                    });\n                }\n                if (data.type === 'line') {\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n                        strokeWidth: 4,\n                        fill: \"none\",\n                        stroke: color,\n                        d: \"M0,\".concat(halfSize, \"h\").concat(thirdSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            H\").concat(SIZE, \"M\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(thirdSize, \",\").concat(halfSize),\n                        className: \"recharts-legend-icon\"\n                    });\n                }\n                if (data.type === 'rect') {\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n                        stroke: \"none\",\n                        fill: color,\n                        d: \"M0,\".concat(SIZE / 8, \"h\").concat(SIZE, \"v\").concat(SIZE * 3 / 4, \"h\").concat(-SIZE, \"z\"),\n                        className: \"recharts-legend-icon\"\n                    });\n                }\n                if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(data.legendIcon)) {\n                    var iconProps = _objectSpread({}, data);\n                    delete iconProps.legendIcon;\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(data.legendIcon, iconProps);\n                }\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shape_Symbols__WEBPACK_IMPORTED_MODULE_3__.Symbols, {\n                    fill: color,\n                    cx: halfSize,\n                    cy: halfSize,\n                    size: SIZE,\n                    sizeType: \"diameter\",\n                    type: data.type\n                });\n            }\n        },\n        {\n            key: \"renderItems\",\n            value: function renderItems() {\n                var _this = this;\n                var _this$props = this.props, payload = _this$props.payload, iconSize = _this$props.iconSize, layout = _this$props.layout, formatter = _this$props.formatter, inactiveColor = _this$props.inactiveColor;\n                var viewBox = {\n                    x: 0,\n                    y: 0,\n                    width: SIZE,\n                    height: SIZE\n                };\n                var itemStyle = {\n                    display: layout === 'horizontal' ? 'inline-block' : 'block',\n                    marginRight: 10\n                };\n                var svgStyle = {\n                    display: 'inline-block',\n                    verticalAlign: 'middle',\n                    marginRight: 4\n                };\n                return payload.map(function(entry, i) {\n                    var finalFormatter = entry.formatter || formatter;\n                    var className = (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_defineProperty(_defineProperty({\n                        'recharts-legend-item': true\n                    }, \"legend-item-\".concat(i), true), \"inactive\", entry.inactive));\n                    if (entry.type === 'none') {\n                        return null;\n                    }\n                    // Do not render entry.value as functions. Always require static string properties.\n                    var entryValue = !lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default()(entry.value) ? entry.value : null;\n                    (0,_util_LogUtils__WEBPACK_IMPORTED_MODULE_4__.warn)(!lodash_isFunction__WEBPACK_IMPORTED_MODULE_1___default()(entry.value), \"The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name=\\\"Name of my Data\\\"/>\" // eslint-disable-line max-len\n                    );\n                    var color = entry.inactive ? inactiveColor : entry.color;\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"li\", _extends({\n                        className: className,\n                        style: itemStyle,\n                        key: \"legend-item-\".concat(i)\n                    }, (0,_util_types__WEBPACK_IMPORTED_MODULE_5__.adaptEventsOfChild)(_this.props, entry, i)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Surface__WEBPACK_IMPORTED_MODULE_6__.Surface, {\n                        width: iconSize,\n                        height: iconSize,\n                        viewBox: viewBox,\n                        style: svgStyle\n                    }, _this.renderIcon(entry)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n                        className: \"recharts-legend-item-text\",\n                        style: {\n                            color: color\n                        }\n                    }, finalFormatter ? finalFormatter(entryValue, entry, i) : entryValue));\n                });\n            }\n        },\n        {\n            key: \"render\",\n            value: function render() {\n                var _this$props2 = this.props, payload = _this$props2.payload, layout = _this$props2.layout, align = _this$props2.align;\n                if (!payload || !payload.length) {\n                    return null;\n                }\n                var finalStyle = {\n                    padding: 0,\n                    margin: 0,\n                    textAlign: layout === 'horizontal' ? align : 'left'\n                };\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"ul\", {\n                    className: \"recharts-default-legend\",\n                    style: finalStyle\n                }, this.renderItems());\n            }\n        }\n    ]);\n}(react__WEBPACK_IMPORTED_MODULE_0__.PureComponent);\n_defineProperty(DefaultLegendContent, \"displayName\", 'Legend');\n_defineProperty(DefaultLegendContent, \"defaultProps\", {\n    iconSize: 14,\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'middle',\n    inactiveColor: '#ccc'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/DefaultLegendContent.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/DefaultTooltipContent.js":
/*!**********************************************************************!*\
  !*** ./node_modules/recharts/es6/component/DefaultTooltipContent.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultTooltipContent: () => (/* binding */ DefaultTooltipContent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_sortBy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/sortBy */ \"(app-pages-browser)/./node_modules/lodash/sortBy.js\");\n/* harmony import */ var lodash_sortBy__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_sortBy__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isNil */ \"(app-pages-browser)/./node_modules/lodash/isNil.js\");\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isNil__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n        var e, n, i, u, a = [], f = !0, o = !1;\n        try {\n            if (i = (t = t.call(r)).next, 0 === l) {\n                if (Object(t) !== t) return;\n                f = !1;\n            } else for(; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n        } catch (r) {\n            o = !0, n = r;\n        } finally{\n            try {\n                if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n            } finally{\n                if (o) throw n;\n            }\n        }\n        return a;\n    }\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Default Tooltip Content\n */ \n\n\n\n\nfunction defaultFormatter(value) {\n    return Array.isArray(value) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumOrStr)(value[0]) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumOrStr)(value[1]) ? value.join(' ~ ') : value;\n}\nvar DefaultTooltipContent = function DefaultTooltipContent(props) {\n    var _props$separator = props.separator, separator = _props$separator === void 0 ? ' : ' : _props$separator, _props$contentStyle = props.contentStyle, contentStyle = _props$contentStyle === void 0 ? {} : _props$contentStyle, _props$itemStyle = props.itemStyle, itemStyle = _props$itemStyle === void 0 ? {} : _props$itemStyle, _props$labelStyle = props.labelStyle, labelStyle = _props$labelStyle === void 0 ? {} : _props$labelStyle, payload = props.payload, formatter = props.formatter, itemSorter = props.itemSorter, wrapperClassName = props.wrapperClassName, labelClassName = props.labelClassName, label = props.label, labelFormatter = props.labelFormatter, _props$accessibilityL = props.accessibilityLayer, accessibilityLayer = _props$accessibilityL === void 0 ? false : _props$accessibilityL;\n    var renderContent = function renderContent() {\n        if (payload && payload.length) {\n            var listStyle = {\n                padding: 0,\n                margin: 0\n            };\n            var items = (itemSorter ? lodash_sortBy__WEBPACK_IMPORTED_MODULE_1___default()(payload, itemSorter) : payload).map(function(entry, i) {\n                if (entry.type === 'none') {\n                    return null;\n                }\n                var finalItemStyle = _objectSpread({\n                    display: 'block',\n                    paddingTop: 4,\n                    paddingBottom: 4,\n                    color: entry.color || '#000'\n                }, itemStyle);\n                var finalFormatter = entry.formatter || formatter || defaultFormatter;\n                var value = entry.value, name = entry.name;\n                var finalValue = value;\n                var finalName = name;\n                if (finalFormatter && finalValue != null && finalName != null) {\n                    var formatted = finalFormatter(value, name, entry, i, payload);\n                    if (Array.isArray(formatted)) {\n                        var _formatted = _slicedToArray(formatted, 2);\n                        finalValue = _formatted[0];\n                        finalName = _formatted[1];\n                    } else {\n                        finalValue = formatted;\n                    }\n                }\n                return(/*#__PURE__*/ // eslint-disable-next-line react/no-array-index-key\n                react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"li\", {\n                    className: \"recharts-tooltip-item\",\n                    key: \"tooltip-item-\".concat(i),\n                    style: finalItemStyle\n                }, (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumOrStr)(finalName) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n                    className: \"recharts-tooltip-item-name\"\n                }, finalName) : null, (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumOrStr)(finalName) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n                    className: \"recharts-tooltip-item-separator\"\n                }, separator) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n                    className: \"recharts-tooltip-item-value\"\n                }, finalValue), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n                    className: \"recharts-tooltip-item-unit\"\n                }, entry.unit || '')));\n            });\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"ul\", {\n                className: \"recharts-tooltip-item-list\",\n                style: listStyle\n            }, items);\n        }\n        return null;\n    };\n    var finalStyle = _objectSpread({\n        margin: 0,\n        padding: 10,\n        backgroundColor: '#fff',\n        border: '1px solid #ccc',\n        whiteSpace: 'nowrap'\n    }, contentStyle);\n    var finalLabelStyle = _objectSpread({\n        margin: 0\n    }, labelStyle);\n    var hasLabel = !lodash_isNil__WEBPACK_IMPORTED_MODULE_2___default()(label);\n    var finalLabel = hasLabel ? label : '';\n    var wrapperCN = (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])('recharts-default-tooltip', wrapperClassName);\n    var labelCN = (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])('recharts-tooltip-label', labelClassName);\n    if (hasLabel && labelFormatter && payload !== undefined && payload !== null) {\n        finalLabel = labelFormatter(label, payload);\n    }\n    var accessibilityAttributes = accessibilityLayer ? {\n        role: 'status',\n        'aria-live': 'assertive'\n    } : {};\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", _extends({\n        className: wrapperCN,\n        style: finalStyle\n    }, accessibilityAttributes), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n        className: labelCN,\n        style: finalLabelStyle\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(finalLabel) ? finalLabel : \"\".concat(finalLabel)), renderContent());\n};\n_c = DefaultTooltipContent;\nvar _c;\n$RefreshReg$(_c, \"DefaultTooltipContent\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/DefaultTooltipContent.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/Label.js":
/*!******************************************************!*\
  !*** ./node_modules/recharts/es6/component/Label.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/isNil */ \"(app-pages-browser)/./node_modules/lodash/isNil.js\");\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_isNil__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isFunction */ \"(app-pages-browser)/./node_modules/lodash/isFunction.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isFunction__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isObject__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isObject */ \"(app-pages-browser)/./node_modules/lodash/isObject.js\");\n/* harmony import */ var lodash_isObject__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isObject__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _Text__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Text */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Text.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_PolarUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/PolarUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/PolarUtils.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nvar _excluded = [\n    \"offset\"\n];\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\n\n\n\n\n\n\n\n\nvar getLabel = function getLabel(props) {\n    var value = props.value, formatter = props.formatter;\n    var label = lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(props.children) ? value : props.children;\n    if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(formatter)) {\n        return formatter(label);\n    }\n    return label;\n};\nvar getDeltaAngle = function getDeltaAngle(startAngle, endAngle) {\n    var sign = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.mathSign)(endAngle - startAngle);\n    var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n    return sign * deltaAngle;\n};\nvar renderRadialLabel = function renderRadialLabel(labelProps, label, attrs) {\n    var position = labelProps.position, viewBox = labelProps.viewBox, offset = labelProps.offset, className = labelProps.className;\n    var _ref = viewBox, cx = _ref.cx, cy = _ref.cy, innerRadius = _ref.innerRadius, outerRadius = _ref.outerRadius, startAngle = _ref.startAngle, endAngle = _ref.endAngle, clockWise = _ref.clockWise;\n    var radius = (innerRadius + outerRadius) / 2;\n    var deltaAngle = getDeltaAngle(startAngle, endAngle);\n    var sign = deltaAngle >= 0 ? 1 : -1;\n    var labelAngle, direction;\n    if (position === 'insideStart') {\n        labelAngle = startAngle + sign * offset;\n        direction = clockWise;\n    } else if (position === 'insideEnd') {\n        labelAngle = endAngle - sign * offset;\n        direction = !clockWise;\n    } else if (position === 'end') {\n        labelAngle = endAngle + sign * offset;\n        direction = clockWise;\n    }\n    direction = deltaAngle <= 0 ? direction : !direction;\n    var startPoint = (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_6__.polarToCartesian)(cx, cy, radius, labelAngle);\n    var endPoint = (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_6__.polarToCartesian)(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n    var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n    var id = lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(labelProps.id) ? (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.uniqueId)('recharts-radial-line-') : labelProps.id;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"text\", _extends({}, attrs, {\n        dominantBaseline: \"central\",\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__[\"default\"])('recharts-radial-bar-label', className)\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n        id: id,\n        d: path\n    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"textPath\", {\n        xlinkHref: \"#\".concat(id)\n    }, label));\n};\nvar getAttrsOfPolarLabel = function getAttrsOfPolarLabel(props) {\n    var viewBox = props.viewBox, offset = props.offset, position = props.position;\n    var _ref2 = viewBox, cx = _ref2.cx, cy = _ref2.cy, innerRadius = _ref2.innerRadius, outerRadius = _ref2.outerRadius, startAngle = _ref2.startAngle, endAngle = _ref2.endAngle;\n    var midAngle = (startAngle + endAngle) / 2;\n    if (position === 'outside') {\n        var _polarToCartesian = (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_6__.polarToCartesian)(cx, cy, outerRadius + offset, midAngle), _x = _polarToCartesian.x, _y = _polarToCartesian.y;\n        return {\n            x: _x,\n            y: _y,\n            textAnchor: _x >= cx ? 'start' : 'end',\n            verticalAnchor: 'middle'\n        };\n    }\n    if (position === 'center') {\n        return {\n            x: cx,\n            y: cy,\n            textAnchor: 'middle',\n            verticalAnchor: 'middle'\n        };\n    }\n    if (position === 'centerTop') {\n        return {\n            x: cx,\n            y: cy,\n            textAnchor: 'middle',\n            verticalAnchor: 'start'\n        };\n    }\n    if (position === 'centerBottom') {\n        return {\n            x: cx,\n            y: cy,\n            textAnchor: 'middle',\n            verticalAnchor: 'end'\n        };\n    }\n    var r = (innerRadius + outerRadius) / 2;\n    var _polarToCartesian2 = (0,_util_PolarUtils__WEBPACK_IMPORTED_MODULE_6__.polarToCartesian)(cx, cy, r, midAngle), x = _polarToCartesian2.x, y = _polarToCartesian2.y;\n    return {\n        x: x,\n        y: y,\n        textAnchor: 'middle',\n        verticalAnchor: 'middle'\n    };\n};\nvar getAttrsOfCartesianLabel = function getAttrsOfCartesianLabel(props) {\n    var viewBox = props.viewBox, parentViewBox = props.parentViewBox, offset = props.offset, position = props.position;\n    var _ref3 = viewBox, x = _ref3.x, y = _ref3.y, width = _ref3.width, height = _ref3.height;\n    // Define vertical offsets and position inverts based on the value being positive or negative\n    var verticalSign = height >= 0 ? 1 : -1;\n    var verticalOffset = verticalSign * offset;\n    var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n    var verticalStart = verticalSign > 0 ? 'start' : 'end';\n    // Define horizontal offsets and position inverts based on the value being positive or negative\n    var horizontalSign = width >= 0 ? 1 : -1;\n    var horizontalOffset = horizontalSign * offset;\n    var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n    var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n    if (position === 'top') {\n        var attrs = {\n            x: x + width / 2,\n            y: y - verticalSign * offset,\n            textAnchor: 'middle',\n            verticalAnchor: verticalEnd\n        };\n        return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n            height: Math.max(y - parentViewBox.y, 0),\n            width: width\n        } : {});\n    }\n    if (position === 'bottom') {\n        var _attrs = {\n            x: x + width / 2,\n            y: y + height + verticalOffset,\n            textAnchor: 'middle',\n            verticalAnchor: verticalStart\n        };\n        return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n            height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n            width: width\n        } : {});\n    }\n    if (position === 'left') {\n        var _attrs2 = {\n            x: x - horizontalOffset,\n            y: y + height / 2,\n            textAnchor: horizontalEnd,\n            verticalAnchor: 'middle'\n        };\n        return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n            width: Math.max(_attrs2.x - parentViewBox.x, 0),\n            height: height\n        } : {});\n    }\n    if (position === 'right') {\n        var _attrs3 = {\n            x: x + width + horizontalOffset,\n            y: y + height / 2,\n            textAnchor: horizontalStart,\n            verticalAnchor: 'middle'\n        };\n        return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n            width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n            height: height\n        } : {});\n    }\n    var sizeAttrs = parentViewBox ? {\n        width: width,\n        height: height\n    } : {};\n    if (position === 'insideLeft') {\n        return _objectSpread({\n            x: x + horizontalOffset,\n            y: y + height / 2,\n            textAnchor: horizontalStart,\n            verticalAnchor: 'middle'\n        }, sizeAttrs);\n    }\n    if (position === 'insideRight') {\n        return _objectSpread({\n            x: x + width - horizontalOffset,\n            y: y + height / 2,\n            textAnchor: horizontalEnd,\n            verticalAnchor: 'middle'\n        }, sizeAttrs);\n    }\n    if (position === 'insideTop') {\n        return _objectSpread({\n            x: x + width / 2,\n            y: y + verticalOffset,\n            textAnchor: 'middle',\n            verticalAnchor: verticalStart\n        }, sizeAttrs);\n    }\n    if (position === 'insideBottom') {\n        return _objectSpread({\n            x: x + width / 2,\n            y: y + height - verticalOffset,\n            textAnchor: 'middle',\n            verticalAnchor: verticalEnd\n        }, sizeAttrs);\n    }\n    if (position === 'insideTopLeft') {\n        return _objectSpread({\n            x: x + horizontalOffset,\n            y: y + verticalOffset,\n            textAnchor: horizontalStart,\n            verticalAnchor: verticalStart\n        }, sizeAttrs);\n    }\n    if (position === 'insideTopRight') {\n        return _objectSpread({\n            x: x + width - horizontalOffset,\n            y: y + verticalOffset,\n            textAnchor: horizontalEnd,\n            verticalAnchor: verticalStart\n        }, sizeAttrs);\n    }\n    if (position === 'insideBottomLeft') {\n        return _objectSpread({\n            x: x + horizontalOffset,\n            y: y + height - verticalOffset,\n            textAnchor: horizontalStart,\n            verticalAnchor: verticalEnd\n        }, sizeAttrs);\n    }\n    if (position === 'insideBottomRight') {\n        return _objectSpread({\n            x: x + width - horizontalOffset,\n            y: y + height - verticalOffset,\n            textAnchor: horizontalEnd,\n            verticalAnchor: verticalEnd\n        }, sizeAttrs);\n    }\n    if (lodash_isObject__WEBPACK_IMPORTED_MODULE_3___default()(position) && ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(position.x) || (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isPercent)(position.x)) && ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(position.y) || (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isPercent)(position.y))) {\n        return _objectSpread({\n            x: x + (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.getPercentValue)(position.x, width),\n            y: y + (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.getPercentValue)(position.y, height),\n            textAnchor: 'end',\n            verticalAnchor: 'end'\n        }, sizeAttrs);\n    }\n    return _objectSpread({\n        x: x + width / 2,\n        y: y + height / 2,\n        textAnchor: 'middle',\n        verticalAnchor: 'middle'\n    }, sizeAttrs);\n};\nvar isPolar = function isPolar(viewBox) {\n    return 'cx' in viewBox && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(viewBox.cx);\n};\nfunction Label(_ref4) {\n    var _ref4$offset = _ref4.offset, offset = _ref4$offset === void 0 ? 5 : _ref4$offset, restProps = _objectWithoutProperties(_ref4, _excluded);\n    var props = _objectSpread({\n        offset: offset\n    }, restProps);\n    var viewBox = props.viewBox, position = props.position, value = props.value, children = props.children, content = props.content, _props$className = props.className, className = _props$className === void 0 ? '' : _props$className, textBreakAll = props.textBreakAll;\n    if (!viewBox || lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(value) && lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(children) && !/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(content) && !lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(content)) {\n        return null;\n    }\n    if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(content)) {\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(content, props);\n    }\n    var label;\n    if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(content)) {\n        label = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(content, props);\n        if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(label)) {\n            return label;\n        }\n    } else {\n        label = getLabel(props);\n    }\n    var isPolarLabel = isPolar(viewBox);\n    var attrs = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_7__.filterProps)(props, true);\n    if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n        return renderRadialLabel(props, label, attrs);\n    }\n    var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_Text__WEBPACK_IMPORTED_MODULE_8__.Text, _extends({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_4__[\"default\"])('recharts-label', className)\n    }, attrs, positionAttrs, {\n        breakAll: textBreakAll\n    }), label);\n}\n_c = Label;\nLabel.displayName = 'Label';\nvar parseViewBox = function parseViewBox(props) {\n    var cx = props.cx, cy = props.cy, angle = props.angle, startAngle = props.startAngle, endAngle = props.endAngle, r = props.r, radius = props.radius, innerRadius = props.innerRadius, outerRadius = props.outerRadius, x = props.x, y = props.y, top = props.top, left = props.left, width = props.width, height = props.height, clockWise = props.clockWise, labelViewBox = props.labelViewBox;\n    if (labelViewBox) {\n        return labelViewBox;\n    }\n    if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(width) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(height)) {\n        if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(x) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(y)) {\n            return {\n                x: x,\n                y: y,\n                width: width,\n                height: height\n            };\n        }\n        if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(top) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(left)) {\n            return {\n                x: top,\n                y: left,\n                width: width,\n                height: height\n            };\n        }\n    }\n    if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(x) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(y)) {\n        return {\n            x: x,\n            y: y,\n            width: 0,\n            height: 0\n        };\n    }\n    if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(cx) && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumber)(cy)) {\n        return {\n            cx: cx,\n            cy: cy,\n            startAngle: startAngle || angle || 0,\n            endAngle: endAngle || angle || 0,\n            innerRadius: innerRadius || 0,\n            outerRadius: outerRadius || radius || r || 0,\n            clockWise: clockWise\n        };\n    }\n    if (props.viewBox) {\n        return props.viewBox;\n    }\n    return {};\n};\nvar parseLabel = function parseLabel(label, viewBox) {\n    if (!label) {\n        return null;\n    }\n    if (label === true) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Label, {\n            key: \"label-implicit\",\n            viewBox: viewBox\n        });\n    }\n    if ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.isNumOrStr)(label)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Label, {\n            key: \"label-implicit\",\n            viewBox: viewBox,\n            value: label\n        });\n    }\n    if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(label)) {\n        if (label.type === Label) {\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(label, {\n                key: 'label-implicit',\n                viewBox: viewBox\n            });\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Label, {\n            key: \"label-implicit\",\n            content: label,\n            viewBox: viewBox\n        });\n    }\n    if (lodash_isFunction__WEBPACK_IMPORTED_MODULE_2___default()(label)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Label, {\n            key: \"label-implicit\",\n            content: label,\n            viewBox: viewBox\n        });\n    }\n    if (lodash_isObject__WEBPACK_IMPORTED_MODULE_3___default()(label)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Label, _extends({\n            viewBox: viewBox\n        }, label, {\n            key: \"label-implicit\"\n        }));\n    }\n    return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n    var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n        return null;\n    }\n    var children = parentProps.children;\n    var parentViewBox = parseViewBox(parentProps);\n    var explicitChildren = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_7__.findAllByType)(children, Label).map(function(child, index) {\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n            viewBox: viewBox || parentViewBox,\n            // eslint-disable-next-line react/no-array-index-key\n            key: \"label-\".concat(index)\n        });\n    });\n    if (!checkPropsLabel) {\n        return explicitChildren;\n    }\n    var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox);\n    return [\n        implicitLabel\n    ].concat(_toConsumableArray(explicitChildren));\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;\nvar _c;\n$RefreshReg$(_c, \"Label\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/Label.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/LabelList.js":
/*!**********************************************************!*\
  !*** ./node_modules/recharts/es6/component/LabelList.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LabelList: () => (/* binding */ LabelList)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/isNil */ \"(app-pages-browser)/./node_modules/lodash/isNil.js\");\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_isNil__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isObject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isObject */ \"(app-pages-browser)/./node_modules/lodash/isObject.js\");\n/* harmony import */ var lodash_isObject__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isObject__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isFunction */ \"(app-pages-browser)/./node_modules/lodash/isFunction.js\");\n/* harmony import */ var lodash_isFunction__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isFunction__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_last__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/last */ \"(app-pages-browser)/./node_modules/lodash/last.js\");\n/* harmony import */ var lodash_last__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_last__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Label */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Label.js\");\n/* harmony import */ var _container_Layer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../container/Layer */ \"(app-pages-browser)/./node_modules/recharts/es6/container/Layer.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\n/* harmony import */ var _util_ChartUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/ChartUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ChartUtils.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nvar _excluded = [\n    \"valueAccessor\"\n], _excluded2 = [\n    \"data\",\n    \"dataKey\",\n    \"clockWise\",\n    \"id\",\n    \"textBreakAll\"\n];\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n\n\n\n\n\n\n\n\n\nvar defaultAccessor = function defaultAccessor(entry) {\n    return Array.isArray(entry.value) ? lodash_last__WEBPACK_IMPORTED_MODULE_4___default()(entry.value) : entry.value;\n};\nfunction LabelList(_ref) {\n    var _ref$valueAccessor = _ref.valueAccessor, valueAccessor = _ref$valueAccessor === void 0 ? defaultAccessor : _ref$valueAccessor, restProps = _objectWithoutProperties(_ref, _excluded);\n    var data = restProps.data, dataKey = restProps.dataKey, clockWise = restProps.clockWise, id = restProps.id, textBreakAll = restProps.textBreakAll, others = _objectWithoutProperties(restProps, _excluded2);\n    if (!data || !data.length) {\n        return null;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_container_Layer__WEBPACK_IMPORTED_MODULE_5__.Layer, {\n        className: \"recharts-label-list\"\n    }, data.map(function(entry, index) {\n        var value = lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(dataKey) ? valueAccessor(entry, index) : (0,_util_ChartUtils__WEBPACK_IMPORTED_MODULE_6__.getValueByDataKey)(entry && entry.payload, dataKey);\n        var idProps = lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(id) ? {} : {\n            id: \"\".concat(id, \"-\").concat(index)\n        };\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_Label__WEBPACK_IMPORTED_MODULE_7__.Label, _extends({}, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.filterProps)(entry, true), others, idProps, {\n            parentViewBox: entry.parentViewBox,\n            value: value,\n            textBreakAll: textBreakAll,\n            viewBox: _Label__WEBPACK_IMPORTED_MODULE_7__.Label.parseViewBox(lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(clockWise) ? entry : _objectSpread(_objectSpread({}, entry), {}, {\n                clockWise: clockWise\n            })),\n            key: \"label-\".concat(index) // eslint-disable-line react/no-array-index-key\n            ,\n            index: index\n        }));\n    }));\n}\n_c = LabelList;\nLabelList.displayName = 'LabelList';\nfunction parseLabelList(label, data) {\n    if (!label) {\n        return null;\n    }\n    if (label === true) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(LabelList, {\n            key: \"labelList-implicit\",\n            data: data\n        });\n    }\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(label) || lodash_isFunction__WEBPACK_IMPORTED_MODULE_3___default()(label)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(LabelList, {\n            key: \"labelList-implicit\",\n            data: data,\n            content: label\n        });\n    }\n    if (lodash_isObject__WEBPACK_IMPORTED_MODULE_2___default()(label)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(LabelList, _extends({\n            data: data\n        }, label, {\n            key: \"labelList-implicit\"\n        }));\n    }\n    return null;\n}\nfunction renderCallByParent(parentProps, data) {\n    var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n        return null;\n    }\n    var children = parentProps.children;\n    var explicitChildren = (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_8__.findAllByType)(children, LabelList).map(function(child, index) {\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n            data: data,\n            // eslint-disable-next-line react/no-array-index-key\n            key: \"labelList-\".concat(index)\n        });\n    });\n    if (!checkPropsLabel) {\n        return explicitChildren;\n    }\n    var implicitLabelList = parseLabelList(parentProps.label, data);\n    return [\n        implicitLabelList\n    ].concat(_toConsumableArray(explicitChildren));\n}\nLabelList.renderCallByParent = renderCallByParent;\nvar _c;\n$RefreshReg$(_c, \"LabelList\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/LabelList.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js":
/*!*******************************************************!*\
  !*** ./node_modules/recharts/es6/component/Legend.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Legend: () => (/* binding */ Legend)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _DefaultLegendContent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DefaultLegendContent */ \"(app-pages-browser)/./node_modules/recharts/es6/component/DefaultLegendContent.js\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_payload_getUniqPayload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/payload/getUniqPayload */ \"(app-pages-browser)/./node_modules/recharts/es6/util/payload/getUniqPayload.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nvar _excluded = [\n    \"ref\"\n];\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n    if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n        return call;\n    } else if (call !== void 0) {\n        throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n            value: subClass,\n            writable: true,\n            configurable: true\n        }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n        writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n/**\n * @fileOverview Legend\n */ \n\n\n\nfunction defaultUniqBy(entry) {\n    return entry.value;\n}\nfunction renderContent(content, props) {\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(content)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(content, props);\n    }\n    if (typeof content === 'function') {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(content, props);\n    }\n    var ref = props.ref, otherProps = _objectWithoutProperties(props, _excluded);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_DefaultLegendContent__WEBPACK_IMPORTED_MODULE_1__.DefaultLegendContent, otherProps);\n}\nvar EPS = 1;\nvar Legend = /*#__PURE__*/ function(_PureComponent) {\n    function Legend() {\n        var _this;\n        _classCallCheck(this, Legend);\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        _this = _callSuper(this, Legend, [].concat(args));\n        _defineProperty(_this, \"lastBoundingBox\", {\n            width: -1,\n            height: -1\n        });\n        return _this;\n    }\n    _inherits(Legend, _PureComponent);\n    return _createClass(Legend, [\n        {\n            key: \"componentDidMount\",\n            value: function componentDidMount() {\n                this.updateBBox();\n            }\n        },\n        {\n            key: \"componentDidUpdate\",\n            value: function componentDidUpdate() {\n                this.updateBBox();\n            }\n        },\n        {\n            key: \"getBBox\",\n            value: function getBBox() {\n                if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n                    var box = this.wrapperNode.getBoundingClientRect();\n                    box.height = this.wrapperNode.offsetHeight;\n                    box.width = this.wrapperNode.offsetWidth;\n                    return box;\n                }\n                return null;\n            }\n        },\n        {\n            key: \"updateBBox\",\n            value: function updateBBox() {\n                var onBBoxUpdate = this.props.onBBoxUpdate;\n                var box = this.getBBox();\n                if (box) {\n                    if (Math.abs(box.width - this.lastBoundingBox.width) > EPS || Math.abs(box.height - this.lastBoundingBox.height) > EPS) {\n                        this.lastBoundingBox.width = box.width;\n                        this.lastBoundingBox.height = box.height;\n                        if (onBBoxUpdate) {\n                            onBBoxUpdate(box);\n                        }\n                    }\n                } else if (this.lastBoundingBox.width !== -1 || this.lastBoundingBox.height !== -1) {\n                    this.lastBoundingBox.width = -1;\n                    this.lastBoundingBox.height = -1;\n                    if (onBBoxUpdate) {\n                        onBBoxUpdate(null);\n                    }\n                }\n            }\n        },\n        {\n            key: \"getBBoxSnapshot\",\n            value: function getBBoxSnapshot() {\n                if (this.lastBoundingBox.width >= 0 && this.lastBoundingBox.height >= 0) {\n                    return _objectSpread({}, this.lastBoundingBox);\n                }\n                return {\n                    width: 0,\n                    height: 0\n                };\n            }\n        },\n        {\n            key: \"getDefaultPosition\",\n            value: function getDefaultPosition(style) {\n                var _this$props = this.props, layout = _this$props.layout, align = _this$props.align, verticalAlign = _this$props.verticalAlign, margin = _this$props.margin, chartWidth = _this$props.chartWidth, chartHeight = _this$props.chartHeight;\n                var hPos, vPos;\n                if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n                    if (align === 'center' && layout === 'vertical') {\n                        var box = this.getBBoxSnapshot();\n                        hPos = {\n                            left: ((chartWidth || 0) - box.width) / 2\n                        };\n                    } else {\n                        hPos = align === 'right' ? {\n                            right: margin && margin.right || 0\n                        } : {\n                            left: margin && margin.left || 0\n                        };\n                    }\n                }\n                if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n                    if (verticalAlign === 'middle') {\n                        var _box = this.getBBoxSnapshot();\n                        vPos = {\n                            top: ((chartHeight || 0) - _box.height) / 2\n                        };\n                    } else {\n                        vPos = verticalAlign === 'bottom' ? {\n                            bottom: margin && margin.bottom || 0\n                        } : {\n                            top: margin && margin.top || 0\n                        };\n                    }\n                }\n                return _objectSpread(_objectSpread({}, hPos), vPos);\n            }\n        },\n        {\n            key: \"render\",\n            value: function render() {\n                var _this2 = this;\n                var _this$props2 = this.props, content = _this$props2.content, width = _this$props2.width, height = _this$props2.height, wrapperStyle = _this$props2.wrapperStyle, payloadUniqBy = _this$props2.payloadUniqBy, payload = _this$props2.payload;\n                var outerStyle = _objectSpread(_objectSpread({\n                    position: 'absolute',\n                    width: width || 'auto',\n                    height: height || 'auto'\n                }, this.getDefaultPosition(wrapperStyle)), wrapperStyle);\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n                    className: \"recharts-legend-wrapper\",\n                    style: outerStyle,\n                    ref: function ref(node) {\n                        _this2.wrapperNode = node;\n                    }\n                }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n                    payload: (0,_util_payload_getUniqPayload__WEBPACK_IMPORTED_MODULE_2__.getUniqPayload)(payload, payloadUniqBy, defaultUniqBy)\n                })));\n            }\n        }\n    ], [\n        {\n            key: \"getWithHeight\",\n            value: function getWithHeight(item, chartWidth) {\n                var _this$defaultProps$it = _objectSpread(_objectSpread({}, this.defaultProps), item.props), layout = _this$defaultProps$it.layout;\n                if (layout === 'vertical' && (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_3__.isNumber)(item.props.height)) {\n                    return {\n                        height: item.props.height\n                    };\n                }\n                if (layout === 'horizontal') {\n                    return {\n                        width: item.props.width || chartWidth\n                    };\n                }\n                return null;\n            }\n        }\n    ]);\n}(react__WEBPACK_IMPORTED_MODULE_0__.PureComponent);\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n    iconSize: 14,\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'bottom'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js":
/*!********************************************************************!*\
  !*** ./node_modules/recharts/es6/component/ResponsiveContainer.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponsiveContainer: () => (/* binding */ ResponsiveContainer)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/throttle */ \"(app-pages-browser)/./node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_LogUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/LogUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/LogUtils.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\nvar _s = $RefreshSig$();\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n        var e, n, i, u, a = [], f = !0, o = !1;\n        try {\n            if (i = (t = t.call(r)).next, 0 === l) {\n                if (Object(t) !== t) return;\n                f = !1;\n            } else for(; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n        } catch (r) {\n            o = !0, n = r;\n        } finally{\n            try {\n                if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n            } finally{\n                if (o) throw n;\n            }\n        }\n        return a;\n    }\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n/**\n * @fileOverview Wrapper component to make charts adapt to the size of parent * DOM\n */ \n\n\n\n\n\nvar ResponsiveContainer = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s(function(_ref, ref) {\n    _s();\n    var aspect = _ref.aspect, _ref$initialDimension = _ref.initialDimension, initialDimension = _ref$initialDimension === void 0 ? {\n        width: -1,\n        height: -1\n    } : _ref$initialDimension, _ref$width = _ref.width, width = _ref$width === void 0 ? '100%' : _ref$width, _ref$height = _ref.height, height = _ref$height === void 0 ? '100%' : _ref$height, _ref$minWidth = _ref.minWidth, minWidth = _ref$minWidth === void 0 ? 0 : _ref$minWidth, minHeight = _ref.minHeight, maxHeight = _ref.maxHeight, children = _ref.children, _ref$debounce = _ref.debounce, debounce = _ref$debounce === void 0 ? 0 : _ref$debounce, id = _ref.id, className = _ref.className, onResize = _ref.onResize, _ref$style = _ref.style, style = _ref$style === void 0 ? {} : _ref$style;\n    var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var onResizeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    onResizeRef.current = onResize;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"ResponsiveContainer.useImperativeHandle\": function() {\n            return Object.defineProperty(containerRef.current, 'current', {\n                get: function get() {\n                    // eslint-disable-next-line no-console\n                    console.warn('The usage of ref.current.current is deprecated and will no longer be supported.');\n                    return containerRef.current;\n                },\n                configurable: true\n            });\n        }\n    }[\"ResponsiveContainer.useImperativeHandle\"]);\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        containerWidth: initialDimension.width,\n        containerHeight: initialDimension.height\n    }), _useState2 = _slicedToArray(_useState, 2), sizes = _useState2[0], setSizes = _useState2[1];\n    var setContainerSize = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ResponsiveContainer.useCallback[setContainerSize]\": function(newWidth, newHeight) {\n            setSizes({\n                \"ResponsiveContainer.useCallback[setContainerSize]\": function(prevState) {\n                    var roundedWidth = Math.round(newWidth);\n                    var roundedHeight = Math.round(newHeight);\n                    if (prevState.containerWidth === roundedWidth && prevState.containerHeight === roundedHeight) {\n                        return prevState;\n                    }\n                    return {\n                        containerWidth: roundedWidth,\n                        containerHeight: roundedHeight\n                    };\n                }\n            }[\"ResponsiveContainer.useCallback[setContainerSize]\"]);\n        }\n    }[\"ResponsiveContainer.useCallback[setContainerSize]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResponsiveContainer.useEffect\": function() {\n            var callback = function callback(entries) {\n                var _onResizeRef$current;\n                var _entries$0$contentRec = entries[0].contentRect, containerWidth = _entries$0$contentRec.width, containerHeight = _entries$0$contentRec.height;\n                setContainerSize(containerWidth, containerHeight);\n                (_onResizeRef$current = onResizeRef.current) === null || _onResizeRef$current === void 0 || _onResizeRef$current.call(onResizeRef, containerWidth, containerHeight);\n            };\n            if (debounce > 0) {\n                callback = lodash_throttle__WEBPACK_IMPORTED_MODULE_2___default()(callback, debounce, {\n                    trailing: true,\n                    leading: false\n                });\n            }\n            var observer = new ResizeObserver(callback);\n            var _containerRef$current = containerRef.current.getBoundingClientRect(), containerWidth = _containerRef$current.width, containerHeight = _containerRef$current.height;\n            setContainerSize(containerWidth, containerHeight);\n            observer.observe(containerRef.current);\n            return ({\n                \"ResponsiveContainer.useEffect\": function() {\n                    observer.disconnect();\n                }\n            })[\"ResponsiveContainer.useEffect\"];\n        }\n    }[\"ResponsiveContainer.useEffect\"], [\n        setContainerSize,\n        debounce\n    ]);\n    var chartContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ResponsiveContainer.useMemo[chartContent]\": function() {\n            var containerWidth = sizes.containerWidth, containerHeight = sizes.containerHeight;\n            if (containerWidth < 0 || containerHeight < 0) {\n                return null;\n            }\n            (0,_util_LogUtils__WEBPACK_IMPORTED_MODULE_3__.warn)((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isPercent)(width) || (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isPercent)(height), \"The width(%s) and height(%s) are both fixed numbers,\\n       maybe you don't need to use a ResponsiveContainer.\", width, height);\n            (0,_util_LogUtils__WEBPACK_IMPORTED_MODULE_3__.warn)(!aspect || aspect > 0, 'The aspect(%s) must be greater than zero.', aspect);\n            var calculatedWidth = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isPercent)(width) ? containerWidth : width;\n            var calculatedHeight = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isPercent)(height) ? containerHeight : height;\n            if (aspect && aspect > 0) {\n                // Preserve the desired aspect ratio\n                if (calculatedWidth) {\n                    // Will default to using width for aspect ratio\n                    calculatedHeight = calculatedWidth / aspect;\n                } else if (calculatedHeight) {\n                    // But we should also take height into consideration\n                    calculatedWidth = calculatedHeight * aspect;\n                }\n                // if maxHeight is set, overwrite if calculatedHeight is greater than maxHeight\n                if (maxHeight && calculatedHeight > maxHeight) {\n                    calculatedHeight = maxHeight;\n                }\n            }\n            (0,_util_LogUtils__WEBPACK_IMPORTED_MODULE_3__.warn)(calculatedWidth > 0 || calculatedHeight > 0, \"The width(%s) and height(%s) of chart should be greater than 0,\\n       please check the style of container, or the props width(%s) and height(%s),\\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\\n       height and width.\", calculatedWidth, calculatedHeight, width, height, minWidth, minHeight, aspect);\n            var isCharts = !Array.isArray(children) && (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_5__.getDisplayName)(children.type).endsWith('Chart');\n            return react__WEBPACK_IMPORTED_MODULE_1___default().Children.map(children, {\n                \"ResponsiveContainer.useMemo[chartContent]\": function(child) {\n                    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(child)) {\n                        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(child, _objectSpread({\n                            width: calculatedWidth,\n                            height: calculatedHeight\n                        }, isCharts ? {\n                            style: _objectSpread({\n                                height: '100%',\n                                width: '100%',\n                                maxHeight: calculatedHeight,\n                                maxWidth: calculatedWidth\n                            }, child.props.style)\n                        } : {}));\n                    }\n                    return child;\n                }\n            }[\"ResponsiveContainer.useMemo[chartContent]\"]);\n        }\n    }[\"ResponsiveContainer.useMemo[chartContent]\"], [\n        aspect,\n        children,\n        height,\n        maxHeight,\n        minHeight,\n        minWidth,\n        sizes,\n        width\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n        id: id ? \"\".concat(id) : undefined,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('recharts-responsive-container', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n            width: width,\n            height: height,\n            minWidth: minWidth,\n            minHeight: minHeight,\n            maxHeight: maxHeight\n        }),\n        ref: containerRef\n    }, chartContent);\n}, \"hmkMSatCc++/CbdG32noCCXOHtg=\")), \"hmkMSatCc++/CbdG32noCCXOHtg=\");\n_c1 = ResponsiveContainer;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResponsiveContainer$forwardRef\");\n$RefreshReg$(_c1, \"ResponsiveContainer\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/Text.js":
/*!*****************************************************!*\
  !*** ./node_modules/recharts/es6/component/Text.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Text: () => (/* binding */ Text)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/isNil */ \"(app-pages-browser)/./node_modules/lodash/isNil.js\");\n/* harmony import */ var lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_isNil__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _util_Global__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/Global */ \"(app-pages-browser)/./node_modules/recharts/es6/util/Global.js\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\n/* harmony import */ var _util_DOMUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/DOMUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DOMUtils.js\");\n/* harmony import */ var _util_ReduceCSSCalc__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/ReduceCSSCalc */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReduceCSSCalc.js\");\nvar _s = $RefreshSig$();\nvar _excluded = [\n    \"x\",\n    \"y\",\n    \"lineHeight\",\n    \"capHeight\",\n    \"scaleToFit\",\n    \"textAnchor\",\n    \"verticalAnchor\",\n    \"fill\"\n], _excluded2 = [\n    \"dx\",\n    \"dy\",\n    \"angle\",\n    \"className\",\n    \"breakAll\"\n];\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n        var e, n, i, u, a = [], f = !0, o = !1;\n        try {\n            if (i = (t = t.call(r)).next, 0 === l) {\n                if (Object(t) !== t) return;\n                f = !1;\n            } else for(; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n        } catch (r) {\n            o = !0, n = r;\n        } finally{\n            try {\n                if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n            } finally{\n                if (o) throw n;\n            }\n        }\n        return a;\n    }\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\n\n\n\n\n\n\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = function calculateWordWidths(_ref) {\n    var children = _ref.children, breakAll = _ref.breakAll, style = _ref.style;\n    try {\n        var words = [];\n        if (!lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(children)) {\n            if (breakAll) {\n                words = children.toString().split('');\n            } else {\n                words = children.toString().split(BREAKING_SPACES);\n            }\n        }\n        var wordsWithComputedWidth = words.map(function(word) {\n            return {\n                word: word,\n                width: (0,_util_DOMUtils__WEBPACK_IMPORTED_MODULE_3__.getStringSize)(word, style).width\n            };\n        });\n        var spaceWidth = breakAll ? 0 : (0,_util_DOMUtils__WEBPACK_IMPORTED_MODULE_3__.getStringSize)(\"\\xA0\", style).width;\n        return {\n            wordsWithComputedWidth: wordsWithComputedWidth,\n            spaceWidth: spaceWidth\n        };\n    } catch (e) {\n        return null;\n    }\n};\nvar calculateWordsByLines = function calculateWordsByLines(_ref2, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) {\n    var maxLines = _ref2.maxLines, children = _ref2.children, style = _ref2.style, breakAll = _ref2.breakAll;\n    var shouldLimitLines = (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumber)(maxLines);\n    var text = children;\n    var calculate = function calculate() {\n        var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n        return words.reduce(function(result, _ref3) {\n            var word = _ref3.word, width = _ref3.width;\n            var currentLine = result[result.length - 1];\n            if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < Number(lineWidth))) {\n                // Word can be added to an existing line\n                currentLine.words.push(word);\n                currentLine.width += width + spaceWidth;\n            } else {\n                // Add first word to line or word is too long to scaleToFit on existing line\n                var newLine = {\n                    words: [\n                        word\n                    ],\n                    width: width\n                };\n                result.push(newLine);\n            }\n            return result;\n        }, []);\n    };\n    var originalResult = calculate(initialWordsWithComputedWith);\n    var findLongestLine = function findLongestLine(words) {\n        return words.reduce(function(a, b) {\n            return a.width > b.width ? a : b;\n        });\n    };\n    if (!shouldLimitLines) {\n        return originalResult;\n    }\n    var suffix = '…';\n    var checkOverflow = function checkOverflow(index) {\n        var tempText = text.slice(0, index);\n        var words = calculateWordWidths({\n            breakAll: breakAll,\n            style: style,\n            children: tempText + suffix\n        }).wordsWithComputedWidth;\n        var result = calculate(words);\n        var doesOverflow = result.length > maxLines || findLongestLine(result).width > Number(lineWidth);\n        return [\n            doesOverflow,\n            result\n        ];\n    };\n    var start = 0;\n    var end = text.length - 1;\n    var iterations = 0;\n    var trimmedResult;\n    while(start <= end && iterations <= text.length - 1){\n        var middle = Math.floor((start + end) / 2);\n        var prev = middle - 1;\n        var _checkOverflow = checkOverflow(prev), _checkOverflow2 = _slicedToArray(_checkOverflow, 2), doesPrevOverflow = _checkOverflow2[0], result = _checkOverflow2[1];\n        var _checkOverflow3 = checkOverflow(middle), _checkOverflow4 = _slicedToArray(_checkOverflow3, 1), doesMiddleOverflow = _checkOverflow4[0];\n        if (!doesPrevOverflow && !doesMiddleOverflow) {\n            start = middle + 1;\n        }\n        if (doesPrevOverflow && doesMiddleOverflow) {\n            end = middle - 1;\n        }\n        if (!doesPrevOverflow && doesMiddleOverflow) {\n            trimmedResult = result;\n            break;\n        }\n        iterations++;\n    }\n    // Fallback to originalResult (result without trimming) if we cannot find the\n    // where to trim.  This should not happen :tm:\n    return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = function getWordsWithoutCalculate(children) {\n    var words = !lodash_isNil__WEBPACK_IMPORTED_MODULE_1___default()(children) ? children.toString().split(BREAKING_SPACES) : [];\n    return [\n        {\n            words: words\n        }\n    ];\n};\nvar getWordsByLines = function getWordsByLines(_ref4) {\n    var width = _ref4.width, scaleToFit = _ref4.scaleToFit, children = _ref4.children, style = _ref4.style, breakAll = _ref4.breakAll, maxLines = _ref4.maxLines;\n    // Only perform calculations if using features that require them (multiline, scaleToFit)\n    if ((width || scaleToFit) && !_util_Global__WEBPACK_IMPORTED_MODULE_5__.Global.isSsr) {\n        var wordsWithComputedWidth, spaceWidth;\n        var wordWidths = calculateWordWidths({\n            breakAll: breakAll,\n            children: children,\n            style: style\n        });\n        if (wordWidths) {\n            var wcw = wordWidths.wordsWithComputedWidth, sw = wordWidths.spaceWidth;\n            wordsWithComputedWidth = wcw;\n            spaceWidth = sw;\n        } else {\n            return getWordsWithoutCalculate(children);\n        }\n        return calculateWordsByLines({\n            breakAll: breakAll,\n            children: children,\n            maxLines: maxLines,\n            style: style\n        }, wordsWithComputedWidth, spaceWidth, width, scaleToFit);\n    }\n    return getWordsWithoutCalculate(children);\n};\nvar DEFAULT_FILL = '#808080';\nvar Text = function Text(_ref5) {\n    _s();\n    var _ref5$x = _ref5.x, propsX = _ref5$x === void 0 ? 0 : _ref5$x, _ref5$y = _ref5.y, propsY = _ref5$y === void 0 ? 0 : _ref5$y, _ref5$lineHeight = _ref5.lineHeight, lineHeight = _ref5$lineHeight === void 0 ? '1em' : _ref5$lineHeight, _ref5$capHeight = _ref5.capHeight, capHeight = _ref5$capHeight === void 0 ? '0.71em' : _ref5$capHeight, _ref5$scaleToFit = _ref5.scaleToFit, scaleToFit = _ref5$scaleToFit === void 0 ? false : _ref5$scaleToFit, _ref5$textAnchor = _ref5.textAnchor, textAnchor = _ref5$textAnchor === void 0 ? 'start' : _ref5$textAnchor, _ref5$verticalAnchor = _ref5.verticalAnchor, verticalAnchor = _ref5$verticalAnchor === void 0 ? 'end' : _ref5$verticalAnchor, _ref5$fill = _ref5.fill, fill = _ref5$fill === void 0 ? DEFAULT_FILL : _ref5$fill, props = _objectWithoutProperties(_ref5, _excluded);\n    var wordsByLines = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"Text.useMemo[wordsByLines]\": function() {\n            return getWordsByLines({\n                breakAll: props.breakAll,\n                children: props.children,\n                maxLines: props.maxLines,\n                scaleToFit: scaleToFit,\n                style: props.style,\n                width: props.width\n            });\n        }\n    }[\"Text.useMemo[wordsByLines]\"], [\n        props.breakAll,\n        props.children,\n        props.maxLines,\n        scaleToFit,\n        props.style,\n        props.width\n    ]);\n    var dx = props.dx, dy = props.dy, angle = props.angle, className = props.className, breakAll = props.breakAll, textProps = _objectWithoutProperties(props, _excluded2);\n    if (!(0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumOrStr)(propsX) || !(0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumOrStr)(propsY)) {\n        return null;\n    }\n    var x = propsX + ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumber)(dx) ? dx : 0);\n    var y = propsY + ((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumber)(dy) ? dy : 0);\n    var startDy;\n    switch(verticalAnchor){\n        case 'start':\n            startDy = (0,_util_ReduceCSSCalc__WEBPACK_IMPORTED_MODULE_6__.reduceCSSCalc)(\"calc(\".concat(capHeight, \")\"));\n            break;\n        case 'middle':\n            startDy = (0,_util_ReduceCSSCalc__WEBPACK_IMPORTED_MODULE_6__.reduceCSSCalc)(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n            break;\n        default:\n            startDy = (0,_util_ReduceCSSCalc__WEBPACK_IMPORTED_MODULE_6__.reduceCSSCalc)(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n            break;\n    }\n    var transforms = [];\n    if (scaleToFit) {\n        var lineWidth = wordsByLines[0].width;\n        var width = props.width;\n        transforms.push(\"scale(\".concat(((0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_4__.isNumber)(width) ? width / lineWidth : 1) / lineWidth, \")\"));\n    }\n    if (angle) {\n        transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n    }\n    if (transforms.length) {\n        textProps.transform = transforms.join(' ');\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"text\", _extends({}, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_7__.filterProps)(textProps, true), {\n        x: x,\n        y: y,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('recharts-text', className),\n        textAnchor: textAnchor,\n        fill: fill.includes('url') ? DEFAULT_FILL : fill\n    }), wordsByLines.map(function(line, index) {\n        var words = line.words.join(breakAll ? '' : ' ');\n        return(/*#__PURE__*/ // duplicate words will cause duplicate keys\n        // eslint-disable-next-line react/no-array-index-key\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"tspan\", {\n            x: x,\n            dy: index === 0 ? startDy : lineHeight,\n            key: \"\".concat(words, \"-\").concat(index)\n        }, words));\n    }));\n};\n_s(Text, \"gGQHLBxGIymP3+d1G2QwuYTCUTw=\");\n_c = Text;\nvar _c;\n$RefreshReg$(_c, \"Text\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/Text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js":
/*!********************************************************!*\
  !*** ./node_modules/recharts/es6/component/Tooltip.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _DefaultTooltipContent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DefaultTooltipContent */ \"(app-pages-browser)/./node_modules/recharts/es6/component/DefaultTooltipContent.js\");\n/* harmony import */ var _TooltipBoundingBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TooltipBoundingBox */ \"(app-pages-browser)/./node_modules/recharts/es6/component/TooltipBoundingBox.js\");\n/* harmony import */ var _util_Global__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/Global */ \"(app-pages-browser)/./node_modules/recharts/es6/util/Global.js\");\n/* harmony import */ var _util_payload_getUniqPayload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/payload/getUniqPayload */ \"(app-pages-browser)/./node_modules/recharts/es6/util/payload/getUniqPayload.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n    if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n        return call;\n    } else if (call !== void 0) {\n        throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n            value: subClass,\n            writable: true,\n            configurable: true\n        }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n        writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Tooltip\n */ \n\n\n\n\nfunction defaultUniqBy(entry) {\n    return entry.dataKey;\n}\nfunction renderContent(content, props) {\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(content)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(content, props);\n    }\n    if (typeof content === 'function') {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(content, props);\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_DefaultTooltipContent__WEBPACK_IMPORTED_MODULE_1__.DefaultTooltipContent, props);\n}\nvar Tooltip = /*#__PURE__*/ function(_PureComponent) {\n    function Tooltip() {\n        _classCallCheck(this, Tooltip);\n        return _callSuper(this, Tooltip, arguments);\n    }\n    _inherits(Tooltip, _PureComponent);\n    return _createClass(Tooltip, [\n        {\n            key: \"render\",\n            value: function render() {\n                var _this = this;\n                var _this$props = this.props, active = _this$props.active, allowEscapeViewBox = _this$props.allowEscapeViewBox, animationDuration = _this$props.animationDuration, animationEasing = _this$props.animationEasing, content = _this$props.content, coordinate = _this$props.coordinate, filterNull = _this$props.filterNull, isAnimationActive = _this$props.isAnimationActive, offset = _this$props.offset, payload = _this$props.payload, payloadUniqBy = _this$props.payloadUniqBy, position = _this$props.position, reverseDirection = _this$props.reverseDirection, useTranslate3d = _this$props.useTranslate3d, viewBox = _this$props.viewBox, wrapperStyle = _this$props.wrapperStyle;\n                var finalPayload = payload !== null && payload !== void 0 ? payload : [];\n                if (filterNull && finalPayload.length) {\n                    finalPayload = (0,_util_payload_getUniqPayload__WEBPACK_IMPORTED_MODULE_2__.getUniqPayload)(payload.filter(function(entry) {\n                        return entry.value != null && (entry.hide !== true || _this.props.includeHidden);\n                    }), payloadUniqBy, defaultUniqBy);\n                }\n                var hasPayload = finalPayload.length > 0;\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_TooltipBoundingBox__WEBPACK_IMPORTED_MODULE_3__.TooltipBoundingBox, {\n                    allowEscapeViewBox: allowEscapeViewBox,\n                    animationDuration: animationDuration,\n                    animationEasing: animationEasing,\n                    isAnimationActive: isAnimationActive,\n                    active: active,\n                    coordinate: coordinate,\n                    hasPayload: hasPayload,\n                    offset: offset,\n                    position: position,\n                    reverseDirection: reverseDirection,\n                    useTranslate3d: useTranslate3d,\n                    viewBox: viewBox,\n                    wrapperStyle: wrapperStyle\n                }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n                    payload: finalPayload\n                })));\n            }\n        }\n    ]);\n}(react__WEBPACK_IMPORTED_MODULE_0__.PureComponent);\n_defineProperty(Tooltip, \"displayName\", 'Tooltip');\n_defineProperty(Tooltip, \"defaultProps\", {\n    accessibilityLayer: false,\n    allowEscapeViewBox: {\n        x: false,\n        y: false\n    },\n    animationDuration: 400,\n    animationEasing: 'ease',\n    contentStyle: {},\n    coordinate: {\n        x: 0,\n        y: 0\n    },\n    cursor: true,\n    cursorStyle: {},\n    filterNull: true,\n    isAnimationActive: !_util_Global__WEBPACK_IMPORTED_MODULE_4__.Global.isSsr,\n    itemStyle: {},\n    labelStyle: {},\n    offset: 10,\n    reverseDirection: {\n        x: false,\n        y: false\n    },\n    separator: ' : ',\n    trigger: 'hover',\n    useTranslate3d: false,\n    viewBox: {\n        x: 0,\n        y: 0,\n        height: 0,\n        width: 0\n    },\n    wrapperStyle: {}\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/component/TooltipBoundingBox.js":
/*!*******************************************************************!*\
  !*** ./node_modules/recharts/es6/component/TooltipBoundingBox.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TooltipBoundingBox: () => (/* binding */ TooltipBoundingBox)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_tooltip_translate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/tooltip/translate */ \"(app-pages-browser)/./node_modules/recharts/es6/util/tooltip/translate.js\");\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", {\n        writable: false\n    });\n    return Constructor;\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n    if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n        return call;\n    } else if (call !== void 0) {\n        throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n    return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n        throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n        constructor: {\n            value: subClass,\n            writable: true,\n            configurable: true\n        }\n    });\n    Object.defineProperty(subClass, \"prototype\", {\n        writable: false\n    });\n    if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\n\n\nvar EPSILON = 1;\nvar TooltipBoundingBox = /*#__PURE__*/ function(_PureComponent) {\n    function TooltipBoundingBox() {\n        var _this;\n        _classCallCheck(this, TooltipBoundingBox);\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        _this = _callSuper(this, TooltipBoundingBox, [].concat(args));\n        _defineProperty(_this, \"state\", {\n            dismissed: false,\n            dismissedAtCoordinate: {\n                x: 0,\n                y: 0\n            },\n            lastBoundingBox: {\n                width: -1,\n                height: -1\n            }\n        });\n        _defineProperty(_this, \"handleKeyDown\", function(event) {\n            if (event.key === 'Escape') {\n                var _this$props$coordinat, _this$props$coordinat2, _this$props$coordinat3, _this$props$coordinat4;\n                _this.setState({\n                    dismissed: true,\n                    dismissedAtCoordinate: {\n                        x: (_this$props$coordinat = (_this$props$coordinat2 = _this.props.coordinate) === null || _this$props$coordinat2 === void 0 ? void 0 : _this$props$coordinat2.x) !== null && _this$props$coordinat !== void 0 ? _this$props$coordinat : 0,\n                        y: (_this$props$coordinat3 = (_this$props$coordinat4 = _this.props.coordinate) === null || _this$props$coordinat4 === void 0 ? void 0 : _this$props$coordinat4.y) !== null && _this$props$coordinat3 !== void 0 ? _this$props$coordinat3 : 0\n                    }\n                });\n            }\n        });\n        return _this;\n    }\n    _inherits(TooltipBoundingBox, _PureComponent);\n    return _createClass(TooltipBoundingBox, [\n        {\n            key: \"updateBBox\",\n            value: function updateBBox() {\n                if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n                    var box = this.wrapperNode.getBoundingClientRect();\n                    if (Math.abs(box.width - this.state.lastBoundingBox.width) > EPSILON || Math.abs(box.height - this.state.lastBoundingBox.height) > EPSILON) {\n                        this.setState({\n                            lastBoundingBox: {\n                                width: box.width,\n                                height: box.height\n                            }\n                        });\n                    }\n                } else if (this.state.lastBoundingBox.width !== -1 || this.state.lastBoundingBox.height !== -1) {\n                    this.setState({\n                        lastBoundingBox: {\n                            width: -1,\n                            height: -1\n                        }\n                    });\n                }\n            }\n        },\n        {\n            key: \"componentDidMount\",\n            value: function componentDidMount() {\n                document.addEventListener('keydown', this.handleKeyDown);\n                this.updateBBox();\n            }\n        },\n        {\n            key: \"componentWillUnmount\",\n            value: function componentWillUnmount() {\n                document.removeEventListener('keydown', this.handleKeyDown);\n            }\n        },\n        {\n            key: \"componentDidUpdate\",\n            value: function componentDidUpdate() {\n                var _this$props$coordinat5, _this$props$coordinat6;\n                if (this.props.active) {\n                    this.updateBBox();\n                }\n                if (!this.state.dismissed) {\n                    return;\n                }\n                if (((_this$props$coordinat5 = this.props.coordinate) === null || _this$props$coordinat5 === void 0 ? void 0 : _this$props$coordinat5.x) !== this.state.dismissedAtCoordinate.x || ((_this$props$coordinat6 = this.props.coordinate) === null || _this$props$coordinat6 === void 0 ? void 0 : _this$props$coordinat6.y) !== this.state.dismissedAtCoordinate.y) {\n                    this.state.dismissed = false;\n                }\n            }\n        },\n        {\n            key: \"render\",\n            value: function render() {\n                var _this2 = this;\n                var _this$props = this.props, active = _this$props.active, allowEscapeViewBox = _this$props.allowEscapeViewBox, animationDuration = _this$props.animationDuration, animationEasing = _this$props.animationEasing, children = _this$props.children, coordinate = _this$props.coordinate, hasPayload = _this$props.hasPayload, isAnimationActive = _this$props.isAnimationActive, offset = _this$props.offset, position = _this$props.position, reverseDirection = _this$props.reverseDirection, useTranslate3d = _this$props.useTranslate3d, viewBox = _this$props.viewBox, wrapperStyle = _this$props.wrapperStyle;\n                var _getTooltipTranslate = (0,_util_tooltip_translate__WEBPACK_IMPORTED_MODULE_1__.getTooltipTranslate)({\n                    allowEscapeViewBox: allowEscapeViewBox,\n                    coordinate: coordinate,\n                    offsetTopLeft: offset,\n                    position: position,\n                    reverseDirection: reverseDirection,\n                    tooltipBox: this.state.lastBoundingBox,\n                    useTranslate3d: useTranslate3d,\n                    viewBox: viewBox\n                }), cssClasses = _getTooltipTranslate.cssClasses, cssProperties = _getTooltipTranslate.cssProperties;\n                var outerStyle = _objectSpread(_objectSpread({\n                    transition: isAnimationActive && active ? \"transform \".concat(animationDuration, \"ms \").concat(animationEasing) : undefined\n                }, cssProperties), {}, {\n                    pointerEvents: 'none',\n                    visibility: !this.state.dismissed && active && hasPayload ? 'visible' : 'hidden',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0\n                }, wrapperStyle);\n                return(/*#__PURE__*/ // This element allow listening to the `Escape` key.\n                // See https://github.com/recharts/recharts/pull/2925\n                react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n                    tabIndex: -1,\n                    className: cssClasses,\n                    style: outerStyle,\n                    ref: function ref(node) {\n                        _this2.wrapperNode = node;\n                    }\n                }, children));\n            }\n        }\n    ]);\n}(react__WEBPACK_IMPORTED_MODULE_0__.PureComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/component/TooltipBoundingBox.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/container/Layer.js":
/*!******************************************************!*\
  !*** ./node_modules/recharts/es6/container/Layer.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Layer: () => (/* binding */ Layer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\nvar _excluded = [\n    \"children\",\n    \"className\"\n];\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n\n\n\nvar Layer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(_c = function(props, ref) {\n    var children = props.children, className = props.className, others = _objectWithoutProperties(props, _excluded);\n    var layerClass = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('recharts-layer', className);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\", _extends({\n        className: layerClass\n    }, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_2__.filterProps)(others, true), {\n        ref: ref\n    }), children);\n});\n_c1 = Layer;\nvar _c, _c1;\n$RefreshReg$(_c, \"Layer$React.forwardRef\");\n$RefreshReg$(_c1, \"Layer\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/container/Layer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/container/Surface.js":
/*!********************************************************!*\
  !*** ./node_modules/recharts/es6/container/Surface.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Surface: () => (/* binding */ Surface)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _util_ReactUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/ReactUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/ReactUtils.js\");\nvar _excluded = [\n    \"children\",\n    \"width\",\n    \"height\",\n    \"viewBox\",\n    \"className\",\n    \"style\",\n    \"title\",\n    \"desc\"\n];\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    for(var key in source){\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n            if (excluded.indexOf(key) >= 0) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\n/**\n * @fileOverview Surface\n */ \n\n\nfunction Surface(props) {\n    var children = props.children, width = props.width, height = props.height, viewBox = props.viewBox, className = props.className, style = props.style, title = props.title, desc = props.desc, others = _objectWithoutProperties(props, _excluded);\n    var svgView = viewBox || {\n        width: width,\n        height: height,\n        x: 0,\n        y: 0\n    };\n    var layerClass = (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('recharts-surface', className);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", _extends({}, (0,_util_ReactUtils__WEBPACK_IMPORTED_MODULE_2__.filterProps)(others, true, 'svg'), {\n        className: layerClass,\n        width: width,\n        height: height,\n        style: style,\n        viewBox: \"\".concat(svgView.x, \" \").concat(svgView.y, \" \").concat(svgView.width, \" \").concat(svgView.height)\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"title\", null, title), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"desc\", null, desc), children);\n}\n_c = Surface;\nvar _c;\n$RefreshReg$(_c, \"Surface\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/container/Surface.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/recharts/es6/context/chartLayoutContext.js":
/*!*****************************************************************!*\
  !*** ./node_modules/recharts/es6/context/chartLayoutContext.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChartHeightContext: () => (/* binding */ ChartHeightContext),\n/* harmony export */   ChartLayoutContextProvider: () => (/* binding */ ChartLayoutContextProvider),\n/* harmony export */   ChartWidthContext: () => (/* binding */ ChartWidthContext),\n/* harmony export */   ClipPathIdContext: () => (/* binding */ ClipPathIdContext),\n/* harmony export */   OffsetContext: () => (/* binding */ OffsetContext),\n/* harmony export */   ViewBoxContext: () => (/* binding */ ViewBoxContext),\n/* harmony export */   XAxisContext: () => (/* binding */ XAxisContext),\n/* harmony export */   YAxisContext: () => (/* binding */ YAxisContext),\n/* harmony export */   useArbitraryXAxis: () => (/* binding */ useArbitraryXAxis),\n/* harmony export */   useArbitraryYAxis: () => (/* binding */ useArbitraryYAxis),\n/* harmony export */   useChartHeight: () => (/* binding */ useChartHeight),\n/* harmony export */   useChartWidth: () => (/* binding */ useChartWidth),\n/* harmony export */   useClipPathId: () => (/* binding */ useClipPathId),\n/* harmony export */   useOffset: () => (/* binding */ useOffset),\n/* harmony export */   useViewBox: () => (/* binding */ useViewBox),\n/* harmony export */   useXAxisOrThrow: () => (/* binding */ useXAxisOrThrow),\n/* harmony export */   useYAxisOrThrow: () => (/* binding */ useYAxisOrThrow),\n/* harmony export */   useYAxisWithFiniteDomainOrRandom: () => (/* binding */ useYAxisWithFiniteDomainOrRandom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var lodash_find__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/find */ \"(app-pages-browser)/./node_modules/lodash/find.js\");\n/* harmony import */ var lodash_find__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_find__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_every__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/every */ \"(app-pages-browser)/./node_modules/lodash/every.js\");\n/* harmony import */ var lodash_every__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_every__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _util_calculateViewBox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/calculateViewBox */ \"(app-pages-browser)/./node_modules/recharts/es6/util/calculateViewBox.js\");\n/* harmony import */ var _util_DataUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/DataUtils */ \"(app-pages-browser)/./node_modules/recharts/es6/util/DataUtils.js\");\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$();\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\n\n\n\n\n\n\nvar XAxisContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nvar YAxisContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nvar ViewBoxContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nvar OffsetContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nvar ClipPathIdContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nvar ChartHeightContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(0);\nvar ChartWidthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(0);\n/**\n * Will add all the properties required to render all individual Recharts components into a React Context.\n *\n * If you want to read these properties, see the collection of hooks exported from this file.\n *\n * @param {object} props CategoricalChartState, plus children\n * @returns {ReactElement} React Context Provider\n */ var ChartLayoutContextProvider = function ChartLayoutContextProvider(props) {\n    var _props$state = props.state, xAxisMap = _props$state.xAxisMap, yAxisMap = _props$state.yAxisMap, offset = _props$state.offset, clipPathId = props.clipPathId, children = props.children, width = props.width, height = props.height;\n    /**\n   * Perhaps we should compute this property when reading? Let's see what is more often used\n   */ var viewBox = (0,_util_calculateViewBox__WEBPACK_IMPORTED_MODULE_4__.calculateViewBox)(offset);\n    /*\n   * This pretends to be a single context but actually is split into multiple smaller ones.\n   * Why?\n   * Because one React Context only allows to set one value.\n   * But we need to set multiple values.\n   * If we do that with one context, then we force re-render on components that might not even be interested\n   * in the part of the state that has changed.\n   *\n   * By splitting into smaller contexts, we allow each components to be optimized and only re-render when its dependencies change.\n   *\n   * To actually achieve the optimal re-render, it is necessary to use React.memo().\n   * See the test file for details.\n   */ return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(XAxisContext.Provider, {\n        value: xAxisMap\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(YAxisContext.Provider, {\n        value: yAxisMap\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(OffsetContext.Provider, {\n        value: offset\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ViewBoxContext.Provider, {\n        value: viewBox\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ClipPathIdContext.Provider, {\n        value: clipPathId\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ChartHeightContext.Provider, {\n        value: height\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ChartWidthContext.Provider, {\n        value: width\n    }, children)))))));\n};\n_c = ChartLayoutContextProvider;\nvar useClipPathId = function useClipPathId() {\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ClipPathIdContext);\n};\n_s(useClipPathId, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction getKeysForDebug(object) {\n    var keys = Object.keys(object);\n    if (keys.length === 0) {\n        return 'There are no available ids.';\n    }\n    return \"Available ids are: \".concat(keys, \".\");\n}\n/**\n * This either finds and returns Axis by the specified ID, or throws an exception if an axis with this ID does not exist.\n *\n * @param xAxisId identifier of the axis - it's either autogenerated ('0'), or passed via `id` prop as <XAxis id='foo' />\n * @returns axis configuration object\n * @throws Error if no axis with this ID exists\n */ var useXAxisOrThrow = function useXAxisOrThrow(xAxisId) {\n    _s1();\n    var xAxisMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(XAxisContext);\n    !(xAxisMap != null) ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(false, 'Could not find Recharts context; are you sure this is rendered inside a Recharts wrapper component?') : 0 : void 0;\n    var xAxis = xAxisMap[xAxisId];\n    !(xAxis != null) ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(false, \"Could not find xAxis by id \\\"\".concat(xAxisId, \"\\\" [\").concat(_typeof(xAxisId), \"]. \").concat(getKeysForDebug(xAxisMap))) : 0 : void 0;\n    return xAxis;\n};\n_s1(useXAxisOrThrow, \"uLqL/ErIGfpjQswv0rkuA1tqKvM=\");\n/**\n * This will find an arbitrary first XAxis. If there's exactly one it always returns that one\n * - but if there are multiple then it can return any of those.\n *\n * If you want specific XAxis out of multiple then prefer using useXAxisOrThrow\n *\n * @returns X axisOptions, or undefined - if there are no X axes\n */ var useArbitraryXAxis = function useArbitraryXAxis() {\n    _s2();\n    var xAxisMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(XAxisContext);\n    return (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.getAnyElementOfObject)(xAxisMap);\n};\n_s2(useArbitraryXAxis, \"uLqL/ErIGfpjQswv0rkuA1tqKvM=\");\n/**\n * This will find an arbitrary first YAxis. If there's exactly one it always returns that one\n * - but if there are multiple then it can return any of those.\n *\n * If you want specific YAxis out of multiple then prefer using useXAxisOrThrow\n *\n * @returns Y axisOptions, or undefined - if there are no Y axes\n */ var useArbitraryYAxis = function useArbitraryYAxis() {\n    _s3();\n    var yAxisMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(YAxisContext);\n    return (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.getAnyElementOfObject)(yAxisMap);\n};\n_s3(useArbitraryYAxis, \"fEz0zjR2HC0oI+VnSgrL41V0dqs=\");\n/**\n * This hooks will:\n * 1st attempt to find an YAxis that has all elements in its domain finite\n * If no such axis exists, it will return an arbitrary YAxis\n * if there are no Y axes then it returns undefined\n *\n * @returns Either Y axisOptions, or undefined if there are no Y axes\n */ var useYAxisWithFiniteDomainOrRandom = function useYAxisWithFiniteDomainOrRandom() {\n    _s4();\n    var yAxisMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(YAxisContext);\n    var yAxisWithFiniteDomain = lodash_find__WEBPACK_IMPORTED_MODULE_2___default()(yAxisMap, function(axis) {\n        return lodash_every__WEBPACK_IMPORTED_MODULE_3___default()(axis.domain, Number.isFinite);\n    });\n    return yAxisWithFiniteDomain || (0,_util_DataUtils__WEBPACK_IMPORTED_MODULE_5__.getAnyElementOfObject)(yAxisMap);\n};\n_s4(useYAxisWithFiniteDomainOrRandom, \"fEz0zjR2HC0oI+VnSgrL41V0dqs=\");\n/**\n * This either finds and returns Axis by the specified ID, or throws an exception if an axis with this ID does not exist.\n *\n * @param yAxisId identifier of the axis - it's either autogenerated ('0'), or passed via `id` prop as <YAxis id='foo' />\n * @returns axis configuration object\n * @throws Error if no axis with this ID exists\n */ var useYAxisOrThrow = function useYAxisOrThrow(yAxisId) {\n    _s5();\n    var yAxisMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(YAxisContext);\n    !(yAxisMap != null) ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(false, 'Could not find Recharts context; are you sure this is rendered inside a Recharts wrapper component?') : 0 : void 0;\n    var yAxis = yAxisMap[yAxisId];\n    !(yAxis != null) ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(false, \"Could not find yAxis by id \\\"\".concat(yAxisId, \"\\\" [\").concat(_typeof(yAxisId), \"]. \").concat(getKeysForDebug(yAxisMap))) : 0 : void 0;\n    return yAxis;\n};\n_s5(useYAxisOrThrow, \"fEz0zjR2HC0oI+VnSgrL41V0dqs=\");\nvar useViewBox = function useViewBox() {\n    _s6();\n    var viewBox = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ViewBoxContext);\n    return viewBox;\n};\n_s6(useViewBox, \"ziC+uI9Lej31e/ji5Zldp1x/AlI=\");\nvar useOffset = function useOffset() {\n    _s7();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(OffsetContext);\n};\n_s7(useOffset, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar useChartWidth = function useChartWidth() {\n    _s8();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ChartWidthContext);\n};\n_s8(useChartWidth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar useChartHeight = function useChartHeight() {\n    _s9();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ChartHeightContext);\n};\n_s9(useChartHeight, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"ChartLayoutContextProvider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/recharts/es6/context/chartLayoutContext.js\n"));

/***/ })

}]);