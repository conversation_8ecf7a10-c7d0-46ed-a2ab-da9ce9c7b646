"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[979],{894:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return o}});let n=r(5155);function o(e){let{Component:t,searchParams:o,params:a,promises:l}=e;{let{createRenderSearchParamsFromClient:e}=r(7205),l=e(o),{createRenderParamsFromClient:u}=r(3558),i=u(a);return(0,n.jsx)(t,{params:i,searchParams:l})}}r(9837),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1315:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let n=r(5929);function o(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2858:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(6494),o=r(2210);function a(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3269:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_HEADER:function(){return n},FLIGHT_HEADERS:function(){return d},NEXT_DID_POSTPONE_HEADER:function(){return p},NEXT_HMR_REFRESH_HEADER:function(){return u},NEXT_IS_PRERENDER_HEADER:function(){return y},NEXT_REWRITTEN_PATH_HEADER:function(){return h},NEXT_REWRITTEN_QUERY_HEADER:function(){return _},NEXT_ROUTER_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return l},NEXT_ROUTER_STALE_TIME_HEADER:function(){return f},NEXT_ROUTER_STATE_TREE_HEADER:function(){return o},NEXT_RSC_UNION_QUERY:function(){return c},NEXT_URL:function(){return i},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",o="Next-Router-State-Tree",a="Next-Router-Prefetch",l="Next-Router-Segment-Prefetch",u="Next-HMR-Refresh",i="Next-Url",s="text/x-component",d=[r,o,a,u,l],c="_rsc",f="x-nextjs-stale-time",p="x-nextjs-postponed",h="x-nextjs-rewritten-path",_="x-nextjs-rewritten-query",y="x-nextjs-prerender";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"attachHydrationErrorState",{enumerable:!0,get:function(){return a}});let n=r(6465),o=r(9771);function a(e){let t={},r=(0,n.testReactHydrationWarning)(e.message),a=(0,n.isHydrationError)(e);if(!(a||r))return;let l=(0,o.getReactHydrationDiffSegments)(e.message);if(l){let u=l[1];t={...e.details,...o.hydrationErrorState,warning:(u&&!r?null:o.hydrationErrorState.warning)||[(0,n.getDefaultHydrationErrorMessage)()],notes:r?"":l[0],reactOutputComponentDiff:u},!o.hydrationErrorState.reactOutputComponentDiff&&u&&(o.hydrationErrorState.reactOutputComponentDiff=u),!u&&a&&o.hydrationErrorState.reactOutputComponentDiff&&(t.reactOutputComponentDiff=o.hydrationErrorState.reactOutputComponentDiff)}else o.hydrationErrorState.warning&&(t={...e.details,...o.hydrationErrorState}),o.hydrationErrorState.reactOutputComponentDiff&&(t.reactOutputComponentDiff=o.hydrationErrorState.reactOutputComponentDiff);e.details=t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3575:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return s}});let n=r(4252),o=n._(r(4232)),a=n._(r(6240)),l=r(8089),u="react-stack-bottom-frame",i=RegExp("(at "+u+" )|("+u+"\\@)");function s(e){let t=(0,a.default)(e),r=t&&e.stack||"",n=t?e.message:"",u=r.split("\n"),s=u.findIndex(e=>i.test(e)),d=s>=0?u.slice(0,s).join("\n"):r,c=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(c,e),(0,l.copyNextErrorCode)(e,c),c.stack=d,function(e){if(!o.default.captureOwnerStack)return;let t=e.stack||"",r=o.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(c),c}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3678:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(6494).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3950:(e,t)=>{function r(e,t){let r=e[e.length-1];(!r||r.stack!==t.stack)&&e.push(t)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"enqueueConsecutiveDedupedError",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3954:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),(0,r(5444).handleGlobalErrors)(),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4181:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return u},getAccessFallbackHTTPStatus:function(){return l},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function l(e){return Number(e.digest.split(";")[1])}function u(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{mountLinkInstance:function(){return s},onLinkVisibilityChanged:function(){return c},onNavigationIntent:function(){return f},pingVisibleLinks:function(){return h},unmountLinkInstance:function(){return d}}),r(9692);let n=r(6158),o=r(9818),a=r(6005),l="function"==typeof WeakMap?new WeakMap:new Map,u=new Set,i="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;c(t.target,e)}},{rootMargin:"200px"}):null;function s(e,t,r,o){let a=null;try{if(a=(0,n.createPrefetchURL)(t),null===a)return}catch(e){("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+t+"' because it cannot be converted to a URL.");return}let u={prefetchHref:a.href,router:r,kind:o,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1};void 0!==l.get(e)&&d(e),l.set(e,u),null!==i&&i.observe(e)}function d(e){let t=l.get(e);if(void 0!==t){l.delete(e),u.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==i&&i.unobserve(e)}function c(e,t){let r=l.get(e);void 0!==r&&(r.isVisible=t,t?u.add(r):u.delete(r),p(r))}function f(e){let t=l.get(e);void 0!==t&&void 0!==t&&(t.wasHoveredOrTouched=!0,p(t))}function p(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}!function(e){(async()=>e.router.prefetch(e.prefetchHref,{kind:e.kind}))().catch(e=>{})}(e)}function h(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of u){let l=n.prefetchTask;if(null!==l&&n.cacheVersion===r&&l.key.nextUrl===e&&l.treeAtTimeOfPrefetch===t)continue;null!==l&&(0,a.cancelPrefetchTask)(l);let u=(0,a.createCacheKey)(n.prefetchHref,e),i=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(u,t,n.kind===o.PrefetchKind.FULL,i),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4970:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientSegmentRoot",{enumerable:!0,get:function(){return o}});let n=r(5155);function o(e){let{Component:t,slots:o,params:a,promise:l}=e;{let{createRenderParamsFromClient:e}=r(3558),l=e(a);return(0,n.jsx)(t,{...o,params:l})}}r(9837),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5128:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return s}});let n=r(8229),o=n._(r(2115)),a=n._(r(5807)),l=r(9148),u="react-stack-bottom-frame",i=RegExp("(at "+u+" )|("+u+"\\@)");function s(e){let t=(0,a.default)(e),r=t&&e.stack||"",n=t?e.message:"",u=r.split("\n"),s=u.findIndex(e=>i.test(e)),d=s>=0?u.slice(0,s).join("\n"):r,c=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(c,e),(0,l.copyNextErrorCode)(e,c),c.stack=d,function(e){if(!o.default.captureOwnerStack)return;let t=e.stack||"",r=o.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(c),c}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5415:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),r(5449),(0,r(6188).appBootstrap)(()=>{let{hydrate:e}=r(4486);r(6158),r(7555),e()}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5444:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleClientError:function(){return g},handleGlobalErrors:function(){return R},useErrorHandler:function(){return b}});let n=r(8229),o=r(2115),a=r(3506),l=r(2858),u=r(9771),i=r(5169),s=n._(r(5807)),d=r(6043),c=r(3950),f=r(5128),p=globalThis.queueMicrotask||(e=>Promise.resolve().then(e)),h=[],_=[],y=[],E=[];function g(e,t,r){let n;if(void 0===r&&(r=!1),e&&(0,s.default)(e))n=r?(0,d.createUnhandledError)(e):e;else{let e=(0,i.formatConsoleArgs)(t),{environmentName:r}=(0,i.parseConsoleArgs)(t);n=(0,d.createUnhandledError)(e,r)}for(let e of(n=(0,f.getReactStitchedError)(n),(0,u.storeHydrationErrorStateFromConsoleArgs)(...t),(0,a.attachHydrationErrorState)(n),(0,c.enqueueConsecutiveDedupedError)(h,n),_))p(()=>{e(n)})}function b(e,t){(0,o.useEffect)(()=>(h.forEach(e),y.forEach(t),_.push(e),E.push(t),()=>{_.splice(_.indexOf(e),1),E.splice(E.indexOf(t),1),h.splice(0,h.length),y.splice(0,y.length)}),[e,t])}function m(e){if((0,l.isNextRouterError)(e.error))return e.preventDefault(),!1;e.error&&g(e.error,[])}function v(e){let t=null==e?void 0:e.reason;if((0,l.isNextRouterError)(t)){e.preventDefault();return}let r=t;for(let e of(r&&!(0,s.default)(r)&&(r=(0,d.createUnhandledError)(r+"")),y.push(r),E))e(r)}function R(){try{Error.stackTraceLimit=50}catch(e){}window.addEventListener("error",m),window.addEventListener("unhandledrejection",v)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5449:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),r(3668);let n=r(589);{let e=r.u;r.u=function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];return(0,n.encodeURIPath)(e(...r))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6002:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),(0,r(6905).patchConsoleError)(),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6043:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createUnhandledError:function(){return o},getUnhandledErrorType:function(){return l},isUnhandledConsoleOrRejection:function(){return a}});let r=Symbol.for("next.console.error.digest"),n=Symbol.for("next.console.error.type");function o(e,t){let o="string"==typeof e?Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}):e;return o[r]="NEXT_UNHANDLED_ERROR",o[n]="string"==typeof e?"string":"error",t&&!o.environmentName&&(o.environmentName=t),o}let a=e=>e&&"NEXT_UNHANDLED_ERROR"===e[r],l=e=>e[n];("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6158:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return N},createPrefetchURL:function(){return w},default:function(){return H}});let n=r(6966),o=r(5155),a=n._(r(2115)),l=r(5227),u=r(9818),i=r(1139),s=r(886),d=r(1365),c=n._(r(6614)),f=r(774),p=r(5929),h=r(7760),_=r(686),y=r(2691),E=r(1822),g=r(4882),b=r(7102),m=r(8946),v=r(8836),R=r(3806);r(6005);let j=r(6825),T=r(2210),O=r(9154);r(4930);let x={};function P(e){return e.origin!==window.location.origin}function w(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return P(t)?null:t}function S(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,o={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,i.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(o,"",n)):window.history.replaceState(o,"",n)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function N(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null}}function M(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,a.useDeferredValue)(r,o)}function A(e){let t,{actionQueue:r,assetPrefix:n,globalError:i}=e,[f,v]=(0,d.useReducer)(r),{canonicalUrl:N}=(0,d.useUnwrapState)(f),{searchParams:A,pathname:H}=(0,a.useMemo)(()=>{let e=new URL(N,window.location.href);return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,g.removeBasePath)(e.pathname):e.pathname}},[N]),D=(0,a.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,a.startTransition)(()=>{v({type:u.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[v]),L=(0,a.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return v({type:u.ACTION_NAVIGATE,url:n,isExternalUrl:P(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t,allowAliasing:!0})},[v]);(0,R.useServerActionDispatcher)(v);let I=(0,a.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=w(e);if(null!==n){var o;(0,O.prefetchReducer)(r.state,{type:u.ACTION_PREFETCH,url:n,kind:null!=(o=null==t?void 0:t.kind)?o:u.PrefetchKind.FULL})}},replace:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;L(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;L(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,a.startTransition)(()=>{v({type:u.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}}),[r,v,L]);(0,a.useEffect)(()=>{window.next&&(window.next.router=I)},[I]),(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(x.pendingMpaPath=void 0,v({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[v]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let r=(0,j.getURLFromRedirectError)(t);(0,j.getRedirectTypeFromError)(t)===T.RedirectType.push?I.push(r,{}):I.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[I]);let{pushRef:U}=(0,d.useUnwrapState)(f);if(U.mpaNavigation){if(x.pendingMpaPath!==N){let e=window.location;U.pendingPush?e.assign(N):e.replace(N),x.pendingMpaPath=N}(0,a.use)(E.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{v({type:u.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),o&&r(o)),t(e,n,o)};let n=e=>{if(e.state){if(!e.state.__NA){window.location.reload();return}(0,a.startTransition)(()=>{v({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:e.state.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[v]);let{cache:F,tree:B,nextUrl:W,focusAndScrollRef:X}=(0,d.useUnwrapState)(f),V=(0,a.useMemo)(()=>(0,y.findHeadInCache)(F,B[1]),[F,B]),z=(0,a.useMemo)(()=>(0,m.getSelectedParams)(B),[B]),K=(0,a.useMemo)(()=>({parentTree:B,parentCacheNode:F,parentSegmentPath:null,url:N}),[B,F,N]),G=(0,a.useMemo)(()=>({changeByServerResponse:D,tree:B,focusAndScrollRef:X,nextUrl:W}),[D,B,X,W]);if(null!==V){let[e,r]=V;t=(0,o.jsx)(C,{headCacheNode:e},r)}else t=null;let J=(0,o.jsxs)(_.RedirectBoundary,{children:[t,F.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:B})]});return J=(0,o.jsx)(c.ErrorBoundary,{errorComponent:i[0],errorStyles:i[1],children:J}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(S,{appRouterState:(0,d.useUnwrapState)(f)}),(0,o.jsx)(k,{}),(0,o.jsx)(s.PathParamsContext.Provider,{value:z,children:(0,o.jsx)(s.PathnameContext.Provider,{value:H,children:(0,o.jsx)(s.SearchParamsContext.Provider,{value:A,children:(0,o.jsx)(l.GlobalLayoutRouterContext.Provider,{value:G,children:(0,o.jsx)(l.AppRouterContext.Provider,{value:I,children:(0,o.jsx)(l.LayoutRouterContext.Provider,{value:K,children:J})})})})})})]})}function H(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:a}=e;return(0,v.useNavFailureHandler)(),(0,o.jsx)(c.ErrorBoundary,{errorComponent:c.default,children:(0,o.jsx)(A,{actionQueue:t,assetPrefix:a,globalError:[r,n]})})}let D=new Set,L=new Set;function k(){let[,e]=a.default.useState(0),t=D.size;return(0,a.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==D.size&&r(),()=>{L.delete(r)}},[t,e]),[...D].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=D.size;return D.add(e),D.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6395:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessErrorFallback",{enumerable:!0,get:function(){return a}}),r(8229);let n=r(5155);r(2115);let o={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function a(e){let{status:t,message:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:t+": "+r}),(0,n.jsx)("div",{style:o.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:o.h1,children:t}),(0,n.jsx)("div",{style:o.desc,children:(0,n.jsx)("h2",{style:o.h2,children:r})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6465:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXTJS_HYDRATION_ERROR_LINK:function(){return i},REACT_HYDRATION_ERROR_LINK:function(){return u},getDefaultHydrationErrorMessage:function(){return s},getHydrationErrorStackInfo:function(){return h},isHydrationError:function(){return d},isReactHydrationErrorMessage:function(){return c},testReactHydrationWarning:function(){return p}});let n=r(8229)._(r(5807)),o=/hydration failed|while hydrating|content does not match|did not match|HTML didn't match/i,a="Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:",l=[a,"A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:"],u="https://react.dev/link/hydration-mismatch",i="https://nextjs.org/docs/messages/react-hydration-error",s=()=>a;function d(e){return(0,n.default)(e)&&o.test(e.message)}function c(e){return l.some(t=>e.startsWith(t))}let f=[/^In HTML, (.+?) cannot be a child of <(.+?)>\.(.*)\nThis will cause a hydration error\.(.*)/,/^In HTML, (.+?) cannot be a descendant of <(.+?)>\.\nThis will cause a hydration error\.(.*)/,/^In HTML, text nodes cannot be a child of <(.+?)>\.\nThis will cause a hydration error\./,/^In HTML, whitespace text nodes cannot be a child of <(.+?)>\. Make sure you don't have any extra whitespace between tags on each line of your source code\.\nThis will cause a hydration error\./,/^Expected server HTML to contain a matching <(.+?)> in <(.+?)>\.(.*)/,/^Did not expect server HTML to contain a <(.+?)> in <(.+?)>\.(.*)/,/^Expected server HTML to contain a matching text node for "(.+?)" in <(.+?)>\.(.*)/,/^Did not expect server HTML to contain the text node "(.+?)" in <(.+?)>\.(.*)/,/^Text content did not match\. Server: "(.+?)" Client: "(.+?)"(.*)/];function p(e){return"string"==typeof e&&!!e&&(e.startsWith("Warning: ")&&(e=e.slice(9)),f.some(t=>t.test(e)))}function h(e){let t=p(e=(e=e.replace(/^Error: /,"")).replace("Warning: ",""));if(!c(e)&&!t)return{message:null,stack:e,diff:""};if(t){let[t,r]=e.split("\n\n");return{message:t.trim(),stack:"",diff:(r||"").trim()}}let r=e.indexOf("\n"),[n,o]=(e=e.slice(r+1).trim()).split(""+u),a=n.trim();if(!o||!(o.length>1))return{message:a,stack:o};{let e=[],t=[];return o.split("\n").forEach(r=>{""!==r.trim()&&(r.trim().startsWith("at ")?e.push(r):t.push(r))}),{message:a,diff:t.join("\n"),stack:e.join("\n")}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6494:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return u},getAccessFallbackHTTPStatus:function(){return l},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function l(e){return Number(e.digest.split(";")[1])}function u(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return h},ErrorBoundaryHandler:function(){return c},GlobalError:function(){return f},default:function(){return p}});let n=r(8229),o=r(5155),a=n._(r(2115)),l=r(9921),u=r(2858);r(8836);let i=void 0,s={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function d(e){let{error:t}=e;if(i){let e=i.getStore();if((null==e?void 0:e.isRevalidate)||(null==e?void 0:e.isStaticGeneration))throw t}return null}class c extends a.default.Component{static getDerivedStateFromError(e){if((0,u.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){let{error:r}=t;return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,o.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,o.jsxs)("html",{id:"__next_error__",children:[(0,o.jsx)("head",{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(d,{error:t}),(0,o.jsx)("div",{style:s.error,children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{style:s.text,children:["Application error: a ",r?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",r?"server logs":"browser console"," for more information)."]}),r?(0,o.jsx)("p",{style:s.text,children:"Digest: "+r}):null]})})]})]})}let p=f;function h(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:a}=e,u=(0,l.useUntrackedPathname)();return t?(0,o.jsx)(c,{pathname:u,errorComponent:t,errorStyles:r,errorScripts:n,children:a}):(0,o.jsx)(o.Fragment,{children:a})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6905:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{originConsoleError:function(){return o},patchConsoleError:function(){return a}}),r(8229),r(5807);let n=r(2858);r(5444),r(5169);let o=globalThis.console.error;function a(){window.console.error=function(){let e;for(var t=arguments.length,r=Array(t),a=0;a<t;a++)r[a]=arguments[a];e=r[0],(0,n.isNextRouterError)(e)||o.apply(window.console,r)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6975:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessFallbackBoundary",{enumerable:!0,get:function(){return d}});let n=r(6966),o=r(5155),a=n._(r(2115)),l=r(9921),u=r(6494);r(3230);let i=r(5227);class s extends a.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,u.isHTTPAccessFallbackError)(e))return{triggeredStatus:(0,u.getAccessFallbackHTTPStatus)(e)};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.triggeredStatus?{triggeredStatus:void 0,previousPathname:e.pathname}:{triggeredStatus:t.triggeredStatus,previousPathname:e.pathname}}render(){let{notFound:e,forbidden:t,unauthorized:r,children:n}=this.props,{triggeredStatus:a}=this.state,l={[u.HTTPAccessErrorStatus.NOT_FOUND]:e,[u.HTTPAccessErrorStatus.FORBIDDEN]:t,[u.HTTPAccessErrorStatus.UNAUTHORIZED]:r};if(a){let i=a===u.HTTPAccessErrorStatus.NOT_FOUND&&e,s=a===u.HTTPAccessErrorStatus.FORBIDDEN&&t,d=a===u.HTTPAccessErrorStatus.UNAUTHORIZED&&r;return i||s||d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("meta",{name:"robots",content:"noindex"}),!1,l[a]]}):n}return n}constructor(e){super(e),this.state={triggeredStatus:void 0,previousPathname:e.pathname}}}function d(e){let{notFound:t,forbidden:r,unauthorized:n,children:u}=e,d=(0,l.useUntrackedPathname)(),c=(0,a.useContext)(i.MissingSlotContext);return t||r||n?(0,o.jsx)(s,{pathname:d,notFound:t,forbidden:r,unauthorized:n,missingSlots:c,children:u}):(0,o.jsx)(o.Fragment,{children:u})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6999:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(4181),o=r(2591);function a(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7555:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return O}});let n=r(8229),o=r(6966),a=r(5155),l=o._(r(2115)),u=n._(r(7650)),i=r(5227),s=r(8586),d=r(1822),c=r(6614),f=r(1127),p=r(4189),h=r(686),_=r(6975),y=r(5637),E=r(4108),g=u.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,b=["bottom","height","left","right","top","width","x","y"];function m(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class v extends l.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,f.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r)r=(0,g.findDOMNode)(this);if(!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return b.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,p.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!m(r,t)&&(e.scrollTop=0,m(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function R(e){let{segmentPath:t,children:r}=e,n=(0,l.useContext)(i.GlobalLayoutRouterContext);if(!n)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});return(0,a.jsx)(v,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function j(e){let{tree:t,segmentPath:r,cacheNode:n,url:o}=e,u=(0,l.useContext)(i.GlobalLayoutRouterContext);if(!u)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});let{changeByServerResponse:c,tree:p}=u,h=null!==n.prefetchRsc?n.prefetchRsc:n.rsc,_=(0,l.useDeferredValue)(n.rsc,h),y="object"==typeof _&&null!==_&&"function"==typeof _.then?(0,l.use)(_):_;if(!y){let e=n.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,o]=t,a=2===t.length;if((0,f.matchSegment)(r[0],n)&&r[1].hasOwnProperty(o)){if(a){let t=e(void 0,r[1][o]);return[r[0],{...r[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[o]:e(t.slice(2),r[1][o])}]}}return r}(["",...r],p),a=(0,E.hasInterceptionRouteInCurrentTree)(p);n.lazyData=e=(0,s.fetchServerResponse)(new URL(o,location.origin),{flightRouterState:t,nextUrl:a?u.nextUrl:null}).then(e=>((0,l.startTransition)(()=>{c({previousTree:p,serverResponse:e})}),e)),(0,l.use)(e)}(0,l.use)(d.unresolvedThenable)}return(0,a.jsx)(i.LayoutRouterContext.Provider,{value:{parentTree:t,parentCacheNode:n,parentSegmentPath:r,url:o},children:y})}function T(e){let t,{loading:r,children:n}=e;if(t="object"==typeof r&&null!==r&&"function"==typeof r.then?(0,l.use)(r):r){let e=t[0],r=t[1],o=t[2];return(0,a.jsx)(l.Suspense,{fallback:(0,a.jsxs)(a.Fragment,{children:[r,o,e]}),children:n})}return(0,a.jsx)(a.Fragment,{children:n})}function O(e){let{parallelRouterKey:t,error:r,errorStyles:n,errorScripts:o,templateStyles:u,templateScripts:s,template:d,notFound:f,forbidden:p,unauthorized:E}=e,g=(0,l.useContext)(i.LayoutRouterContext);if(!g)throw Object.defineProperty(Error("invariant expected layout router to be mounted"),"__NEXT_ERROR_CODE",{value:"E56",enumerable:!1,configurable:!0});let{parentTree:b,parentCacheNode:m,parentSegmentPath:v,url:O}=g,x=m.parallelRoutes,P=x.get(t);P||(P=new Map,x.set(t,P));let w=b[0],S=b[1][t],N=S[0],M=null===v?[t]:v.concat([w,t]),C=(0,y.createRouterCacheKey)(N),A=(0,y.createRouterCacheKey)(N,!0),H=P.get(C);if(void 0===H){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};H=e,P.set(C,e)}let D=m.loading;return(0,a.jsxs)(i.TemplateContext.Provider,{value:(0,a.jsx)(R,{segmentPath:M,children:(0,a.jsx)(c.ErrorBoundary,{errorComponent:r,errorStyles:n,errorScripts:o,children:(0,a.jsx)(T,{loading:D,children:(0,a.jsx)(_.HTTPAccessFallbackBoundary,{notFound:f,forbidden:p,unauthorized:E,children:(0,a.jsx)(h.RedirectBoundary,{children:(0,a.jsx)(j,{url:O,tree:S,cacheNode:H,segmentPath:M})})})})})}),children:[u,s,d]},A)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7760:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return l}});let n=r(2115),o=r(7650),a="next-route-announcer";function l(e){let{tree:t}=e,[r,l]=(0,n.useState)(null);(0,n.useEffect)(()=>(l(function(){var e;let t=document.getElementsByName(a)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[u,i]=(0,n.useState)(""),s=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&i(e),s.current=e},[t]),r?(0,o.createPortal)(u,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9771:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getHydrationWarningType:function(){return u},getReactHydrationDiffSegments:function(){return d},hydrationErrorState:function(){return o},storeHydrationErrorStateFromConsoleArgs:function(){return c}});let n=r(6465),o={},a=new Set(["Warning: In HTML, %s cannot be a child of <%s>.%s\nThis will cause a hydration error.%s","Warning: In HTML, %s cannot be a descendant of <%s>.\nThis will cause a hydration error.%s","Warning: In HTML, text nodes cannot be a child of <%s>.\nThis will cause a hydration error.","Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\nThis will cause a hydration error.","Warning: Expected server HTML to contain a matching <%s> in <%s>.%s","Warning: Did not expect server HTML to contain a <%s> in <%s>.%s"]),l=new Set(['Warning: Expected server HTML to contain a matching text node for "%s" in <%s>.%s','Warning: Did not expect server HTML to contain the text node "%s" in <%s>.%s']),u=e=>{if("string"!=typeof e)return"text";let t=e.startsWith("Warning: ")?e:"Warning: "+e;return i(t)?"tag":s(t)?"text-in-tag":"text"},i=e=>a.has(e),s=e=>l.has(e),d=e=>{if(e){let{message:t,diff:r}=(0,n.getHydrationErrorStackInfo)(e);if(t)return[t,r]}};function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[a,l,i,...s]=t;if((0,n.testReactHydrationWarning)(a)){let e=a.startsWith("Warning: ");3===t.length&&(i="");let r=[a,l,i],n=(s[s.length-1]||"").trim();e?o.reactOutputComponentDiff=function(e,t,r,n){let o=-1,a=-1,l=u(e),i=n.split("\n").map((e,n)=>{e=e.trim();let[,l,u]=/at (\w+)( \((.*)\))?/.exec(e)||[];return u||(l===t&&-1===o?o=n:l!==r||-1!==a||(a=n)),u?"":l}).filter(Boolean).reverse(),s="";for(let e=0;e<i.length;e++){let t=i[e],r="tag"===l&&e===i.length-o-1,n="tag"===l&&e===i.length-a-1;r||n?s+="> "+" ".repeat(Math.max(2*e-2,0)+2)+"<"+t+">\n":s+=" ".repeat(2*e+2)+"<"+t+">\n"}if("text"===l){let e=" ".repeat(2*i.length);s+="+ "+e+'"'+t+'"\n'+("- "+e+'"'+r)+'"\n'}else if("text-in-tag"===l){let e=" ".repeat(2*i.length);s+="> "+e+"<"+r+">\n"+(">   "+e+'"'+t)+'"\n'}return s}(a,l,i,n):o.reactOutputComponentDiff=n,o.warning=r,o.serverContent=l,o.clientContent=i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);