/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_n"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/reflect-utils.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/reflect-utils.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    describeHasCheckingStringProperty: function() {\n        return describeHasCheckingStringProperty;\n    },\n    describeStringPropertyAccess: function() {\n        return describeStringPropertyAccess;\n    },\n    wellKnownProperties: function() {\n        return wellKnownProperties;\n    }\n});\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/;\nfunction describeStringPropertyAccess(target, prop) {\n    if (isDefinitelyAValidIdentifier.test(prop)) {\n        return \"`\" + target + \".\" + prop + \"`\";\n    }\n    return \"`\" + target + \"[\" + JSON.stringify(prop) + \"]`\";\n}\nfunction describeHasCheckingStringProperty(target, prop) {\n    const stringifiedProp = JSON.stringify(prop);\n    return \"`Reflect.has(\" + target + \", \" + stringifiedProp + \")`, `\" + stringifiedProp + \" in \" + target + \"`, or similar\";\n}\nconst wellKnownProperties = new Set([\n    'hasOwnProperty',\n    'isPrototypeOf',\n    'propertyIsEnumerable',\n    'toString',\n    'valueOf',\n    'toLocaleString',\n    // Promise prototype\n    // fallthrough\n    'then',\n    'catch',\n    'finally',\n    // React Promise extension\n    // fallthrough\n    'status',\n    // React introspection\n    'displayName',\n    // Common tested properties\n    // fallthrough\n    'toJSON',\n    '$$typeof',\n    '__esModule'\n]); //# sourceMappingURL=reflect-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/reflect-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n    enumerable: true,\n    get: function() {\n        return warnOnce;\n    }\n}));\nlet warnOnce = (_)=>{};\nif (true) {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n} //# sourceMappingURL=warn-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs0Q0FXU0E7OztlQUFBQTs7O0FBWFQsSUFBSUEsV0FBVyxDQUFDQyxLQUFlO0FBQy9CLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDLE1BQU1HLFdBQVcsSUFBSUM7SUFDckJOLFdBQVcsQ0FBQ087UUFDVixJQUFJLENBQUNGLFNBQVNHLEdBQUcsQ0FBQ0QsTUFBTTtZQUN0QkUsUUFBUUMsSUFBSSxDQUFDSDtRQUNmO1FBQ0FGLFNBQVNNLEdBQUcsQ0FBQ0o7SUFDZjtBQUNGIiwic291cmNlcyI6WyJFOlxcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHNcXHdhcm4tb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgd2Fybk9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3Qgd2FybmluZ3MgPSBuZXcgU2V0PHN0cmluZz4oKVxuICB3YXJuT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghd2FybmluZ3MuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUud2Fybihtc2cpXG4gICAgfVxuICAgIHdhcm5pbmdzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgd2Fybk9uY2UgfVxuIl0sIm5hbWVzIjpbIndhcm5PbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsIndhcm5pbmdzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsIndhcm4iLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\geist\\\\dist\\\\sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"}":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/local/target.css?{"path":"node_modules\\geist\\dist\\sans.js","import":"","arguments":[{"src":"./fonts/geist-sans/Geist-Variable.woff2","variable":"--font-geist-sans","weight":"100 900"}],"variableName":"GeistSans"} ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'GeistSans', 'GeistSans Fallback'\"},\"className\":\"__className_fb8f2c\",\"variable\":\"__variable_fb8f2c\"};\n    if(true) {\n      // 1751835600597\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvbG9jYWwvdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJub2RlX21vZHVsZXNcXFxcZ2Vpc3RcXFxcZGlzdFxcXFxzYW5zLmpzXCIsXCJpbXBvcnRcIjpcIlwiLFwiYXJndW1lbnRzXCI6W3tcInNyY1wiOlwiLi9mb250cy9nZWlzdC1zYW5zL0dlaXN0LVZhcmlhYmxlLndvZmYyXCIsXCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LXNhbnNcIixcIndlaWdodFwiOlwiMTAwIDkwMFwifV0sXCJ2YXJpYWJsZU5hbWVcIjpcIkdlaXN0U2Fuc1wifSIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQixTQUFTLGlEQUFpRDtBQUM1RSxPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBc0gsY0FBYyxzREFBc0Q7QUFDeE4sTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZm9udFxcbG9jYWxcXHRhcmdldC5jc3M/e1wicGF0aFwiOlwibm9kZV9tb2R1bGVzXFxnZWlzdFxcZGlzdFxcc2Fucy5qc1wiLFwiaW1wb3J0XCI6XCJcIixcImFyZ3VtZW50c1wiOlt7XCJzcmNcIjpcIi5cXGZvbnRzXFxnZWlzdC1zYW5zXFxHZWlzdC1WYXJpYWJsZS53b2ZmMlwiLFwidmFyaWFibGVcIjpcIi0tZm9udC1nZWlzdC1zYW5zXCIsXCJ3ZWlnaHRcIjpcIjEwMCA5MDBcIn1dLFwidmFyaWFibGVOYW1lXCI6XCJHZWlzdFNhbnNcIn18YXBwLXBhZ2VzLWJyb3dzZXIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ0dlaXN0U2FucycsICdHZWlzdFNhbnMgRmFsbGJhY2snXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9mYjhmMmNcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlX2ZiOGYyY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUxODM1NjAwNTk3XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkU6L2JvdC90cmFkaW5nYm90X2ZpbmFsL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\geist\\\\dist\\\\sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"}\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n    enumerable: true,\n    get: function() {\n        return warnOnce;\n    }\n}));\nlet warnOnce = (_)=>{};\nif (true) {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n} //# sourceMappingURL=warn-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs0Q0FXU0E7OztlQUFBQTs7O0FBWFQsSUFBSUEsV0FBVyxDQUFDQyxLQUFlO0FBQy9CLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDLE1BQU1HLFdBQVcsSUFBSUM7SUFDckJOLFdBQVcsQ0FBQ087UUFDVixJQUFJLENBQUNGLFNBQVNHLEdBQUcsQ0FBQ0QsTUFBTTtZQUN0QkUsUUFBUUMsSUFBSSxDQUFDSDtRQUNmO1FBQ0FGLFNBQVNNLEdBQUcsQ0FBQ0o7SUFDZjtBQUNGIiwic291cmNlcyI6WyJFOlxcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHNcXHdhcm4tb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgd2Fybk9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3Qgd2FybmluZ3MgPSBuZXcgU2V0PHN0cmluZz4oKVxuICB3YXJuT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghd2FybmluZ3MuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUud2Fybihtc2cpXG4gICAgfVxuICAgIHdhcm5pbmdzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgd2Fybk9uY2UgfVxuIl0sIm5hbWVzIjpbIndhcm5PbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsIndhcm5pbmdzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsIndhcm4iLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/scheduler/cjs/scheduler.development.js":
/*!*************************************************************!*\
  !*** ./node_modules/scheduler/cjs/scheduler.development.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var enableSchedulerDebugging = false;\nvar enableProfiling = false;\nvar frameYieldMs = 5;\n\nfunction push(heap, node) {\n  var index = heap.length;\n  heap.push(node);\n  siftUp(heap, node, index);\n}\nfunction peek(heap) {\n  return heap.length === 0 ? null : heap[0];\n}\nfunction pop(heap) {\n  if (heap.length === 0) {\n    return null;\n  }\n\n  var first = heap[0];\n  var last = heap.pop();\n\n  if (last !== first) {\n    heap[0] = last;\n    siftDown(heap, last, 0);\n  }\n\n  return first;\n}\n\nfunction siftUp(heap, node, i) {\n  var index = i;\n\n  while (index > 0) {\n    var parentIndex = index - 1 >>> 1;\n    var parent = heap[parentIndex];\n\n    if (compare(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node;\n      heap[index] = parent;\n      index = parentIndex;\n    } else {\n      // The parent is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction siftDown(heap, node, i) {\n  var index = i;\n  var length = heap.length;\n  var halfLength = length >>> 1;\n\n  while (index < halfLength) {\n    var leftIndex = (index + 1) * 2 - 1;\n    var left = heap[leftIndex];\n    var rightIndex = leftIndex + 1;\n    var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n    if (compare(left, node) < 0) {\n      if (rightIndex < length && compare(right, left) < 0) {\n        heap[index] = right;\n        heap[rightIndex] = node;\n        index = rightIndex;\n      } else {\n        heap[index] = left;\n        heap[leftIndex] = node;\n        index = leftIndex;\n      }\n    } else if (rightIndex < length && compare(right, node) < 0) {\n      heap[index] = right;\n      heap[rightIndex] = node;\n      index = rightIndex;\n    } else {\n      // Neither child is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction compare(a, b) {\n  // Compare sort index first, then task id.\n  var diff = a.sortIndex - b.sortIndex;\n  return diff !== 0 ? diff : a.id - b.id;\n}\n\n// TODO: Use symbols?\nvar ImmediatePriority = 1;\nvar UserBlockingPriority = 2;\nvar NormalPriority = 3;\nvar LowPriority = 4;\nvar IdlePriority = 5;\n\nfunction markTaskErrored(task, ms) {\n}\n\n/* eslint-disable no-var */\n\nvar hasPerformanceNow = typeof performance === 'object' && typeof performance.now === 'function';\n\nif (hasPerformanceNow) {\n  var localPerformance = performance;\n\n  exports.unstable_now = function () {\n    return localPerformance.now();\n  };\n} else {\n  var localDate = Date;\n  var initialTime = localDate.now();\n\n  exports.unstable_now = function () {\n    return localDate.now() - initialTime;\n  };\n} // Max 31 bit integer. The max integer size in V8 for 32-bit systems.\n// Math.pow(2, 30) - 1\n// 0b111111111111111111111111111111\n\n\nvar maxSigned31BitInt = 1073741823; // Times out immediately\n\nvar IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n\nvar USER_BLOCKING_PRIORITY_TIMEOUT = 250;\nvar NORMAL_PRIORITY_TIMEOUT = 5000;\nvar LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n\nvar IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n\nvar taskQueue = [];\nvar timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\nvar taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\nvar currentTask = null;\nvar currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrance.\n\nvar isPerformingWork = false;\nvar isHostCallbackScheduled = false;\nvar isHostTimeoutScheduled = false; // Capture local references to native APIs, in case a polyfill overrides them.\n\nvar localSetTimeout = typeof setTimeout === 'function' ? setTimeout : null;\nvar localClearTimeout = typeof clearTimeout === 'function' ? clearTimeout : null;\nvar localSetImmediate = typeof setImmediate !== 'undefined' ? setImmediate : null; // IE and Node.js + jsdom\n\nvar isInputPending = typeof navigator !== 'undefined' && navigator.scheduling !== undefined && navigator.scheduling.isInputPending !== undefined ? navigator.scheduling.isInputPending.bind(navigator.scheduling) : null;\n\nfunction advanceTimers(currentTime) {\n  // Check for tasks that are no longer delayed and add them to the queue.\n  var timer = peek(timerQueue);\n\n  while (timer !== null) {\n    if (timer.callback === null) {\n      // Timer was cancelled.\n      pop(timerQueue);\n    } else if (timer.startTime <= currentTime) {\n      // Timer fired. Transfer to the task queue.\n      pop(timerQueue);\n      timer.sortIndex = timer.expirationTime;\n      push(taskQueue, timer);\n    } else {\n      // Remaining timers are pending.\n      return;\n    }\n\n    timer = peek(timerQueue);\n  }\n}\n\nfunction handleTimeout(currentTime) {\n  isHostTimeoutScheduled = false;\n  advanceTimers(currentTime);\n\n  if (!isHostCallbackScheduled) {\n    if (peek(taskQueue) !== null) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    } else {\n      var firstTimer = peek(timerQueue);\n\n      if (firstTimer !== null) {\n        requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n      }\n    }\n  }\n}\n\nfunction flushWork(hasTimeRemaining, initialTime) {\n\n\n  isHostCallbackScheduled = false;\n\n  if (isHostTimeoutScheduled) {\n    // We scheduled a timeout but it's no longer needed. Cancel it.\n    isHostTimeoutScheduled = false;\n    cancelHostTimeout();\n  }\n\n  isPerformingWork = true;\n  var previousPriorityLevel = currentPriorityLevel;\n\n  try {\n    if (enableProfiling) {\n      try {\n        return workLoop(hasTimeRemaining, initialTime);\n      } catch (error) {\n        if (currentTask !== null) {\n          var currentTime = exports.unstable_now();\n          markTaskErrored(currentTask, currentTime);\n          currentTask.isQueued = false;\n        }\n\n        throw error;\n      }\n    } else {\n      // No catch in prod code path.\n      return workLoop(hasTimeRemaining, initialTime);\n    }\n  } finally {\n    currentTask = null;\n    currentPriorityLevel = previousPriorityLevel;\n    isPerformingWork = false;\n  }\n}\n\nfunction workLoop(hasTimeRemaining, initialTime) {\n  var currentTime = initialTime;\n  advanceTimers(currentTime);\n  currentTask = peek(taskQueue);\n\n  while (currentTask !== null && !(enableSchedulerDebugging )) {\n    if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {\n      // This currentTask hasn't expired, and we've reached the deadline.\n      break;\n    }\n\n    var callback = currentTask.callback;\n\n    if (typeof callback === 'function') {\n      currentTask.callback = null;\n      currentPriorityLevel = currentTask.priorityLevel;\n      var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n\n      var continuationCallback = callback(didUserCallbackTimeout);\n      currentTime = exports.unstable_now();\n\n      if (typeof continuationCallback === 'function') {\n        currentTask.callback = continuationCallback;\n      } else {\n\n        if (currentTask === peek(taskQueue)) {\n          pop(taskQueue);\n        }\n      }\n\n      advanceTimers(currentTime);\n    } else {\n      pop(taskQueue);\n    }\n\n    currentTask = peek(taskQueue);\n  } // Return whether there's additional work\n\n\n  if (currentTask !== null) {\n    return true;\n  } else {\n    var firstTimer = peek(timerQueue);\n\n    if (firstTimer !== null) {\n      requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n    }\n\n    return false;\n  }\n}\n\nfunction unstable_runWithPriority(priorityLevel, eventHandler) {\n  switch (priorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n    case LowPriority:\n    case IdlePriority:\n      break;\n\n    default:\n      priorityLevel = NormalPriority;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_next(eventHandler) {\n  var priorityLevel;\n\n  switch (currentPriorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n      // Shift down to normal priority\n      priorityLevel = NormalPriority;\n      break;\n\n    default:\n      // Anything lower than normal priority should remain at the current level.\n      priorityLevel = currentPriorityLevel;\n      break;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_wrapCallback(callback) {\n  var parentPriorityLevel = currentPriorityLevel;\n  return function () {\n    // This is a fork of runWithPriority, inlined for performance.\n    var previousPriorityLevel = currentPriorityLevel;\n    currentPriorityLevel = parentPriorityLevel;\n\n    try {\n      return callback.apply(this, arguments);\n    } finally {\n      currentPriorityLevel = previousPriorityLevel;\n    }\n  };\n}\n\nfunction unstable_scheduleCallback(priorityLevel, callback, options) {\n  var currentTime = exports.unstable_now();\n  var startTime;\n\n  if (typeof options === 'object' && options !== null) {\n    var delay = options.delay;\n\n    if (typeof delay === 'number' && delay > 0) {\n      startTime = currentTime + delay;\n    } else {\n      startTime = currentTime;\n    }\n  } else {\n    startTime = currentTime;\n  }\n\n  var timeout;\n\n  switch (priorityLevel) {\n    case ImmediatePriority:\n      timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n      break;\n\n    case UserBlockingPriority:\n      timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n      break;\n\n    case IdlePriority:\n      timeout = IDLE_PRIORITY_TIMEOUT;\n      break;\n\n    case LowPriority:\n      timeout = LOW_PRIORITY_TIMEOUT;\n      break;\n\n    case NormalPriority:\n    default:\n      timeout = NORMAL_PRIORITY_TIMEOUT;\n      break;\n  }\n\n  var expirationTime = startTime + timeout;\n  var newTask = {\n    id: taskIdCounter++,\n    callback: callback,\n    priorityLevel: priorityLevel,\n    startTime: startTime,\n    expirationTime: expirationTime,\n    sortIndex: -1\n  };\n\n  if (startTime > currentTime) {\n    // This is a delayed task.\n    newTask.sortIndex = startTime;\n    push(timerQueue, newTask);\n\n    if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n      // All tasks are delayed, and this is the task with the earliest delay.\n      if (isHostTimeoutScheduled) {\n        // Cancel an existing timeout.\n        cancelHostTimeout();\n      } else {\n        isHostTimeoutScheduled = true;\n      } // Schedule a timeout.\n\n\n      requestHostTimeout(handleTimeout, startTime - currentTime);\n    }\n  } else {\n    newTask.sortIndex = expirationTime;\n    push(taskQueue, newTask);\n    // wait until the next time we yield.\n\n\n    if (!isHostCallbackScheduled && !isPerformingWork) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    }\n  }\n\n  return newTask;\n}\n\nfunction unstable_pauseExecution() {\n}\n\nfunction unstable_continueExecution() {\n\n  if (!isHostCallbackScheduled && !isPerformingWork) {\n    isHostCallbackScheduled = true;\n    requestHostCallback(flushWork);\n  }\n}\n\nfunction unstable_getFirstCallbackNode() {\n  return peek(taskQueue);\n}\n\nfunction unstable_cancelCallback(task) {\n  // remove from the queue because you can't remove arbitrary nodes from an\n  // array based heap, only the first one.)\n\n\n  task.callback = null;\n}\n\nfunction unstable_getCurrentPriorityLevel() {\n  return currentPriorityLevel;\n}\n\nvar isMessageLoopRunning = false;\nvar scheduledHostCallback = null;\nvar taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n// thread, like user events. By default, it yields multiple times per frame.\n// It does not attempt to align with frame boundaries, since most tasks don't\n// need to be frame aligned; for those that do, use requestAnimationFrame.\n\nvar frameInterval = frameYieldMs;\nvar startTime = -1;\n\nfunction shouldYieldToHost() {\n  var timeElapsed = exports.unstable_now() - startTime;\n\n  if (timeElapsed < frameInterval) {\n    // The main thread has only been blocked for a really short amount of time;\n    // smaller than a single frame. Don't yield yet.\n    return false;\n  } // The main thread has been blocked for a non-negligible amount of time. We\n\n\n  return true;\n}\n\nfunction requestPaint() {\n\n}\n\nfunction forceFrameRate(fps) {\n  if (fps < 0 || fps > 125) {\n    // Using console['error'] to evade Babel and ESLint\n    console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n    return;\n  }\n\n  if (fps > 0) {\n    frameInterval = Math.floor(1000 / fps);\n  } else {\n    // reset the framerate\n    frameInterval = frameYieldMs;\n  }\n}\n\nvar performWorkUntilDeadline = function () {\n  if (scheduledHostCallback !== null) {\n    var currentTime = exports.unstable_now(); // Keep track of the start time so we can measure how long the main thread\n    // has been blocked.\n\n    startTime = currentTime;\n    var hasTimeRemaining = true; // If a scheduler task throws, exit the current browser task so the\n    // error can be observed.\n    //\n    // Intentionally not using a try-catch, since that makes some debugging\n    // techniques harder. Instead, if `scheduledHostCallback` errors, then\n    // `hasMoreWork` will remain true, and we'll continue the work loop.\n\n    var hasMoreWork = true;\n\n    try {\n      hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n    } finally {\n      if (hasMoreWork) {\n        // If there's more work, schedule the next message event at the end\n        // of the preceding one.\n        schedulePerformWorkUntilDeadline();\n      } else {\n        isMessageLoopRunning = false;\n        scheduledHostCallback = null;\n      }\n    }\n  } else {\n    isMessageLoopRunning = false;\n  } // Yielding to the browser will give it a chance to paint, so we can\n};\n\nvar schedulePerformWorkUntilDeadline;\n\nif (typeof localSetImmediate === 'function') {\n  // Node.js and old IE.\n  // There's a few reasons for why we prefer setImmediate.\n  //\n  // Unlike MessageChannel, it doesn't prevent a Node.js process from exiting.\n  // (Even though this is a DOM fork of the Scheduler, you could get here\n  // with a mix of Node.js 15+, which has a MessageChannel, and jsdom.)\n  // https://github.com/facebook/react/issues/20756\n  //\n  // But also, it runs earlier which is the semantic we want.\n  // If other browsers ever implement it, it's better to use it.\n  // Although both of these would be inferior to native scheduling.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetImmediate(performWorkUntilDeadline);\n  };\n} else if (typeof MessageChannel !== 'undefined') {\n  // DOM and Worker environments.\n  // We prefer MessageChannel because of the 4ms setTimeout clamping.\n  var channel = new MessageChannel();\n  var port = channel.port2;\n  channel.port1.onmessage = performWorkUntilDeadline;\n\n  schedulePerformWorkUntilDeadline = function () {\n    port.postMessage(null);\n  };\n} else {\n  // We should only fallback here in non-browser environments.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetTimeout(performWorkUntilDeadline, 0);\n  };\n}\n\nfunction requestHostCallback(callback) {\n  scheduledHostCallback = callback;\n\n  if (!isMessageLoopRunning) {\n    isMessageLoopRunning = true;\n    schedulePerformWorkUntilDeadline();\n  }\n}\n\nfunction requestHostTimeout(callback, ms) {\n  taskTimeoutID = localSetTimeout(function () {\n    callback(exports.unstable_now());\n  }, ms);\n}\n\nfunction cancelHostTimeout() {\n  localClearTimeout(taskTimeoutID);\n  taskTimeoutID = -1;\n}\n\nvar unstable_requestPaint = requestPaint;\nvar unstable_Profiling =  null;\n\nexports.unstable_IdlePriority = IdlePriority;\nexports.unstable_ImmediatePriority = ImmediatePriority;\nexports.unstable_LowPriority = LowPriority;\nexports.unstable_NormalPriority = NormalPriority;\nexports.unstable_Profiling = unstable_Profiling;\nexports.unstable_UserBlockingPriority = UserBlockingPriority;\nexports.unstable_cancelCallback = unstable_cancelCallback;\nexports.unstable_continueExecution = unstable_continueExecution;\nexports.unstable_forceFrameRate = forceFrameRate;\nexports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\nexports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\nexports.unstable_next = unstable_next;\nexports.unstable_pauseExecution = unstable_pauseExecution;\nexports.unstable_requestPaint = unstable_requestPaint;\nexports.unstable_runWithPriority = unstable_runWithPriority;\nexports.unstable_scheduleCallback = unstable_scheduleCallback;\nexports.unstable_shouldYield = shouldYieldToHost;\nexports.unstable_wrapCallback = unstable_wrapCallback;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/scheduler/cjs/scheduler.development.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/scheduler/index.js":
/*!*****************************************!*\
  !*** ./node_modules/scheduler/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(pages-dir-browser)/./node_modules/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zY2hlZHVsZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsSUFBSSxLQUFxQyxFQUFFLEVBRTFDLENBQUM7QUFDRixFQUFFLHVKQUEwRDtBQUM1RCIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcc2NoZWR1bGVyXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvc2NoZWR1bGVyLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3NjaGVkdWxlci5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/scheduler/index.js\n"));

/***/ })

}]);