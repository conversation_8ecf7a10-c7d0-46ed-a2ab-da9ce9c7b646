"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_a"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/assign-location.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/client/assign-location.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"assignLocation\", ({\n    enumerable: true,\n    get: function() {\n        return assignLocation;\n    }\n}));\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nfunction assignLocation(location, url) {\n    if (location.startsWith('.')) {\n        const urlBase = url.origin + url.pathname;\n        return new URL(// new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n        // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n        (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location);\n    }\n    return new URL((0, _addbasepath.addBasePath)(location), url.href);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=assign-location.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/assign-location.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-announcer.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppRouterAnnouncer\", ({\n    enumerable: true,\n    get: function() {\n        return AppRouterAnnouncer;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst ANNOUNCER_TYPE = 'next-route-announcer';\nconst ANNOUNCER_ID = '__next-route-announcer__';\nfunction getAnnouncerNode() {\n    var _existingAnnouncer_shadowRoot;\n    const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0];\n    if (existingAnnouncer == null ? void 0 : (_existingAnnouncer_shadowRoot = existingAnnouncer.shadowRoot) == null ? void 0 : _existingAnnouncer_shadowRoot.childNodes[0]) {\n        return existingAnnouncer.shadowRoot.childNodes[0];\n    } else {\n        const container = document.createElement(ANNOUNCER_TYPE);\n        container.style.cssText = 'position:absolute';\n        const announcer = document.createElement('div');\n        announcer.ariaLive = 'assertive';\n        announcer.id = ANNOUNCER_ID;\n        announcer.role = 'alert';\n        announcer.style.cssText = 'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal';\n        // Use shadow DOM here to avoid any potential CSS bleed\n        const shadow = container.attachShadow({\n            mode: 'open'\n        });\n        shadow.appendChild(announcer);\n        document.body.appendChild(container);\n        return announcer;\n    }\n}\nfunction AppRouterAnnouncer(param) {\n    let { tree } = param;\n    const [portalNode, setPortalNode] = (0, _react.useState)(null);\n    (0, _react.useEffect)(()=>{\n        const announcer = getAnnouncerNode();\n        setPortalNode(announcer);\n        return ()=>{\n            const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0];\n            if (container == null ? void 0 : container.isConnected) {\n                document.body.removeChild(container);\n            }\n        };\n    }, []);\n    const [routeAnnouncement, setRouteAnnouncement] = (0, _react.useState)('');\n    const previousTitle = (0, _react.useRef)(undefined);\n    (0, _react.useEffect)(()=>{\n        let currentTitle = '';\n        if (document.title) {\n            currentTitle = document.title;\n        } else {\n            const pageHeader = document.querySelector('h1');\n            if (pageHeader) {\n                currentTitle = pageHeader.innerText || pageHeader.textContent || '';\n            }\n        }\n        // Only announce the title change, but not for the first load because screen\n        // readers do that automatically.\n        if (previousTitle.current !== undefined && previousTitle.current !== currentTitle) {\n            setRouteAnnouncement(currentTitle);\n        }\n        previousTitle.current = currentTitle;\n    }, [\n        tree\n    ]);\n    return portalNode ? /*#__PURE__*/ (0, _reactdom.createPortal)(routeAnnouncement, portalNode) : null;\n}\n_c = AppRouterAnnouncer;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-announcer.js.map\nvar _c;\n$RefreshReg$(_c, \"AppRouterAnnouncer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-headers.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_HEADER: function() {\n        return ACTION_HEADER;\n    },\n    FLIGHT_HEADERS: function() {\n        return FLIGHT_HEADERS;\n    },\n    NEXT_DID_POSTPONE_HEADER: function() {\n        return NEXT_DID_POSTPONE_HEADER;\n    },\n    NEXT_HMR_REFRESH_HEADER: function() {\n        return NEXT_HMR_REFRESH_HEADER;\n    },\n    NEXT_IS_PRERENDER_HEADER: function() {\n        return NEXT_IS_PRERENDER_HEADER;\n    },\n    NEXT_REWRITTEN_PATH_HEADER: function() {\n        return NEXT_REWRITTEN_PATH_HEADER;\n    },\n    NEXT_REWRITTEN_QUERY_HEADER: function() {\n        return NEXT_REWRITTEN_QUERY_HEADER;\n    },\n    NEXT_ROUTER_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_SEGMENT_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_STALE_TIME_HEADER: function() {\n        return NEXT_ROUTER_STALE_TIME_HEADER;\n    },\n    NEXT_ROUTER_STATE_TREE_HEADER: function() {\n        return NEXT_ROUTER_STATE_TREE_HEADER;\n    },\n    NEXT_RSC_UNION_QUERY: function() {\n        return NEXT_RSC_UNION_QUERY;\n    },\n    NEXT_URL: function() {\n        return NEXT_URL;\n    },\n    RSC_CONTENT_TYPE_HEADER: function() {\n        return RSC_CONTENT_TYPE_HEADER;\n    },\n    RSC_HEADER: function() {\n        return RSC_HEADER;\n    }\n});\nconst RSC_HEADER = 'RSC';\nconst ACTION_HEADER = 'Next-Action';\nconst NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree';\nconst NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch';\nconst NEXT_ROUTER_SEGMENT_PREFETCH_HEADER = 'Next-Router-Segment-Prefetch';\nconst NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh';\nconst NEXT_URL = 'Next-Url';\nconst RSC_CONTENT_TYPE_HEADER = 'text/x-component';\nconst FLIGHT_HEADERS = [\n    RSC_HEADER,\n    NEXT_ROUTER_STATE_TREE_HEADER,\n    NEXT_ROUTER_PREFETCH_HEADER,\n    NEXT_HMR_REFRESH_HEADER,\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n];\nconst NEXT_RSC_UNION_QUERY = '_rsc';\nconst NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time';\nconst NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed';\nconst NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path';\nconst NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query';\nconst NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-headers.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _usereducer = __webpack_require__(/*! ./use-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _appcallserver = __webpack_require__(/*! ../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null\n    };\n}\n/**\n * Server response that only patches the cache and tree.\n */ function useChangeByServerResponse(dispatch) {\n    return (0, _react.useCallback)((param)=>{\n        let { previousTree, serverResponse } = param;\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                type: _routerreducertypes.ACTION_SERVER_PATCH,\n                previousTree,\n                serverResponse\n            });\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction useNavigate(dispatch) {\n    return (0, _react.useCallback)((href, navigateType, shouldScroll)=>{\n        const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n        if (false) {}\n        return dispatch({\n            type: _routerreducertypes.ACTION_NAVIGATE,\n            url,\n            isExternalUrl: isExternalURL(url),\n            locationSearch: location.search,\n            shouldScroll: shouldScroll != null ? shouldScroll : true,\n            navigateType,\n            allowAliasing: true\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    _s();\n    let { actionQueue, assetPrefix, globalError } = param;\n    const [state, dispatch] = (0, _usereducer.useReducer)(actionQueue);\n    const { canonicalUrl } = (0, _usereducer.useUnwrapState)(state);\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    const changeByServerResponse = useChangeByServerResponse(dispatch);\n    const navigate = useNavigate(dispatch);\n    (0, _appcallserver.useServerActionDispatcher)(dispatch);\n    /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */ const appRouter = (0, _react.useMemo)(()=>{\n        const routerInstance = {\n            back: ()=>window.history.back(),\n            forward: ()=>window.history.forward(),\n            prefetch:  false ? // cache. So we don't need to dispatch an action.\n            0 : (href, options)=>{\n                // Use the old prefetch implementation.\n                const url = createPrefetchURL(href);\n                if (url !== null) {\n                    var _options_kind;\n                    // The prefetch reducer doesn't actually update any state or\n                    // trigger a rerender. It just writes to a mutable cache. So we\n                    // shouldn't bother calling setState/dispatch; we can just re-run\n                    // the reducer directly using the current state.\n                    // TODO: Refactor this away from a \"reducer\" so it's\n                    // less confusing.\n                    (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                        type: _routerreducertypes.ACTION_PREFETCH,\n                        url,\n                        kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n                    });\n                }\n            },\n            replace: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'replace', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            push: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'push', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            refresh: ()=>{\n                (0, _react.startTransition)(()=>{\n                    dispatch({\n                        type: _routerreducertypes.ACTION_REFRESH,\n                        origin: window.location.origin\n                    });\n                });\n            },\n            hmrRefresh: ()=>{\n                if (false) {} else {\n                    (0, _react.startTransition)(()=>{\n                        dispatch({\n                            type: _routerreducertypes.ACTION_HMR_REFRESH,\n                            origin: window.location.origin\n                        });\n                    });\n                }\n            }\n        };\n        return routerInstance;\n    }, [\n        actionQueue,\n        dispatch,\n        navigate\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Exists for debugging purposes. Don't use in application code.\n        if (window.next) {\n            window.next.router = appRouter;\n        }\n    }, [\n        appRouter\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = (0, _usereducer.useUnwrapState)(state);\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: appRouter,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            appRouter,\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            dispatch({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, [\n        dispatch\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    appRouter.push(url, {});\n                } else {\n                    appRouter.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, [\n        appRouter\n    ]);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = (0, _usereducer.useUnwrapState)(state);\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location1 = window.location;\n            if (pushRef.pendingPush) {\n                location1.assign(canonicalUrl);\n            } else {\n                location1.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(window.location.href),\n                    tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n                });\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, [\n        dispatch\n    ]);\n    const { cache, tree, nextUrl, focusAndScrollRef } = (0, _usereducer.useUnwrapState)(state);\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            changeByServerResponse,\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        changeByServerResponse,\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: (0, _usereducer.useUnwrapState)(state)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: appRouter,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_s(Router, \"bU8t8nCPb2ycaFr1siwKA2Gych0=\", false, function() {\n    return [\n        useChangeByServerResponse,\n        useNavigate\n    ];\n});\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s1();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s1(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-page.js ***!
  \*****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientPageRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientPageRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientPageRoot(param) {\n    let { Component, searchParams, params, promises } = param;\n    if (false) {} else {\n        const { createRenderSearchParamsFromClient } = __webpack_require__(/*! ../request/search-params.browser */ \"(app-pages-browser)/./node_modules/next/dist/client/request/search-params.browser.js\");\n        const clientSearchParams = createRenderSearchParamsFromClient(searchParams);\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/client/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            params: clientParams,\n            searchParams: clientSearchParams\n        });\n    }\n}\n_c = ClientPageRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-page.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientPageRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-segment.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientSegmentRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientSegmentRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientSegmentRoot(param) {\n    let { Component, slots, params, promise } = param;\n    if (false) {} else {\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/client/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            ...slots,\n            params: clientParams\n        });\n    }\n}\n_c = ClientSegmentRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-segment.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientSegmentRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DevRootHTTPAccessFallbackBoundary: function() {\n        return DevRootHTTPAccessFallbackBoundary;\n    },\n    bailOnRootNotFound: function() {\n        return bailOnRootNotFound;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _errorboundary = __webpack_require__(/*! ./http-access-fallback/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\nfunction bailOnRootNotFound() {\n    throw Object.defineProperty(new Error('notFound() is not allowed to use in root layout'), \"__NEXT_ERROR_CODE\", {\n        value: \"E192\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction NotAllowedRootHTTPFallbackError() {\n    bailOnRootNotFound();\n    return null;\n}\n_c = NotAllowedRootHTTPFallbackError;\nfunction DevRootHTTPAccessFallbackBoundary(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.HTTPAccessFallbackBoundary, {\n        notFound: /*#__PURE__*/ (0, _jsxruntime.jsx)(NotAllowedRootHTTPFallbackError, {}),\n        children: children\n    });\n}\n_c1 = DevRootHTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-root-http-access-fallback-boundary.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"NotAllowedRootHTTPFallbackError\");\n$RefreshReg$(_c1, \"DevRootHTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZGV2LXJvb3QtaHR0cC1hY2Nlc3MtZmFsbGJhY2stYm91bmRhcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBZWdCQSxpQ0FBaUM7ZUFBakNBOztJQVRBQyxrQkFBa0I7ZUFBbEJBOzs7Ozs0RUFKRTsyQ0FDeUI7QUFHcEMsU0FBU0E7SUFDZCxNQUFNLHFCQUE0RCxDQUE1RCxJQUFJQyxNQUFNLG9EQUFWO2VBQUE7b0JBQUE7c0JBQUE7SUFBMkQ7QUFDbkU7QUFFQTtJQUNFRDtJQUNBLE9BQU87QUFDVDtLQUhTRTtBQUtGLDJDQUEyQyxLQUlqRDtJQUppRCxNQUNoREMsUUFBUSxFQUdULEdBSmlEO0lBS2hELHFCQUNFLHFCQUFDQyxlQUFBQSwwQkFBMEI7UUFBQ0MsVUFBQUEsV0FBQUEsR0FBVSxxQkFBQ0gsaUNBQUFBLENBQUFBO2tCQUNwQ0M7O0FBR1A7TUFWZ0JKIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGRldi1yb290LWh0dHAtYWNjZXNzLWZhbGxiYWNrLWJvdW5kYXJ5LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgSFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnkgfSBmcm9tICcuL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5J1xuXG4vLyBUT0RPOiBlcnJvciBvbiB1c2luZyBmb3JiaWRkZW4gYW5kIHVuYXV0aG9yaXplZCBpbiByb290IGxheW91dFxuZXhwb3J0IGZ1bmN0aW9uIGJhaWxPblJvb3ROb3RGb3VuZCgpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdub3RGb3VuZCgpIGlzIG5vdCBhbGxvd2VkIHRvIHVzZSBpbiByb290IGxheW91dCcpXG59XG5cbmZ1bmN0aW9uIE5vdEFsbG93ZWRSb290SFRUUEZhbGxiYWNrRXJyb3IoKSB7XG4gIGJhaWxPblJvb3ROb3RGb3VuZCgpXG4gIHJldHVybiBudWxsXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBEZXZSb290SFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnkoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxIVFRQQWNjZXNzRmFsbGJhY2tCb3VuZGFyeSBub3RGb3VuZD17PE5vdEFsbG93ZWRSb290SFRUUEZhbGxiYWNrRXJyb3IgLz59PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvSFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnk+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJEZXZSb290SFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnkiLCJiYWlsT25Sb290Tm90Rm91bmQiLCJFcnJvciIsIk5vdEFsbG93ZWRSb290SFRUUEZhbGxiYWNrRXJyb3IiLCJjaGlsZHJlbiIsIkhUVFBBY2Nlc3NGYWxsYmFja0JvdW5kYXJ5Iiwibm90Rm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/error-boundary.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorBoundary: function() {\n        return ErrorBoundary;\n    },\n    ErrorBoundaryHandler: function() {\n        return ErrorBoundaryHandler;\n    },\n    GlobalError: function() {\n        return GlobalError;\n    },\n    // Exported so that the import signature in the loaders can be identical to user\n    // supplied custom global error signatures.\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ./navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _isnextroutererror = __webpack_require__(/*! ./is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst workAsyncStorage =  false ? 0 : undefined;\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    text: {\n        fontSize: '14px',\n        fontWeight: 400,\n        lineHeight: '28px',\n        margin: '0 8px'\n    }\n};\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError(param) {\n    let { error } = param;\n    if (workAsyncStorage) {\n        const store = workAsyncStorage.getStore();\n        if ((store == null ? void 0 : store.isRevalidate) || (store == null ? void 0 : store.isStaticGeneration)) {\n            console.error(error);\n            throw error;\n        }\n    }\n    return null;\n}\n_c = HandleISRError;\nclass ErrorBoundaryHandler extends _react.default.Component {\n    static getDerivedStateFromError(error) {\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            // Re-throw if an expected internal Next.js router error occurs\n            // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n            throw error;\n        }\n        return {\n            error\n        };\n    }\n    static getDerivedStateFromProps(props, state) {\n        const { error } = state;\n        // if we encounter an error while\n        // a navigation is pending we shouldn't render\n        // the error boundary and instead should fallback\n        // to a hard navigation to attempt recovering\n        if (false) {}\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.error) {\n            return {\n                error: null,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            error: state.error,\n            previousPathname: props.pathname\n        };\n    }\n    // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n    render() {\n        if (this.state.error) {\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(HandleISRError, {\n                        error: this.state.error\n                    }),\n                    this.props.errorStyles,\n                    this.props.errorScripts,\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(this.props.errorComponent, {\n                        error: this.state.error,\n                        reset: this.reset\n                    })\n                ]\n            });\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.reset = ()=>{\n            this.setState({\n                error: null\n            });\n        };\n        this.state = {\n            error: null,\n            previousPathname: this.props.pathname\n        };\n    }\n}\nfunction GlobalError(param) {\n    let { error } = param;\n    const digest = error == null ? void 0 : error.digest;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"html\", {\n        id: \"__next_error__\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"head\", {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(HandleISRError, {\n                        error: error\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                        style: styles.error,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"h2\", {\n                                    style: styles.text,\n                                    children: [\n                                        \"Application error: a \",\n                                        digest ? 'server' : 'client',\n                                        \"-side exception has occurred while loading \",\n                                        window.location.hostname,\n                                        \" (see the\",\n                                        ' ',\n                                        digest ? 'server logs' : 'browser console',\n                                        \" for more information).\"\n                                    ]\n                                }),\n                                digest ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    style: styles.text,\n                                    children: \"Digest: \" + digest\n                                }) : null\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = GlobalError;\nconst _default = GlobalError;\nfunction ErrorBoundary(param) {\n    let { errorComponent, errorStyles, errorScripts, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these errors can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    if (errorComponent) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorBoundaryHandler, {\n            pathname: pathname,\n            errorComponent: errorComponent,\n            errorStyles: errorStyles,\n            errorScripts: errorScripts,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = ErrorBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"HandleISRError\");\n$RefreshReg$(_c1, \"GlobalError\");\n$RefreshReg$(_c2, \"ErrorBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"attachHydrationErrorState\", ({\n    enumerable: true,\n    get: function() {\n        return attachHydrationErrorState;\n    }\n}));\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nfunction attachHydrationErrorState(error) {\n    let parsedHydrationErrorState = {};\n    const isHydrationWarning = (0, _ishydrationerror.testReactHydrationWarning)(error.message);\n    const isHydrationRuntimeError = (0, _ishydrationerror.isHydrationError)(error);\n    // If it's not hydration warnings or errors, skip\n    if (!(isHydrationRuntimeError || isHydrationWarning)) {\n        return;\n    }\n    const reactHydrationDiffSegments = (0, _hydrationerrorinfo.getReactHydrationDiffSegments)(error.message);\n    // If the reactHydrationDiffSegments exists\n    // and the diff (reactHydrationDiffSegments[1]) exists\n    // e.g. the hydration diff log error.\n    if (reactHydrationDiffSegments) {\n        const diff = reactHydrationDiffSegments[1];\n        parsedHydrationErrorState = {\n            ...error.details,\n            ..._hydrationerrorinfo.hydrationErrorState,\n            // If diff is present in error, we don't need to pick up the console logged warning.\n            // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n            // - if hydration error no diff, then leverage the one from the hydration diff log.\n            warning: (diff && !isHydrationWarning ? null : _hydrationerrorinfo.hydrationErrorState.warning) || [\n                (0, _ishydrationerror.getDefaultHydrationErrorMessage)()\n            ],\n            // When it's hydration diff log, do not show notes section.\n            // This condition is only for the 1st squashed error.\n            notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n            reactOutputComponentDiff: diff\n        };\n        // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (!_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff && diff) {\n            _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff = diff;\n        }\n        // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n        if (!diff && isHydrationRuntimeError && _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    } else {\n        // Normal runtime error, where it doesn't contain the hydration diff.\n        // If there's any extra information in the error message to display,\n        // append it to the error message details property\n        if (_hydrationerrorinfo.hydrationErrorState.warning) {\n            // The patched console.error found hydration errors logged by React\n            // Append the logged warning to the error message\n            parsedHydrationErrorState = {\n                ...error.details,\n                // It contains the warning, component stack, server and client tag names\n                ..._hydrationerrorinfo.hydrationErrorState\n            };\n        }\n        // Consume the cached hydration diff.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    }\n    // If it's a hydration error, store the hydration error state into the error object\n    ;\n    error.details = parsedHydrationErrorState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=attach-hydration-error-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/console-error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/console-error.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createUnhandledError: function() {\n        return createUnhandledError;\n    },\n    getUnhandledErrorType: function() {\n        return getUnhandledErrorType;\n    },\n    isUnhandledConsoleOrRejection: function() {\n        return isUnhandledConsoleOrRejection;\n    }\n});\nconst digestSym = Symbol.for('next.console.error.digest');\nconst consoleTypeSym = Symbol.for('next.console.error.type');\nfunction createUnhandledError(message, environmentName) {\n    const error = typeof message === 'string' ? Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    }) : message;\n    error[digestSym] = 'NEXT_UNHANDLED_ERROR';\n    error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error';\n    if (environmentName && !error.environmentName) {\n        error.environmentName = environmentName;\n    }\n    return error;\n}\nconst isUnhandledConsoleOrRejection = (error)=>{\n    return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR';\n};\nconst getUnhandledErrorType = (error)=>{\n    return error[consoleTypeSym];\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/enqueue-client-error.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/enqueue-client-error.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Dedupe the two consecutive errors: If the previous one is same as current one, ignore the current one.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"enqueueConsecutiveDedupedError\", ({\n    enumerable: true,\n    get: function() {\n        return enqueueConsecutiveDedupedError;\n    }\n}));\nfunction enqueueConsecutiveDedupedError(queue, error) {\n    const previousError = queue[queue.length - 1];\n    // Compare the error stack to dedupe the consecutive errors\n    if (previousError && previousError.stack === error.stack) {\n        return;\n    }\n    queue.push(error);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=enqueue-client-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3JzL2VucXVldWUtY2xpZW50LWVycm9yLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUF5Rzs7Ozs7a0VBQ3pGQTs7O2VBQUFBOzs7QUFBVCxTQUFTQSwrQkFDZEMsS0FBbUIsRUFDbkJDLEtBQVk7SUFFWixNQUFNQyxnQkFBZ0JGLEtBQUssQ0FBQ0EsTUFBTUcsTUFBTSxHQUFHLEVBQUU7SUFDN0MsMkRBQTJEO0lBQzNELElBQUlELGlCQUFpQkEsY0FBY0UsS0FBSyxLQUFLSCxNQUFNRyxLQUFLLEVBQUU7UUFDeEQ7SUFDRjtJQUNBSixNQUFNSyxJQUFJLENBQUNKO0FBQ2IiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xcZXJyb3JzXFxlbnF1ZXVlLWNsaWVudC1lcnJvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBEZWR1cGUgdGhlIHR3byBjb25zZWN1dGl2ZSBlcnJvcnM6IElmIHRoZSBwcmV2aW91cyBvbmUgaXMgc2FtZSBhcyBjdXJyZW50IG9uZSwgaWdub3JlIHRoZSBjdXJyZW50IG9uZS5cbmV4cG9ydCBmdW5jdGlvbiBlbnF1ZXVlQ29uc2VjdXRpdmVEZWR1cGVkRXJyb3IoXG4gIHF1ZXVlOiBBcnJheTxFcnJvcj4sXG4gIGVycm9yOiBFcnJvclxuKSB7XG4gIGNvbnN0IHByZXZpb3VzRXJyb3IgPSBxdWV1ZVtxdWV1ZS5sZW5ndGggLSAxXVxuICAvLyBDb21wYXJlIHRoZSBlcnJvciBzdGFjayB0byBkZWR1cGUgdGhlIGNvbnNlY3V0aXZlIGVycm9yc1xuICBpZiAocHJldmlvdXNFcnJvciAmJiBwcmV2aW91c0Vycm9yLnN0YWNrID09PSBlcnJvci5zdGFjaykge1xuICAgIHJldHVyblxuICB9XG4gIHF1ZXVlLnB1c2goZXJyb3IpXG59XG4iXSwibmFtZXMiOlsiZW5xdWV1ZUNvbnNlY3V0aXZlRGVkdXBlZEVycm9yIiwicXVldWUiLCJlcnJvciIsInByZXZpb3VzRXJyb3IiLCJsZW5ndGgiLCJzdGFjayIsInB1c2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/enqueue-client-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/hydration-error-info.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getHydrationWarningType: function() {\n        return getHydrationWarningType;\n    },\n    getReactHydrationDiffSegments: function() {\n        return getReactHydrationDiffSegments;\n    },\n    hydrationErrorState: function() {\n        return hydrationErrorState;\n    },\n    storeHydrationErrorStateFromConsoleArgs: function() {\n        return storeHydrationErrorStateFromConsoleArgs;\n    }\n});\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst hydrationErrorState = {};\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n    'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n    \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n    'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n    'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s'\n]);\nconst textAndTagsMismatchWarnings = new Set([\n    'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n    'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s'\n]);\nconst getHydrationWarningType = (message)=>{\n    if (typeof message !== 'string') {\n        // TODO: Doesn't make sense to treat no message as a hydration error message.\n        // We should bail out somewhere earlier.\n        return 'text';\n    }\n    const normalizedMessage = message.startsWith('Warning: ') ? message : \"Warning: \" + message;\n    if (isHtmlTagsWarning(normalizedMessage)) return 'tag';\n    if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag';\n    return 'text';\n};\nconst isHtmlTagsWarning = (message)=>htmlTagsWarnings.has(message);\nconst isTextInTagsMismatchWarning = (msg)=>textAndTagsMismatchWarnings.has(msg);\nconst getReactHydrationDiffSegments = (msg)=>{\n    if (msg) {\n        const { message, diff } = (0, _ishydrationerror.getHydrationErrorStackInfo)(msg);\n        if (message) return [\n            message,\n            diff\n        ];\n    }\n    return undefined;\n};\nfunction storeHydrationErrorStateFromConsoleArgs() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    let [msg, firstContent, secondContent, ...rest] = args;\n    if ((0, _ishydrationerror.testReactHydrationWarning)(msg)) {\n        // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n        // when the 3rd argument is not the component stack but an empty string\n        const isReact18 = msg.startsWith('Warning: ');\n        // For some warnings, there's only 1 argument for template.\n        // The second argument is the diff or component stack.\n        if (args.length === 3) {\n            secondContent = '';\n        }\n        const warning = [\n            // remove the last %s from the message\n            msg,\n            firstContent,\n            secondContent\n        ];\n        const lastArg = (rest[rest.length - 1] || '').trim();\n        if (!isReact18) {\n            hydrationErrorState.reactOutputComponentDiff = lastArg;\n        } else {\n            hydrationErrorState.reactOutputComponentDiff = generateHydrationDiffReact18(msg, firstContent, secondContent, lastArg);\n        }\n        hydrationErrorState.warning = warning;\n        hydrationErrorState.serverContent = firstContent;\n        hydrationErrorState.clientContent = secondContent;\n    }\n}\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */ function generateHydrationDiffReact18(message, firstContent, secondContent, lastArg) {\n    const componentStack = lastArg;\n    let firstIndex = -1;\n    let secondIndex = -1;\n    const hydrationWarningType = getHydrationWarningType(message);\n    // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n    const components = componentStack.split('\\n') // .reverse()\n    .map((line, index)=>{\n        // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n        line = line.trim();\n        // extract `<space>at <component>` to `<<component>>`\n        // e.g. `  at Foo` -> `<Foo>`\n        const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || [];\n        // If there's no location then it's user-land stack frame\n        if (!location) {\n            if (component === firstContent && firstIndex === -1) {\n                firstIndex = index;\n            } else if (component === secondContent && secondIndex === -1) {\n                secondIndex = index;\n            }\n        }\n        return location ? '' : component;\n    }).filter(Boolean).reverse();\n    let diff = '';\n    for(let i = 0; i < components.length; i++){\n        const component = components[i];\n        const matchFirstContent = hydrationWarningType === 'tag' && i === components.length - firstIndex - 1;\n        const matchSecondContent = hydrationWarningType === 'tag' && i === components.length - secondIndex - 1;\n        if (matchFirstContent || matchSecondContent) {\n            const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2);\n            diff += \"> \" + spaces + \"<\" + component + \">\\n\";\n        } else {\n            const spaces = ' '.repeat(i * 2 + 2);\n            diff += spaces + \"<\" + component + \">\\n\";\n        }\n    }\n    if (hydrationWarningType === 'text') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"+ \" + spaces + '\"' + firstContent + '\"\\n';\n        diff += \"- \" + spaces + '\"' + secondContent + '\"\\n';\n    } else if (hydrationWarningType === 'text-in-tag') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"> \" + spaces + \"<\" + secondContent + \">\\n\";\n        diff += \">   \" + spaces + '\"' + firstContent + '\"\\n';\n    }\n    return diff;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hydration-error-info.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/runtime-error-handler.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RuntimeErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return RuntimeErrorHandler;\n    }\n}));\nconst RuntimeErrorHandler = {\n    hadRuntimeError: false\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=runtime-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3JzL3J1bnRpbWUtZXJyb3ItaGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiOzs7O3VEQUFhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxzQkFBc0I7SUFDakNDLGlCQUFpQjtBQUNuQiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxlcnJvcnNcXHJ1bnRpbWUtZXJyb3ItaGFuZGxlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUnVudGltZUVycm9ySGFuZGxlciA9IHtcbiAgaGFkUnVudGltZUVycm9yOiBmYWxzZSxcbn1cbiJdLCJuYW1lcyI6WyJSdW50aW1lRXJyb3JIYW5kbGVyIiwiaGFkUnVudGltZUVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/stitched-error.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getReactStitchedError\", ({\n    enumerable: true,\n    get: function() {\n        return getReactStitchedError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _errortelemetryutils = __webpack_require__(/*! ../../../lib/error-telemetry-utils */ \"(app-pages-browser)/./node_modules/next/dist/lib/error-telemetry-utils.js\");\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame';\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\"(at \" + REACT_ERROR_STACK_BOTTOM_FRAME + \" )|(\" + REACT_ERROR_STACK_BOTTOM_FRAME + \"\\\\@)\");\nfunction getReactStitchedError(err) {\n    const isErrorInstance = (0, _iserror.default)(err);\n    const originStack = isErrorInstance ? err.stack || '' : '';\n    const originMessage = isErrorInstance ? err.message : '';\n    const stackLines = originStack.split('\\n');\n    const indexOfSplit = stackLines.findIndex((line)=>REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line));\n    const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n    ;\n    let newStack = isOriginalReactError ? stackLines.slice(0, indexOfSplit).join('\\n') : originStack;\n    const newError = Object.defineProperty(new Error(originMessage), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    // Copy all enumerable properties, e.g. digest\n    Object.assign(newError, err);\n    (0, _errortelemetryutils.copyNextErrorCode)(err, newError);\n    newError.stack = newStack;\n    // Avoid duplicate overriding stack frames\n    appendOwnerStack(newError);\n    return newError;\n}\nfunction appendOwnerStack(error) {\n    if (!_react.default.captureOwnerStack) {\n        return;\n    }\n    let stack = error.stack || '';\n    // This module is only bundled in development mode so this is safe.\n    const ownerStack = _react.default.captureOwnerStack();\n    // Avoid duplicate overriding stack frames\n    if (ownerStack && stack.endsWith(ownerStack) === false) {\n        stack += ownerStack;\n        // Override stack\n        error.stack = stack;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stitched-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/use-error-handler.js ***!
  \******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleClientError: function() {\n        return handleClientError;\n    },\n    handleGlobalErrors: function() {\n        return handleGlobalErrors;\n    },\n    useErrorHandler: function() {\n        return useErrorHandler;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _attachhydrationerrorstate = __webpack_require__(/*! ./attach-hydration-error-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _consoleerror = __webpack_require__(/*! ./console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/console-error.js\");\nconst _enqueueclienterror = __webpack_require__(/*! ./enqueue-client-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/enqueue-client-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst queueMicroTask = globalThis.queueMicrotask || ((cb)=>Promise.resolve().then(cb));\nconst errorQueue = [];\nconst errorHandlers = [];\nconst rejectionQueue = [];\nconst rejectionHandlers = [];\nfunction handleClientError(originError, consoleErrorArgs, capturedFromConsole) {\n    if (capturedFromConsole === void 0) capturedFromConsole = false;\n    let error;\n    if (!originError || !(0, _iserror.default)(originError)) {\n        // If it's not an error, format the args into an error\n        const formattedErrorMessage = (0, _console.formatConsoleArgs)(consoleErrorArgs);\n        const { environmentName } = (0, _console.parseConsoleArgs)(consoleErrorArgs);\n        error = (0, _consoleerror.createUnhandledError)(formattedErrorMessage, environmentName);\n    } else {\n        error = capturedFromConsole ? (0, _consoleerror.createUnhandledError)(originError) : originError;\n    }\n    error = (0, _stitchederror.getReactStitchedError)(error);\n    (0, _hydrationerrorinfo.storeHydrationErrorStateFromConsoleArgs)(...consoleErrorArgs);\n    (0, _attachhydrationerrorstate.attachHydrationErrorState)(error);\n    (0, _enqueueclienterror.enqueueConsecutiveDedupedError)(errorQueue, error);\n    for (const handler of errorHandlers){\n        // Delayed the error being passed to React Dev Overlay,\n        // avoid the state being synchronously updated in the component.\n        queueMicroTask(()=>{\n            handler(error);\n        });\n    }\n}\nfunction useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection) {\n    (0, _react.useEffect)(()=>{\n        // Handle queued errors.\n        errorQueue.forEach(handleOnUnhandledError);\n        rejectionQueue.forEach(handleOnUnhandledRejection);\n        // Listen to new errors.\n        errorHandlers.push(handleOnUnhandledError);\n        rejectionHandlers.push(handleOnUnhandledRejection);\n        return ()=>{\n            // Remove listeners.\n            errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1);\n            rejectionHandlers.splice(rejectionHandlers.indexOf(handleOnUnhandledRejection), 1);\n            // Reset error queues.\n            errorQueue.splice(0, errorQueue.length);\n            rejectionQueue.splice(0, rejectionQueue.length);\n        };\n    }, [\n        handleOnUnhandledError,\n        handleOnUnhandledRejection\n    ]);\n}\nfunction onUnhandledError(event) {\n    if ((0, _isnextroutererror.isNextRouterError)(event.error)) {\n        event.preventDefault();\n        return false;\n    }\n    // When there's an error property present, we log the error to error overlay.\n    // Otherwise we don't do anything as it's not logging in the console either.\n    if (event.error) {\n        handleClientError(event.error, []);\n    }\n}\nfunction onUnhandledRejection(ev) {\n    const reason = ev == null ? void 0 : ev.reason;\n    if ((0, _isnextroutererror.isNextRouterError)(reason)) {\n        ev.preventDefault();\n        return;\n    }\n    let error = reason;\n    if (error && !(0, _iserror.default)(error)) {\n        error = (0, _consoleerror.createUnhandledError)(error + '');\n    }\n    rejectionQueue.push(error);\n    for (const handler of rejectionHandlers){\n        handler(error);\n    }\n}\nfunction handleGlobalErrors() {\n    if (true) {\n        try {\n            // Increase the number of stack frames on the client\n            Error.stackTraceLimit = 50;\n        } catch (e) {}\n        window.addEventListener('error', onUnhandledError);\n        window.addEventListener('unhandledrejection', onUnhandledRejection);\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/forbidden.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/components/forbidden.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"forbidden\", ({\n    enumerable: true,\n    get: function() {\n        return forbidden;\n    }\n}));\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback/http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */ const DIGEST = \"\" + _httpaccessfallback.HTTP_ERROR_FALLBACK_ERROR_CODE + \";403\";\nfunction forbidden() {\n    if (true) {\n        throw Object.defineProperty(new Error(\"`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E488\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=forbidden.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/forbidden.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/handle-global-errors.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\n(0, _useerrorhandler.handleGlobalErrors)();\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-global-errors.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZ2xvYmFscy9oYW5kbGUtZ2xvYmFsLWVycm9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7OzZDQUFtQztBQUVuQ0EsQ0FBQUEsR0FBQUEsaUJBQUFBLGtCQUFrQiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxnbG9iYWxzXFxoYW5kbGUtZ2xvYmFsLWVycm9ycy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBoYW5kbGVHbG9iYWxFcnJvcnMgfSBmcm9tICcuLi9lcnJvcnMvdXNlLWVycm9yLWhhbmRsZXInXG5cbmhhbmRsZUdsb2JhbEVycm9ycygpXG4iXSwibmFtZXMiOlsiaGFuZGxlR2xvYmFsRXJyb3JzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleClientError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args, true);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/patch-console.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst _interceptconsoleerror = __webpack_require__(/*! ./intercept-console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\");\n(0, _interceptconsoleerror.patchConsoleError)();\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=patch-console.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZ2xvYmFscy9wYXRjaC1jb25zb2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBQWtDO0FBRWxDQSxDQUFBQSxHQUFBQSx1QkFBQUEsaUJBQWlCIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGdsb2JhbHNcXHBhdGNoLWNvbnNvbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcGF0Y2hDb25zb2xlRXJyb3IgfSBmcm9tICcuL2ludGVyY2VwdC1jb25zb2xlLWVycm9yJ1xuXG5wYXRjaENvbnNvbGVFcnJvcigpXG4iXSwibmFtZXMiOlsicGF0Y2hDb25zb2xlRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    HTTPAccessErrorStatus: function() {\n        return HTTPAccessErrorStatus;\n    },\n    HTTP_ERROR_FALLBACK_ERROR_CODE: function() {\n        return HTTP_ERROR_FALLBACK_ERROR_CODE;\n    },\n    getAccessFallbackErrorTypeByStatus: function() {\n        return getAccessFallbackErrorTypeByStatus;\n    },\n    getAccessFallbackHTTPStatus: function() {\n        return getAccessFallbackHTTPStatus;\n    },\n    isHTTPAccessFallbackError: function() {\n        return isHTTPAccessFallbackError;\n    }\n});\nconst HTTPAccessErrorStatus = {\n    NOT_FOUND: 404,\n    FORBIDDEN: 403,\n    UNAUTHORIZED: 401\n};\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus));\nconst HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK';\nfunction isHTTPAccessFallbackError(error) {\n    if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n        return false;\n    }\n    const [prefix, httpStatus] = error.digest.split(';');\n    return prefix === HTTP_ERROR_FALLBACK_ERROR_CODE && ALLOWED_CODES.has(Number(httpStatus));\n}\nfunction getAccessFallbackHTTPStatus(error) {\n    const httpStatus = error.digest.split(';')[1];\n    return Number(httpStatus);\n}\nfunction getAccessFallbackErrorTypeByStatus(status) {\n    switch(status){\n        case 401:\n            return 'unauthorized';\n        case 403:\n            return 'forbidden';\n        case 404:\n            return 'not-found';\n        default:\n            return;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=http-access-fallback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/is-hydration-error.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    NEXTJS_HYDRATION_ERROR_LINK: function() {\n        return NEXTJS_HYDRATION_ERROR_LINK;\n    },\n    REACT_HYDRATION_ERROR_LINK: function() {\n        return REACT_HYDRATION_ERROR_LINK;\n    },\n    getDefaultHydrationErrorMessage: function() {\n        return getDefaultHydrationErrorMessage;\n    },\n    getHydrationErrorStackInfo: function() {\n        return getHydrationErrorStackInfo;\n    },\n    isHydrationError: function() {\n        return isHydrationError;\n    },\n    isReactHydrationErrorMessage: function() {\n        return isReactHydrationErrorMessage;\n    },\n    testReactHydrationWarning: function() {\n        return testReactHydrationWarning;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst hydrationErrorRegex = /hydration failed|while hydrating|content does not match|did not match|HTML didn't match/i;\nconst reactUnifiedMismatchWarning = \"Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:\";\nconst reactHydrationStartMessages = [\n    reactUnifiedMismatchWarning,\n    \"A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:\"\n];\nconst REACT_HYDRATION_ERROR_LINK = 'https://react.dev/link/hydration-mismatch';\nconst NEXTJS_HYDRATION_ERROR_LINK = 'https://nextjs.org/docs/messages/react-hydration-error';\nconst getDefaultHydrationErrorMessage = ()=>{\n    return reactUnifiedMismatchWarning;\n};\nfunction isHydrationError(error) {\n    return (0, _iserror.default)(error) && hydrationErrorRegex.test(error.message);\n}\nfunction isReactHydrationErrorMessage(msg) {\n    return reactHydrationStartMessages.some((prefix)=>msg.startsWith(prefix));\n}\nconst hydrationWarningRegexes = [\n    /^In HTML, (.+?) cannot be a child of <(.+?)>\\.(.*)\\nThis will cause a hydration error\\.(.*)/,\n    /^In HTML, (.+?) cannot be a descendant of <(.+?)>\\.\\nThis will cause a hydration error\\.(.*)/,\n    /^In HTML, text nodes cannot be a child of <(.+?)>\\.\\nThis will cause a hydration error\\./,\n    /^In HTML, whitespace text nodes cannot be a child of <(.+?)>\\. Make sure you don't have any extra whitespace between tags on each line of your source code\\.\\nThis will cause a hydration error\\./,\n    /^Expected server HTML to contain a matching <(.+?)> in <(.+?)>\\.(.*)/,\n    /^Did not expect server HTML to contain a <(.+?)> in <(.+?)>\\.(.*)/,\n    /^Expected server HTML to contain a matching text node for \"(.+?)\" in <(.+?)>\\.(.*)/,\n    /^Did not expect server HTML to contain the text node \"(.+?)\" in <(.+?)>\\.(.*)/,\n    /^Text content did not match\\. Server: \"(.+?)\" Client: \"(.+?)\"(.*)/\n];\nfunction testReactHydrationWarning(msg) {\n    if (typeof msg !== 'string' || !msg) return false;\n    // React 18 has the `Warning: ` prefix.\n    // React 19 does not.\n    if (msg.startsWith('Warning: ')) {\n        msg = msg.slice('Warning: '.length);\n    }\n    return hydrationWarningRegexes.some((regex)=>regex.test(msg));\n}\nfunction getHydrationErrorStackInfo(rawMessage) {\n    rawMessage = rawMessage.replace(/^Error: /, '');\n    rawMessage = rawMessage.replace('Warning: ', '');\n    const isReactHydrationWarning = testReactHydrationWarning(rawMessage);\n    if (!isReactHydrationErrorMessage(rawMessage) && !isReactHydrationWarning) {\n        return {\n            message: null,\n            stack: rawMessage,\n            diff: ''\n        };\n    }\n    if (isReactHydrationWarning) {\n        const [message, diffLog] = rawMessage.split('\\n\\n');\n        return {\n            message: message.trim(),\n            stack: '',\n            diff: (diffLog || '').trim()\n        };\n    }\n    const firstLineBreak = rawMessage.indexOf('\\n');\n    rawMessage = rawMessage.slice(firstLineBreak + 1).trim();\n    const [message, trailing] = rawMessage.split(\"\" + REACT_HYDRATION_ERROR_LINK);\n    const trimmedMessage = message.trim();\n    // React built-in hydration diff starts with a newline, checking if length is > 1\n    if (trailing && trailing.length > 1) {\n        const stacks = [];\n        const diffs = [];\n        trailing.split('\\n').forEach((line)=>{\n            if (line.trim() === '') return;\n            if (line.trim().startsWith('at ')) {\n                stacks.push(line);\n            } else {\n                diffs.push(line);\n            }\n        });\n        return {\n            message: trimmedMessage,\n            diff: diffs.join('\\n'),\n            stack: stacks.join('\\n')\n        };\n    } else {\n        return {\n            message: trimmedMessage,\n            stack: trailing\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=is-hydration-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/is-next-router-error.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isNextRouterError\", ({\n    enumerable: true,\n    get: function() {\n        return isNextRouterError;\n    }\n}));\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback/http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nfunction isNextRouterError(error) {\n    return (0, _redirecterror.isRedirectError)(error) || (0, _httpaccessfallback.isHTTPAccessFallbackError)(error);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=is-next-router-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaXMtbmV4dC1yb3V0ZXItZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFXZ0JBOzs7ZUFBQUE7OztnREFSVDsyQ0FDNkM7QUFPN0MsU0FBU0Esa0JBQ2RDLEtBQWM7SUFFZCxPQUFPQyxDQUFBQSxHQUFBQSxlQUFBQSxlQUFBQSxFQUFnQkQsVUFBVUUsQ0FBQUEsR0FBQUEsb0JBQUFBLHlCQUFBQSxFQUEwQkY7QUFDN0QiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xcaXMtbmV4dC1yb3V0ZXItZXJyb3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgaXNIVFRQQWNjZXNzRmFsbGJhY2tFcnJvcixcbiAgdHlwZSBIVFRQQWNjZXNzRmFsbGJhY2tFcnJvcixcbn0gZnJvbSAnLi9odHRwLWFjY2Vzcy1mYWxsYmFjay9odHRwLWFjY2Vzcy1mYWxsYmFjaydcbmltcG9ydCB7IGlzUmVkaXJlY3RFcnJvciwgdHlwZSBSZWRpcmVjdEVycm9yIH0gZnJvbSAnLi9yZWRpcmVjdC1lcnJvcidcblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIGVycm9yIGlzIGEgbmF2aWdhdGlvbiBzaWduYWwgZXJyb3IuIFRoZXNlIGVycm9ycyBhcmVcbiAqIHRocm93biBieSB1c2VyIGNvZGUgdG8gcGVyZm9ybSBuYXZpZ2F0aW9uIG9wZXJhdGlvbnMgYW5kIGludGVycnVwdCB0aGUgUmVhY3RcbiAqIHJlbmRlci5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzTmV4dFJvdXRlckVycm9yKFxuICBlcnJvcjogdW5rbm93blxuKTogZXJyb3IgaXMgUmVkaXJlY3RFcnJvciB8IEhUVFBBY2Nlc3NGYWxsYmFja0Vycm9yIHtcbiAgcmV0dXJuIGlzUmVkaXJlY3RFcnJvcihlcnJvcikgfHwgaXNIVFRQQWNjZXNzRmFsbGJhY2tFcnJvcihlcnJvcilcbn1cbiJdLCJuYW1lcyI6WyJpc05leHRSb3V0ZXJFcnJvciIsImVycm9yIiwiaXNSZWRpcmVjdEVycm9yIiwiaXNIVFRQQWNjZXNzRmFsbGJhY2tFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _errorboundary1 = __webpack_require__(/*! ./http-access-fallback/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./router-reducer/reducers/has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                'refetch'\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = _reactdom.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (false) {}\n    // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n    // We need to lazily reference it.\n    const internal_reactDOMfindDOMNode = __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode;\n    return internal_reactDOMfindDOMNode(instance);\n}\nconst rectProperties = [\n    'bottom',\n    'height',\n    'left',\n    'right',\n    'top',\n    'width',\n    'x',\n    'y'\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        'sticky',\n        'fixed'\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn('Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:', element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === 'top') {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args), this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    if (true) {\n                        var _domNode_parentElement;\n                        if (((_domNode_parentElement = domNode.parentElement) == null ? void 0 : _domNode_parentElement.localName) === 'head') {\n                        // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n                        // This is always a bug in Next.js and caused by React hoisting metadata.\n                        // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n                        }\n                    }\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        ;\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        // Scroll into view doesn't scroll horizontally by default when not needed\n                        ;\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant global layout router not mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E473\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef,\n        children: children\n    });\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { tree, segmentPath, cacheNode, url } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant global layout router not mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E473\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { changeByServerResponse, tree: fullTree } = context;\n    // `rsc` represents the renderable node for this segment.\n    // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n    // We should use that on initial render instead of `rsc`. Then we'll switch\n    // to `rsc` when the dynamic response streams in.\n    //\n    // If no prefetch data is available, then we go straight to rendering `rsc`.\n    const resolvedPrefetchRsc = cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    const rsc = (0, _react.useDeferredValue)(cacheNode.rsc, resolvedPrefetchRsc);\n    // `rsc` is either a React node or a promise for a React node, except we\n    // special case `null` to represent that this segment's data is missing. If\n    // it's a promise, we need to unwrap it so we can determine whether or not the\n    // data is missing.\n    const resolvedRsc = typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function' ? (0, _react.use)(rsc) : rsc;\n    if (!resolvedRsc) {\n        // The data for this segment is not available, and there's no pending\n        // navigation that will be able to fulfill it. We need to fetch more from\n        // the server and patch the cache.\n        // Check if there's already a pending request.\n        let lazyData = cacheNode.lazyData;\n        if (lazyData === null) {\n            /**\n       * Router state with refetch marker added\n       */ // TODO-APP: remove ''\n            const refetchTree = walkAddRefetch([\n                '',\n                ...segmentPath\n            ], fullTree);\n            const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(fullTree);\n            cacheNode.lazyData = lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), {\n                flightRouterState: refetchTree,\n                nextUrl: includeNextUrl ? context.nextUrl : null\n            }).then((serverResponse)=>{\n                (0, _react.startTransition)(()=>{\n                    changeByServerResponse({\n                        previousTree: fullTree,\n                        serverResponse\n                    });\n                });\n                return serverResponse;\n            });\n            // Suspend while waiting for lazyData to resolve\n            (0, _react.use)(lazyData);\n        }\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    // If we get to this point, then we know we have something we can render.\n    const subtree = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            parentTree: tree,\n            parentCacheNode: cacheNode,\n            parentSegmentPath: segmentPath,\n            // TODO-APP: overriding of url for parallel routes\n            url: url\n        },\n        children: resolvedRsc\n    });\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { loading, children } = param;\n    // If loading is a promise, unwrap it. This happens in cases where we haven't\n    // yet received the loading data from the server — which includes whether or\n    // not this layout has a loading component at all.\n    //\n    // It's OK to suspend here instead of inside the fallback because this\n    // promise will resolve simultaneously with the data for the segment itself.\n    // So it will never suspend for longer than it would have if we didn't use\n    // a Suspense fallback at all.\n    let loadingModuleData;\n    if (typeof loading === 'object' && loading !== null && typeof loading.then === 'function') {\n        const promiseForLoading = loading;\n        loadingModuleData = (0, _react.use)(promiseForLoading);\n    } else {\n        loadingModuleData = loading;\n    }\n    if (loadingModuleData) {\n        const loadingRsc = loadingModuleData[0];\n        const loadingStyles = loadingModuleData[1];\n        const loadingScripts = loadingModuleData[2];\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    loadingStyles,\n                    loadingScripts,\n                    loadingRsc\n                ]\n            }),\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, error, errorStyles, errorScripts, templateStyles, templateScripts, template, notFound, forbidden, unauthorized } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant expected layout router to be mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E56\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { parentTree, parentCacheNode, parentSegmentPath, url } = context;\n    // Get the CacheNode for this segment by reading it from the parent segment's\n    // child map.\n    const parentParallelRoutes = parentCacheNode.parallelRoutes;\n    let segmentMap = parentParallelRoutes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!segmentMap) {\n        segmentMap = new Map();\n        parentParallelRoutes.set(parallelRouterKey, segmentMap);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const parentTreeSegment = parentTree[0];\n    const tree = parentTree[1][parallelRouterKey];\n    const treeSegment = tree[0];\n    const segmentPath = parentSegmentPath === null ? // the code. We should clean this up.\n    [\n        parallelRouterKey\n    ] : parentSegmentPath.concat([\n        parentTreeSegment,\n        parallelRouterKey\n    ]);\n    // The \"state\" key of a segment is the one passed to React — it represents the\n    // identity of the UI tree. Whenever the state key changes, the tree is\n    // recreated and the state is reset. In the App Router model, search params do\n    // not cause state to be lost, so two segments with the same segment path but\n    // different search params should have the same state key.\n    //\n    // The \"cache\" key of a segment, however, *does* include the search params, if\n    // it's possible that the segment accessed the search params on the server.\n    // (This only applies to page segments; layout segments cannot access search\n    // params on the server.)\n    const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(treeSegment);\n    const stateKey = (0, _createroutercachekey.createRouterCacheKey)(treeSegment, true) // no search params\n    ;\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey);\n    if (cacheNode === undefined) {\n        // When data is not available during rendering client-side we need to fetch\n        // it from the server.\n        const newLazyCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(),\n            loading: null\n        };\n        // Flight data fetch kicked off during render and put into the cache.\n        cacheNode = newLazyCacheNode;\n        segmentMap.set(cacheKey, newLazyCacheNode);\n    }\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */ // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_approutercontextsharedruntime.TemplateContext.Provider, {\n        value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ScrollAndFocusHandler, {\n            segmentPath: segmentPath,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n                errorComponent: error,\n                errorStyles: errorStyles,\n                errorScripts: errorScripts,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(LoadingBoundary, {\n                    loading: loadingModuleData,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary1.HTTPAccessFallbackBoundary, {\n                        notFound: notFound,\n                        forbidden: forbidden,\n                        unauthorized: unauthorized,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_redirectboundary.RedirectBoundary, {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerLayoutRouter, {\n                                url: url,\n                                tree: tree,\n                                cacheNode: cacheNode,\n                                segmentPath: segmentPath\n                            })\n                        })\n                    })\n                })\n            })\n        }),\n        children: [\n            templateStyles,\n            templateScripts,\n            template\n        ]\n    }, stateKey);\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQWllQTs7O2VBQXdCQTs7Ozs7OzZFQTVjakI7K0VBQ2M7MkRBS2Q7aURBQzZCO2dEQUNEOzJDQUNMOzJDQUNEO2dEQUNNOzhDQUNGOzRDQUNVO2tEQUNOOytEQUNhO0FBRWxEOzs7Q0FHQyxHQUNELFNBQVNDLGVBQ1BDLGlCQUFnRCxFQUNoREMsY0FBaUM7SUFFakMsSUFBSUQsbUJBQW1CO1FBQ3JCLE1BQU0sQ0FBQ0UsU0FBU0MsaUJBQWlCLEdBQUdIO1FBQ3BDLE1BQU1JLFNBQVNKLGtCQUFrQkssTUFBTSxLQUFLO1FBRTVDLElBQUlDLENBQUFBLEdBQUFBLGVBQUFBLFlBQUFBLEVBQWFMLGNBQWMsQ0FBQyxFQUFFLEVBQUVDLFVBQVU7WUFDNUMsSUFBSUQsY0FBYyxDQUFDLEVBQUUsQ0FBQ00sY0FBYyxDQUFDSixtQkFBbUI7Z0JBQ3RELElBQUlDLFFBQVE7b0JBQ1YsTUFBTUksVUFBVVQsZUFDZFUsV0FDQVIsY0FBYyxDQUFDLEVBQUUsQ0FBQ0UsaUJBQWlCO29CQUVyQyxPQUFPO3dCQUNMRixjQUFjLENBQUMsRUFBRTt3QkFDakI7NEJBQ0UsR0FBR0EsY0FBYyxDQUFDLEVBQUU7NEJBQ3BCLENBQUNFLGlCQUFpQixFQUFFO2dDQUNsQkssT0FBTyxDQUFDLEVBQUU7Z0NBQ1ZBLE9BQU8sQ0FBQyxFQUFFO2dDQUNWQSxPQUFPLENBQUMsRUFBRTtnQ0FDVjs2QkFDRDt3QkFDSDtxQkFDRDtnQkFDSDtnQkFFQSxPQUFPO29CQUNMUCxjQUFjLENBQUMsRUFBRTtvQkFDakI7d0JBQ0UsR0FBR0EsY0FBYyxDQUFDLEVBQUU7d0JBQ3BCLENBQUNFLGlCQUFpQixFQUFFSixlQUNsQkMsa0JBQWtCVSxLQUFLLENBQUMsSUFDeEJULGNBQWMsQ0FBQyxFQUFFLENBQUNFLGlCQUFpQjtvQkFFdkM7aUJBQ0Q7WUFDSDtRQUNGO0lBQ0Y7SUFFQSxPQUFPRjtBQUNUO0FBRUEsTUFBTVUsK0RBQ0pDLFVBQUFBLE9BQVEsQ0FDUkQsNERBQTREO0FBRTlELDRGQUE0RjtBQUM1Rjs7Q0FFQyxHQUNELFNBQVNFLFlBQ1BDLFFBQWdEO0lBRWhELCtCQUErQjtJQUMvQixJQUFJLEtBQTZCLEVBQUUsRUFBTztJQUUxQyx1R0FBdUc7SUFDdkcsa0NBQWtDO0lBQ2xDLE1BQU1FLCtCQUNKTCw2REFBNkRFLFdBQVc7SUFDMUUsT0FBT0csNkJBQTZCRjtBQUN0QztBQUVBLE1BQU1HLGlCQUFpQjtJQUNyQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFDRDs7Q0FFQyxHQUNELFNBQVNDLGtCQUFrQkMsT0FBb0I7SUFDN0Msa0dBQWtHO0lBQ2xHLDBGQUEwRjtJQUMxRixtREFBbUQ7SUFDbkQsSUFBSTtRQUFDO1FBQVU7S0FBUSxDQUFDQyxRQUFRLENBQUNDLGlCQUFpQkYsU0FBU0csUUFBUSxHQUFHO1FBQ3BFLElBQUlDLElBQW9CLEVBQW9CO1lBQzFDRyxRQUFRQyxJQUFJLENBQ1YsNEZBQ0FSO1FBRUo7UUFDQSxPQUFPO0lBQ1Q7SUFFQSwyRkFBMkY7SUFDM0Ysd0RBQXdEO0lBQ3hELE1BQU1TLE9BQU9ULFFBQVFVLHFCQUFxQjtJQUMxQyxPQUFPWixlQUFlYSxLQUFLLENBQUMsQ0FBQ0MsT0FBU0gsSUFBSSxDQUFDRyxLQUFLLEtBQUs7QUFDdkQ7QUFFQTs7Q0FFQyxHQUNELFNBQVNDLHVCQUF1QmIsT0FBb0IsRUFBRWMsY0FBc0I7SUFDMUUsTUFBTUwsT0FBT1QsUUFBUVUscUJBQXFCO0lBQzFDLE9BQU9ELEtBQUtNLEdBQUcsSUFBSSxLQUFLTixLQUFLTSxHQUFHLElBQUlEO0FBQ3RDO0FBRUE7Ozs7O0NBS0MsR0FDRCxTQUFTRSx1QkFBdUJDLFlBQW9CO0lBQ2xELCtFQUErRTtJQUMvRSxJQUFJQSxpQkFBaUIsT0FBTztRQUMxQixPQUFPQyxTQUFTQyxJQUFJO0lBQ3RCO1FBSUVEO0lBRkYscUZBQXFGO0lBQ3JGLE9BQ0VBLENBQUFBLDJCQUFBQSxTQUFTRSxjQUFjLENBQUNILGFBQUFBLEtBQUFBLE9BQXhCQywyQkFDQSxTQUNTRyxpQkFBaUIsQ0FBQ0osYUFBYSxDQUFDLEVBQUU7QUFFL0M7QUFNQSxNQUFNSyxtQ0FBbUNDLE9BQUFBLE9BQUssQ0FBQ0MsU0FBUztJQTRHdERDLG9CQUFvQjtRQUNsQixJQUFJLENBQUNDLHFCQUFxQjtJQUM1QjtJQUVBQyxxQkFBcUI7UUFDbkIsc0pBQXNKO1FBQ3RKLElBQUksSUFBSSxDQUFDQyxLQUFLLENBQUNDLGlCQUFpQixDQUFDQyxLQUFLLEVBQUU7WUFDdEMsSUFBSSxDQUFDSixxQkFBcUI7UUFDNUI7SUFDRjtJQUVBSyxTQUFTO1FBQ1AsT0FBTyxJQUFJLENBQUNILEtBQUssQ0FBQ0ksUUFBUTtJQUM1Qjs7UUF6SEYscUJBQ0VOLHFCQUFBQSxHQUF3QjtZQUN0QixxR0FBcUc7WUFDckcsTUFBTSxFQUFFRyxpQkFBaUIsRUFBRUksV0FBVyxFQUFFLEdBQUcsSUFBSSxDQUFDTCxLQUFLO1lBRXJELElBQUlDLGtCQUFrQkMsS0FBSyxFQUFFO2dCQUMzQix1RUFBdUU7Z0JBQ3ZFLDZFQUE2RTtnQkFDN0Usd0VBQXdFO2dCQUN4RSxJQUNFRCxrQkFBa0JLLFlBQVksQ0FBQ2hELE1BQU0sS0FBSyxLQUMxQyxDQUFDMkMsa0JBQWtCSyxZQUFZLENBQUNDLElBQUksQ0FBQyxDQUFDQyx1QkFDcENILFlBQVl0QixLQUFLLENBQUMsQ0FBQzVCLFNBQVNzRCxRQUMxQmxELENBQUFBLEdBQUFBLGVBQUFBLFlBQUFBLEVBQWFKLFNBQVNxRCxvQkFBb0IsQ0FBQ0MsTUFBTSxLQUdyRDtvQkFDQTtnQkFDRjtnQkFFQSxJQUFJQyxVQUVpQztnQkFDckMsTUFBTXJCLGVBQWVZLGtCQUFrQlosWUFBWTtnQkFFbkQsSUFBSUEsY0FBYztvQkFDaEJxQixVQUFVdEIsdUJBQXVCQztnQkFDbkM7Z0JBRUEsa0dBQWtHO2dCQUNsRyx5RUFBeUU7Z0JBQ3pFLElBQUksQ0FBQ3FCLFNBQVM7b0JBQ1pBLFVBQVU1QyxZQUFZLElBQUk7Z0JBQzVCO2dCQUVBLHVHQUF1RztnQkFDdkcsSUFBSSxDQUFFNEMsQ0FBQUEsbUJBQW1CQyxPQUFBQSxDQUFNLEVBQUk7b0JBQ2pDO2dCQUNGO2dCQUVBLDRGQUE0RjtnQkFDNUYsMkVBQTJFO2dCQUMzRSxNQUFPLENBQUVELENBQUFBLG1CQUFtQkUsV0FBQUEsQ0FBVSxJQUFNekMsa0JBQWtCdUMsU0FBVTtvQkFDdEUsSUFBSWxDLElBQW9CLEVBQW1COzRCQUNyQ2tDO3dCQUFKLElBQUlBLENBQUFBLENBQUFBLHlCQUFBQSxRQUFRRyxhQUFBQSxLQUFhLGdCQUFyQkgsdUJBQXVCSSxTQUFBQSxNQUFjLFFBQVE7d0JBQy9DLDJGQUEyRjt3QkFDM0YseUVBQXlFO3dCQUN6RSxpSEFBaUg7d0JBQ25IO29CQUNGO29CQUVBLHVHQUF1RztvQkFDdkcsSUFBSUosUUFBUUssa0JBQWtCLEtBQUssTUFBTTt3QkFDdkM7b0JBQ0Y7b0JBQ0FMLFVBQVVBLFFBQVFLLGtCQUFrQjtnQkFDdEM7Z0JBRUEsNkVBQTZFO2dCQUM3RWQsa0JBQWtCQyxLQUFLLEdBQUc7Z0JBQzFCRCxrQkFBa0JaLFlBQVksR0FBRztnQkFDakNZLGtCQUFrQkssWUFBWSxHQUFHLEVBQUU7Z0JBRW5DVSxDQUFBQSxHQUFBQSxvQkFBQUEsa0JBQUFBLEVBQ0U7b0JBQ0UsdUVBQXVFO29CQUN2RSxJQUFJM0IsY0FBYzs7d0JBQ2RxQixRQUF3Qk8sY0FBYzt3QkFFeEM7b0JBQ0Y7b0JBQ0Esb0ZBQW9GO29CQUNwRiw0Q0FBNEM7b0JBQzVDLE1BQU1DLGNBQWM1QixTQUFTNkIsZUFBZTtvQkFDNUMsTUFBTWpDLGlCQUFpQmdDLFlBQVlFLFlBQVk7b0JBRS9DLG9FQUFvRTtvQkFDcEUsSUFBSW5DLHVCQUF1QnlCLFNBQXdCeEIsaUJBQWlCO3dCQUNsRTtvQkFDRjtvQkFFQSwyRkFBMkY7b0JBQzNGLGtIQUFrSDtvQkFDbEgscUhBQXFIO29CQUNySCw2SEFBNkg7b0JBQzdIZ0MsWUFBWUcsU0FBUyxHQUFHO29CQUV4QixtRkFBbUY7b0JBQ25GLElBQUksQ0FBQ3BDLHVCQUF1QnlCLFNBQXdCeEIsaUJBQWlCO3dCQUNuRSwwRUFBMEU7O3dCQUN4RXdCLFFBQXdCTyxjQUFjO29CQUMxQztnQkFDRixHQUNBO29CQUNFLG9EQUFvRDtvQkFDcERLLGlCQUFpQjtvQkFDakJDLGdCQUFnQnRCLGtCQUFrQnNCLGNBQWM7Z0JBQ2xEO2dCQUdGLHdFQUF3RTtnQkFDeEV0QixrQkFBa0JzQixjQUFjLEdBQUc7Z0JBRW5DLDJCQUEyQjtnQkFDM0JiLFFBQVFjLEtBQUs7WUFDZjtRQUNGOztBQWdCRjtBQUVBLCtCQUErQixLQU05QjtJQU44QixNQUM3Qm5CLFdBQVcsRUFDWEQsUUFBUSxFQUlULEdBTjhCO0lBTzdCLE1BQU1zQixVQUFVQyxDQUFBQSxHQUFBQSxPQUFBQSxVQUFBQSxFQUFXQywrQkFBQUEseUJBQXlCO0lBQ3BELElBQUksQ0FBQ0YsU0FBUztRQUNaLE1BQU0scUJBQXVELENBQXZELElBQUlHLE1BQU0sK0NBQVY7bUJBQUE7d0JBQUE7MEJBQUE7UUFBc0Q7SUFDOUQ7SUFFQSxxQkFDRSxxQkFBQ25DLDRCQUFBQTtRQUNDVyxhQUFhQTtRQUNiSixtQkFBbUJ5QixRQUFRekIsaUJBQWlCO2tCQUUzQ0c7O0FBR1A7S0FwQlNxQjtBQXNCVDs7Q0FFQyxHQUNELDJCQUEyQixLQVUxQjtJQVYwQixNQUN6Qk0sSUFBSSxFQUNKMUIsV0FBVyxFQUNYMkIsU0FBUyxFQUNUQyxHQUFHLEVBTUosR0FWMEI7SUFXekIsTUFBTVAsVUFBVUMsQ0FBQUEsR0FBQUEsT0FBQUEsVUFBQUEsRUFBV0MsK0JBQUFBLHlCQUF5QjtJQUNwRCxJQUFJLENBQUNGLFNBQVM7UUFDWixNQUFNLHFCQUF1RCxDQUF2RCxJQUFJRyxNQUFNLCtDQUFWO21CQUFBO3dCQUFBOzBCQUFBO1FBQXNEO0lBQzlEO0lBRUEsTUFBTSxFQUFFSyxzQkFBc0IsRUFBRUgsTUFBTUksUUFBUSxFQUFFLEdBQUdUO0lBRW5ELHlEQUF5RDtJQUV6RCw0RUFBNEU7SUFDNUUsMkVBQTJFO0lBQzNFLGlEQUFpRDtJQUNqRCxFQUFFO0lBQ0YsNEVBQTRFO0lBQzVFLE1BQU1VLHNCQUNKSixVQUFVSyxXQUFXLEtBQUssT0FBT0wsVUFBVUssV0FBVyxHQUFHTCxVQUFVTSxHQUFHO0lBRXhFLDJFQUEyRTtJQUMzRSwyRUFBMkU7SUFDM0Usc0NBQXNDO0lBQ3RDLE1BQU1BLE1BQVdDLENBQUFBLEdBQUFBLE9BQUFBLGdCQUFBQSxFQUFpQlAsVUFBVU0sR0FBRyxFQUFFRjtJQUVqRCx3RUFBd0U7SUFDeEUsMkVBQTJFO0lBQzNFLDhFQUE4RTtJQUM5RSxtQkFBbUI7SUFDbkIsTUFBTUksY0FDSixPQUFPRixRQUFRLFlBQVlBLFFBQVEsUUFBUSxPQUFPQSxJQUFJRyxJQUFJLEtBQUssYUFDM0RDLENBQUFBLEdBQUFBLE9BQUFBLEdBQUcsRUFBQ0osT0FDSkE7SUFFTixJQUFJLENBQUNFLGFBQWE7UUFDaEIscUVBQXFFO1FBQ3JFLHlFQUF5RTtRQUN6RSxrQ0FBa0M7UUFFbEMsOENBQThDO1FBQzlDLElBQUlHLFdBQVdYLFVBQVVXLFFBQVE7UUFDakMsSUFBSUEsYUFBYSxNQUFNO1lBQ3JCOztPQUVDLEdBQ0Qsc0JBQXNCO1lBQ3RCLE1BQU1DLGNBQWM1RixlQUFlO2dCQUFDO21CQUFPcUQ7YUFBWSxFQUFFOEI7WUFDekQsTUFBTVUsaUJBQWlCQyxDQUFBQSxHQUFBQSxtQ0FBQUEsaUNBQUFBLEVBQWtDWDtZQUN6REgsVUFBVVcsUUFBUSxHQUFHQSxXQUFXSSxDQUFBQSxHQUFBQSxxQkFBQUEsbUJBQUFBLEVBQzlCLElBQUlDLElBQUlmLEtBQUtnQixTQUFTQyxNQUFNLEdBQzVCO2dCQUNFQyxtQkFBbUJQO2dCQUNuQlEsU0FBU1AsaUJBQWlCbkIsUUFBUTBCLE9BQU8sR0FBRztZQUM5QyxHQUNBWCxJQUFJLENBQUMsQ0FBQ1k7Z0JBQ05DLENBQUFBLEdBQUFBLE9BQUFBLGVBQUFBLEVBQWdCO29CQUNkcEIsdUJBQXVCO3dCQUNyQnFCLGNBQWNwQjt3QkFDZGtCO29CQUNGO2dCQUNGO2dCQUVBLE9BQU9BO1lBQ1Q7WUFFQSxnREFBZ0Q7WUFDaERYLENBQUFBLEdBQUFBLE9BQUFBLEdBQUFBLEVBQUlDO1FBQ047UUFDQSx5R0FBeUc7UUFDekcsaUlBQWlJO1FBQ2pJRCxDQUFBQSxHQUFBQSxPQUFBQSxHQUFBQSxFQUFJYyxvQkFBQUEsa0JBQWtCO0lBQ3hCO0lBRUEseUVBQXlFO0lBQ3pFLE1BQU1DLFVBQ0osY0FDQSxxQkFBQ0MsK0JBQUFBLFVBRDJFLFNBQ3hELENBQUNDLFFBQVE7UUFDM0JDLE9BQU87WUFDTEMsWUFBWTlCO1lBQ1orQixpQkFBaUI5QjtZQUNqQitCLG1CQUFtQjFEO1lBRW5CLGtEQUFrRDtZQUNsRDRCLEtBQUtBO1FBQ1A7a0JBRUNPOztJQUdMLGlGQUFpRjtJQUNqRixPQUFPaUI7QUFDVDtNQW5HUzNCO0FBcUdUOzs7Q0FHQyxHQUNELHlCQUF5QixLQU14QjtJQU53QixNQUN2Qm1DLE9BQU8sRUFDUDdELFFBQVEsRUFJVCxHQU53QjtJQU92Qiw2RUFBNkU7SUFDN0UsNEVBQTRFO0lBQzVFLGtEQUFrRDtJQUNsRCxFQUFFO0lBQ0Ysc0VBQXNFO0lBQ3RFLDRFQUE0RTtJQUM1RSwwRUFBMEU7SUFDMUUsOEJBQThCO0lBQzlCLElBQUk4RDtJQUNKLElBQ0UsT0FBT0QsWUFBWSxZQUNuQkEsWUFBWSxRQUNaLE9BQVFBLFFBQWdCeEIsSUFBSSxLQUFLLFlBQ2pDO1FBQ0EsTUFBTTBCLG9CQUFvQkY7UUFDMUJDLG9CQUFvQnhCLENBQUFBLEdBQUFBLE9BQUFBLEdBQUFBLEVBQUl5QjtJQUMxQixPQUFPO1FBQ0xELG9CQUFvQkQ7SUFDdEI7SUFFQSxJQUFJQyxtQkFBbUI7UUFDckIsTUFBTUUsYUFBYUYsaUJBQWlCLENBQUMsRUFBRTtRQUN2QyxNQUFNRyxnQkFBZ0JILGlCQUFpQixDQUFDLEVBQUU7UUFDMUMsTUFBTUksaUJBQWlCSixpQkFBaUIsQ0FBQyxFQUFFO1FBQzNDLHFCQUNFLHFCQUFDSyxPQUFBQSxRQUFRO1lBQ1BDLFVBQUFBLFdBQUFBLEdBQ0U7O29CQUNHSDtvQkFDQUM7b0JBQ0FGOzs7c0JBSUpoRTs7SUFHUDtJQUVBLHFCQUFPO2tCQUFHQTs7QUFDWjtNQS9DUzREO0FBcURNLDJCQUEyQixLQXNCekM7SUF0QnlDLE1BQ3hDUyxpQkFBaUIsRUFDakJDLEtBQUssRUFDTEMsV0FBVyxFQUNYQyxZQUFZLEVBQ1pDLGNBQWMsRUFDZEMsZUFBZSxFQUNmQyxRQUFRLEVBQ1JDLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxZQUFZLEVBWWIsR0F0QnlDO0lBdUJ4QyxNQUFNeEQsVUFBVUMsQ0FBQUEsR0FBQUEsT0FBQUEsVUFBQUEsRUFBVytCLCtCQUFBQSxtQkFBbUI7SUFDOUMsSUFBSSxDQUFDaEMsU0FBUztRQUNaLE1BQU0scUJBQTJELENBQTNELElBQUlHLE1BQU0sbURBQVY7bUJBQUE7d0JBQUE7MEJBQUE7UUFBMEQ7SUFDbEU7SUFFQSxNQUFNLEVBQUVnQyxVQUFVLEVBQUVDLGVBQWUsRUFBRUMsaUJBQWlCLEVBQUU5QixHQUFHLEVBQUUsR0FBR1A7SUFFaEUsNkVBQTZFO0lBQzdFLGFBQWE7SUFDYixNQUFNeUQsdUJBQXVCckIsZ0JBQWdCc0IsY0FBYztJQUMzRCxJQUFJQyxhQUFhRixxQkFBcUJHLEdBQUcsQ0FBQ2I7SUFDMUMsbUVBQW1FO0lBQ25FLHlKQUF5SjtJQUN6SixJQUFJLENBQUNZLFlBQVk7UUFDZkEsYUFBYSxJQUFJRTtRQUNqQkoscUJBQXFCSyxHQUFHLENBQUNmLG1CQUFtQlk7SUFDOUM7SUFFQSxxQ0FBcUM7SUFDckMsOElBQThJO0lBQzlJLE1BQU1JLG9CQUFvQjVCLFVBQVUsQ0FBQyxFQUFFO0lBQ3ZDLE1BQU05QixPQUFPOEIsVUFBVSxDQUFDLEVBQUUsQ0FBQ1ksa0JBQWtCO0lBQzdDLE1BQU1pQixjQUFjM0QsSUFBSSxDQUFDLEVBQUU7SUFFM0IsTUFBTTFCLGNBQ0owRCxzQkFBc0IsT0FFbEIscUNBQ3FDO0lBQ3JDO1FBQUNVO0tBQWtCLEdBQ25CVixrQkFBa0I0QixNQUFNLENBQUM7UUFBQ0Y7UUFBbUJoQjtLQUFrQjtJQUVyRSw4RUFBOEU7SUFDOUUsdUVBQXVFO0lBQ3ZFLDhFQUE4RTtJQUM5RSw2RUFBNkU7SUFDN0UsMERBQTBEO0lBQzFELEVBQUU7SUFDRiw4RUFBOEU7SUFDOUUsMkVBQTJFO0lBQzNFLDRFQUE0RTtJQUM1RSx5QkFBeUI7SUFDekIsTUFBTW1CLFdBQVdDLENBQUFBLEdBQUFBLHNCQUFBQSxvQkFBQUEsRUFBcUJIO0lBQ3RDLE1BQU1JLFdBQVdELENBQUFBLEdBQUFBLHNCQUFBQSxvQkFBQUEsRUFBcUJILGFBQWEsTUFBTSxtQkFBbUI7O0lBRTVFLHlEQUF5RDtJQUN6RCxJQUFJMUQsWUFBWXFELFdBQVdDLEdBQUcsQ0FBQ007SUFDL0IsSUFBSTVELGNBQWN0RSxXQUFXO1FBQzNCLDJFQUEyRTtRQUMzRSxzQkFBc0I7UUFDdEIsTUFBTXFJLG1CQUFrQztZQUN0Q3BELFVBQVU7WUFDVkwsS0FBSztZQUNMRCxhQUFhO1lBQ2IyRCxNQUFNO1lBQ05DLGNBQWM7WUFDZGIsZ0JBQWdCLElBQUlHO1lBQ3BCdEIsU0FBUztRQUNYO1FBRUEscUVBQXFFO1FBQ3JFakMsWUFBWStEO1FBQ1pWLFdBQVdHLEdBQUcsQ0FBQ0ksVUFBVUc7SUFDM0I7SUFFQTs7Ozs7Ozs7RUFRQSxHQUVBLDRFQUE0RTtJQUM1RSx3RUFBd0U7SUFDeEUsMkVBQTJFO0lBQzNFLDJFQUEyRTtJQUMzRSw0RUFBNEU7SUFDNUUsMEVBQTBFO0lBQzFFLDhFQUE4RTtJQUM5RSw2REFBNkQ7SUFDN0QsTUFBTTdCLG9CQUFvQkosZ0JBQWdCRyxPQUFPO0lBRWpELHFCQUNFLHNCQUFDaUMsK0JBQUFBLGVBQWUsQ0FBQ3ZDLFFBQVE7UUFFdkJDLE9BQUFBLFdBQUFBLEdBQ0UscUJBQUNuQyx1QkFBQUE7WUFBc0JwQixhQUFhQTtzQkFDbEMsbUNBQUM4RixlQUFBQSxhQUFhO2dCQUNaQyxnQkFBZ0IxQjtnQkFDaEJDLGFBQWFBO2dCQUNiQyxjQUFjQTswQkFFZCxtQ0FBQ1osaUJBQUFBO29CQUFnQkMsU0FBU0M7OEJBQ3hCLG1DQUFDbUMsZ0JBQUFBLDBCQUEwQjt3QkFDekJyQixVQUFVQTt3QkFDVkMsV0FBV0E7d0JBQ1hDLGNBQWNBO2tDQUVkLG1DQUFDb0Isa0JBQUFBLGdCQUFnQjtzQ0FDZixtQ0FBQ3hFLG1CQUFBQTtnQ0FDQ0csS0FBS0E7Z0NBQ0xGLE1BQU1BO2dDQUNOQyxXQUFXQTtnQ0FDWDNCLGFBQWFBOzs7Ozs7OztZQVMxQndFO1lBQ0FDO1lBQ0FDOztPQTlCSWU7QUFpQ1g7TUEvSXdCL0kiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xcbGF5b3V0LXJvdXRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB0eXBlIHtcbiAgQ2FjaGVOb2RlLFxuICBMYXp5Q2FjaGVOb2RlLFxuICBMb2FkaW5nTW9kdWxlRGF0YSxcbn0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9hcHAtcm91dGVyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUnXG5pbXBvcnQgdHlwZSB7XG4gIEZsaWdodFJvdXRlclN0YXRlLFxuICBGbGlnaHRTZWdtZW50UGF0aCxcbn0gZnJvbSAnLi4vLi4vc2VydmVyL2FwcC1yZW5kZXIvdHlwZXMnXG5pbXBvcnQgdHlwZSB7IEVycm9yQ29tcG9uZW50IH0gZnJvbSAnLi9lcnJvci1ib3VuZGFyeSdcbmltcG9ydCB0eXBlIHsgRm9jdXNBbmRTY3JvbGxSZWYgfSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzJ1xuXG5pbXBvcnQgUmVhY3QsIHtcbiAgdXNlQ29udGV4dCxcbiAgdXNlLFxuICBzdGFydFRyYW5zaXRpb24sXG4gIFN1c3BlbnNlLFxuICB1c2VEZWZlcnJlZFZhbHVlLFxuICB0eXBlIEpTWCxcbn0gZnJvbSAncmVhY3QnXG5pbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tJ1xuaW1wb3J0IHtcbiAgTGF5b3V0Um91dGVyQ29udGV4dCxcbiAgR2xvYmFsTGF5b3V0Um91dGVyQ29udGV4dCxcbiAgVGVtcGxhdGVDb250ZXh0LFxufSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2FwcC1yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IGZldGNoU2VydmVyUmVzcG9uc2UgfSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL2ZldGNoLXNlcnZlci1yZXNwb25zZSdcbmltcG9ydCB7IHVucmVzb2x2ZWRUaGVuYWJsZSB9IGZyb20gJy4vdW5yZXNvbHZlZC10aGVuYWJsZSdcbmltcG9ydCB7IEVycm9yQm91bmRhcnkgfSBmcm9tICcuL2Vycm9yLWJvdW5kYXJ5J1xuaW1wb3J0IHsgbWF0Y2hTZWdtZW50IH0gZnJvbSAnLi9tYXRjaC1zZWdtZW50cydcbmltcG9ydCB7IGhhbmRsZVNtb290aFNjcm9sbCB9IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2hhbmRsZS1zbW9vdGgtc2Nyb2xsJ1xuaW1wb3J0IHsgUmVkaXJlY3RCb3VuZGFyeSB9IGZyb20gJy4vcmVkaXJlY3QtYm91bmRhcnknXG5pbXBvcnQgeyBIVFRQQWNjZXNzRmFsbGJhY2tCb3VuZGFyeSB9IGZyb20gJy4vaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItYm91bmRhcnknXG5pbXBvcnQgeyBjcmVhdGVSb3V0ZXJDYWNoZUtleSB9IGZyb20gJy4vcm91dGVyLXJlZHVjZXIvY3JlYXRlLXJvdXRlci1jYWNoZS1rZXknXG5pbXBvcnQgeyBoYXNJbnRlcmNlcHRpb25Sb3V0ZUluQ3VycmVudFRyZWUgfSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL3JlZHVjZXJzL2hhcy1pbnRlcmNlcHRpb24tcm91dGUtaW4tY3VycmVudC10cmVlJ1xuXG4vKipcbiAqIEFkZCByZWZldGNoIG1hcmtlciB0byByb3V0ZXIgc3RhdGUgYXQgdGhlIHBvaW50IG9mIHRoZSBjdXJyZW50IGxheW91dCBzZWdtZW50LlxuICogVGhpcyBlbnN1cmVzIHRoZSByZXNwb25zZSByZXR1cm5lZCBpcyBub3QgZnVydGhlciBkb3duIHRoYW4gdGhlIGN1cnJlbnQgbGF5b3V0IHNlZ21lbnQuXG4gKi9cbmZ1bmN0aW9uIHdhbGtBZGRSZWZldGNoKFxuICBzZWdtZW50UGF0aFRvV2FsazogRmxpZ2h0U2VnbWVudFBhdGggfCB1bmRlZmluZWQsXG4gIHRyZWVUb1JlY3JlYXRlOiBGbGlnaHRSb3V0ZXJTdGF0ZVxuKTogRmxpZ2h0Um91dGVyU3RhdGUge1xuICBpZiAoc2VnbWVudFBhdGhUb1dhbGspIHtcbiAgICBjb25zdCBbc2VnbWVudCwgcGFyYWxsZWxSb3V0ZUtleV0gPSBzZWdtZW50UGF0aFRvV2Fsa1xuICAgIGNvbnN0IGlzTGFzdCA9IHNlZ21lbnRQYXRoVG9XYWxrLmxlbmd0aCA9PT0gMlxuXG4gICAgaWYgKG1hdGNoU2VnbWVudCh0cmVlVG9SZWNyZWF0ZVswXSwgc2VnbWVudCkpIHtcbiAgICAgIGlmICh0cmVlVG9SZWNyZWF0ZVsxXS5oYXNPd25Qcm9wZXJ0eShwYXJhbGxlbFJvdXRlS2V5KSkge1xuICAgICAgICBpZiAoaXNMYXN0KSB7XG4gICAgICAgICAgY29uc3Qgc3ViVHJlZSA9IHdhbGtBZGRSZWZldGNoKFxuICAgICAgICAgICAgdW5kZWZpbmVkLFxuICAgICAgICAgICAgdHJlZVRvUmVjcmVhdGVbMV1bcGFyYWxsZWxSb3V0ZUtleV1cbiAgICAgICAgICApXG4gICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHRyZWVUb1JlY3JlYXRlWzBdLFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAuLi50cmVlVG9SZWNyZWF0ZVsxXSxcbiAgICAgICAgICAgICAgW3BhcmFsbGVsUm91dGVLZXldOiBbXG4gICAgICAgICAgICAgICAgc3ViVHJlZVswXSxcbiAgICAgICAgICAgICAgICBzdWJUcmVlWzFdLFxuICAgICAgICAgICAgICAgIHN1YlRyZWVbMl0sXG4gICAgICAgICAgICAgICAgJ3JlZmV0Y2gnLFxuICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICBdXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgIHRyZWVUb1JlY3JlYXRlWzBdLFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIC4uLnRyZWVUb1JlY3JlYXRlWzFdLFxuICAgICAgICAgICAgW3BhcmFsbGVsUm91dGVLZXldOiB3YWxrQWRkUmVmZXRjaChcbiAgICAgICAgICAgICAgc2VnbWVudFBhdGhUb1dhbGsuc2xpY2UoMiksXG4gICAgICAgICAgICAgIHRyZWVUb1JlY3JlYXRlWzFdW3BhcmFsbGVsUm91dGVLZXldXG4gICAgICAgICAgICApLFxuICAgICAgICAgIH0sXG4gICAgICAgIF1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gdHJlZVRvUmVjcmVhdGVcbn1cblxuY29uc3QgX19ET01fSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFID0gKFxuICBSZWFjdERPTSBhcyBhbnlcbikuX19ET01fSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFXG5cbi8vIFRPRE8tQVBQOiBSZXBsYWNlIHdpdGggbmV3IFJlYWN0IEFQSSBmb3IgZmluZGluZyBkb20gbm9kZXMgd2l0aG91dCBhIGByZWZgIHdoZW4gYXZhaWxhYmxlXG4vKipcbiAqIFdyYXBzIFJlYWN0RE9NLmZpbmRET01Ob2RlIHdpdGggYWRkaXRpb25hbCBsb2dpYyB0byBoaWRlIFJlYWN0IFN0cmljdCBNb2RlIHdhcm5pbmdcbiAqL1xuZnVuY3Rpb24gZmluZERPTU5vZGUoXG4gIGluc3RhbmNlOiBSZWFjdC5SZWFjdEluc3RhbmNlIHwgbnVsbCB8IHVuZGVmaW5lZFxuKTogRWxlbWVudCB8IFRleHQgfCBudWxsIHtcbiAgLy8gVHJlZS1zaGFrZSBmb3Igc2VydmVyIGJ1bmRsZVxuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsXG5cbiAgLy8gX19ET01fSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFLmZpbmRET01Ob2RlIGlzIG51bGwgZHVyaW5nIG1vZHVsZSBpbml0LlxuICAvLyBXZSBuZWVkIHRvIGxhemlseSByZWZlcmVuY2UgaXQuXG4gIGNvbnN0IGludGVybmFsX3JlYWN0RE9NZmluZERPTU5vZGUgPVxuICAgIF9fRE9NX0lOVEVSTkFMU19ET19OT1RfVVNFX09SX1dBUk5fVVNFUlNfVEhFWV9DQU5OT1RfVVBHUkFERS5maW5kRE9NTm9kZVxuICByZXR1cm4gaW50ZXJuYWxfcmVhY3RET01maW5kRE9NTm9kZShpbnN0YW5jZSlcbn1cblxuY29uc3QgcmVjdFByb3BlcnRpZXMgPSBbXG4gICdib3R0b20nLFxuICAnaGVpZ2h0JyxcbiAgJ2xlZnQnLFxuICAncmlnaHQnLFxuICAndG9wJyxcbiAgJ3dpZHRoJyxcbiAgJ3gnLFxuICAneScsXG5dIGFzIGNvbnN0XG4vKipcbiAqIENoZWNrIGlmIGEgSFRNTEVsZW1lbnQgaXMgaGlkZGVuIG9yIGZpeGVkL3N0aWNreSBwb3NpdGlvblxuICovXG5mdW5jdGlvbiBzaG91bGRTa2lwRWxlbWVudChlbGVtZW50OiBIVE1MRWxlbWVudCkge1xuICAvLyB3ZSBpZ25vcmUgZml4ZWQgb3Igc3RpY2t5IHBvc2l0aW9uZWQgZWxlbWVudHMgc2luY2UgdGhleSdsbCBsaWtlbHkgcGFzcyB0aGUgXCJpbi12aWV3cG9ydFwiIGNoZWNrXG4gIC8vIGFuZCB3aWxsIHJlc3VsdCBpbiBhIHNpdHVhdGlvbiB3ZSBiYWlsIG9uIHNjcm9sbCBiZWNhdXNlIG9mIHNvbWV0aGluZyBsaWtlIGEgZml4ZWQgbmF2LFxuICAvLyBldmVuIHRob3VnaCB0aGUgYWN0dWFsIHBhZ2UgY29udGVudCBpcyBvZmZzY3JlZW5cbiAgaWYgKFsnc3RpY2t5JywgJ2ZpeGVkJ10uaW5jbHVkZXMoZ2V0Q29tcHV0ZWRTdHlsZShlbGVtZW50KS5wb3NpdGlvbikpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgJ1NraXBwaW5nIGF1dG8tc2Nyb2xsIGJlaGF2aW9yIGR1ZSB0byBgcG9zaXRpb246IHN0aWNreWAgb3IgYHBvc2l0aW9uOiBmaXhlZGAgb24gZWxlbWVudDonLFxuICAgICAgICBlbGVtZW50XG4gICAgICApXG4gICAgfVxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICAvLyBVc2VzIGBnZXRCb3VuZGluZ0NsaWVudFJlY3RgIHRvIGNoZWNrIGlmIHRoZSBlbGVtZW50IGlzIGhpZGRlbiBpbnN0ZWFkIG9mIGBvZmZzZXRQYXJlbnRgXG4gIC8vIGJlY2F1c2UgYG9mZnNldFBhcmVudGAgZG9lc24ndCBjb25zaWRlciBkb2N1bWVudC9ib2R5XG4gIGNvbnN0IHJlY3QgPSBlbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpXG4gIHJldHVybiByZWN0UHJvcGVydGllcy5ldmVyeSgoaXRlbSkgPT4gcmVjdFtpdGVtXSA9PT0gMClcbn1cblxuLyoqXG4gKiBDaGVjayBpZiB0aGUgdG9wIGNvcm5lciBvZiB0aGUgSFRNTEVsZW1lbnQgaXMgaW4gdGhlIHZpZXdwb3J0LlxuICovXG5mdW5jdGlvbiB0b3BPZkVsZW1lbnRJblZpZXdwb3J0KGVsZW1lbnQ6IEhUTUxFbGVtZW50LCB2aWV3cG9ydEhlaWdodDogbnVtYmVyKSB7XG4gIGNvbnN0IHJlY3QgPSBlbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpXG4gIHJldHVybiByZWN0LnRvcCA+PSAwICYmIHJlY3QudG9wIDw9IHZpZXdwb3J0SGVpZ2h0XG59XG5cbi8qKlxuICogRmluZCB0aGUgRE9NIG5vZGUgZm9yIGEgaGFzaCBmcmFnbWVudC5cbiAqIElmIGB0b3BgIHRoZSBwYWdlIGhhcyB0byBzY3JvbGwgdG8gdGhlIHRvcCBvZiB0aGUgcGFnZS4gVGhpcyBtaXJyb3JzIHRoZSBicm93c2VyJ3MgYmVoYXZpb3IuXG4gKiBJZiB0aGUgaGFzaCBmcmFnbWVudCBpcyBhbiBpZCwgdGhlIHBhZ2UgaGFzIHRvIHNjcm9sbCB0byB0aGUgZWxlbWVudCB3aXRoIHRoYXQgaWQuXG4gKiBJZiB0aGUgaGFzaCBmcmFnbWVudCBpcyBhIG5hbWUsIHRoZSBwYWdlIGhhcyB0byBzY3JvbGwgdG8gdGhlIGZpcnN0IGVsZW1lbnQgd2l0aCB0aGF0IG5hbWUuXG4gKi9cbmZ1bmN0aW9uIGdldEhhc2hGcmFnbWVudERvbU5vZGUoaGFzaEZyYWdtZW50OiBzdHJpbmcpIHtcbiAgLy8gSWYgdGhlIGhhc2ggZnJhZ21lbnQgaXMgYHRvcGAgdGhlIHBhZ2UgaGFzIHRvIHNjcm9sbCB0byB0aGUgdG9wIG9mIHRoZSBwYWdlLlxuICBpZiAoaGFzaEZyYWdtZW50ID09PSAndG9wJykge1xuICAgIHJldHVybiBkb2N1bWVudC5ib2R5XG4gIH1cblxuICAvLyBJZiB0aGUgaGFzaCBmcmFnbWVudCBpcyBhbiBpZCwgdGhlIHBhZ2UgaGFzIHRvIHNjcm9sbCB0byB0aGUgZWxlbWVudCB3aXRoIHRoYXQgaWQuXG4gIHJldHVybiAoXG4gICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoaGFzaEZyYWdtZW50KSA/P1xuICAgIC8vIElmIHRoZSBoYXNoIGZyYWdtZW50IGlzIGEgbmFtZSwgdGhlIHBhZ2UgaGFzIHRvIHNjcm9sbCB0byB0aGUgZmlyc3QgZWxlbWVudCB3aXRoIHRoYXQgbmFtZS5cbiAgICBkb2N1bWVudC5nZXRFbGVtZW50c0J5TmFtZShoYXNoRnJhZ21lbnQpWzBdXG4gIClcbn1cbmludGVyZmFjZSBTY3JvbGxBbmRGb2N1c0hhbmRsZXJQcm9wcyB7XG4gIGZvY3VzQW5kU2Nyb2xsUmVmOiBGb2N1c0FuZFNjcm9sbFJlZlxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG4gIHNlZ21lbnRQYXRoOiBGbGlnaHRTZWdtZW50UGF0aFxufVxuY2xhc3MgSW5uZXJTY3JvbGxBbmRGb2N1c0hhbmRsZXIgZXh0ZW5kcyBSZWFjdC5Db21wb25lbnQ8U2Nyb2xsQW5kRm9jdXNIYW5kbGVyUHJvcHM+IHtcbiAgaGFuZGxlUG90ZW50aWFsU2Nyb2xsID0gKCkgPT4ge1xuICAgIC8vIEhhbmRsZSBzY3JvbGwgYW5kIGZvY3VzLCBpdCdzIG9ubHkgYXBwbGllZCBvbmNlIGluIHRoZSBmaXJzdCB1c2VFZmZlY3QgdGhhdCB0cmlnZ2VycyB0aGF0IGNoYW5nZWQuXG4gICAgY29uc3QgeyBmb2N1c0FuZFNjcm9sbFJlZiwgc2VnbWVudFBhdGggfSA9IHRoaXMucHJvcHNcblxuICAgIGlmIChmb2N1c0FuZFNjcm9sbFJlZi5hcHBseSkge1xuICAgICAgLy8gc2VnbWVudFBhdGhzIGlzIGFuIGFycmF5IG9mIHNlZ21lbnQgcGF0aHMgdGhhdCBzaG91bGQgYmUgc2Nyb2xsZWQgdG9cbiAgICAgIC8vIGlmIHRoZSBjdXJyZW50IHNlZ21lbnQgcGF0aCBpcyBub3QgaW4gdGhlIGFycmF5LCB0aGUgc2Nyb2xsIGlzIG5vdCBhcHBsaWVkXG4gICAgICAvLyB1bmxlc3MgdGhlIGFycmF5IGlzIGVtcHR5LCBpbiB3aGljaCBjYXNlIHRoZSBzY3JvbGwgaXMgYWx3YXlzIGFwcGxpZWRcbiAgICAgIGlmIChcbiAgICAgICAgZm9jdXNBbmRTY3JvbGxSZWYuc2VnbWVudFBhdGhzLmxlbmd0aCAhPT0gMCAmJlxuICAgICAgICAhZm9jdXNBbmRTY3JvbGxSZWYuc2VnbWVudFBhdGhzLnNvbWUoKHNjcm9sbFJlZlNlZ21lbnRQYXRoKSA9PlxuICAgICAgICAgIHNlZ21lbnRQYXRoLmV2ZXJ5KChzZWdtZW50LCBpbmRleCkgPT5cbiAgICAgICAgICAgIG1hdGNoU2VnbWVudChzZWdtZW50LCBzY3JvbGxSZWZTZWdtZW50UGF0aFtpbmRleF0pXG4gICAgICAgICAgKVxuICAgICAgICApXG4gICAgICApIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGxldCBkb21Ob2RlOlxuICAgICAgICB8IFJldHVyblR5cGU8dHlwZW9mIGdldEhhc2hGcmFnbWVudERvbU5vZGU+XG4gICAgICAgIHwgUmV0dXJuVHlwZTx0eXBlb2YgZmluZERPTU5vZGU+ID0gbnVsbFxuICAgICAgY29uc3QgaGFzaEZyYWdtZW50ID0gZm9jdXNBbmRTY3JvbGxSZWYuaGFzaEZyYWdtZW50XG5cbiAgICAgIGlmIChoYXNoRnJhZ21lbnQpIHtcbiAgICAgICAgZG9tTm9kZSA9IGdldEhhc2hGcmFnbWVudERvbU5vZGUoaGFzaEZyYWdtZW50KVxuICAgICAgfVxuXG4gICAgICAvLyBgZmluZERPTU5vZGVgIGlzIHRyaWNreSBiZWNhdXNlIGl0IHJldHVybnMganVzdCB0aGUgZmlyc3QgY2hpbGQgaWYgdGhlIGNvbXBvbmVudCBpcyBhIGZyYWdtZW50LlxuICAgICAgLy8gVGhpcyBhbHJlYWR5IGNhdXNlZCBhIGJ1ZyB3aGVyZSB0aGUgZmlyc3QgY2hpbGQgd2FzIGEgPGxpbmsvPiBpbiBoZWFkLlxuICAgICAgaWYgKCFkb21Ob2RlKSB7XG4gICAgICAgIGRvbU5vZGUgPSBmaW5kRE9NTm9kZSh0aGlzKVxuICAgICAgfVxuXG4gICAgICAvLyBJZiB0aGVyZSBpcyBubyBET00gbm9kZSB0aGlzIGxheW91dC1yb3V0ZXIgbGV2ZWwgaXMgc2tpcHBlZC4gSXQnbGwgYmUgaGFuZGxlZCBoaWdoZXItdXAgaW4gdGhlIHRyZWUuXG4gICAgICBpZiAoIShkb21Ob2RlIGluc3RhbmNlb2YgRWxlbWVudCkpIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIC8vIFZlcmlmeSBpZiB0aGUgZWxlbWVudCBpcyBhIEhUTUxFbGVtZW50IGFuZCBpZiB3ZSB3YW50IHRvIGNvbnNpZGVyIGl0IGZvciBzY3JvbGwgYmVoYXZpb3IuXG4gICAgICAvLyBJZiB0aGUgZWxlbWVudCBpcyBza2lwcGVkLCB0cnkgdG8gc2VsZWN0IHRoZSBuZXh0IHNpYmxpbmcgYW5kIHRyeSBhZ2Fpbi5cbiAgICAgIHdoaWxlICghKGRvbU5vZGUgaW5zdGFuY2VvZiBIVE1MRWxlbWVudCkgfHwgc2hvdWxkU2tpcEVsZW1lbnQoZG9tTm9kZSkpIHtcbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICAgICAgICBpZiAoZG9tTm9kZS5wYXJlbnRFbGVtZW50Py5sb2NhbE5hbWUgPT09ICdoZWFkJykge1xuICAgICAgICAgICAgLy8gVE9ETzogV2UgZW50ZXIgdGhpcyBzdGF0ZSB3aGVuIG1ldGFkYXRhIHdhcyByZW5kZXJlZCBhcyBwYXJ0IG9mIHRoZSBwYWdlIG9yIHZpYSBOZXh0LmpzLlxuICAgICAgICAgICAgLy8gVGhpcyBpcyBhbHdheXMgYSBidWcgaW4gTmV4dC5qcyBhbmQgY2F1c2VkIGJ5IFJlYWN0IGhvaXN0aW5nIG1ldGFkYXRhLlxuICAgICAgICAgICAgLy8gV2UgbmVlZCB0byByZXBsYWNlIGBmaW5kRE9NTm9kZWAgaW4gZmF2b3Igb2YgRnJhZ21lbnQgUmVmcyAod2hlbiBhdmFpbGFibGUpIHNvIHRoYXQgd2UgY2FuIHNraXAgb3ZlciBtZXRhZGF0YS5cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBObyBzaWJsaW5ncyBmb3VuZCB0aGF0IG1hdGNoIHRoZSBjcml0ZXJpYSBhcmUgZm91bmQsIHNvIGhhbmRsZSBzY3JvbGwgaGlnaGVyIHVwIGluIHRoZSB0cmVlIGluc3RlYWQuXG4gICAgICAgIGlmIChkb21Ob2RlLm5leHRFbGVtZW50U2libGluZyA9PT0gbnVsbCkge1xuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGRvbU5vZGUgPSBkb21Ob2RlLm5leHRFbGVtZW50U2libGluZ1xuICAgICAgfVxuXG4gICAgICAvLyBTdGF0ZSBpcyBtdXRhdGVkIHRvIGVuc3VyZSB0aGF0IHRoZSBmb2N1cyBhbmQgc2Nyb2xsIGlzIGFwcGxpZWQgb25seSBvbmNlLlxuICAgICAgZm9jdXNBbmRTY3JvbGxSZWYuYXBwbHkgPSBmYWxzZVxuICAgICAgZm9jdXNBbmRTY3JvbGxSZWYuaGFzaEZyYWdtZW50ID0gbnVsbFxuICAgICAgZm9jdXNBbmRTY3JvbGxSZWYuc2VnbWVudFBhdGhzID0gW11cblxuICAgICAgaGFuZGxlU21vb3RoU2Nyb2xsKFxuICAgICAgICAoKSA9PiB7XG4gICAgICAgICAgLy8gSW4gY2FzZSBvZiBoYXNoIHNjcm9sbCwgd2Ugb25seSBuZWVkIHRvIHNjcm9sbCB0aGUgZWxlbWVudCBpbnRvIHZpZXdcbiAgICAgICAgICBpZiAoaGFzaEZyYWdtZW50KSB7XG4gICAgICAgICAgICA7KGRvbU5vZGUgYXMgSFRNTEVsZW1lbnQpLnNjcm9sbEludG9WaWV3KClcblxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgfVxuICAgICAgICAgIC8vIFN0b3JlIHRoZSBjdXJyZW50IHZpZXdwb3J0IGhlaWdodCBiZWNhdXNlIHJlYWRpbmcgYGNsaWVudEhlaWdodGAgY2F1c2VzIGEgcmVmbG93LFxuICAgICAgICAgIC8vIGFuZCBpdCB3b24ndCBjaGFuZ2UgZHVyaW5nIHRoaXMgZnVuY3Rpb24uXG4gICAgICAgICAgY29uc3QgaHRtbEVsZW1lbnQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnRcbiAgICAgICAgICBjb25zdCB2aWV3cG9ydEhlaWdodCA9IGh0bWxFbGVtZW50LmNsaWVudEhlaWdodFxuXG4gICAgICAgICAgLy8gSWYgdGhlIGVsZW1lbnQncyB0b3AgZWRnZSBpcyBhbHJlYWR5IGluIHRoZSB2aWV3cG9ydCwgZXhpdCBlYXJseS5cbiAgICAgICAgICBpZiAodG9wT2ZFbGVtZW50SW5WaWV3cG9ydChkb21Ob2RlIGFzIEhUTUxFbGVtZW50LCB2aWV3cG9ydEhlaWdodCkpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIE90aGVyd2lzZSwgdHJ5IHNjcm9sbGluZyBnbyB0aGUgdG9wIG9mIHRoZSBkb2N1bWVudCB0byBiZSBiYWNrd2FyZCBjb21wYXRpYmxlIHdpdGggcGFnZXNcbiAgICAgICAgICAvLyBzY3JvbGxJbnRvVmlldygpIGNhbGxlZCBvbiBgPGh0bWwvPmAgZWxlbWVudCBzY3JvbGxzIGhvcml6b250YWxseSBvbiBjaHJvbWUgYW5kIGZpcmVmb3ggKHRoYXQgc2hvdWxkbid0IGhhcHBlbilcbiAgICAgICAgICAvLyBXZSBjb3VsZCB1c2UgaXQgdG8gc2Nyb2xsIGhvcml6b250YWxseSBmb2xsb3dpbmcgUlRMIGJ1dCB0aGF0IGFsc28gc2VlbXMgdG8gYmUgYnJva2VuIC0gaXQgd2lsbCBhbHdheXMgc2Nyb2xsIGxlZnRcbiAgICAgICAgICAvLyBzY3JvbGxMZWZ0ID0gMCBhbHNvIHNlZW1zIHRvIGlnbm9yZSBSVEwgYW5kIG1hbnVhbGx5IGNoZWNraW5nIGZvciBSVEwgaXMgdG9vIG11Y2ggaGFzc2xlIHNvIHdlIHdpbGwgc2Nyb2xsIGp1c3QgdmVydGljYWxseVxuICAgICAgICAgIGh0bWxFbGVtZW50LnNjcm9sbFRvcCA9IDBcblxuICAgICAgICAgIC8vIFNjcm9sbCB0byBkb21Ob2RlIGlmIGRvbU5vZGUgaXMgbm90IGluIHZpZXdwb3J0IHdoZW4gc2Nyb2xsZWQgdG8gdG9wIG9mIGRvY3VtZW50XG4gICAgICAgICAgaWYgKCF0b3BPZkVsZW1lbnRJblZpZXdwb3J0KGRvbU5vZGUgYXMgSFRNTEVsZW1lbnQsIHZpZXdwb3J0SGVpZ2h0KSkge1xuICAgICAgICAgICAgLy8gU2Nyb2xsIGludG8gdmlldyBkb2Vzbid0IHNjcm9sbCBob3Jpem9udGFsbHkgYnkgZGVmYXVsdCB3aGVuIG5vdCBuZWVkZWRcbiAgICAgICAgICAgIDsoZG9tTm9kZSBhcyBIVE1MRWxlbWVudCkuc2Nyb2xsSW50b1ZpZXcoKVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIC8vIFdlIHdpbGwgZm9yY2UgbGF5b3V0IGJ5IHF1ZXJ5aW5nIGRvbU5vZGUgcG9zaXRpb25cbiAgICAgICAgICBkb250Rm9yY2VMYXlvdXQ6IHRydWUsXG4gICAgICAgICAgb25seUhhc2hDaGFuZ2U6IGZvY3VzQW5kU2Nyb2xsUmVmLm9ubHlIYXNoQ2hhbmdlLFxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIC8vIE11dGF0ZSBhZnRlciBzY3JvbGxpbmcgc28gdGhhdCBpdCBjYW4gYmUgcmVhZCBieSBgaGFuZGxlU21vb3RoU2Nyb2xsYFxuICAgICAgZm9jdXNBbmRTY3JvbGxSZWYub25seUhhc2hDaGFuZ2UgPSBmYWxzZVxuXG4gICAgICAvLyBTZXQgZm9jdXMgb24gdGhlIGVsZW1lbnRcbiAgICAgIGRvbU5vZGUuZm9jdXMoKVxuICAgIH1cbiAgfVxuXG4gIGNvbXBvbmVudERpZE1vdW50KCkge1xuICAgIHRoaXMuaGFuZGxlUG90ZW50aWFsU2Nyb2xsKClcbiAgfVxuXG4gIGNvbXBvbmVudERpZFVwZGF0ZSgpIHtcbiAgICAvLyBCZWNhdXNlIHRoaXMgcHJvcGVydHkgaXMgb3ZlcndyaXR0ZW4gaW4gaGFuZGxlUG90ZW50aWFsU2Nyb2xsIGl0J3MgZmluZSB0byBhbHdheXMgcnVuIGl0IHdoZW4gdHJ1ZSBhcyBpdCdsbCBiZSBzZXQgdG8gZmFsc2UgZm9yIHN1YnNlcXVlbnQgcmVuZGVycy5cbiAgICBpZiAodGhpcy5wcm9wcy5mb2N1c0FuZFNjcm9sbFJlZi5hcHBseSkge1xuICAgICAgdGhpcy5oYW5kbGVQb3RlbnRpYWxTY3JvbGwoKVxuICAgIH1cbiAgfVxuXG4gIHJlbmRlcigpIHtcbiAgICByZXR1cm4gdGhpcy5wcm9wcy5jaGlsZHJlblxuICB9XG59XG5cbmZ1bmN0aW9uIFNjcm9sbEFuZEZvY3VzSGFuZGxlcih7XG4gIHNlZ21lbnRQYXRoLFxuICBjaGlsZHJlbixcbn06IHtcbiAgc2VnbWVudFBhdGg6IEZsaWdodFNlZ21lbnRQYXRoXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoR2xvYmFsTGF5b3V0Um91dGVyQ29udGV4dClcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdpbnZhcmlhbnQgZ2xvYmFsIGxheW91dCByb3V0ZXIgbm90IG1vdW50ZWQnKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8SW5uZXJTY3JvbGxBbmRGb2N1c0hhbmRsZXJcbiAgICAgIHNlZ21lbnRQYXRoPXtzZWdtZW50UGF0aH1cbiAgICAgIGZvY3VzQW5kU2Nyb2xsUmVmPXtjb250ZXh0LmZvY3VzQW5kU2Nyb2xsUmVmfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0lubmVyU2Nyb2xsQW5kRm9jdXNIYW5kbGVyPlxuICApXG59XG5cbi8qKlxuICogSW5uZXJMYXlvdXRSb3V0ZXIgaGFuZGxlcyByZW5kZXJpbmcgdGhlIHByb3ZpZGVkIHNlZ21lbnQgYmFzZWQgb24gdGhlIGNhY2hlLlxuICovXG5mdW5jdGlvbiBJbm5lckxheW91dFJvdXRlcih7XG4gIHRyZWUsXG4gIHNlZ21lbnRQYXRoLFxuICBjYWNoZU5vZGUsXG4gIHVybCxcbn06IHtcbiAgdHJlZTogRmxpZ2h0Um91dGVyU3RhdGVcbiAgc2VnbWVudFBhdGg6IEZsaWdodFNlZ21lbnRQYXRoXG4gIGNhY2hlTm9kZTogQ2FjaGVOb2RlXG4gIHVybDogc3RyaW5nXG59KSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEdsb2JhbExheW91dFJvdXRlckNvbnRleHQpXG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcignaW52YXJpYW50IGdsb2JhbCBsYXlvdXQgcm91dGVyIG5vdCBtb3VudGVkJylcbiAgfVxuXG4gIGNvbnN0IHsgY2hhbmdlQnlTZXJ2ZXJSZXNwb25zZSwgdHJlZTogZnVsbFRyZWUgfSA9IGNvbnRleHRcblxuICAvLyBgcnNjYCByZXByZXNlbnRzIHRoZSByZW5kZXJhYmxlIG5vZGUgZm9yIHRoaXMgc2VnbWVudC5cblxuICAvLyBJZiB0aGlzIHNlZ21lbnQgaGFzIGEgYHByZWZldGNoUnNjYCwgaXQncyB0aGUgc3RhdGljYWxseSBwcmVmZXRjaGVkIGRhdGEuXG4gIC8vIFdlIHNob3VsZCB1c2UgdGhhdCBvbiBpbml0aWFsIHJlbmRlciBpbnN0ZWFkIG9mIGByc2NgLiBUaGVuIHdlJ2xsIHN3aXRjaFxuICAvLyB0byBgcnNjYCB3aGVuIHRoZSBkeW5hbWljIHJlc3BvbnNlIHN0cmVhbXMgaW4uXG4gIC8vXG4gIC8vIElmIG5vIHByZWZldGNoIGRhdGEgaXMgYXZhaWxhYmxlLCB0aGVuIHdlIGdvIHN0cmFpZ2h0IHRvIHJlbmRlcmluZyBgcnNjYC5cbiAgY29uc3QgcmVzb2x2ZWRQcmVmZXRjaFJzYyA9XG4gICAgY2FjaGVOb2RlLnByZWZldGNoUnNjICE9PSBudWxsID8gY2FjaGVOb2RlLnByZWZldGNoUnNjIDogY2FjaGVOb2RlLnJzY1xuXG4gIC8vIFdlIHVzZSBgdXNlRGVmZXJyZWRWYWx1ZWAgdG8gaGFuZGxlIHN3aXRjaGluZyBiZXR3ZWVuIHRoZSBwcmVmZXRjaGVkIGFuZFxuICAvLyBmaW5hbCB2YWx1ZXMuIFRoZSBzZWNvbmQgYXJndW1lbnQgaXMgcmV0dXJuZWQgb24gaW5pdGlhbCByZW5kZXIsIHRoZW4gaXRcbiAgLy8gcmUtcmVuZGVycyB3aXRoIHRoZSBmaXJzdCBhcmd1bWVudC5cbiAgY29uc3QgcnNjOiBhbnkgPSB1c2VEZWZlcnJlZFZhbHVlKGNhY2hlTm9kZS5yc2MsIHJlc29sdmVkUHJlZmV0Y2hSc2MpXG5cbiAgLy8gYHJzY2AgaXMgZWl0aGVyIGEgUmVhY3Qgbm9kZSBvciBhIHByb21pc2UgZm9yIGEgUmVhY3Qgbm9kZSwgZXhjZXB0IHdlXG4gIC8vIHNwZWNpYWwgY2FzZSBgbnVsbGAgdG8gcmVwcmVzZW50IHRoYXQgdGhpcyBzZWdtZW50J3MgZGF0YSBpcyBtaXNzaW5nLiBJZlxuICAvLyBpdCdzIGEgcHJvbWlzZSwgd2UgbmVlZCB0byB1bndyYXAgaXQgc28gd2UgY2FuIGRldGVybWluZSB3aGV0aGVyIG9yIG5vdCB0aGVcbiAgLy8gZGF0YSBpcyBtaXNzaW5nLlxuICBjb25zdCByZXNvbHZlZFJzYzogUmVhY3QuUmVhY3ROb2RlID1cbiAgICB0eXBlb2YgcnNjID09PSAnb2JqZWN0JyAmJiByc2MgIT09IG51bGwgJiYgdHlwZW9mIHJzYy50aGVuID09PSAnZnVuY3Rpb24nXG4gICAgICA/IHVzZShyc2MpXG4gICAgICA6IHJzY1xuXG4gIGlmICghcmVzb2x2ZWRSc2MpIHtcbiAgICAvLyBUaGUgZGF0YSBmb3IgdGhpcyBzZWdtZW50IGlzIG5vdCBhdmFpbGFibGUsIGFuZCB0aGVyZSdzIG5vIHBlbmRpbmdcbiAgICAvLyBuYXZpZ2F0aW9uIHRoYXQgd2lsbCBiZSBhYmxlIHRvIGZ1bGZpbGwgaXQuIFdlIG5lZWQgdG8gZmV0Y2ggbW9yZSBmcm9tXG4gICAgLy8gdGhlIHNlcnZlciBhbmQgcGF0Y2ggdGhlIGNhY2hlLlxuXG4gICAgLy8gQ2hlY2sgaWYgdGhlcmUncyBhbHJlYWR5IGEgcGVuZGluZyByZXF1ZXN0LlxuICAgIGxldCBsYXp5RGF0YSA9IGNhY2hlTm9kZS5sYXp5RGF0YVxuICAgIGlmIChsYXp5RGF0YSA9PT0gbnVsbCkge1xuICAgICAgLyoqXG4gICAgICAgKiBSb3V0ZXIgc3RhdGUgd2l0aCByZWZldGNoIG1hcmtlciBhZGRlZFxuICAgICAgICovXG4gICAgICAvLyBUT0RPLUFQUDogcmVtb3ZlICcnXG4gICAgICBjb25zdCByZWZldGNoVHJlZSA9IHdhbGtBZGRSZWZldGNoKFsnJywgLi4uc2VnbWVudFBhdGhdLCBmdWxsVHJlZSlcbiAgICAgIGNvbnN0IGluY2x1ZGVOZXh0VXJsID0gaGFzSW50ZXJjZXB0aW9uUm91dGVJbkN1cnJlbnRUcmVlKGZ1bGxUcmVlKVxuICAgICAgY2FjaGVOb2RlLmxhenlEYXRhID0gbGF6eURhdGEgPSBmZXRjaFNlcnZlclJlc3BvbnNlKFxuICAgICAgICBuZXcgVVJMKHVybCwgbG9jYXRpb24ub3JpZ2luKSxcbiAgICAgICAge1xuICAgICAgICAgIGZsaWdodFJvdXRlclN0YXRlOiByZWZldGNoVHJlZSxcbiAgICAgICAgICBuZXh0VXJsOiBpbmNsdWRlTmV4dFVybCA/IGNvbnRleHQubmV4dFVybCA6IG51bGwsXG4gICAgICAgIH1cbiAgICAgICkudGhlbigoc2VydmVyUmVzcG9uc2UpID0+IHtcbiAgICAgICAgc3RhcnRUcmFuc2l0aW9uKCgpID0+IHtcbiAgICAgICAgICBjaGFuZ2VCeVNlcnZlclJlc3BvbnNlKHtcbiAgICAgICAgICAgIHByZXZpb3VzVHJlZTogZnVsbFRyZWUsXG4gICAgICAgICAgICBzZXJ2ZXJSZXNwb25zZSxcbiAgICAgICAgICB9KVxuICAgICAgICB9KVxuXG4gICAgICAgIHJldHVybiBzZXJ2ZXJSZXNwb25zZVxuICAgICAgfSlcblxuICAgICAgLy8gU3VzcGVuZCB3aGlsZSB3YWl0aW5nIGZvciBsYXp5RGF0YSB0byByZXNvbHZlXG4gICAgICB1c2UobGF6eURhdGEpXG4gICAgfVxuICAgIC8vIFN1c3BlbmQgaW5maW5pdGVseSBhcyBgY2hhbmdlQnlTZXJ2ZXJSZXNwb25zZWAgd2lsbCBjYXVzZSBhIGRpZmZlcmVudCBwYXJ0IG9mIHRoZSB0cmVlIHRvIGJlIHJlbmRlcmVkLlxuICAgIC8vIEEgZmFsc2V5IGByZXNvbHZlZFJzY2AgaW5kaWNhdGVzIG1pc3NpbmcgZGF0YSAtLSB3ZSBzaG91bGQgbm90IGNvbW1pdCB0aGF0IGJyYW5jaCwgYW5kIHdlIG5lZWQgdG8gd2FpdCBmb3IgdGhlIGRhdGEgdG8gYXJyaXZlLlxuICAgIHVzZSh1bnJlc29sdmVkVGhlbmFibGUpIGFzIG5ldmVyXG4gIH1cblxuICAvLyBJZiB3ZSBnZXQgdG8gdGhpcyBwb2ludCwgdGhlbiB3ZSBrbm93IHdlIGhhdmUgc29tZXRoaW5nIHdlIGNhbiByZW5kZXIuXG4gIGNvbnN0IHN1YnRyZWUgPSAoXG4gICAgLy8gVGhlIGxheW91dCByb3V0ZXIgY29udGV4dCBuYXJyb3dzIGRvd24gdHJlZSBhbmQgY2hpbGROb2RlcyBhdCBlYWNoIGxldmVsLlxuICAgIDxMYXlvdXRSb3V0ZXJDb250ZXh0LlByb3ZpZGVyXG4gICAgICB2YWx1ZT17e1xuICAgICAgICBwYXJlbnRUcmVlOiB0cmVlLFxuICAgICAgICBwYXJlbnRDYWNoZU5vZGU6IGNhY2hlTm9kZSxcbiAgICAgICAgcGFyZW50U2VnbWVudFBhdGg6IHNlZ21lbnRQYXRoLFxuXG4gICAgICAgIC8vIFRPRE8tQVBQOiBvdmVycmlkaW5nIG9mIHVybCBmb3IgcGFyYWxsZWwgcm91dGVzXG4gICAgICAgIHVybDogdXJsLFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7cmVzb2x2ZWRSc2N9XG4gICAgPC9MYXlvdXRSb3V0ZXJDb250ZXh0LlByb3ZpZGVyPlxuICApXG4gIC8vIEVuc3VyZSByb290IGxheW91dCBpcyBub3Qgd3JhcHBlZCBpbiBhIGRpdiBhcyB0aGUgcm9vdCBsYXlvdXQgcmVuZGVycyBgPGh0bWw+YFxuICByZXR1cm4gc3VidHJlZVxufVxuXG4vKipcbiAqIFJlbmRlcnMgc3VzcGVuc2UgYm91bmRhcnkgd2l0aCB0aGUgcHJvdmlkZWQgXCJsb2FkaW5nXCIgcHJvcGVydHkgYXMgdGhlIGZhbGxiYWNrLlxuICogSWYgbm8gbG9hZGluZyBwcm9wZXJ0eSBpcyBwcm92aWRlZCBpdCByZW5kZXJzIHRoZSBjaGlsZHJlbiB3aXRob3V0IGEgc3VzcGVuc2UgYm91bmRhcnkuXG4gKi9cbmZ1bmN0aW9uIExvYWRpbmdCb3VuZGFyeSh7XG4gIGxvYWRpbmcsXG4gIGNoaWxkcmVuLFxufToge1xuICBsb2FkaW5nOiBMb2FkaW5nTW9kdWxlRGF0YSB8IFByb21pc2U8TG9hZGluZ01vZHVsZURhdGE+XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pOiBKU1guRWxlbWVudCB7XG4gIC8vIElmIGxvYWRpbmcgaXMgYSBwcm9taXNlLCB1bndyYXAgaXQuIFRoaXMgaGFwcGVucyBpbiBjYXNlcyB3aGVyZSB3ZSBoYXZlbid0XG4gIC8vIHlldCByZWNlaXZlZCB0aGUgbG9hZGluZyBkYXRhIGZyb20gdGhlIHNlcnZlciDigJQgd2hpY2ggaW5jbHVkZXMgd2hldGhlciBvclxuICAvLyBub3QgdGhpcyBsYXlvdXQgaGFzIGEgbG9hZGluZyBjb21wb25lbnQgYXQgYWxsLlxuICAvL1xuICAvLyBJdCdzIE9LIHRvIHN1c3BlbmQgaGVyZSBpbnN0ZWFkIG9mIGluc2lkZSB0aGUgZmFsbGJhY2sgYmVjYXVzZSB0aGlzXG4gIC8vIHByb21pc2Ugd2lsbCByZXNvbHZlIHNpbXVsdGFuZW91c2x5IHdpdGggdGhlIGRhdGEgZm9yIHRoZSBzZWdtZW50IGl0c2VsZi5cbiAgLy8gU28gaXQgd2lsbCBuZXZlciBzdXNwZW5kIGZvciBsb25nZXIgdGhhbiBpdCB3b3VsZCBoYXZlIGlmIHdlIGRpZG4ndCB1c2VcbiAgLy8gYSBTdXNwZW5zZSBmYWxsYmFjayBhdCBhbGwuXG4gIGxldCBsb2FkaW5nTW9kdWxlRGF0YVxuICBpZiAoXG4gICAgdHlwZW9mIGxvYWRpbmcgPT09ICdvYmplY3QnICYmXG4gICAgbG9hZGluZyAhPT0gbnVsbCAmJlxuICAgIHR5cGVvZiAobG9hZGluZyBhcyBhbnkpLnRoZW4gPT09ICdmdW5jdGlvbidcbiAgKSB7XG4gICAgY29uc3QgcHJvbWlzZUZvckxvYWRpbmcgPSBsb2FkaW5nIGFzIFByb21pc2U8TG9hZGluZ01vZHVsZURhdGE+XG4gICAgbG9hZGluZ01vZHVsZURhdGEgPSB1c2UocHJvbWlzZUZvckxvYWRpbmcpXG4gIH0gZWxzZSB7XG4gICAgbG9hZGluZ01vZHVsZURhdGEgPSBsb2FkaW5nIGFzIExvYWRpbmdNb2R1bGVEYXRhXG4gIH1cblxuICBpZiAobG9hZGluZ01vZHVsZURhdGEpIHtcbiAgICBjb25zdCBsb2FkaW5nUnNjID0gbG9hZGluZ01vZHVsZURhdGFbMF1cbiAgICBjb25zdCBsb2FkaW5nU3R5bGVzID0gbG9hZGluZ01vZHVsZURhdGFbMV1cbiAgICBjb25zdCBsb2FkaW5nU2NyaXB0cyA9IGxvYWRpbmdNb2R1bGVEYXRhWzJdXG4gICAgcmV0dXJuIChcbiAgICAgIDxTdXNwZW5zZVxuICAgICAgICBmYWxsYmFjaz17XG4gICAgICAgICAgPD5cbiAgICAgICAgICAgIHtsb2FkaW5nU3R5bGVzfVxuICAgICAgICAgICAge2xvYWRpbmdTY3JpcHRzfVxuICAgICAgICAgICAge2xvYWRpbmdSc2N9XG4gICAgICAgICAgPC8+XG4gICAgICAgIH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9TdXNwZW5zZT5cbiAgICApXG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59XG5cbi8qKlxuICogT3V0ZXJMYXlvdXRSb3V0ZXIgaGFuZGxlcyB0aGUgY3VycmVudCBzZWdtZW50IGFzIHdlbGwgYXMgPE9mZnNjcmVlbj4gcmVuZGVyaW5nIG9mIG90aGVyIHNlZ21lbnRzLlxuICogSXQgY2FuIGJlIHJlbmRlcmVkIG5leHQgdG8gZWFjaCBvdGhlciB3aXRoIGEgZGlmZmVyZW50IGBwYXJhbGxlbFJvdXRlcktleWAsIGFsbG93aW5nIGZvciBQYXJhbGxlbCByb3V0ZXMuXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE91dGVyTGF5b3V0Um91dGVyKHtcbiAgcGFyYWxsZWxSb3V0ZXJLZXksXG4gIGVycm9yLFxuICBlcnJvclN0eWxlcyxcbiAgZXJyb3JTY3JpcHRzLFxuICB0ZW1wbGF0ZVN0eWxlcyxcbiAgdGVtcGxhdGVTY3JpcHRzLFxuICB0ZW1wbGF0ZSxcbiAgbm90Rm91bmQsXG4gIGZvcmJpZGRlbixcbiAgdW5hdXRob3JpemVkLFxufToge1xuICBwYXJhbGxlbFJvdXRlcktleTogc3RyaW5nXG4gIGVycm9yOiBFcnJvckNvbXBvbmVudCB8IHVuZGVmaW5lZFxuICBlcnJvclN0eWxlczogUmVhY3QuUmVhY3ROb2RlIHwgdW5kZWZpbmVkXG4gIGVycm9yU2NyaXB0czogUmVhY3QuUmVhY3ROb2RlIHwgdW5kZWZpbmVkXG4gIHRlbXBsYXRlU3R5bGVzOiBSZWFjdC5SZWFjdE5vZGUgfCB1bmRlZmluZWRcbiAgdGVtcGxhdGVTY3JpcHRzOiBSZWFjdC5SZWFjdE5vZGUgfCB1bmRlZmluZWRcbiAgdGVtcGxhdGU6IFJlYWN0LlJlYWN0Tm9kZVxuICBub3RGb3VuZDogUmVhY3QuUmVhY3ROb2RlIHwgdW5kZWZpbmVkXG4gIGZvcmJpZGRlbjogUmVhY3QuUmVhY3ROb2RlIHwgdW5kZWZpbmVkXG4gIHVuYXV0aG9yaXplZDogUmVhY3QuUmVhY3ROb2RlIHwgdW5kZWZpbmVkXG59KSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KExheW91dFJvdXRlckNvbnRleHQpXG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcignaW52YXJpYW50IGV4cGVjdGVkIGxheW91dCByb3V0ZXIgdG8gYmUgbW91bnRlZCcpXG4gIH1cblxuICBjb25zdCB7IHBhcmVudFRyZWUsIHBhcmVudENhY2hlTm9kZSwgcGFyZW50U2VnbWVudFBhdGgsIHVybCB9ID0gY29udGV4dFxuXG4gIC8vIEdldCB0aGUgQ2FjaGVOb2RlIGZvciB0aGlzIHNlZ21lbnQgYnkgcmVhZGluZyBpdCBmcm9tIHRoZSBwYXJlbnQgc2VnbWVudCdzXG4gIC8vIGNoaWxkIG1hcC5cbiAgY29uc3QgcGFyZW50UGFyYWxsZWxSb3V0ZXMgPSBwYXJlbnRDYWNoZU5vZGUucGFyYWxsZWxSb3V0ZXNcbiAgbGV0IHNlZ21lbnRNYXAgPSBwYXJlbnRQYXJhbGxlbFJvdXRlcy5nZXQocGFyYWxsZWxSb3V0ZXJLZXkpXG4gIC8vIElmIHRoZSBwYXJhbGxlbCByb3V0ZXIgY2FjaGUgbm9kZSBkb2VzIG5vdCBleGlzdCB5ZXQsIGNyZWF0ZSBpdC5cbiAgLy8gVGhpcyB3cml0ZXMgdG8gdGhlIGNhY2hlIHdoZW4gdGhlcmUgaXMgbm8gaXRlbSBpbiB0aGUgY2FjaGUgeWV0LiBJdCBuZXZlciAqb3ZlcndyaXRlcyogZXhpc3RpbmcgY2FjaGUgaXRlbXMgd2hpY2ggaXMgd2h5IGl0J3Mgc2FmZSBpbiBjb25jdXJyZW50IG1vZGUuXG4gIGlmICghc2VnbWVudE1hcCkge1xuICAgIHNlZ21lbnRNYXAgPSBuZXcgTWFwKClcbiAgICBwYXJlbnRQYXJhbGxlbFJvdXRlcy5zZXQocGFyYWxsZWxSb3V0ZXJLZXksIHNlZ21lbnRNYXApXG4gIH1cblxuICAvLyBHZXQgdGhlIGFjdGl2ZSBzZWdtZW50IGluIHRoZSB0cmVlXG4gIC8vIFRoZSByZWFzb24gYXJyYXlzIGFyZSB1c2VkIGluIHRoZSBkYXRhIGZvcm1hdCBpcyB0aGF0IHRoZXNlIGFyZSB0cmFuc2ZlcnJlZCBmcm9tIHRoZSBzZXJ2ZXIgdG8gdGhlIGJyb3dzZXIgc28gaXQncyBvcHRpbWl6ZWQgdG8gc2F2ZSBieXRlcy5cbiAgY29uc3QgcGFyZW50VHJlZVNlZ21lbnQgPSBwYXJlbnRUcmVlWzBdXG4gIGNvbnN0IHRyZWUgPSBwYXJlbnRUcmVlWzFdW3BhcmFsbGVsUm91dGVyS2V5XVxuICBjb25zdCB0cmVlU2VnbWVudCA9IHRyZWVbMF1cblxuICBjb25zdCBzZWdtZW50UGF0aCA9XG4gICAgcGFyZW50U2VnbWVudFBhdGggPT09IG51bGxcbiAgICAgID8gLy8gVE9ETzogVGhlIHJvb3Qgc2VnbWVudCB2YWx1ZSBpcyBjdXJyZW50bHkgb21pdHRlZCBmcm9tIHRoZSBzZWdtZW50XG4gICAgICAgIC8vIHBhdGguIFRoaXMgaGFzIGxlZCB0byBhIGJ1bmNoIG9mIHNwZWNpYWwgY2FzZXMgc2NhdHRlcmVkIHRocm91Z2hvdXRcbiAgICAgICAgLy8gdGhlIGNvZGUuIFdlIHNob3VsZCBjbGVhbiB0aGlzIHVwLlxuICAgICAgICBbcGFyYWxsZWxSb3V0ZXJLZXldXG4gICAgICA6IHBhcmVudFNlZ21lbnRQYXRoLmNvbmNhdChbcGFyZW50VHJlZVNlZ21lbnQsIHBhcmFsbGVsUm91dGVyS2V5XSlcblxuICAvLyBUaGUgXCJzdGF0ZVwiIGtleSBvZiBhIHNlZ21lbnQgaXMgdGhlIG9uZSBwYXNzZWQgdG8gUmVhY3Qg4oCUIGl0IHJlcHJlc2VudHMgdGhlXG4gIC8vIGlkZW50aXR5IG9mIHRoZSBVSSB0cmVlLiBXaGVuZXZlciB0aGUgc3RhdGUga2V5IGNoYW5nZXMsIHRoZSB0cmVlIGlzXG4gIC8vIHJlY3JlYXRlZCBhbmQgdGhlIHN0YXRlIGlzIHJlc2V0LiBJbiB0aGUgQXBwIFJvdXRlciBtb2RlbCwgc2VhcmNoIHBhcmFtcyBkb1xuICAvLyBub3QgY2F1c2Ugc3RhdGUgdG8gYmUgbG9zdCwgc28gdHdvIHNlZ21lbnRzIHdpdGggdGhlIHNhbWUgc2VnbWVudCBwYXRoIGJ1dFxuICAvLyBkaWZmZXJlbnQgc2VhcmNoIHBhcmFtcyBzaG91bGQgaGF2ZSB0aGUgc2FtZSBzdGF0ZSBrZXkuXG4gIC8vXG4gIC8vIFRoZSBcImNhY2hlXCIga2V5IG9mIGEgc2VnbWVudCwgaG93ZXZlciwgKmRvZXMqIGluY2x1ZGUgdGhlIHNlYXJjaCBwYXJhbXMsIGlmXG4gIC8vIGl0J3MgcG9zc2libGUgdGhhdCB0aGUgc2VnbWVudCBhY2Nlc3NlZCB0aGUgc2VhcmNoIHBhcmFtcyBvbiB0aGUgc2VydmVyLlxuICAvLyAoVGhpcyBvbmx5IGFwcGxpZXMgdG8gcGFnZSBzZWdtZW50czsgbGF5b3V0IHNlZ21lbnRzIGNhbm5vdCBhY2Nlc3Mgc2VhcmNoXG4gIC8vIHBhcmFtcyBvbiB0aGUgc2VydmVyLilcbiAgY29uc3QgY2FjaGVLZXkgPSBjcmVhdGVSb3V0ZXJDYWNoZUtleSh0cmVlU2VnbWVudClcbiAgY29uc3Qgc3RhdGVLZXkgPSBjcmVhdGVSb3V0ZXJDYWNoZUtleSh0cmVlU2VnbWVudCwgdHJ1ZSkgLy8gbm8gc2VhcmNoIHBhcmFtc1xuXG4gIC8vIFJlYWQgc2VnbWVudCBwYXRoIGZyb20gdGhlIHBhcmFsbGVsIHJvdXRlciBjYWNoZSBub2RlLlxuICBsZXQgY2FjaGVOb2RlID0gc2VnbWVudE1hcC5nZXQoY2FjaGVLZXkpXG4gIGlmIChjYWNoZU5vZGUgPT09IHVuZGVmaW5lZCkge1xuICAgIC8vIFdoZW4gZGF0YSBpcyBub3QgYXZhaWxhYmxlIGR1cmluZyByZW5kZXJpbmcgY2xpZW50LXNpZGUgd2UgbmVlZCB0byBmZXRjaFxuICAgIC8vIGl0IGZyb20gdGhlIHNlcnZlci5cbiAgICBjb25zdCBuZXdMYXp5Q2FjaGVOb2RlOiBMYXp5Q2FjaGVOb2RlID0ge1xuICAgICAgbGF6eURhdGE6IG51bGwsXG4gICAgICByc2M6IG51bGwsXG4gICAgICBwcmVmZXRjaFJzYzogbnVsbCxcbiAgICAgIGhlYWQ6IG51bGwsXG4gICAgICBwcmVmZXRjaEhlYWQ6IG51bGwsXG4gICAgICBwYXJhbGxlbFJvdXRlczogbmV3IE1hcCgpLFxuICAgICAgbG9hZGluZzogbnVsbCxcbiAgICB9XG5cbiAgICAvLyBGbGlnaHQgZGF0YSBmZXRjaCBraWNrZWQgb2ZmIGR1cmluZyByZW5kZXIgYW5kIHB1dCBpbnRvIHRoZSBjYWNoZS5cbiAgICBjYWNoZU5vZGUgPSBuZXdMYXp5Q2FjaGVOb2RlXG4gICAgc2VnbWVudE1hcC5zZXQoY2FjaGVLZXksIG5ld0xhenlDYWNoZU5vZGUpXG4gIH1cblxuICAvKlxuICAgIC0gRXJyb3IgYm91bmRhcnlcbiAgICAgIC0gT25seSByZW5kZXJzIGVycm9yIGJvdW5kYXJ5IGlmIGVycm9yIGNvbXBvbmVudCBpcyBwcm92aWRlZC5cbiAgICAgIC0gUmVuZGVyZWQgZm9yIGVhY2ggc2VnbWVudCB0byBlbnN1cmUgdGhleSBoYXZlIHRoZWlyIG93biBlcnJvciBzdGF0ZS5cbiAgICAtIExvYWRpbmcgYm91bmRhcnlcbiAgICAgIC0gT25seSByZW5kZXJzIHN1c3BlbnNlIGJvdW5kYXJ5IGlmIGxvYWRpbmcgY29tcG9uZW50cyBpcyBwcm92aWRlZC5cbiAgICAgIC0gUmVuZGVyZWQgZm9yIGVhY2ggc2VnbWVudCB0byBlbnN1cmUgdGhleSBoYXZlIHRoZWlyIG93biBsb2FkaW5nIHN0YXRlLlxuICAgICAgLSBQYXNzZWQgdG8gdGhlIHJvdXRlciBkdXJpbmcgcmVuZGVyaW5nIHRvIGVuc3VyZSBpdCBjYW4gYmUgaW1tZWRpYXRlbHkgcmVuZGVyZWQgd2hlbiBzdXNwZW5kaW5nIG9uIGEgRmxpZ2h0IGZldGNoLlxuICAqL1xuXG4gIC8vIFRPRE86IFRoZSBsb2FkaW5nIG1vZHVsZSBkYXRhIGZvciBhIHNlZ21lbnQgaXMgc3RvcmVkIG9uIHRoZSBwYXJlbnQsIHRoZW5cbiAgLy8gYXBwbGllZCB0byBlYWNoIG9mIHRoYXQgcGFyZW50IHNlZ21lbnQncyBwYXJhbGxlbCByb3V0ZSBzbG90cy4gSW4gdGhlXG4gIC8vIHNpbXBsZSBjYXNlIHdoZXJlIHRoZXJlJ3Mgb25seSBvbmUgcGFyYWxsZWwgcm91dGUgKHRoZSBgY2hpbGRyZW5gIHNsb3QpLFxuICAvLyB0aGlzIGlzIG5vIGRpZmZlcmVudCBmcm9tIGlmIHRoZSBsb2FkaW5nIG1vZHVsZSBkYXRhIHdoZXJlIHN0b3JlZCBvbiB0aGVcbiAgLy8gY2hpbGQgZGlyZWN0bHkuIEJ1dCBJJ20gbm90IHN1cmUgdGhpcyBhY3R1YWxseSBtYWtlcyBzZW5zZSB3aGVuIHRoZXJlIGFyZVxuICAvLyBtdWx0aXBsZSBwYXJhbGxlbCByb3V0ZXMuIEl0J3Mgbm90IGEgaHVnZSBpc3N1ZSBiZWNhdXNlIHlvdSBhbHdheXMgaGF2ZVxuICAvLyB0aGUgb3B0aW9uIHRvIGRlZmluZSBhIG5hcnJvd2VyIGxvYWRpbmcgYm91bmRhcnkgZm9yIGEgcGFydGljdWxhciBzbG90LiBCdXRcbiAgLy8gdGhpcyBzb3J0IG9mIHNtZWxscyBsaWtlIGFuIGltcGxlbWVudGF0aW9uIGFjY2lkZW50IHRvIG1lLlxuICBjb25zdCBsb2FkaW5nTW9kdWxlRGF0YSA9IHBhcmVudENhY2hlTm9kZS5sb2FkaW5nXG5cbiAgcmV0dXJuIChcbiAgICA8VGVtcGxhdGVDb250ZXh0LlByb3ZpZGVyXG4gICAgICBrZXk9e3N0YXRlS2V5fVxuICAgICAgdmFsdWU9e1xuICAgICAgICA8U2Nyb2xsQW5kRm9jdXNIYW5kbGVyIHNlZ21lbnRQYXRoPXtzZWdtZW50UGF0aH0+XG4gICAgICAgICAgPEVycm9yQm91bmRhcnlcbiAgICAgICAgICAgIGVycm9yQ29tcG9uZW50PXtlcnJvcn1cbiAgICAgICAgICAgIGVycm9yU3R5bGVzPXtlcnJvclN0eWxlc31cbiAgICAgICAgICAgIGVycm9yU2NyaXB0cz17ZXJyb3JTY3JpcHRzfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxMb2FkaW5nQm91bmRhcnkgbG9hZGluZz17bG9hZGluZ01vZHVsZURhdGF9PlxuICAgICAgICAgICAgICA8SFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnlcbiAgICAgICAgICAgICAgICBub3RGb3VuZD17bm90Rm91bmR9XG4gICAgICAgICAgICAgICAgZm9yYmlkZGVuPXtmb3JiaWRkZW59XG4gICAgICAgICAgICAgICAgdW5hdXRob3JpemVkPXt1bmF1dGhvcml6ZWR9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UmVkaXJlY3RCb3VuZGFyeT5cbiAgICAgICAgICAgICAgICAgIDxJbm5lckxheW91dFJvdXRlclxuICAgICAgICAgICAgICAgICAgICB1cmw9e3VybH1cbiAgICAgICAgICAgICAgICAgICAgdHJlZT17dHJlZX1cbiAgICAgICAgICAgICAgICAgICAgY2FjaGVOb2RlPXtjYWNoZU5vZGV9XG4gICAgICAgICAgICAgICAgICAgIHNlZ21lbnRQYXRoPXtzZWdtZW50UGF0aH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9SZWRpcmVjdEJvdW5kYXJ5PlxuICAgICAgICAgICAgICA8L0hUVFBBY2Nlc3NGYWxsYmFja0JvdW5kYXJ5PlxuICAgICAgICAgICAgPC9Mb2FkaW5nQm91bmRhcnk+XG4gICAgICAgICAgPC9FcnJvckJvdW5kYXJ5PlxuICAgICAgICA8L1Njcm9sbEFuZEZvY3VzSGFuZGxlcj5cbiAgICAgIH1cbiAgICA+XG4gICAgICB7dGVtcGxhdGVTdHlsZXN9XG4gICAgICB7dGVtcGxhdGVTY3JpcHRzfVxuICAgICAge3RlbXBsYXRlfVxuICAgIDwvVGVtcGxhdGVDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiT3V0ZXJMYXlvdXRSb3V0ZXIiLCJ3YWxrQWRkUmVmZXRjaCIsInNlZ21lbnRQYXRoVG9XYWxrIiwidHJlZVRvUmVjcmVhdGUiLCJzZWdtZW50IiwicGFyYWxsZWxSb3V0ZUtleSIsImlzTGFzdCIsImxlbmd0aCIsIm1hdGNoU2VnbWVudCIsImhhc093blByb3BlcnR5Iiwic3ViVHJlZSIsInVuZGVmaW5lZCIsInNsaWNlIiwiX19ET01fSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFIiwiUmVhY3RET00iLCJmaW5kRE9NTm9kZSIsImluc3RhbmNlIiwid2luZG93IiwiaW50ZXJuYWxfcmVhY3RET01maW5kRE9NTm9kZSIsInJlY3RQcm9wZXJ0aWVzIiwic2hvdWxkU2tpcEVsZW1lbnQiLCJlbGVtZW50IiwiaW5jbHVkZXMiLCJnZXRDb21wdXRlZFN0eWxlIiwicG9zaXRpb24iLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJjb25zb2xlIiwid2FybiIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJldmVyeSIsIml0ZW0iLCJ0b3BPZkVsZW1lbnRJblZpZXdwb3J0Iiwidmlld3BvcnRIZWlnaHQiLCJ0b3AiLCJnZXRIYXNoRnJhZ21lbnREb21Ob2RlIiwiaGFzaEZyYWdtZW50IiwiZG9jdW1lbnQiLCJib2R5IiwiZ2V0RWxlbWVudEJ5SWQiLCJnZXRFbGVtZW50c0J5TmFtZSIsIklubmVyU2Nyb2xsQW5kRm9jdXNIYW5kbGVyIiwiUmVhY3QiLCJDb21wb25lbnQiLCJjb21wb25lbnREaWRNb3VudCIsImhhbmRsZVBvdGVudGlhbFNjcm9sbCIsImNvbXBvbmVudERpZFVwZGF0ZSIsInByb3BzIiwiZm9jdXNBbmRTY3JvbGxSZWYiLCJhcHBseSIsInJlbmRlciIsImNoaWxkcmVuIiwic2VnbWVudFBhdGgiLCJzZWdtZW50UGF0aHMiLCJzb21lIiwic2Nyb2xsUmVmU2VnbWVudFBhdGgiLCJpbmRleCIsImRvbU5vZGUiLCJFbGVtZW50IiwiSFRNTEVsZW1lbnQiLCJwYXJlbnRFbGVtZW50IiwibG9jYWxOYW1lIiwibmV4dEVsZW1lbnRTaWJsaW5nIiwiaGFuZGxlU21vb3RoU2Nyb2xsIiwic2Nyb2xsSW50b1ZpZXciLCJodG1sRWxlbWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsaWVudEhlaWdodCIsInNjcm9sbFRvcCIsImRvbnRGb3JjZUxheW91dCIsIm9ubHlIYXNoQ2hhbmdlIiwiZm9jdXMiLCJTY3JvbGxBbmRGb2N1c0hhbmRsZXIiLCJjb250ZXh0IiwidXNlQ29udGV4dCIsIkdsb2JhbExheW91dFJvdXRlckNvbnRleHQiLCJFcnJvciIsIklubmVyTGF5b3V0Um91dGVyIiwidHJlZSIsImNhY2hlTm9kZSIsInVybCIsImNoYW5nZUJ5U2VydmVyUmVzcG9uc2UiLCJmdWxsVHJlZSIsInJlc29sdmVkUHJlZmV0Y2hSc2MiLCJwcmVmZXRjaFJzYyIsInJzYyIsInVzZURlZmVycmVkVmFsdWUiLCJyZXNvbHZlZFJzYyIsInRoZW4iLCJ1c2UiLCJsYXp5RGF0YSIsInJlZmV0Y2hUcmVlIiwiaW5jbHVkZU5leHRVcmwiLCJoYXNJbnRlcmNlcHRpb25Sb3V0ZUluQ3VycmVudFRyZWUiLCJmZXRjaFNlcnZlclJlc3BvbnNlIiwiVVJMIiwibG9jYXRpb24iLCJvcmlnaW4iLCJmbGlnaHRSb3V0ZXJTdGF0ZSIsIm5leHRVcmwiLCJzZXJ2ZXJSZXNwb25zZSIsInN0YXJ0VHJhbnNpdGlvbiIsInByZXZpb3VzVHJlZSIsInVucmVzb2x2ZWRUaGVuYWJsZSIsInN1YnRyZWUiLCJMYXlvdXRSb3V0ZXJDb250ZXh0IiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInBhcmVudFRyZWUiLCJwYXJlbnRDYWNoZU5vZGUiLCJwYXJlbnRTZWdtZW50UGF0aCIsIkxvYWRpbmdCb3VuZGFyeSIsImxvYWRpbmciLCJsb2FkaW5nTW9kdWxlRGF0YSIsInByb21pc2VGb3JMb2FkaW5nIiwibG9hZGluZ1JzYyIsImxvYWRpbmdTdHlsZXMiLCJsb2FkaW5nU2NyaXB0cyIsIlN1c3BlbnNlIiwiZmFsbGJhY2siLCJwYXJhbGxlbFJvdXRlcktleSIsImVycm9yIiwiZXJyb3JTdHlsZXMiLCJlcnJvclNjcmlwdHMiLCJ0ZW1wbGF0ZVN0eWxlcyIsInRlbXBsYXRlU2NyaXB0cyIsInRlbXBsYXRlIiwibm90Rm91bmQiLCJmb3JiaWRkZW4iLCJ1bmF1dGhvcml6ZWQiLCJwYXJlbnRQYXJhbGxlbFJvdXRlcyIsInBhcmFsbGVsUm91dGVzIiwic2VnbWVudE1hcCIsImdldCIsIk1hcCIsInNldCIsInBhcmVudFRyZWVTZWdtZW50IiwidHJlZVNlZ21lbnQiLCJjb25jYXQiLCJjYWNoZUtleSIsImNyZWF0ZVJvdXRlckNhY2hlS2V5Iiwic3RhdGVLZXkiLCJuZXdMYXp5Q2FjaGVOb2RlIiwiaGVhZCIsInByZWZldGNoSGVhZCIsIlRlbXBsYXRlQ29udGV4dCIsIkVycm9yQm91bmRhcnkiLCJlcnJvckNvbXBvbmVudCIsIkhUVFBBY2Nlc3NGYWxsYmFja0JvdW5kYXJ5IiwiUmVkaXJlY3RCb3VuZGFyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    unmountLinkInstance: function() {\n        return unmountLinkInstance;\n    }\n});\nconst _actionqueue = __webpack_require__(/*! ../../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction mountLinkInstance(element, href, router, kind) {\n    let prefetchUrl = null;\n    try {\n        prefetchUrl = (0, _approuter.createPrefetchURL)(href);\n        if (prefetchUrl === null) {\n            // We only track the link if it's prefetchable. For example, this excludes\n            // links to external URLs.\n            return;\n        }\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return;\n    }\n    const instance = {\n        prefetchHref: prefetchUrl.href,\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1\n    };\n    const existingInstance = links.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountLinkInstance(element);\n    }\n    links.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction unmountLinkInstance(element) {\n    const instance = links.get(element);\n    if (instance !== undefined) {\n        links.delete(element);\n        visibleLinks.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        visibleLinks.add(instance);\n    } else {\n        visibleLinks.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element) {\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with bumpPrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    if (existingPrefetchTask === null) {\n        // Initiate a prefetch task.\n        const appRouterState = (0, _actionqueue.getCurrentAppRouterState)();\n        if (appRouterState !== null) {\n            const nextUrl = appRouterState.nextUrl;\n            const treeAtTimeOfPrefetch = appRouterState.tree;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n            instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n        }\n    } else {\n        // We already have an old task object that we can reschedule. This is\n        // effectively the same as canceling the old task and creating a new one.\n        (0, _segmentcache.bumpPrefetchTask)(existingPrefetchTask, priority);\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of visibleLinks){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ })

}]);