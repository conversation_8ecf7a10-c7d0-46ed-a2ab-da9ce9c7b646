"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_n"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/not-found.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/components/not-found.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"notFound\", ({\n    enumerable: true,\n    get: function() {\n        return notFound;\n    }\n}));\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback/http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */ const DIGEST = \"\" + _httpaccessfallback.HTTP_ERROR_FALLBACK_ERROR_CODE + \";404\";\nfunction notFound() {\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=not-found.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/promise-queue.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PromiseQueue\", ({\n    enumerable: true,\n    get: function() {\n        return PromiseQueue;\n    }\n}));\nconst _class_private_field_loose_base = __webpack_require__(/*! @swc/helpers/_/_class_private_field_loose_base */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_loose_base.js\");\nconst _class_private_field_loose_key = __webpack_require__(/*! @swc/helpers/_/_class_private_field_loose_key */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_loose_key.js\");\nvar _maxConcurrency = /*#__PURE__*/ _class_private_field_loose_key._(\"_maxConcurrency\"), _runningCount = /*#__PURE__*/ _class_private_field_loose_key._(\"_runningCount\"), _queue = /*#__PURE__*/ _class_private_field_loose_key._(\"_queue\"), _processNext = /*#__PURE__*/ _class_private_field_loose_key._(\"_processNext\");\nclass PromiseQueue {\n    enqueue(promiseFn) {\n        let taskResolve;\n        let taskReject;\n        const taskPromise = new Promise((resolve, reject)=>{\n            taskResolve = resolve;\n            taskReject = reject;\n        });\n        const task = async ()=>{\n            try {\n                _class_private_field_loose_base._(this, _runningCount)[_runningCount]++;\n                const result = await promiseFn();\n                taskResolve(result);\n            } catch (error) {\n                taskReject(error);\n            } finally{\n                _class_private_field_loose_base._(this, _runningCount)[_runningCount]--;\n                _class_private_field_loose_base._(this, _processNext)[_processNext]();\n            }\n        };\n        const enqueueResult = {\n            promiseFn: taskPromise,\n            task\n        };\n        // wonder if we should take a LIFO approach here\n        _class_private_field_loose_base._(this, _queue)[_queue].push(enqueueResult);\n        _class_private_field_loose_base._(this, _processNext)[_processNext]();\n        return taskPromise;\n    }\n    bump(promiseFn) {\n        const index = _class_private_field_loose_base._(this, _queue)[_queue].findIndex((item)=>item.promiseFn === promiseFn);\n        if (index > -1) {\n            const bumpedItem = _class_private_field_loose_base._(this, _queue)[_queue].splice(index, 1)[0];\n            _class_private_field_loose_base._(this, _queue)[_queue].unshift(bumpedItem);\n            _class_private_field_loose_base._(this, _processNext)[_processNext](true);\n        }\n    }\n    constructor(maxConcurrency = 5){\n        Object.defineProperty(this, _processNext, {\n            value: processNext\n        });\n        Object.defineProperty(this, _maxConcurrency, {\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, _runningCount, {\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, _queue, {\n            writable: true,\n            value: void 0\n        });\n        _class_private_field_loose_base._(this, _maxConcurrency)[_maxConcurrency] = maxConcurrency;\n        _class_private_field_loose_base._(this, _runningCount)[_runningCount] = 0;\n        _class_private_field_loose_base._(this, _queue)[_queue] = [];\n    }\n}\nfunction processNext(forced) {\n    if (forced === void 0) forced = false;\n    if ((_class_private_field_loose_base._(this, _runningCount)[_runningCount] < _class_private_field_loose_base._(this, _maxConcurrency)[_maxConcurrency] || forced) && _class_private_field_loose_base._(this, _queue)[_queue].length > 0) {\n        var _class_private_field_loose_base__queue_shift;\n        (_class_private_field_loose_base__queue_shift = _class_private_field_loose_base._(this, _queue)[_queue].shift()) == null ? void 0 : _class_private_field_loose_base__queue_shift.task();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=promise-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlayErrorBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlayErrorBoundary;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../../errors/runtime-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\");\nconst _errorboundary = __webpack_require__(/*! ../../error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nfunction ErroredHtml(param) {\n    let { globalError: [GlobalError, globalErrorStyles], error } = param;\n    if (!error) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"html\", {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"head\", {}),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"body\", {})\n            ]\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_errorboundary.ErrorBoundary, {\n        errorComponent: _errorboundary.GlobalError,\n        children: [\n            globalErrorStyles,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(GlobalError, {\n                error: error\n            })\n        ]\n    });\n}\n_c = ErroredHtml;\nclass AppDevOverlayErrorBoundary extends _react.PureComponent {\n    static getDerivedStateFromError(error) {\n        if (!error.stack) {\n            return {\n                isReactError: false,\n                reactError: null\n            };\n        }\n        _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError = true;\n        return {\n            isReactError: true,\n            reactError: error\n        };\n    }\n    componentDidCatch() {\n        this.props.onError(this.state.isReactError);\n    }\n    render() {\n        const { children, globalError } = this.props;\n        const { isReactError, reactError } = this.state;\n        const fallback = /*#__PURE__*/ (0, _jsxruntime.jsx)(ErroredHtml, {\n            globalError: globalError,\n            error: reactError\n        });\n        return isReactError ? fallback : children;\n    }\n    constructor(...args){\n        super(...args), this.state = {\n            isReactError: false,\n            reactError: null\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay-error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"ErroredHtml\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors() {\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError, []);\n            }\n        }, [\n            ssrError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {}),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                state: state,\n                isErrorOverlayOpen: isErrorOverlayOpen,\n                setIsErrorOverlayOpen: setIsErrorOverlayOpen\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRootLevelDevOverlayElement\", ({\n    enumerable: true,\n    get: function() {\n        return createRootLevelDevOverlayElement;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _appdevoverlay = __webpack_require__(/*! ./app-dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\");\nconst _getsocketurl = __webpack_require__(/*! ../utils/get-socket-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nfunction createRootLevelDevOverlayElement(reactEl) {\n    const rootLayoutMissingTags = window.__next_root_layout_missing_tags;\n    const hasMissingTags = !!(rootLayoutMissingTags == null ? void 0 : rootLayoutMissingTags.length);\n    const socketUrl = (0, _getsocketurl.getSocketUrl)( false || '');\n    const socket = new window.WebSocket(\"\" + socketUrl + \"/_next/webpack-hmr\");\n    // add minimal \"hot reload\" support for RSC errors\n    const handler = (event)=>{\n        let obj;\n        try {\n            obj = JSON.parse(event.data);\n        } catch (e) {}\n        if (!obj || !('action' in obj)) {\n            return;\n        }\n        if (obj.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES) {\n            window.location.reload();\n        }\n    };\n    socket.addEventListener('message', handler);\n    const FallbackLayout = hasMissingTags ? (param)=>{\n        let { children } = param;\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"html\", {\n            id: \"__next_error__\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"body\", {\n                children: children\n            })\n        });\n    } : _react.default.Fragment;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(FallbackLayout, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_appdevoverlay.AppDevOverlay, {\n            state: {\n                ..._shared.INITIAL_OVERLAY_STATE,\n                rootLayoutMissingTags,\n                routerType: 'app'\n            },\n            globalError: [\n                _errorboundary.default,\n                null\n            ],\n            children: reactEl\n        })\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-entry.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return HotReload;\n    },\n    waitForWebpackRuntimeHotUpdate: function() {\n        return waitForWebpackRuntimeHotUpdate;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _formatwebpackmessages = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../utils/format-webpack-messages */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js\"));\nconst _navigation = __webpack_require__(/*! ../../navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _parsestack = __webpack_require__(/*! ../utils/parse-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js\");\nconst _appdevoverlay = __webpack_require__(/*! ./app-dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../../errors/runtime-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\");\nconst _usewebsocket = __webpack_require__(/*! ../utils/use-websocket */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js\");\nconst _parsecomponentstack = __webpack_require__(/*! ../utils/parse-component-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js\");\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _extractmodulesfromturbopackmessage = __webpack_require__(/*! ../../../../server/dev/extract-modules-from-turbopack-message */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/extract-modules-from-turbopack-message.js\");\nconst _navigationuntracked = __webpack_require__(/*! ../../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _stitchederror = __webpack_require__(/*! ../../errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserrorthrownwhilerenderingrsc = __webpack_require__(/*! ../../../lib/is-error-thrown-while-rendering-rsc */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js\");\nconst _handledevbuildindicatorhmrevents = __webpack_require__(/*! ../../../dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\");\nlet mostRecentCompilationHash = null;\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now());\nlet reloading = false;\nlet startLatency = null;\nlet turbopackLastUpdateLatency = null;\nlet turbopackUpdatedModules = new Set();\nlet pendingHotUpdateWebpack = Promise.resolve();\nlet resolvePendingHotUpdateWebpack = ()=>{};\nfunction setPendingHotUpdateWebpack() {\n    pendingHotUpdateWebpack = new Promise((resolve)=>{\n        resolvePendingHotUpdateWebpack = ()=>{\n            resolve();\n        };\n    });\n}\nfunction waitForWebpackRuntimeHotUpdate() {\n    return pendingHotUpdateWebpack;\n}\nfunction handleBeforeHotUpdateWebpack(dispatcher, hasUpdates) {\n    if (hasUpdates) {\n        dispatcher.onBeforeRefresh();\n    }\n}\nfunction handleSuccessfulHotUpdateWebpack(dispatcher, sendMessage, updatedModules) {\n    resolvePendingHotUpdateWebpack();\n    dispatcher.onBuildOk();\n    reportHmrLatency(sendMessage, updatedModules);\n    dispatcher.onRefresh();\n}\nfunction reportHmrLatency(sendMessage, updatedModules) {\n    if (!startLatency) return;\n    // turbopack has a debounce for the \"built\" event which we don't want to\n    // incorrectly show in this number, use the last TURBOPACK_MESSAGE time\n    let endLatency = turbopackLastUpdateLatency != null ? turbopackLastUpdateLatency : Date.now();\n    const latency = endLatency - startLatency;\n    console.log(\"[Fast Refresh] done in \" + latency + \"ms\");\n    sendMessage(JSON.stringify({\n        event: 'client-hmr-latency',\n        id: window.__nextDevClientId,\n        startTime: startLatency,\n        endTime: endLatency,\n        page: window.location.pathname,\n        updatedModules,\n        // Whether the page (tab) was hidden at the time the event occurred.\n        // This can impact the accuracy of the event's timing.\n        isPageHidden: document.visibilityState === 'hidden'\n    }));\n}\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n    // Update last known compilation hash.\n    mostRecentCompilationHash = hash;\n}\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */ function isUpdateAvailable() {\n    if (false) {}\n    /* globals __webpack_hash__ */ // __webpack_hash__ is the hash of the current compilation.\n    // It's a global variable injected by Webpack.\n    return mostRecentCompilationHash !== __webpack_require__.h();\n}\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n    // @ts-expect-error module.hot exists\n    return module.hot.status() === 'idle';\n}\nfunction afterApplyUpdates(fn) {\n    if (canApplyUpdates()) {\n        fn();\n    } else {\n        function handler(status) {\n            if (status === 'idle') {\n                // @ts-expect-error module.hot exists\n                module.hot.removeStatusHandler(handler);\n                fn();\n            }\n        }\n        // @ts-expect-error module.hot exists\n        module.hot.addStatusHandler(handler);\n    }\n}\nfunction performFullReload(err, sendMessage) {\n    const stackTrace = err && (err.stack && err.stack.split('\\n').slice(0, 5).join('\\n') || err.message || err + '');\n    sendMessage(JSON.stringify({\n        event: 'client-full-reload',\n        stackTrace,\n        hadRuntimeError: !!_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError,\n        dependencyChain: err ? err.dependencyChain : undefined\n    }));\n    if (reloading) return;\n    reloading = true;\n    window.location.reload();\n}\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onBeforeUpdate, onHotUpdateSuccess, sendMessage, dispatcher) {\n    if (!isUpdateAvailable() || !canApplyUpdates()) {\n        resolvePendingHotUpdateWebpack();\n        dispatcher.onBuildOk();\n        reportHmrLatency(sendMessage, []);\n        return;\n    }\n    function handleApplyUpdates(err, updatedModules) {\n        if (err || _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n            if (err) {\n                console.warn('[Fast Refresh] performing full reload\\n\\n' + \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" + 'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' + 'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' + 'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' + 'Fast Refresh requires at least one parent function component in your React tree.');\n            } else if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n            }\n            performFullReload(err, sendMessage);\n            return;\n        }\n        const hasUpdates = Boolean(updatedModules.length);\n        if (typeof onHotUpdateSuccess === 'function') {\n            // Maybe we want to do something.\n            onHotUpdateSuccess(updatedModules);\n        }\n        if (isUpdateAvailable()) {\n            // While we were updating, there was a new update! Do it again.\n            tryApplyUpdates(hasUpdates ? ()=>{} : onBeforeUpdate, hasUpdates ? ()=>dispatcher.onBuildOk() : onHotUpdateSuccess, sendMessage, dispatcher);\n        } else {\n            dispatcher.onBuildOk();\n            if (false) {}\n        }\n    }\n    // https://webpack.js.org/api/hot-module-replacement/#check\n    // @ts-expect-error module.hot exists\n    module.hot.check(/* autoApply */ false).then((updatedModules)=>{\n        if (!updatedModules) {\n            return null;\n        }\n        if (typeof onBeforeUpdate === 'function') {\n            const hasUpdates = Boolean(updatedModules.length);\n            onBeforeUpdate(hasUpdates);\n        }\n        // https://webpack.js.org/api/hot-module-replacement/#apply\n        // @ts-expect-error module.hot exists\n        return module.hot.apply();\n    }).then((updatedModules)=>{\n        handleApplyUpdates(null, updatedModules);\n    }, (err)=>{\n        handleApplyUpdates(err, null);\n    });\n}\n/** Handles messages from the sevrer for the App Router. */ function processMessage(obj, sendMessage, processTurbopackMessage, router, dispatcher, appIsrManifestRef, pathnameRef) {\n    if (!('action' in obj)) {\n        return;\n    }\n    function handleErrors(errors) {\n        // \"Massage\" webpack messages.\n        const formatted = (0, _formatwebpackmessages.default)({\n            errors: errors,\n            warnings: []\n        });\n        // Only show the first error.\n        dispatcher.onBuildError(formatted.errors[0]);\n        // Also log them to the console.\n        for(let i = 0; i < formatted.errors.length; i++){\n            console.error((0, _stripansi.default)(formatted.errors[i]));\n        }\n        // Do not attempt to reload now.\n        // We will reload on next success instead.\n        if (false) {}\n    }\n    function handleHotUpdate() {\n        if (false) {} else {\n            tryApplyUpdates(function onBeforeHotUpdate(hasUpdates) {\n                handleBeforeHotUpdateWebpack(dispatcher, hasUpdates);\n            }, function onSuccessfulHotUpdate(webpackUpdatedModules) {\n                // Only dismiss it when we're sure it's a hot update.\n                // Otherwise it would flicker right before the reload.\n                handleSuccessfulHotUpdateWebpack(dispatcher, sendMessage, webpackUpdatedModules);\n            }, sendMessage, dispatcher);\n        }\n    }\n    switch(obj.action){\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST:\n            {\n                if (true) {\n                    if (appIsrManifestRef) {\n                        appIsrManifestRef.current = obj.data;\n                        // handle initial status on receiving manifest\n                        // navigation is handled in useEffect for pathname changes\n                        // as we'll receive the updated manifest before usePathname\n                        // triggers for new value\n                        if (pathnameRef.current in obj.data) {\n                            dispatcher.onStaticIndicator(true);\n                        } else {\n                            dispatcher.onStaticIndicator(false);\n                        }\n                    }\n                }\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n            {\n                startLatency = Date.now();\n                turbopackLastUpdateLatency = null;\n                turbopackUpdatedModules.clear();\n                if (true) {\n                    setPendingHotUpdateWebpack();\n                }\n                console.log('[Fast Refresh] rebuilding');\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n            {\n                if (obj.hash) {\n                    handleAvailableHash(obj.hash);\n                }\n                const { errors, warnings } = obj;\n                // Is undefined when it's a 'built' event\n                if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo);\n                if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug);\n                if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator);\n                const hasErrors = Boolean(errors && errors.length);\n                // Compilation with errors (e.g. syntax error or missing modules).\n                if (hasErrors) {\n                    sendMessage(JSON.stringify({\n                        event: 'client-error',\n                        errorCount: errors.length,\n                        clientId: __nextDevClientId\n                    }));\n                    handleErrors(errors);\n                    return;\n                }\n                const hasWarnings = Boolean(warnings && warnings.length);\n                if (hasWarnings) {\n                    sendMessage(JSON.stringify({\n                        event: 'client-warning',\n                        warningCount: warnings.length,\n                        clientId: __nextDevClientId\n                    }));\n                    // Print warnings to the console.\n                    const formattedMessages = (0, _formatwebpackmessages.default)({\n                        warnings: warnings,\n                        errors: []\n                    });\n                    for(let i = 0; i < formattedMessages.warnings.length; i++){\n                        if (i === 5) {\n                            console.warn('There were more warnings in other files.\\n' + 'You can find a complete log in the terminal.');\n                            break;\n                        }\n                        console.warn((0, _stripansi.default)(formattedMessages.warnings[i]));\n                    }\n                // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n                }\n                sendMessage(JSON.stringify({\n                    event: 'client-success',\n                    clientId: __nextDevClientId\n                }));\n                if (obj.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n                    // Handle hot updates\n                    handleHotUpdate();\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED:\n            {\n                processTurbopackMessage({\n                    type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n                    data: {\n                        sessionId: obj.data.sessionId\n                    }\n                });\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE:\n            {\n                dispatcher.onBeforeRefresh();\n                processTurbopackMessage({\n                    type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n                    data: obj.data\n                });\n                dispatcher.onRefresh();\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n                    performFullReload(null, sendMessage);\n                }\n                for (const module1 of (0, _extractmodulesfromturbopackmessage.extractModulesFromTurbopackMessage)(obj.data)){\n                    turbopackUpdatedModules.add(module1);\n                }\n                turbopackLastUpdateLatency = Date.now();\n                break;\n            }\n        // TODO-APP: make server component change more granular\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES:\n            {\n                sendMessage(JSON.stringify({\n                    event: 'server-component-reload-page',\n                    clientId: __nextDevClientId,\n                    hash: obj.hash\n                }));\n                // Store the latest hash in a session cookie so that it's sent back to the\n                // server with any subsequent requests.\n                document.cookie = \"__next_hmr_refresh_hash__=\" + obj.hash;\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    if (reloading) return;\n                    reloading = true;\n                    return window.location.reload();\n                }\n                (0, _react.startTransition)(()=>{\n                    router.hmrRefresh();\n                    dispatcher.onRefresh();\n                });\n                if (false) {}\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE:\n            {\n                sendMessage(JSON.stringify({\n                    event: 'client-reload-page',\n                    clientId: __nextDevClientId\n                }));\n                if (reloading) return;\n                reloading = true;\n                return window.location.reload();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE:\n            {\n                // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n                return router.hmrRefresh();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR:\n            {\n                const { errorJSON } = obj;\n                if (errorJSON) {\n                    const { message, stack } = JSON.parse(errorJSON);\n                    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                        value: \"E394\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    error.stack = stack;\n                    handleErrors([\n                        error\n                    ]);\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE:\n            {\n                return;\n            }\n        default:\n            {}\n    }\n}\nfunction HotReload(param) {\n    let { assetPrefix, children, globalError } = param;\n    const [state, dispatch] = (0, _shared.useErrorOverlayReducer)('app');\n    const dispatcher = (0, _react.useMemo)(()=>{\n        return {\n            onBuildOk () {\n                dispatch({\n                    type: _shared.ACTION_BUILD_OK\n                });\n            },\n            onBuildError (message) {\n                dispatch({\n                    type: _shared.ACTION_BUILD_ERROR,\n                    message\n                });\n            },\n            onBeforeRefresh () {\n                dispatch({\n                    type: _shared.ACTION_BEFORE_REFRESH\n                });\n            },\n            onRefresh () {\n                dispatch({\n                    type: _shared.ACTION_REFRESH\n                });\n            },\n            onVersionInfo (versionInfo) {\n                dispatch({\n                    type: _shared.ACTION_VERSION_INFO,\n                    versionInfo\n                });\n            },\n            onStaticIndicator (status) {\n                dispatch({\n                    type: _shared.ACTION_STATIC_INDICATOR,\n                    staticIndicator: status\n                });\n            },\n            onDebugInfo (debugInfo) {\n                dispatch({\n                    type: _shared.ACTION_DEBUG_INFO,\n                    debugInfo\n                });\n            },\n            onDevIndicator (devIndicator) {\n                dispatch({\n                    type: _shared.ACTION_DEV_INDICATOR,\n                    devIndicator\n                });\n            }\n        };\n    }, [\n        dispatch\n    ]);\n    //  We render a separate error overlay at the root when an error is thrown from rendering RSC, so\n    //  we should not render an additional error overlay in the descendent. However, we need to\n    //  keep rendering these hooks to ensure HMR works when the error is addressed.\n    const shouldRenderErrorOverlay = (0, _react.useSyncExternalStore)(()=>()=>{}, ()=>!(0, _iserrorthrownwhilerenderingrsc.shouldRenderRootLevelErrorOverlay)(), ()=>true);\n    const handleOnUnhandledError = (0, _react.useCallback)((error)=>{\n        const errorDetails = error.details;\n        // Component stack is added to the error in use-error-handler in case there was a hydration error\n        const componentStackTrace = error._componentStack || (errorDetails == null ? void 0 : errorDetails.componentStack);\n        const warning = errorDetails == null ? void 0 : errorDetails.warning;\n        dispatch({\n            type: _shared.ACTION_UNHANDLED_ERROR,\n            reason: error,\n            frames: (0, _parsestack.parseStack)(error.stack || ''),\n            componentStackFrames: typeof componentStackTrace === 'string' ? (0, _parsecomponentstack.parseComponentStack)(componentStackTrace) : undefined,\n            warning\n        });\n    }, [\n        dispatch\n    ]);\n    const handleOnUnhandledRejection = (0, _react.useCallback)((reason)=>{\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(reason);\n        dispatch({\n            type: _shared.ACTION_UNHANDLED_REJECTION,\n            reason: stitchedError,\n            frames: (0, _parsestack.parseStack)(stitchedError.stack || '')\n        });\n    }, [\n        dispatch\n    ]);\n    (0, _useerrorhandler.useErrorHandler)(handleOnUnhandledError, handleOnUnhandledRejection);\n    const webSocketRef = (0, _usewebsocket.useWebsocket)(assetPrefix);\n    (0, _usewebsocket.useWebsocketPing)(webSocketRef);\n    const sendMessage = (0, _usewebsocket.useSendMessage)(webSocketRef);\n    const processTurbopackMessage = (0, _usewebsocket.useTurbopack)(sendMessage, (err)=>performFullReload(err, sendMessage));\n    const router = (0, _navigation.useRouter)();\n    // We don't want access of the pathname for the dev tools to trigger a dynamic\n    // access (as the dev overlay will never be present in production).\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const appIsrManifestRef = (0, _react.useRef)({});\n    const pathnameRef = (0, _react.useRef)(pathname);\n    if (true) {\n        // this conditional is only for dead-code elimination which\n        // isn't a runtime conditional only build-time so ignore hooks rule\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            pathnameRef.current = pathname;\n            const appIsrManifest = appIsrManifestRef.current;\n            if (appIsrManifest) {\n                if (pathname && pathname in appIsrManifest) {\n                    try {\n                        dispatcher.onStaticIndicator(true);\n                    } catch (reason) {\n                        let message = '';\n                        if (reason instanceof DOMException) {\n                            var _reason_stack;\n                            // Most likely a SecurityError, because of an unavailable localStorage\n                            message = (_reason_stack = reason.stack) != null ? _reason_stack : reason.message;\n                        } else if (reason instanceof Error) {\n                            var _reason_stack1;\n                            message = 'Error: ' + reason.message + '\\n' + ((_reason_stack1 = reason.stack) != null ? _reason_stack1 : '');\n                        } else {\n                            message = 'Unexpected Exception: ' + reason;\n                        }\n                        console.warn('[HMR] ' + message);\n                    }\n                } else {\n                    dispatcher.onStaticIndicator(false);\n                }\n            }\n        }, [\n            pathname,\n            dispatcher\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        const websocket = webSocketRef.current;\n        if (!websocket) return;\n        const handler = (event)=>{\n            try {\n                const obj = JSON.parse(event.data);\n                (0, _handledevbuildindicatorhmrevents.handleDevBuildIndicatorHmrEvents)(obj);\n                processMessage(obj, sendMessage, processTurbopackMessage, router, dispatcher, appIsrManifestRef, pathnameRef);\n            } catch (err) {\n                var _err_stack;\n                console.warn('[HMR] Invalid message: ' + JSON.stringify(event.data) + '\\n' + ((_err_stack = err == null ? void 0 : err.stack) != null ? _err_stack : ''));\n            }\n        };\n        websocket.addEventListener('message', handler);\n        return ()=>websocket.removeEventListener('message', handler);\n    }, [\n        sendMessage,\n        router,\n        webSocketRef,\n        dispatcher,\n        processTurbopackMessage,\n        appIsrManifestRef\n    ]);\n    if (shouldRenderErrorOverlay) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_appdevoverlay.AppDevOverlay, {\n            state: state,\n            globalError: globalError,\n            children: children\n        });\n    }\n    return children;\n}\n_c = HotReload;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-reloader-client.js.map\nvar _c;\n$RefreshReg$(_c, \"HotReload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"FontStyles\", ({\n    enumerable: true,\n    get: function() {\n        return FontStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _css = __webpack_require__(/*! ../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n      /* latin-ext */\\n      @font-face {\\n        font-family: '__nextjs-Geist';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-latin-ext.woff2) format('woff2');\\n        unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7,\\n          U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F,\\n          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,\\n          U+A720-A7FF;\\n      }\\n      /* latin-ext */\\n      @font-face {\\n        font-family: '__nextjs-Geist Mono';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-mono-latin-ext.woff2) format('woff2');\\n        unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7,\\n          U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F,\\n          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,\\n          U+A720-A7FF;\\n      }\\n      /* latin */\\n      @font-face {\\n        font-family: '__nextjs-Geist';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-latin.woff2) format('woff2');\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      /* latin */\\n      @font-face {\\n        font-family: '__nextjs-Geist Mono';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-mono-latin.woff2) format('woff2');\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n    \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst FontStyles = ()=>{\n    (0, _react.useInsertionEffect)(()=>{\n        const style = document.createElement('style');\n        style.textContent = (0, _css.css)(_templateObject());\n        document.head.appendChild(style);\n        return ()=>{\n            document.head.removeChild(style);\n        };\n    }, []);\n    return null;\n};\n_c = FontStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=font-styles.js.map\nvar _c;\n$RefreshReg$(_c, \"FontStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/shared.js ***!
  \******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_BEFORE_REFRESH: function() {\n        return ACTION_BEFORE_REFRESH;\n    },\n    ACTION_BUILD_ERROR: function() {\n        return ACTION_BUILD_ERROR;\n    },\n    ACTION_BUILD_OK: function() {\n        return ACTION_BUILD_OK;\n    },\n    ACTION_DEBUG_INFO: function() {\n        return ACTION_DEBUG_INFO;\n    },\n    ACTION_DEV_INDICATOR: function() {\n        return ACTION_DEV_INDICATOR;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_STATIC_INDICATOR: function() {\n        return ACTION_STATIC_INDICATOR;\n    },\n    ACTION_UNHANDLED_ERROR: function() {\n        return ACTION_UNHANDLED_ERROR;\n    },\n    ACTION_UNHANDLED_REJECTION: function() {\n        return ACTION_UNHANDLED_REJECTION;\n    },\n    ACTION_VERSION_INFO: function() {\n        return ACTION_VERSION_INFO;\n    },\n    INITIAL_OVERLAY_STATE: function() {\n        return INITIAL_OVERLAY_STATE;\n    },\n    REACT_REFRESH_FULL_RELOAD_FROM_ERROR: function() {\n        return REACT_REFRESH_FULL_RELOAD_FROM_ERROR;\n    },\n    STORAGE_KEY_POSITION: function() {\n        return STORAGE_KEY_POSITION;\n    },\n    STORAGE_KEY_THEME: function() {\n        return STORAGE_KEY_THEME;\n    },\n    useErrorOverlayReducer: function() {\n        return useErrorOverlayReducer;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nvar _process_env___NEXT_DEV_INDICATOR;\nconst ACTION_STATIC_INDICATOR = 'static-indicator';\nconst ACTION_BUILD_OK = 'build-ok';\nconst ACTION_BUILD_ERROR = 'build-error';\nconst ACTION_BEFORE_REFRESH = 'before-fast-refresh';\nconst ACTION_REFRESH = 'fast-refresh';\nconst ACTION_VERSION_INFO = 'version-info';\nconst ACTION_UNHANDLED_ERROR = 'unhandled-error';\nconst ACTION_UNHANDLED_REJECTION = 'unhandled-rejection';\nconst ACTION_DEBUG_INFO = 'debug-info';\nconst ACTION_DEV_INDICATOR = 'dev-indicator';\nconst STORAGE_KEY_THEME = '__nextjs-dev-tools-theme';\nconst STORAGE_KEY_POSITION = '__nextjs-dev-tools-position';\nfunction pushErrorFilterDuplicates(errors, err) {\n    return [\n        ...errors.filter((e)=>{\n            // Filter out duplicate errors\n            return e.event.reason.stack !== err.event.reason.stack;\n        }),\n        err\n    ];\n}\nconst shouldDisableDevIndicator = ((_process_env___NEXT_DEV_INDICATOR = true) == null ? void 0 : _process_env___NEXT_DEV_INDICATOR.toString()) === 'false';\nconst INITIAL_OVERLAY_STATE = {\n    nextId: 1,\n    buildError: null,\n    errors: [],\n    notFound: false,\n    staticIndicator: false,\n    // To prevent flickering, set the initial state to disabled.\n    disableDevIndicator: true,\n    refreshState: {\n        type: 'idle'\n    },\n    rootLayoutMissingTags: [],\n    versionInfo: {\n        installed: '0.0.0',\n        staleness: 'unknown'\n    },\n    debugInfo: {\n        devtoolsFrontendUrl: undefined\n    }\n};\nfunction getInitialState(routerType) {\n    return {\n        ...INITIAL_OVERLAY_STATE,\n        routerType\n    };\n}\nfunction useErrorOverlayReducer(routerType) {\n    return (0, _react.useReducer)((_state, action)=>{\n        switch(action.type){\n            case ACTION_DEBUG_INFO:\n                {\n                    return {\n                        ..._state,\n                        debugInfo: action.debugInfo\n                    };\n                }\n            case ACTION_STATIC_INDICATOR:\n                {\n                    return {\n                        ..._state,\n                        staticIndicator: action.staticIndicator\n                    };\n                }\n            case ACTION_BUILD_OK:\n                {\n                    return {\n                        ..._state,\n                        buildError: null\n                    };\n                }\n            case ACTION_BUILD_ERROR:\n                {\n                    return {\n                        ..._state,\n                        buildError: action.message\n                    };\n                }\n            case ACTION_BEFORE_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        refreshState: {\n                            type: 'pending',\n                            errors: []\n                        }\n                    };\n                }\n            case ACTION_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        buildError: null,\n                        errors: // and UNHANDLED_REJECTION events might be dispatched between the\n                        // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n                        // around until the next refresh. Otherwise we run into a race\n                        // condition where those errors would be cleared on refresh completion\n                        // before they can be displayed.\n                        _state.refreshState.type === 'pending' ? _state.refreshState.errors : [],\n                        refreshState: {\n                            type: 'idle'\n                        }\n                    };\n                }\n            case ACTION_UNHANDLED_ERROR:\n            case ACTION_UNHANDLED_REJECTION:\n                {\n                    switch(_state.refreshState.type){\n                        case 'idle':\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    errors: pushErrorFilterDuplicates(_state.errors, {\n                                        id: _state.nextId,\n                                        event: action\n                                    })\n                                };\n                            }\n                        case 'pending':\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    refreshState: {\n                                        ..._state.refreshState,\n                                        errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                                            id: _state.nextId,\n                                            event: action\n                                        })\n                                    }\n                                };\n                            }\n                        default:\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const _ = _state.refreshState;\n                            return _state;\n                    }\n                }\n            case ACTION_VERSION_INFO:\n                {\n                    return {\n                        ..._state,\n                        versionInfo: action.versionInfo\n                    };\n                }\n            case ACTION_DEV_INDICATOR:\n                {\n                    return {\n                        ..._state,\n                        disableDevIndicator: shouldDisableDevIndicator || !!action.devIndicator.disabledUntil\n                    };\n                }\n            default:\n                {\n                    return _state;\n                }\n        }\n    }, getInitialState(routerType));\n}\nconst REACT_REFRESH_FULL_RELOAD_FROM_ERROR = '[Fast Refresh] performing full reload because your application had an unrecoverable error';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shared.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\n"));

/***/ })

}]);