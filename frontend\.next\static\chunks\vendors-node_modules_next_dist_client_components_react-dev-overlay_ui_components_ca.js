"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js ***!
  \***********************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    CALL_STACK_FRAME_STYLES: function() {\n        return CALL_STACK_FRAME_STYLES;\n    },\n    CallStackFrame: function() {\n        return CallStackFrame;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _external = __webpack_require__(/*! ../../icons/external */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/external.js\");\nconst _stackframe = __webpack_require__(/*! ../../../utils/stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../utils/use-open-in-editor */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\");\nconst CallStackFrame = function CallStackFrame(param) {\n    let { frame } = param;\n    var _frame_originalStackFrame;\n    // TODO: ability to expand resolved frames\n    const f = (_frame_originalStackFrame = frame.originalStackFrame) != null ? _frame_originalStackFrame : frame.sourceStackFrame;\n    const hasSource = Boolean(frame.originalCodeFrame);\n    const open = (0, _useopenineditor.useOpenInEditor)(hasSource ? {\n        file: f.file,\n        lineNumber: f.lineNumber,\n        column: f.column\n    } : undefined);\n    // Format method to strip out the webpack layer prefix.\n    // e.g. (app-pages-browser)/./app/page.tsx -> ./app/page.tsx\n    const formattedMethod = f.methodName.replace(/^\\([\\w-]+\\)\\//, '');\n    // Formatted file source could be empty. e.g. <anonymous> will be formatted to empty string,\n    // we'll skip rendering the frame in this case.\n    const fileSource = (0, _stackframe.getFrameSource)(f);\n    if (!fileSource) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-call-stack-frame\": true,\n        \"data-nextjs-call-stack-frame-no-source\": !hasSource,\n        \"data-nextjs-call-stack-frame-ignored\": frame.ignored,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"call-stack-frame-method-name\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                        text: formattedMethod\n                    }),\n                    hasSource && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        onClick: open,\n                        className: \"open-in-editor-button\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_external.ExternalIcon, {\n                            width: 16,\n                            height: 16\n                        })\n                    }),\n                    frame.error ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        className: \"source-mapping-error-button\",\n                        onClick: ()=>console.error(frame.reason),\n                        title: \"Sourcemapping failed. Click to log cause of error.\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_external.SourceMappingErrorIcon, {\n                            width: 16,\n                            height: 16\n                        })\n                    }) : null\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"call-stack-frame-file-source\",\n                \"data-has-source\": hasSource,\n                children: fileSource\n            })\n        ]\n    });\n};\n_c = CallStackFrame;\nconst CALL_STACK_FRAME_STYLES = '\\n  [data-nextjs-call-stack-frame-no-source] {\\n    padding: 6px 8px;\\n    margin-bottom: 4px;\\n\\n    border-radius: var(--rounded-lg);\\n  }\\n\\n  [data-nextjs-call-stack-frame-no-source]:last-child {\\n    margin-bottom: 0;\\n  }\\n\\n  [data-nextjs-call-stack-frame-ignored=\"true\"] {\\n    opacity: 0.6;\\n  }\\n\\n  [data-nextjs-call-stack-frame] {\\n    user-select: text;\\n    display: block;\\n    box-sizing: border-box;\\n\\n    user-select: text;\\n    -webkit-user-select: text;\\n    -moz-user-select: text;\\n    -ms-user-select: text;\\n\\n    padding: 6px 8px;\\n\\n    border-radius: var(--rounded-lg);\\n  }\\n\\n  .call-stack-frame-method-name {\\n    display: flex;\\n    align-items: center;\\n    gap: 4px;\\n\\n    margin-bottom: 4px;\\n    font-family: var(--font-stack-monospace);\\n\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n\\n    svg {\\n      width: var(--size-16px);\\n      height: var(--size-16px);\\n    }\\n  }\\n\\n  .open-in-editor-button, .source-mapping-error-button {\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border-radius: var(--rounded-full);\\n    padding: 4px;\\n    color: var(--color-font);\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n\\n    &:focus-visible {\\n      outline: var(--focus-ring);\\n      outline-offset: -2px;\\n    }\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n  }\\n\\n  .call-stack-frame-file-source {\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n  }\\n';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=call-stack-frame.js.map\nvar _c;\n$RefreshReg$(_c, \"CallStackFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js ***!
  \***********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    CODE_FRAME_STYLES: function() {\n        return CODE_FRAME_STYLES;\n    },\n    CodeFrame: function() {\n        return CodeFrame;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _anser = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/anser */ \"(app-pages-browser)/./node_modules/next/dist/compiled/anser/index.js\"));\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _stackframe = __webpack_require__(/*! ../../../utils/stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../utils/use-open-in-editor */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\");\nconst _external = __webpack_require__(/*! ../../icons/external */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/external.js\");\nconst _file = __webpack_require__(/*! ../../icons/file */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/file.js\");\nfunction CodeFrame(param) {\n    let { stackFrame, codeFrame } = param;\n    var _stackFrame_file;\n    // Strip leading spaces out of the code frame:\n    const formattedFrame = (0, _react.useMemo)(()=>{\n        const lines = codeFrame.split(/\\r?\\n/g);\n        // Find the minimum length of leading spaces after `|` in the code frame\n        const miniLeadingSpacesLength = lines.map((line)=>/^>? +\\d+ +\\| [ ]+/.exec((0, _stripansi.default)(line)) === null ? null : /^>? +\\d+ +\\| ( *)/.exec((0, _stripansi.default)(line))).filter(Boolean).map((v)=>v.pop()).reduce((c, n)=>isNaN(c) ? n.length : Math.min(c, n.length), NaN);\n        // When the minimum length of leading spaces is greater than 1, remove them\n        // from the code frame to help the indentation looks better when there's a lot leading spaces.\n        if (miniLeadingSpacesLength > 1) {\n            return lines.map((line, a)=>~(a = line.indexOf('|')) ? line.substring(0, a) + line.substring(a).replace(\"^\\\\ {\" + miniLeadingSpacesLength + \"}\", '') : line).join('\\n');\n        }\n        return lines.join('\\n');\n    }, [\n        codeFrame\n    ]);\n    const decoded = (0, _react.useMemo)(()=>{\n        return _anser.default.ansiToJson(formattedFrame, {\n            json: true,\n            use_classes: true,\n            remove_empty: true\n        });\n    }, [\n        formattedFrame\n    ]);\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file: stackFrame.file,\n        lineNumber: stackFrame.lineNumber,\n        column: stackFrame.column\n    });\n    const fileExtension = stackFrame == null ? void 0 : (_stackFrame_file = stackFrame.file) == null ? void 0 : _stackFrame_file.split('.').pop();\n    // TODO: make the caret absolute\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-codeframe\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                className: \"code-frame-header\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                    className: \"code-frame-link\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            className: \"code-frame-icon\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_file.FileIcon, {\n                                lang: fileExtension\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                            \"data-text\": true,\n                            children: [\n                                (0, _stackframe.getFrameSource)(stackFrame),\n                                \" @\",\n                                ' ',\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                    text: stackFrame.methodName\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            \"aria-label\": \"Open in editor\",\n                            \"data-with-open-in-editor-link-source-file\": true,\n                            onClick: open,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                className: \"code-frame-icon\",\n                                \"data-icon\": \"right\",\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_external.ExternalIcon, {\n                                    width: 16,\n                                    height: 16\n                                })\n                            })\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                className: \"code-frame-pre\",\n                children: decoded.map((entry, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        style: {\n                            color: entry.fg ? \"var(--color-\" + entry.fg + \")\" : undefined,\n                            ...entry.decoration === 'bold' ? // above 600, hence a temporary fix is to use 500 for bold.\n                            {\n                                fontWeight: 500\n                            } : entry.decoration === 'italic' ? {\n                                fontStyle: 'italic'\n                            } : undefined\n                        },\n                        children: entry.content\n                    }, \"frame-\" + index))\n            })\n        ]\n    });\n}\n_c = CodeFrame;\nconst CODE_FRAME_STYLES = \"\\n  [data-nextjs-codeframe] {\\n    background-color: var(--color-background-200);\\n    overflow: hidden;\\n    color: var(--color-gray-1000);\\n    text-overflow: ellipsis;\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: 8px;\\n    font-family: var(--font-stack-monospace);\\n    font-size: var(--size-12);\\n    line-height: var(--size-16);\\n    margin: 8px 0;\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n  }\\n\\n  .code-frame-link,\\n  .code-frame-pre {\\n    padding: 12px;\\n  }\\n\\n  .code-frame-link svg {\\n    flex-shrink: 0;\\n  }\\n\\n  .code-frame-link [data-text] {\\n    display: inline-flex;\\n    text-align: left;\\n    margin: auto 6px;\\n  }\\n\\n  .code-frame-pre {\\n    white-space: pre-wrap;\\n  }\\n\\n  .code-frame-header {\\n    width: 100%;\\n    transition: background 100ms ease-out;\\n    border-radius: 8px 8px 0 0;\\n    border-bottom: 1px solid var(--color-gray-400);\\n  }\\n\\n  [data-with-open-in-editor-link-source-file] {\\n    padding: 4px;\\n    margin: -4px 0 -4px auto;\\n    border-radius: var(--rounded-full);\\n    margin-left: auto;\\n\\n    &:focus-visible {\\n      outline: var(--focus-ring);\\n      outline-offset: -2px;\\n    }\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n  }\\n\\n  [data-nextjs-codeframe]::selection,\\n  [data-nextjs-codeframe] *::selection {\\n    background-color: var(--color-ansi-selection);\\n  }\\n\\n  [data-nextjs-codeframe] *:not(a) {\\n    color: inherit;\\n    background-color: transparent;\\n    font-family: var(--font-stack-monospace);\\n  }\\n\\n  [data-nextjs-codeframe] > * {\\n    margin: 0;\\n  }\\n\\n  .code-frame-link {\\n    display: flex;\\n    margin: 0;\\n    outline: 0;\\n  }\\n  .code-frame-link [data-icon='right'] {\\n    margin-left: auto;\\n  }\\n\\n  [data-nextjs-codeframe] div > pre {\\n    overflow: hidden;\\n    display: inline-block;\\n  }\\n\\n  [data-nextjs-codeframe] svg {\\n    color: var(--color-gray-900);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=code-frame.js.map\nvar _c;\n$RefreshReg$(_c, \"CodeFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js ***!
  \*******************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    COPY_BUTTON_STYLES: function() {\n        return COPY_BUTTON_STYLES;\n    },\n    CopyButton: function() {\n        return CopyButton;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _cx = __webpack_require__(/*! ../../utils/cx */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nfunction useCopyLegacy(content) {\n    _s();\n    // This would be simpler with useActionState but we need to support React 18 here.\n    // React 18 also doesn't have async transitions.\n    const [copyState, dispatch] = _react.useReducer({\n        \"useCopyLegacy.useReducer\": (state, action)=>{\n            if (action.type === 'reset') {\n                return {\n                    state: 'initial'\n                };\n            }\n            if (action.type === 'copied') {\n                return {\n                    state: 'success'\n                };\n            }\n            if (action.type === 'copying') {\n                return {\n                    state: 'pending'\n                };\n            }\n            if (action.type === 'error') {\n                return {\n                    state: 'error',\n                    error: action.error\n                };\n            }\n            return state;\n        }\n    }[\"useCopyLegacy.useReducer\"], {\n        state: 'initial'\n    });\n    function copy() {\n        if (isPending) {\n            return;\n        }\n        if (!navigator.clipboard) {\n            dispatch({\n                type: 'error',\n                error: Object.defineProperty(new Error('Copy to clipboard is not supported in this browser'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E376\",\n                    enumerable: false,\n                    configurable: true\n                })\n            });\n        } else {\n            dispatch({\n                type: 'copying'\n            });\n            navigator.clipboard.writeText(content).then(()=>{\n                dispatch({\n                    type: 'copied'\n                });\n            }, (error)=>{\n                dispatch({\n                    type: 'error',\n                    error\n                });\n            });\n        }\n    }\n    const reset = _react.useCallback({\n        \"useCopyLegacy.useCallback[reset]\": ()=>{\n            dispatch({\n                type: 'reset'\n            });\n        }\n    }[\"useCopyLegacy.useCallback[reset]\"], []);\n    const isPending = copyState.state === 'pending';\n    return [\n        copyState,\n        copy,\n        reset,\n        isPending\n    ];\n}\n_s(useCopyLegacy, \"hTZjSt/cdkW7Y9WuPQJ5lxBOPCc=\");\nfunction useCopyModern(content) {\n    _s1();\n    const [copyState, dispatch, isPending] = _react.useActionState({\n        \"useCopyModern.useActionState\": (state, action)=>{\n            if (action === 'reset') {\n                return {\n                    state: 'initial'\n                };\n            }\n            if (action === 'copy') {\n                if (!navigator.clipboard) {\n                    return {\n                        state: 'error',\n                        error: Object.defineProperty(new Error('Copy to clipboard is not supported in this browser'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E376\",\n                            enumerable: false,\n                            configurable: true\n                        })\n                    };\n                }\n                return navigator.clipboard.writeText(content).then({\n                    \"useCopyModern.useActionState\": ()=>{\n                        return {\n                            state: 'success'\n                        };\n                    }\n                }[\"useCopyModern.useActionState\"], {\n                    \"useCopyModern.useActionState\": (error)=>{\n                        return {\n                            state: 'error',\n                            error\n                        };\n                    }\n                }[\"useCopyModern.useActionState\"]);\n            }\n            return state;\n        }\n    }[\"useCopyModern.useActionState\"], {\n        state: 'initial'\n    });\n    function copy() {\n        _react.startTransition(()=>{\n            dispatch('copy');\n        });\n    }\n    const reset = _react.useCallback({\n        \"useCopyModern.useCallback[reset]\": ()=>{\n            dispatch('reset');\n        }\n    }[\"useCopyModern.useCallback[reset]\"], [\n        // TODO: `dispatch` from `useActionState` is not reactive.\n        // Remove from dependencies once https://github.com/facebook/react/pull/29665 is released.\n        dispatch\n    ]);\n    return [\n        copyState,\n        copy,\n        reset,\n        isPending\n    ];\n}\n_s1(useCopyModern, \"bm8EPZwjhKG1elXk5Q3PR5uIKA8=\", false, function() {\n    return [\n        _react.useActionState\n    ];\n});\nconst useCopy = typeof _react.useActionState === 'function' ? useCopyModern : useCopyLegacy;\nfunction CopyButton(param) {\n    _s2();\n    let { actionLabel, successLabel, content, icon, disabled, ...props } = param;\n    const [copyState, copy, reset, isPending] = useCopy(content);\n    const error = copyState.state === 'error' ? copyState.error : null;\n    _react.useEffect({\n        \"CopyButton.useEffect\": ()=>{\n            if (error !== null) {\n                // Additional console.error to get the stack.\n                console.error(error);\n            }\n        }\n    }[\"CopyButton.useEffect\"], [\n        error\n    ]);\n    _react.useEffect({\n        \"CopyButton.useEffect\": ()=>{\n            if (copyState.state === 'success') {\n                const timeoutId = setTimeout({\n                    \"CopyButton.useEffect.timeoutId\": ()=>{\n                        reset();\n                    }\n                }[\"CopyButton.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"CopyButton.useEffect\": ()=>{\n                        clearTimeout(timeoutId);\n                    }\n                })[\"CopyButton.useEffect\"];\n            }\n        }\n    }[\"CopyButton.useEffect\"], [\n        isPending,\n        copyState.state,\n        reset\n    ]);\n    const isDisabled = isPending || disabled;\n    const label = copyState.state === 'success' ? successLabel : actionLabel;\n    // Assign default icon\n    const renderedIcon = copyState.state === 'success' ? /*#__PURE__*/ (0, _jsxruntime.jsx)(CopySuccessIcon, {}) : icon || /*#__PURE__*/ (0, _jsxruntime.jsx)(CopyIcon, {\n        width: 14,\n        height: 14,\n        className: \"error-overlay-toolbar-button-icon\"\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n        ...props,\n        type: \"button\",\n        title: label,\n        \"aria-label\": label,\n        \"aria-disabled\": isDisabled,\n        disabled: isDisabled,\n        \"data-nextjs-copy-button\": true,\n        className: (0, _cx.cx)(props.className, 'nextjs-data-copy-button', \"nextjs-data-copy-button--\" + copyState.state),\n        onClick: ()=>{\n            if (!isDisabled) {\n                copy();\n            }\n        },\n        children: [\n            renderedIcon,\n            copyState.state === 'error' ? \" \" + copyState.error : null\n        ]\n    });\n}\n_s2(CopyButton, \"IQyXV+jf8IbwtkGNcvjxwQuiFSU=\", false, function() {\n    return [\n        useCopy\n    ];\n});\n_c = CopyButton;\nfunction CopyIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M2.406.438c-.845 0-1.531.685-1.531 1.53v6.563c0 .846.686 1.531 1.531 1.531H3.937V8.75H2.406a.219.219 0 0 1-.219-.219V1.97c0-.121.098-.219.22-.219h4.812c.12 0 .218.098.218.219v.656H8.75v-.656c0-.846-.686-1.532-1.531-1.532H2.406zm4.375 3.5c-.845 0-1.531.685-1.531 1.53v6.563c0 .846.686 1.531 1.531 1.531h4.813c.845 0 1.531-.685 1.531-1.53V5.468c0-.846-.686-1.532-1.531-1.532H6.78zm-.218 1.53c0-.12.097-.218.218-.218h4.813c.12 0 .219.098.219.219v6.562c0 .121-.098.219-.22.219H6.782a.219.219 0 0 1-.218-.219V5.47z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c1 = CopyIcon;\nfunction CopySuccessIcon() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        height: \"16\",\n        xlinkTitle: \"copied\",\n        viewBox: \"0 0 16 16\",\n        width: \"16\",\n        stroke: \"currentColor\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            d: \"M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z\"\n        })\n    });\n}\n_c2 = CopySuccessIcon;\nconst COPY_BUTTON_STYLES = \"\\n  .nextjs-data-copy-button {\\n    color: inherit;\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n  }\\n  .nextjs-data-copy-button--initial:hover {\\n    cursor: pointer;\\n  }\\n  .nextjs-data-copy-button--error,\\n  .nextjs-data-copy-button--error:hover {\\n    color: var(--color-ansi-red);\\n  }\\n  .nextjs-data-copy-button--success {\\n    color: var(--color-ansi-green);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CopyButton\");\n$RefreshReg$(_c1, \"CopyIcon\");\n$RefreshReg$(_c2, \"CopySuccessIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\n"));

/***/ })

}]);