"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js ***!
  \***********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HotlinkedText\", ({\n    enumerable: true,\n    get: function() {\n        return HotlinkedText;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _magicidentifier = __webpack_require__(/*! ../../../../../../shared/lib/magic-identifier */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/magic-identifier.js\");\nconst linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/i;\nconst splitRegexp = new RegExp(\"(\" + _magicidentifier.MAGIC_IDENTIFIER_REGEX.source + \"|\\\\s+)\");\nconst HotlinkedText = function HotlinkedText(props) {\n    const { text, matcher } = props;\n    const wordsAndWhitespaces = text.split(splitRegexp);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: wordsAndWhitespaces.map((word, index)=>{\n            if (linkRegex.test(word)) {\n                const link = linkRegex.exec(word);\n                const href = link[0];\n                // If link matcher is present but the link doesn't match, don't turn it into a link\n                if (typeof matcher === 'function' && !matcher(href)) {\n                    return word;\n                }\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.Fragment, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noreferrer noopener\",\n                        children: word\n                    })\n                }, \"link-\" + index);\n            }\n            try {\n                const decodedWord = (0, _magicidentifier.decodeMagicIdentifier)(word);\n                if (decodedWord !== word) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"i\", {\n                        children: [\n                            '{',\n                            decodedWord,\n                            '}'\n                        ]\n                    }, \"ident-\" + index);\n                }\n            } catch (e) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"i\", {\n                    children: [\n                        '{',\n                        word,\n                        \" (decoding failed: \",\n                        '' + e,\n                        \")\",\n                        '}'\n                    ]\n                }, \"ident-\" + index);\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.Fragment, {\n                children: word\n            }, \"text-\" + index);\n        })\n    });\n};\n_c = HotlinkedText;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"HotlinkedText\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js ***!
  \**************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PseudoHtmlDiff\", ({\n    enumerable: true,\n    get: function() {\n        return PseudoHtmlDiff;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _collapseicon = __webpack_require__(/*! ../../icons/collapse-icon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/collapse-icon.js\");\nfunction PseudoHtmlDiff(param) {\n    let { firstContent, secondContent, hydrationMismatchType, reactOutputComponentDiff, ...props } = param;\n    const [isDiffCollapsed, toggleCollapseHtml] = (0, _react.useState)(true);\n    const htmlComponents = (0, _react.useMemo)(()=>{\n        const componentStacks = [];\n        const reactComponentDiffLines = reactOutputComponentDiff.split('\\n');\n        reactComponentDiffLines.forEach((line, index)=>{\n            const isDiffLine = line[0] === '+' || line[0] === '-';\n            const isHighlightedLine = line[0] === '>';\n            const hasSign = isDiffLine || isHighlightedLine;\n            const sign = hasSign ? line[0] : '';\n            const signIndex = hasSign ? line.indexOf(sign) : -1;\n            const [prefix, suffix] = hasSign ? [\n                line.slice(0, signIndex),\n                line.slice(signIndex + 1)\n            ] : [\n                line,\n                ''\n            ];\n            if (isDiffLine) {\n                componentStacks.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    \"data-nextjs-container-errors-pseudo-html-line\": true,\n                    \"data-nextjs-container-errors-pseudo-html--diff\": sign === '+' ? 'add' : 'remove',\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                        children: [\n                            prefix,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                \"data-nextjs-container-errors-pseudo-html-line-sign\": true,\n                                children: sign\n                            }),\n                            suffix,\n                            '\\n'\n                        ]\n                    })\n                }, 'comp-diff' + index));\n            } else {\n                // In general, if it's not collapsed, show the whole diff\n                componentStacks.push(/*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                    \"data-nextjs-container-errors-pseudo-html-line\": true,\n                    ...isHighlightedLine ? {\n                        'data-nextjs-container-errors-pseudo-html--diff': 'error'\n                    } : undefined,\n                    children: [\n                        prefix,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html-line-sign\": true,\n                            children: sign\n                        }),\n                        suffix,\n                        '\\n'\n                    ]\n                }, 'comp-diff' + index));\n            }\n        });\n        return componentStacks;\n    }, [\n        reactOutputComponentDiff\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-container-errors-pseudo-html\": true,\n        \"data-nextjs-container-errors-pseudo-html-collapse\": isDiffCollapsed,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                tabIndex: 10,\n                \"data-nextjs-container-errors-pseudo-html-collapse-button\": true,\n                onClick: ()=>toggleCollapseHtml(!isDiffCollapsed),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_collapseicon.CollapseIcon, {\n                    collapsed: isDiffCollapsed\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                ...props,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                    children: htmlComponents\n                })\n            })\n        ]\n    });\n}\n_c = PseudoHtmlDiff;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=diff-view.js.map\nvar _c;\n$RefreshReg$(_c, \"PseudoHtmlDiff\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js ***!
  \*********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    lock: function() {\n        return lock;\n    },\n    unlock: function() {\n        return unlock;\n    }\n});\nlet previousBodyPaddingRight;\nlet previousBodyOverflowSetting;\nlet activeLocks = 0;\nfunction lock() {\n    setTimeout(()=>{\n        if (activeLocks++ > 0) {\n            return;\n        }\n        const scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n        if (scrollBarGap > 0) {\n            previousBodyPaddingRight = document.body.style.paddingRight;\n            document.body.style.paddingRight = \"\" + scrollBarGap + \"px\";\n        }\n        previousBodyOverflowSetting = document.body.style.overflow;\n        document.body.style.overflow = 'hidden';\n    });\n}\nfunction unlock() {\n    setTimeout(()=>{\n        if (activeLocks === 0 || --activeLocks !== 0) {\n            return;\n        }\n        if (previousBodyPaddingRight !== undefined) {\n            document.body.style.paddingRight = previousBodyPaddingRight;\n            previousBodyPaddingRight = undefined;\n        }\n        if (previousBodyOverflowSetting !== undefined) {\n            document.body.style.overflow = previousBodyOverflowSetting;\n            previousBodyOverflowSetting = undefined;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body-locker.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return _overlay.Overlay;\n    }\n}));\nconst _overlay = __webpack_require__(/*! ./overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9vdmVybGF5L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBQVNBOzs7ZUFBQUEsU0FBQUEsT0FBTzs7O3FDQUFRIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcb3ZlcmxheVxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IE92ZXJsYXkgfSBmcm9tICcuL292ZXJsYXknXG4iXSwibmFtZXMiOlsiT3ZlcmxheSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js ***!
  \*****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return Overlay;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _bodylocker = __webpack_require__(/*! ./body-locker */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\");\nconst Overlay = function Overlay(param) {\n    _s();\n    let { className, children, fixed, ...props } = param;\n    _react.useEffect({\n        \"Overlay.useEffect\": ()=>{\n            (0, _bodylocker.lock)();\n            return ({\n                \"Overlay.useEffect\": ()=>{\n                    (0, _bodylocker.unlock)();\n                }\n            })[\"Overlay.useEffect\"];\n        }\n    }[\"Overlay.useEffect\"], []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-dialog-overlay\": true,\n        className: className,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-nextjs-dialog-backdrop\": true,\n                \"data-nextjs-dialog-backdrop-fixed\": fixed ? true : undefined\n            }),\n            children\n        ]\n    });\n};\n_s(Overlay, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Overlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"Overlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js ***!
  \****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  [data-nextjs-dialog-overlay] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 9000;\\n\\n    display: flex;\\n    align-content: center;\\n    align-items: center;\\n    flex-direction: column;\\n    padding: 10vh 15px 0;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      padding: 15px 15px 0;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    background-color: var(--color-backdrop);\\n    backdrop-filter: blur(10px);\\n    pointer-events: all;\\n    z-index: -1;\\n  }\\n\\n  [data-nextjs-dialog-backdrop-fixed] {\\n    cursor: not-allowed;\\n    -webkit-backdrop-filter: blur(8px);\\n    backdrop-filter: blur(8px);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9vdmVybGF5L3N0eWxlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OzBDQXlDU0E7OztlQUFBQTs7O0FBekNULE1BQU1BLFNBQVUiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxvdmVybGF5XFxzdHlsZXMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0eWxlcyA9IGBcbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1vdmVybGF5XSB7XG4gICAgcG9zaXRpb246IGZpeGVkO1xuICAgIHRvcDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgbGVmdDogMDtcbiAgICB6LWluZGV4OiA5MDAwO1xuXG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1jb250ZW50OiBjZW50ZXI7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIHBhZGRpbmc6IDEwdmggMTVweCAwO1xuICB9XG5cbiAgQG1lZGlhIChtYXgtaGVpZ2h0OiA4MTJweCkge1xuICAgIFtkYXRhLW5leHRqcy1kaWFsb2ctb3ZlcmxheV0ge1xuICAgICAgcGFkZGluZzogMTVweCAxNXB4IDA7XG4gICAgfVxuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcF0ge1xuICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICB0b3A6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgYm90dG9tOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY29sb3ItYmFja2Ryb3ApO1xuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgICBwb2ludGVyLWV2ZW50czogYWxsO1xuICAgIHotaW5kZXg6IC0xO1xuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcC1maXhlZF0ge1xuICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgfVxuYFxuXG5leHBvcnQgeyBzdHlsZXMgfVxuIl0sIm5hbWVzIjpbInN0eWxlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ShadowPortal\", ({\n    enumerable: true,\n    get: function() {\n        return ShadowPortal;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst _shared = __webpack_require__(/*! ../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nfunction ShadowPortal(param) {\n    _s();\n    let { children } = param;\n    let portalNode = _react.useRef(null);\n    let shadowNode = _react.useRef(null);\n    let [, forceUpdate] = _react.useState();\n    _react.useLayoutEffect({\n        \"ShadowPortal.useLayoutEffect\": ()=>{\n            const ownerDocument = document;\n            portalNode.current = ownerDocument.createElement('nextjs-portal');\n            // load default color preference from localstorage\n            if (typeof localStorage !== 'undefined') {\n                const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n                if (theme === 'dark') {\n                    portalNode.current.classList.add('dark');\n                    portalNode.current.classList.remove('light');\n                } else if (theme === 'light') {\n                    portalNode.current.classList.remove('dark');\n                    portalNode.current.classList.add('light');\n                }\n            }\n            shadowNode.current = portalNode.current.attachShadow({\n                mode: 'open'\n            });\n            ownerDocument.body.appendChild(portalNode.current);\n            forceUpdate({});\n            return ({\n                \"ShadowPortal.useLayoutEffect\": ()=>{\n                    if (portalNode.current && portalNode.current.ownerDocument) {\n                        portalNode.current.ownerDocument.body.removeChild(portalNode.current);\n                    }\n                }\n            })[\"ShadowPortal.useLayoutEffect\"];\n        }\n    }[\"ShadowPortal.useLayoutEffect\"], []);\n    return shadowNode.current ? /*#__PURE__*/ (0, _reactdom.createPortal)(children, shadowNode.current) : null;\n}\n_s(ShadowPortal, \"P7YL0rn/sjH62F7+OsEXN5GMw3U=\");\n_c = ShadowPortal;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shadow-portal.js.map\nvar _c;\n$RefreshReg$(_c, \"ShadowPortal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js\n"));

/***/ })

}]);