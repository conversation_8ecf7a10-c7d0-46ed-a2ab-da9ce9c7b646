"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst badgeVariants = {\n    variant: {\n        default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\"\n    }\n};\nconst Badge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant = \"default\", ...props } = param;\n    const variantClasses = badgeVariants.variant[variant] || badgeVariants.variant.default;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", variantClasses, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 22,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Badge;\nBadge.displayName = \"Badge\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Badge$React.forwardRef\");\n$RefreshReg$(_c1, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst TabsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n    activeTab: '',\n    setActiveTab: ()=>{}\n});\nconst Tabs = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { defaultValue = '', value, onValueChange, children, className, ...props } = param;\n    _s();\n    const [activeTab, setActiveTab] = react__WEBPACK_IMPORTED_MODULE_1__.useState(value || defaultValue);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Tabs.useEffect\": ()=>{\n            if (value !== undefined) {\n                setActiveTab(value);\n            }\n        }\n    }[\"Tabs.useEffect\"], [\n        value\n    ]);\n    const handleTabChange = (newValue)=>{\n        if (value === undefined) {\n            setActiveTab(newValue);\n        }\n        onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(newValue);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContext.Provider, {\n        value: {\n            activeTab,\n            setActiveTab: handleTabChange\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: className,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n            lineNumber: 59,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 58,\n        columnNumber: 7\n    }, undefined);\n}, \"k0FhWJTCExh/hFGuT1R1UiXiugA=\")), \"k0FhWJTCExh/hFGuT1R1UiXiugA=\");\n_c1 = Tabs;\nTabs.displayName = \"Tabs\";\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n});\n_c3 = TabsList;\nTabsList.displayName = \"TabsList\";\nconst TabsTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = _s1((param, ref)=>{\n    let { className, value, children, onClick, ...props } = param;\n    _s1();\n    const { activeTab, setActiveTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    const isActive = activeTab === value;\n    const handleClick = ()=>{\n        setActiveTab(value);\n        onClick === null || onClick === void 0 ? void 0 : onClick();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", isActive && \"bg-background text-foreground shadow-sm\", className),\n        onClick: handleClick,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 95,\n        columnNumber: 7\n    }, undefined);\n}, \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\")), \"pR0SVtEudEYsDDKRUYB3cTMIJHk=\");\n_c5 = TabsTrigger;\nTabsTrigger.displayName = \"TabsTrigger\";\nconst TabsContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = _s2((param, ref)=>{\n    let { className, value, children, ...props } = param;\n    _s2();\n    const { activeTab } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(TabsContext);\n    if (activeTab !== value) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 121,\n        columnNumber: 7\n    }, undefined);\n}, \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\")), \"K7ZG7fP8e1+itESE7n7QTWlG8XM=\");\n_c7 = TabsContent;\nTabsContent.displayName = \"TabsContent\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Tabs$React.forwardRef\");\n$RefreshReg$(_c1, \"Tabs\");\n$RefreshReg$(_c2, \"TabsList$React.forwardRef\");\n$RefreshReg$(_c3, \"TabsList\");\n$RefreshReg$(_c4, \"TabsTrigger$React.forwardRef\");\n$RefreshReg$(_c5, \"TabsTrigger\");\n$RefreshReg$(_c6, \"TabsContent$React.forwardRef\");\n$RefreshReg$(_c7, \"TabsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tabs.tsx\n"));

/***/ })

});