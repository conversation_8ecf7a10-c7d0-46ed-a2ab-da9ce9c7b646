"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Start with false to ensure server-client consistency\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start with true to prevent hydration mismatch\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track hydration state\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Hydration effect - runs only on client after mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    console.log('🔐 Initializing authentication...');\n                    // Check existing authentication from localStorage\n                    const storedAuthStatus = localStorage.getItem('plutoAuth');\n                    const authToken = localStorage.getItem('plutoAuthToken');\n                    if (storedAuthStatus === 'true' && authToken) {\n                        console.log('🔐 Found existing auth token - authenticating user');\n                        setIsAuthenticated(true);\n                        setIsLoading(false);\n                        setIsHydrated(true);\n                        // Now that authentication is confirmed, check backend connection\n                        try {\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                            sessionManager.checkBackendConnectionWhenReady().catch({\n                                \"AuthProvider.useEffect.initializeAuth\": ()=>{\n                                // Silent fail - session manager will handle fallback\n                                }\n                            }[\"AuthProvider.useEffect.initializeAuth\"]);\n                        } catch (error) {\n                            console.error('🔐 Error loading SessionManager:', error);\n                        }\n                        return;\n                    }\n                    // If not authenticated, try auto-login for development\n                    console.log('🔐 No existing auth found - attempting auto-login');\n                    try {\n                        // Small delay to ensure backend is ready\n                        await new Promise({\n                            \"AuthProvider.useEffect.initializeAuth\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"AuthProvider.useEffect.initializeAuth\"]);\n                        const response = await fetch('http://localhost:5000/auth/login', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                username: 'testuser',\n                                password: 'password123'\n                            })\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            localStorage.setItem('plutoAuth', 'true');\n                            localStorage.setItem('plutoAuthToken', data.access_token);\n                            localStorage.setItem('plutoRefreshToken', data.refresh_token);\n                            localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                            setIsAuthenticated(true);\n                            console.log('🔐 Auto-logged in with test user for development');\n                            // Now that authentication is established, check backend connection\n                            try {\n                                const { SessionManager } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\"));\n                                const sessionManager = SessionManager.getInstance();\n                                sessionManager.checkBackendConnectionWhenReady().catch({\n                                    \"AuthProvider.useEffect.initializeAuth\": ()=>{\n                                    // Silent fail - session manager will handle fallback\n                                    }\n                                }[\"AuthProvider.useEffect.initializeAuth\"]);\n                            } catch (error) {\n                                console.error('🔐 Error loading SessionManager after auto-login:', error);\n                            }\n                        } else {\n                            console.log('🔐 Auto-login failed - server response not ok:', response.status);\n                        }\n                    } catch (error) {\n                        console.log('🔐 Auto-login failed - network error:', error);\n                    }\n                    console.log('🔐 Authentication initialization complete');\n                    setIsLoading(false);\n                    setIsHydrated(true);\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            // Initialize authentication\n            initializeAuth();\n            // Failsafe timeout to prevent infinite loading\n            const timeoutId = setTimeout({\n                \"AuthProvider.useEffect.timeoutId\": ()=>{\n                    console.warn('🔐 Authentication initialization timeout - forcing completion');\n                    setIsLoading(false);\n                    setIsHydrated(true);\n                }\n            }[\"AuthProvider.useEffect.timeoutId\"], 10000); // 10 second timeout\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Only run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Route based on authentication status\n            if (isAuthenticated && (pathname === '/login' || pathname === '/')) {\n                router.replace('/dashboard');\n            } else if (!isAuthenticated && pathname !== '/login' && pathname !== '/') {\n                router.replace('/login');\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        pathname,\n        router\n    ]);\n    const login = async (username, password)=>{\n        setIsLoading(true);\n        try {\n            const success = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(username, password);\n            if (success) {\n                setIsAuthenticated(true);\n                router.push('/dashboard');\n                return true;\n            }\n            setIsAuthenticated(false);\n            return false;\n        } catch (error) {\n            console.error('Login failed:', error);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setIsAuthenticated(false);\n            router.push('/login');\n        }\n    };\n    // No loading screen needed since we initialize immediately from localStorage\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"WOFDDoScdEyRYXUfzRRWUPu8dM8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});