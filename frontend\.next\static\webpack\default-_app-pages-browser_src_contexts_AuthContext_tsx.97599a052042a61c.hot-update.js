"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("default-_app-pages-browser_src_contexts_AuthContext_tsx",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Start with false to ensure server-client consistency\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start with true to prevent hydration mismatch\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track hydration state\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Hydration effect - runs only on client after mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    console.log('🔐 Initializing authentication...');\n                    // Check existing authentication from localStorage\n                    const storedAuthStatus = localStorage.getItem('plutoAuth');\n                    const authToken = localStorage.getItem('plutoAuthToken');\n                    if (storedAuthStatus === 'true' && authToken) {\n                        console.log('🔐 Found existing auth token - authenticating user');\n                        setIsAuthenticated(true);\n                        setIsLoading(false);\n                        setIsHydrated(true);\n                        // Now that authentication is confirmed, check backend connection\n                        try {\n                            const { SessionManager } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_un\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_l\"), __webpack_require__.e(\"vendors-node_modules_n\"), __webpack_require__.e(\"vendors-node_modules_next_font_local_target_css-1d2c50c7\"), __webpack_require__.e(\"vendors-node_modules_t\"), __webpack_require__.e(\"default-_app-pages-browser_src_lib_session-manager_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\"));\n                            const sessionManager = SessionManager.getInstance();\n                            sessionManager.checkBackendConnectionWhenReady().catch({\n                                \"AuthProvider.useEffect.initializeAuth\": ()=>{\n                                // Silent fail - session manager will handle fallback\n                                }\n                            }[\"AuthProvider.useEffect.initializeAuth\"]);\n                        } catch (error) {\n                            console.error('🔐 Error loading SessionManager:', error);\n                        }\n                        return;\n                    }\n                    // If not authenticated, try auto-login for development\n                    console.log('🔐 No existing auth found - attempting auto-login');\n                    try {\n                        // Small delay to ensure backend is ready\n                        await new Promise({\n                            \"AuthProvider.useEffect.initializeAuth\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"AuthProvider.useEffect.initializeAuth\"]);\n                        const response = await fetch('http://localhost:5000/auth/login', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                username: 'testuser',\n                                password: 'password123'\n                            })\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            localStorage.setItem('plutoAuth', 'true');\n                            localStorage.setItem('plutoAuthToken', data.access_token);\n                            localStorage.setItem('plutoRefreshToken', data.refresh_token);\n                            localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                            setIsAuthenticated(true);\n                            console.log('🔐 Auto-logged in with test user for development');\n                            // Now that authentication is established, check backend connection\n                            try {\n                                const { SessionManager } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_un\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_l\"), __webpack_require__.e(\"vendors-node_modules_n\"), __webpack_require__.e(\"vendors-node_modules_next_font_local_target_css-1d2c50c7\"), __webpack_require__.e(\"vendors-node_modules_t\"), __webpack_require__.e(\"default-_app-pages-browser_src_lib_session-manager_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\"));\n                                const sessionManager = SessionManager.getInstance();\n                                sessionManager.checkBackendConnectionWhenReady().catch({\n                                    \"AuthProvider.useEffect.initializeAuth\": ()=>{\n                                    // Silent fail - session manager will handle fallback\n                                    }\n                                }[\"AuthProvider.useEffect.initializeAuth\"]);\n                            } catch (error) {\n                                console.error('🔐 Error loading SessionManager after auto-login:', error);\n                            }\n                        } else {\n                            console.log('🔐 Auto-login failed - server response not ok:', response.status);\n                        }\n                    } catch (error) {\n                        console.log('🔐 Auto-login failed - network error:', error);\n                    }\n                    console.log('🔐 Authentication initialization complete');\n                    setIsLoading(false);\n                    setIsHydrated(true);\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            // Initialize authentication\n            initializeAuth();\n            // Failsafe timeout to prevent infinite loading\n            const timeoutId = setTimeout({\n                \"AuthProvider.useEffect.timeoutId\": ()=>{\n                    console.warn('🔐 Authentication initialization timeout - forcing completion');\n                    setIsLoading(false);\n                    setIsHydrated(true);\n                }\n            }[\"AuthProvider.useEffect.timeoutId\"], 10000); // 10 second timeout\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Only run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Route based on authentication status\n            if (isAuthenticated && (pathname === '/login' || pathname === '/')) {\n                router.replace('/dashboard');\n            } else if (!isAuthenticated && pathname !== '/login' && pathname !== '/') {\n                router.replace('/login');\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        pathname,\n        router\n    ]);\n    const login = async (username, password)=>{\n        setIsLoading(true);\n        try {\n            const success = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(username, password);\n            if (success) {\n                setIsAuthenticated(true);\n                router.push('/dashboard');\n                return true;\n            }\n            setIsAuthenticated(false);\n            return false;\n        } catch (error) {\n            console.error('Login failed:', error);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setIsAuthenticated(false);\n            router.push('/login');\n        }\n    };\n    // No loading screen needed since we initialize immediately from localStorage\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"WOFDDoScdEyRYXUfzRRWUPu8dM8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});