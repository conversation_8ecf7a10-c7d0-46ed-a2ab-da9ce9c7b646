"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("default-_app-pages-browser_src_contexts_TradingContext_tsx-_app-pages-browser_src_lib_utils_ts",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For StablecoinSwap mode, we need to calculate the ratio via stablecoin prices\n        if (config.tradingMode === \"StablecoinSwap\" && config.preferredStablecoin) {\n            try {\n                // Get both crypto prices in terms of the preferred stablecoin\n                const crypto1StablecoinPrice = await getStablecoinExchangeRate(config.crypto1, config.preferredStablecoin);\n                const crypto2StablecoinPrice = await getStablecoinExchangeRate(config.crypto2, config.preferredStablecoin);\n                if (crypto1StablecoinPrice > 0 && crypto2StablecoinPrice > 0) {\n                    // Calculate Crypto1/Crypto2 ratio via stablecoin\n                    const ratio = crypto1StablecoinPrice / crypto2StablecoinPrice;\n                    console.log(\"✅ StablecoinSwap price via \".concat(config.preferredStablecoin, \": \").concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio.toFixed(6)));\n                    console.log(\"   \".concat(config.crypto1, \"/\").concat(config.preferredStablecoin, \" = \").concat(crypto1StablecoinPrice));\n                    console.log(\"   \".concat(config.crypto2, \"/\").concat(config.preferredStablecoin, \" = \").concat(crypto2StablecoinPrice));\n                    return ratio;\n                }\n            } catch (stablecoinError) {\n                console.warn('Stablecoin price calculation failed, falling back to direct pair...', stablecoinError);\n            }\n        }\n        // For SimpleSpot mode or fallback: try direct pair fetching\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            // For both modes, we want Crypto1/Crypto2 ratio\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (crypto.toUpperCase() === stablecoin.toUpperCase()) return 1.0;\n        // First try Binance API for direct pair\n        const binanceSymbol = \"\".concat(crypto.toUpperCase()).concat(stablecoin.toUpperCase());\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(binanceSymbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Stablecoin rate from Binance: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn(\"Binance API failed for \".concat(binanceSymbol, \", trying CoinGecko...\"));\n        }\n        // Fallback to CoinGecko API\n        const cryptoId = getCoinGeckoId(crypto);\n        const stablecoinId = getCoinGeckoId(stablecoin);\n        if (cryptoId && stablecoinId) {\n            // Try direct conversion\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId];\n                if (rate > 0) {\n                    console.log(\"✅ Stablecoin rate from CoinGecko: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Final fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"⚠️ Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 108000,\n        'ETH': 2100,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    let basePrice;\n    if (config.tradingMode === \"StablecoinSwap\") {\n        // For stablecoin swap mode: Crypto1/Crypto2 = Current market Price\n        // This shows how many units of Crypto2 equals 1 unit of Crypto1\n        basePrice = crypto1USDPrice / crypto2USDPrice;\n        console.log(\"\\uD83D\\uDCCA StablecoinSwap price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    } else {\n        // Simple Spot mode: Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n        basePrice = crypto1USDPrice / crypto2USDPrice;\n        console.log(\"\\uD83D\\uDCCA SimpleSpot price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    }\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    return finalPrice;\n};\n// Duplicate TradingAction type removed - using the one defined above\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\n// Initial state with default balances (will be updated by global balances later)\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst GLOBAL_BALANCE_KEY = 'pluto_global_balances';\nconst BOT_STATUS_KEY = 'pluto_bot_status';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\n// Global balance persistence functions\nconst saveGlobalBalances = (crypto1Balance, crypto2Balance, stablecoinBalance)=>{\n    try {\n        if (true) {\n            const balances = {\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(GLOBAL_BALANCE_KEY, JSON.stringify(balances));\n            console.log('💰 Global balances saved:', balances);\n        }\n    } catch (error) {\n        console.error('Failed to save global balances:', error);\n    }\n};\nconst loadGlobalBalances = ()=>{\n    try {\n        if (true) {\n            const savedBalances = localStorage.getItem(GLOBAL_BALANCE_KEY);\n            if (savedBalances) {\n                const parsed = JSON.parse(savedBalances);\n                console.log('💰 Global balances loaded:', parsed);\n                return {\n                    crypto1Balance: parsed.crypto1Balance || 10,\n                    crypto2Balance: parsed.crypto2Balance || 100000,\n                    stablecoinBalance: parsed.stablecoinBalance || 0\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load global balances:', error);\n    }\n    return {\n        crypto1Balance: 10,\n        crypto2Balance: 100000,\n        stablecoinBalance: 0\n    };\n};\nconst saveBotStatus = (status)=>{\n    if (true) {\n        try {\n            localStorage.setItem(BOT_STATUS_KEY, status);\n        } catch (error) {\n            console.error('Failed to save bot status to localStorage:', error);\n        }\n    }\n};\nconst loadBotStatus = ()=>{\n    if (true) {\n        try {\n            const saved = localStorage.getItem(BOT_STATUS_KEY);\n            if (saved && (saved === 'Stopped' || saved === 'WarmingUp' || saved === 'Running')) {\n                return saved;\n            }\n        } catch (error) {\n            console.error('Failed to load bot status from localStorage:', error);\n        }\n    }\n    // Return default status if loading fails\n    return 'Stopped';\n};\n// Create initial state with global balances - called after loadGlobalBalances is defined\nconst createInitialTradingState = ()=>{\n    const globalBalances = loadGlobalBalances();\n    return {\n        config: initialBaseConfig,\n        targetPriceRows: [],\n        orderHistory: [],\n        appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n        currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n        botSystemStatus: 'Stopped',\n        crypto1Balance: globalBalances.crypto1Balance,\n        crypto2Balance: globalBalances.crypto2Balance,\n        stablecoinBalance: globalBalances.stablecoinBalance,\n        backendStatus: 'unknown'\n    };\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2\n            };\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            saveBotStatus('WarmingUp');\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            saveBotStatus('Running');\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            saveBotStatus('Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            saveBotStatus('Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            saveBotStatus('Stopped');\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Toast hook for notifications - temporarily disabled\n    // const { toast } = useToast();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        console.log('🔄 TradingContext: Initializing state...');\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with fresh state but keeping global balances');\n                // Clear the URL parameter to avoid confusion - use setTimeout to avoid render issues\n                setTimeout(()=>{\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }, 0);\n                return createInitialTradingState(); // Use fresh state with current global balances\n            }\n        }\n        // Try to get session manager, but handle case where it might not be ready yet\n        let sessionManager;\n        let currentSessionId;\n        try {\n            sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            currentSessionId = sessionManager.getCurrentSessionId();\n            console.log('🔄 TradingContext: SessionManager available, current session:', currentSessionId);\n        } catch (error) {\n            console.log('🔄 TradingContext: SessionManager not ready yet, falling back to localStorage');\n            sessionManager = null;\n            currentSessionId = null;\n        }\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 No current session - starting with fresh state but keeping global balances');\n            return createInitialTradingState(); // Use fresh state with current global balances\n        }\n        // If we have a session and session manager is available, try to load it\n        if (sessionManager && currentSessionId) {\n            try {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession) {\n                    const savedBotStatus = loadBotStatus();\n                    console.log('🔄 Restoring session from session manager:', currentSession.name);\n                    console.log('🔄 Bot status restored to:', savedBotStatus);\n                    return {\n                        ...initialTradingState,\n                        config: currentSession.config,\n                        targetPriceRows: currentSession.targetPriceRows,\n                        orderHistory: currentSession.orderHistory,\n                        currentMarketPrice: currentSession.currentMarketPrice,\n                        crypto1Balance: currentSession.crypto1Balance,\n                        crypto2Balance: currentSession.crypto2Balance,\n                        stablecoinBalance: currentSession.stablecoinBalance,\n                        botSystemStatus: savedBotStatus // Restore previous bot status to maintain continuity\n                    };\n                }\n            } catch (error) {\n                console.error('🔄 Error loading session from session manager:', error);\n            }\n        }\n        // Fallback to localStorage if session manager fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            const savedBotStatus = loadBotStatus();\n            console.log('🔄 Restoring from localStorage backup');\n            console.log('🔄 Bot status restored to:', savedBotStatus);\n            const globalBalances = loadGlobalBalances();\n            return {\n                ...createInitialTradingState(),\n                ...savedState,\n                // Always use current global balances\n                crypto1Balance: globalBalances.crypto1Balance,\n                crypto2Balance: globalBalances.crypto2Balance,\n                stablecoinBalance: globalBalances.stablecoinBalance,\n                // Restore previous bot status to maintain continuity\n                botSystemStatus: savedBotStatus\n            };\n        }\n        console.log('⚠️ No session data found, starting fresh with global balances');\n        return createInitialTradingState();\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Telegram error notification function - defined early to avoid initialization issues\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage, context)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping error notification');\n                    return;\n                }\n                // Format error message with emoji and structure\n                let message = \"⚠️ <b>Error Alert</b>\\n\\n\";\n                message += \"<b>Type:</b> \".concat(errorType, \"\\n\");\n                message += \"<b>Error:</b> \".concat(errorMessage, \"\\n\");\n                if (context) {\n                    message += \"<b>Context:</b> \".concat(context, \"\\n\");\n                }\n                if (state.config.crypto1 && state.config.crypto2) {\n                    message += \"<b>Trading Pair:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"\\n\");\n                }\n                message += \"<b>Time:</b> \".concat(new Date().toLocaleString(), \"\\n\");\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram error notification:', response.statusText);\n                } else {\n                    console.log('✅ Telegram error notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                // Send error notification for price fetching failures\n                sendTelegramErrorNotification('Price Fetch Error', \"Failed to fetch market price: \".concat(error instanceof Error ? error.message : 'Unknown error'), \"Trading pair: \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2));\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch,\n        sendTelegramErrorNotification\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    // Audio queue system for handling multiple simultaneous sound requests\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const isPlayingAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingAudioRef.current || audioQueueRef.current.length === 0 || !audioRef.current) return;\n            isPlayingAudioRef.current = true;\n            const { soundKey, sessionId } = audioQueueRef.current.shift();\n            try {\n                // Get session-specific alarm settings\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                const targetSessionId = sessionId || sessionManager.getCurrentSessionId();\n                const targetSession = targetSessionId ? sessionManager.loadSession(targetSessionId) : null;\n                const alarmSettings = (targetSession === null || targetSession === void 0 ? void 0 : targetSession.alarmSettings) || state.appSettings;\n                if (!alarmSettings.soundAlertsEnabled) {\n                    return; // Skip if sound is disabled\n                }\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    // Validate and fix sound path if needed\n                    let validSoundPath = soundPath;\n                    // Fix old /sounds/ paths to /ringtones/\n                    if (soundPath.startsWith('/sounds/')) {\n                        validSoundPath = soundPath.replace('/sounds/', '/ringtones/');\n                        console.warn(\"Fixed deprecated sound path: \".concat(soundPath, \" -> \").concat(validSoundPath));\n                    }\n                    // Fallback to default sound if path doesn't exist in ringtones\n                    if (validSoundPath.startsWith('/ringtones/') && !validSoundPath.includes('data:audio')) {\n                        const knownRingtones = [\n                            'cheer.wav',\n                            'chest1.wav',\n                            'chime2.wav',\n                            'bells.wav',\n                            'bird1.wav',\n                            'bird7.wav',\n                            'sparrow1.wav',\n                            'space_bells4a.wav',\n                            'sanctuary1.wav',\n                            'marble1.wav',\n                            'foundry2.wav',\n                            'G_hades_curse.wav',\n                            'G_hades_demat.wav',\n                            'G_hades_sanctify.wav',\n                            'dark2.wav',\n                            'Satyr_atk4.wav',\n                            'S_mon1.mp3',\n                            'S_mon2.mp3',\n                            'wolf4.wav',\n                            'goatherd1.wav',\n                            'tax3.wav',\n                            'G_hades_mat.wav'\n                        ];\n                        const filename = validSoundPath.split('/').pop();\n                        if (filename && !knownRingtones.includes(filename)) {\n                            validSoundPath = '/ringtones/cheer.wav'; // Default fallback\n                            console.warn(\"Unknown ringtone file: \".concat(soundPath, \", using default: \").concat(validSoundPath));\n                        }\n                    }\n                    // Stop any currently playing audio to prevent conflicts\n                    audioRef.current.pause();\n                    audioRef.current.currentTime = 0;\n                    audioRef.current.src = validSoundPath;\n                    // Wait for audio to load before playing\n                    await new Promise({\n                        \"TradingProvider.useCallback[processAudioQueue]\": (resolve, reject)=>{\n                            var _audioRef_current, _audioRef_current1, _audioRef_current2;\n                            const onCanPlay = {\n                                \"TradingProvider.useCallback[processAudioQueue].onCanPlay\": ()=>{\n                                    var _audioRef_current, _audioRef_current1;\n                                    (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.removeEventListener('canplaythrough', onCanPlay);\n                                    (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.removeEventListener('error', onError);\n                                    resolve();\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue].onCanPlay\"];\n                            const onError = {\n                                \"TradingProvider.useCallback[processAudioQueue].onError\": (e)=>{\n                                    var _audioRef_current, _audioRef_current1;\n                                    (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.removeEventListener('canplaythrough', onCanPlay);\n                                    (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.removeEventListener('error', onError);\n                                    reject(e);\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue].onError\"];\n                            (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.addEventListener('canplaythrough', onCanPlay, {\n                                once: true\n                            });\n                            (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.addEventListener('error', onError, {\n                                once: true\n                            });\n                            (_audioRef_current2 = audioRef.current) === null || _audioRef_current2 === void 0 ? void 0 : _audioRef_current2.load();\n                        }\n                    }[\"TradingProvider.useCallback[processAudioQueue]\"]);\n                    // Play the sound with timeout\n                    await audioRef.current.play();\n                    // Set timeout to stop audio after 2 seconds\n                    setTimeout({\n                        \"TradingProvider.useCallback[processAudioQueue]\": ()=>{\n                            if (audioRef.current) {\n                                audioRef.current.pause();\n                                audioRef.current.currentTime = 0;\n                            }\n                        }\n                    }[\"TradingProvider.useCallback[processAudioQueue]\"], 2000);\n                }\n            } catch (error) {\n                // Handle audio errors gracefully\n                if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('interrupted') || error.message.includes('play() request'))) {\n                    console.debug('Audio play interrupted (non-critical):', error.message);\n                } else {\n                    console.error(\"Error playing sound:\", error);\n                    // Try fallback sound for critical errors\n                    if (audioRef.current && audioRef.current.src !== '/ringtones/cheer.wav') {\n                        try {\n                            audioRef.current.src = '/ringtones/cheer.wav';\n                            await audioRef.current.play();\n                            setTimeout({\n                                \"TradingProvider.useCallback[processAudioQueue]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0;\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue]\"], 2000);\n                        } catch (fallbackError) {\n                            console.error(\"Fallback sound also failed:\", fallbackError);\n                        }\n                    }\n                }\n            } finally{\n                isPlayingAudioRef.current = false;\n                // Process next item in queue after a short delay\n                setTimeout({\n                    \"TradingProvider.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"TradingProvider.useCallback[processAudioQueue]\"], 150);\n            }\n        }\n    }[\"TradingProvider.useCallback[processAudioQueue]\"], [\n        state.appSettings\n    ]);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey, sessionId)=>{\n            // Add to queue instead of playing immediately\n            audioQueueRef.current.push({\n                soundKey,\n                sessionId\n            });\n            processAudioQueue();\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        processAudioQueue\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                playSound('soundOrderExecution');\n                                // Show toast notification for BUY - temporarily disabled\n                                /*toast({\n                type: 'success',\n                title: '🟢 BUY EXECUTED',\n                description: `Bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`,\n                duration: 2000\n              });*/ // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Send error notification for insufficient balance\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for BUY order\"));\n                                sendTelegramErrorNotification('Insufficient Balance', \"Cannot execute BUY order - insufficient \".concat(config.crypto2, \" balance\"), \"Required: \".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \", Available: \").concat(currentCrypto2Balance.toFixed(2), \" \").concat(config.crypto2));\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss - convert USDT profit to BTC equivalent\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"\\uD83D\\uDCB0 SELL Trade P/L: Crypto2=\".concat(realizedProfit.toFixed(6), \", Crypto1=\").concat(realizedProfitCrypto1.toFixed(6)));\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            playSound('soundOrderExecution');\n                            // Show toast notification for SELL - temporarily disabled\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            /*toast({\n              type: realizedProfit > 0 ? 'success' : realizedProfit < 0 ? 'warning' : 'info',\n              title: '🔴 SELL EXECUTED',\n              description: `Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} | ${profitEmoji} Profit: $${realizedProfit.toFixed(2)}`,\n              duration: 2000\n            });*/ // Send Telegram notification for SELL\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Calculate cost basis for this BUY operation in terms of crypto2\n                                const costBasisCrypto2 = amountCrypto2ToUse;\n                                const costBasisStablecoin = stablecoinObtained;\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        // For SELL step of BUY operation: show cost basis as negative (money spent)\n                                        realizedProfitLossCrypto2: -costBasisCrypto2,\n                                        realizedProfitLossCrypto1: crypto1StablecoinPrice > 0 ? -costBasisCrypto2 / crypto1StablecoinPrice : 0\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        // For BUY step: show the value acquired (positive)\n                                        realizedProfitLossCrypto2: costBasisCrypto2,\n                                        realizedProfitLossCrypto1: crypto1Bought // Amount of crypto1 acquired\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                playSound('soundOrderExecution');\n                                // Show toast notification for Stablecoin BUY - temporarily disabled\n                                /*toast({\n                type: 'success',\n                title: '🟢 BUY EXECUTED (Stablecoin)',\n                description: `Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} via ${config.preferredStablecoin}`,\n                duration: 2000\n              });*/ // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss - convert Crypto2 profit to Crypto1 equivalent\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    // For SELL step: show the stablecoin value received (positive)\n                                    realizedProfitLossCrypto2: stablecoinFromC1Sell / crypto2StablecoinPrice,\n                                    realizedProfitLossCrypto1: amountCrypto1ToSell // Amount of crypto1 sold (negative impact on holdings)\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    // For BUY step: show the net profit/loss from the complete operation\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"\\uD83D\\uDCB0 STABLECOIN SWAP P/L: Crypto2=\".concat(realizedProfitInCrypto2.toFixed(6), \", Crypto1=\").concat(realizedProfitCrypto1.toFixed(6)));\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            playSound('soundOrderExecution');\n                            // Show toast notification for Stablecoin SELL - temporarily disabled\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            /*toast({\n              type: realizedProfitInCrypto2 > 0 ? 'success' : realizedProfitInCrypto2 < 0 ? 'warning' : 'info',\n              title: '🔴 SELL EXECUTED (Stablecoin)',\n              description: `Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} | ${profitEmoji} Profit: $${realizedProfitInCrypto2.toFixed(2)}`,\n              duration: 2000\n            });*/ // Send Telegram notification for Stablecoin SELL\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        // Calculate unrealized profit/loss based on current market price vs original cost\n                        if (state.config.tradingMode === \"StablecoinSwap\") {\n                            // For stablecoin swap: Calculate profit based on current market price vs target price\n                            // If current price > target price = profit (positive income)\n                            // If current price < target price = loss (negative income)\n                            const targetPrice = row.targetPrice || 0;\n                            const priceDifference = currentPrice - targetPrice;\n                            const profitPercentage = targetPrice > 0 ? priceDifference / targetPrice : 0;\n                            // Calculate profit/loss based on the amount held and price difference\n                            const totalUnrealizedProfitInCrypto2 = row.crypto1AmountHeld * priceDifference;\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;\n                            }\n                        } else {\n                            // Simple spot mode logic\n                            const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;\n                            }\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], []);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], []);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config, undefined, {\n                        crypto1: state.crypto1Balance,\n                        crypto2: state.crypto2Balance,\n                        stablecoin: state.stablecoinBalance\n                    }).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Note: No need to stop backend bot during crypto pair change\n                        // Frontend bot system is separate from backend bot system\n                        // The frontend bot status will be reset with the new crypto pair\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config, {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        }).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]).catch({\n                            \"TradingProvider.useEffect\": (error)=>{\n                                console.error('❌ Failed to auto-save session during crypto pair change:', error);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Don't auto-create new session for crypto pair change\n                    // User should manually create session after setting target prices\n                    console.log('🔄 Crypto pair changed, session cleared. Set target prices and start bot to create new session.');\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config, {\n                                        crypto1: state.crypto1Balance,\n                                        crypto2: state.crypto2Balance,\n                                        stablecoin: state.stablecoinBalance\n                                    }).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]).catch({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (error)=>{\n                                            console.error('❌ Failed to create offline backup session:', error);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        console.log(\"Network Disconnected: Bot stopped and session saved. Trading paused until connection restored.\");\n                    } else if (isOnline && !isInitial) {\n                        console.log(\"Network Reconnected: Connection restored. You can resume trading.\");\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Session is active if it's the current session\n                            );\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Save global balances whenever they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveGlobalBalances(state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Initialize with global balances on mount (only if using default balances)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const isDefaultBalances = state.crypto1Balance === 10 && state.crypto2Balance === 100000 && state.stablecoinBalance === 0;\n            if (isDefaultBalances) {\n                const globalBalances = loadGlobalBalances();\n                if (globalBalances.crypto1Balance !== 10 || globalBalances.crypto2Balance !== 100000 || globalBalances.stablecoinBalance !== 0) {\n                    dispatch({\n                        type: 'UPDATE_BALANCES',\n                        payload: {\n                            crypto1: globalBalances.crypto1Balance,\n                            crypto2: globalBalances.crypto2Balance,\n                            stablecoin: globalBalances.stablecoinBalance\n                        }\n                    });\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Only run once on mount\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        }).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // New session is active when created\n                                );\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]).catch({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (error)=>{\n                                console.error('Failed to create new session:', error);\n                                sendTelegramErrorNotification('Session Creation Error', 'Failed to create new trading session', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    sendTelegramErrorNotification('Session Save Error', 'Cannot save session - no trading pair selected', 'Please select both crypto1 and crypto2 before saving');\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Current session is active\n                );\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                sendTelegramErrorNotification('Session Save Error', 'Unexpected error while saving session', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state,\n        sendTelegramErrorNotification\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1990,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"6DQ7CJRNGUw3aB37LeA29hbbLMc=\");\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9UcmFkaW5nQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRXFHO0FBRWlCO0FBQ2xGO0FBRXBDLHdDQUF3QztBQUN4QyxNQUFNVywyQkFBMkI7SUFBQztJQUFRO0lBQVE7Q0FBTTtBQUNqQjtBQUNnQjtBQUNnQztBQXFEdkYsMERBQTBEO0FBQzFELE1BQU1NLDhCQUE4QixDQUFDQztJQUNuQyw0Q0FBNEM7SUFDNUMsSUFBSSxDQUFDQSxPQUFPQyxPQUFPLElBQUksQ0FBQ0QsT0FBT0UsT0FBTyxFQUFFO1FBQ3RDLE9BQU87SUFDVDtJQUNBLHlDQUF5QztJQUN6QyxPQUFPQyw2QkFBNkJIO0FBQ3RDO0FBRUEsaUVBQWlFO0FBQ2pFLE1BQU1JLHdCQUF3QixPQUFPSjtJQUNuQyxJQUFJO1FBQ0YsNENBQTRDO1FBQzVDLElBQUksQ0FBQ0EsT0FBT0MsT0FBTyxJQUFJLENBQUNELE9BQU9FLE9BQU8sRUFBRTtZQUN0QyxPQUFPO1FBQ1Q7UUFFQSxnRkFBZ0Y7UUFDaEYsSUFBSUYsT0FBT0ssV0FBVyxLQUFLLG9CQUFvQkwsT0FBT00sbUJBQW1CLEVBQUU7WUFDekUsSUFBSTtnQkFDRiw4REFBOEQ7Z0JBQzlELE1BQU1DLHlCQUF5QixNQUFNQywwQkFBMEJSLE9BQU9DLE9BQU8sRUFBRUQsT0FBT00sbUJBQW1CO2dCQUN6RyxNQUFNRyx5QkFBeUIsTUFBTUQsMEJBQTBCUixPQUFPRSxPQUFPLEVBQUVGLE9BQU9NLG1CQUFtQjtnQkFFekcsSUFBSUMseUJBQXlCLEtBQUtFLHlCQUF5QixHQUFHO29CQUM1RCxpREFBaUQ7b0JBQ2pELE1BQU1DLFFBQVFILHlCQUF5QkU7b0JBQ3ZDRSxRQUFRQyxHQUFHLENBQUMsOEJBQTZEWixPQUEvQkEsT0FBT00sbUJBQW1CLEVBQUMsTUFBc0JOLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBdUJTLE9BQXBCVixPQUFPRSxPQUFPLEVBQUMsT0FBc0IsT0FBakJRLE1BQU1HLE9BQU8sQ0FBQztvQkFDN0hGLFFBQVFDLEdBQUcsQ0FBQyxNQUF3QlosT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUFtQ00sT0FBaENQLE9BQU9NLG1CQUFtQixFQUFDLE9BQTRCLE9BQXZCQztvQkFDcEVJLFFBQVFDLEdBQUcsQ0FBQyxNQUF3QlosT0FBbEJBLE9BQU9FLE9BQU8sRUFBQyxLQUFtQ08sT0FBaENULE9BQU9NLG1CQUFtQixFQUFDLE9BQTRCLE9BQXZCRztvQkFDcEUsT0FBT0M7Z0JBQ1Q7WUFDRixFQUFFLE9BQU9JLGlCQUFpQjtnQkFDeEJILFFBQVFJLElBQUksQ0FBQyx1RUFBdUVEO1lBQ3RGO1FBQ0Y7UUFFQSw0REFBNEQ7UUFDNUQsTUFBTUUsU0FBUyxHQUFvQmhCLE9BQWpCQSxPQUFPQyxPQUFPLEVBQWtCLE9BQWZELE9BQU9FLE9BQU8sRUFBR2UsV0FBVztRQUUvRCx3QkFBd0I7UUFDeEIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxzREFBNkQsT0FBUEg7WUFDbkYsSUFBSUUsU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU1DLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtnQkFDaEMsTUFBTUMsUUFBUUMsV0FBV0gsS0FBS0UsS0FBSztnQkFDbkMsSUFBSUEsUUFBUSxHQUFHO29CQUNiWixRQUFRQyxHQUFHLENBQUMsaUNBQW1EWixPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQXVCc0IsT0FBcEJ2QixPQUFPRSxPQUFPLEVBQUMsT0FBVyxPQUFOcUI7b0JBQ25GLE9BQU9BO2dCQUNUO1lBQ0Y7UUFDRixFQUFFLE9BQU9FLGNBQWM7WUFDckJkLFFBQVFJLElBQUksQ0FBQyw2Q0FBNkNVO1FBQzVEO1FBRUEscURBQXFEO1FBQ3JELElBQUk7WUFDRixNQUFNQyxZQUFZQyxlQUFlM0IsT0FBT0MsT0FBTztZQUMvQyxNQUFNMkIsWUFBWUQsZUFBZTNCLE9BQU9FLE9BQU87WUFFL0MsZ0RBQWdEO1lBQ2hELElBQUl3QixhQUFhRSxXQUFXO2dCQUMxQixNQUFNVixXQUFXLE1BQU1DLE1BQU0scURBQWdGUyxPQUEzQkYsV0FBVSxtQkFBMkIsT0FBVkU7Z0JBQzdHLElBQUlWLFNBQVNFLEVBQUUsRUFBRTt3QkFFREM7b0JBRGQsTUFBTUEsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO29CQUNoQyxNQUFNQyxTQUFRRixrQkFBQUEsSUFBSSxDQUFDSyxVQUFVLGNBQWZMLHNDQUFBQSxlQUFpQixDQUFDTyxVQUFVO29CQUMxQyxJQUFJTCxRQUFRLEdBQUc7d0JBQ2JaLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBcURaLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBdUJzQixPQUFwQnZCLE9BQU9FLE9BQU8sRUFBQyxPQUFXLE9BQU5xQjt3QkFDckYsT0FBT0E7b0JBQ1Q7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT00sWUFBWTtZQUNuQmxCLFFBQVFJLElBQUksQ0FBQyw2Q0FBNkNjO1FBQzVEO1FBRUEsK0JBQStCO1FBQy9CLE1BQU1DLFlBQVkzQiw2QkFBNkJIO1FBQy9DVyxRQUFRQyxHQUFHLENBQUMsd0JBQTBDWixPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQXVCNkIsT0FBcEI5QixPQUFPRSxPQUFPLEVBQUMsT0FBZSxPQUFWNEI7UUFDMUUsT0FBT0E7SUFFVCxFQUFFLE9BQU9DLE9BQU87UUFDZHBCLFFBQVFvQixLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPNUIsNkJBQTZCSDtJQUN0QztBQUNGO0FBRUEseURBQXlEO0FBQ3pELE1BQU0yQixpQkFBaUIsQ0FBQ1g7SUFDdEIsTUFBTWdCLFVBQXFDO1FBQ3pDLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsU0FBUztRQUNULFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLFFBQVE7UUFDUixRQUFRO1FBQ1IsUUFBUTtRQUNSLE9BQU87SUFDVDtJQUNBLE9BQU9BLE9BQU8sQ0FBQ2hCLE9BQU9DLFdBQVcsR0FBRyxJQUFJO0FBQzFDO0FBRUEsd0VBQXdFO0FBQ3hFLE1BQU1ULDRCQUE0QixPQUFPeUIsUUFBZ0JDO0lBQ3ZELElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsSUFBSUQsT0FBT2hCLFdBQVcsT0FBT2lCLFdBQVdqQixXQUFXLElBQUksT0FBTztRQUU5RCx3Q0FBd0M7UUFDeEMsTUFBTWtCLGdCQUFnQixHQUEwQkQsT0FBdkJELE9BQU9oQixXQUFXLElBQThCLE9BQXpCaUIsV0FBV2pCLFdBQVc7UUFDdEUsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxzREFBb0UsT0FBZGdCO1lBQ25GLElBQUlqQixTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQyxNQUFNQyxRQUFRQyxXQUFXSCxLQUFLRSxLQUFLO2dCQUNuQyxJQUFJQSxRQUFRLEdBQUc7b0JBQ2JaLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBNkNzQixPQUFWRCxRQUFPLEtBQW1CVixPQUFoQlcsWUFBVyxPQUFXLE9BQU5YO29CQUN6RSxPQUFPQTtnQkFDVDtZQUNGO1FBQ0YsRUFBRSxPQUFPRSxjQUFjO1lBQ3JCZCxRQUFRSSxJQUFJLENBQUMsMEJBQXdDLE9BQWRvQixlQUFjO1FBQ3ZEO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1DLFdBQVdULGVBQWVNO1FBQ2hDLE1BQU1JLGVBQWVWLGVBQWVPO1FBRXBDLElBQUlFLFlBQVlDLGNBQWM7WUFDNUIsd0JBQXdCO1lBQ3hCLE1BQU1uQixXQUFXLE1BQU1DLE1BQU0scURBQStFa0IsT0FBMUJELFVBQVMsbUJBQThCLE9BQWJDO1lBQzVHLElBQUluQixTQUFTRSxFQUFFLEVBQUU7b0JBRUZDO2dCQURiLE1BQU1BLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtnQkFDaEMsTUFBTWdCLFFBQU9qQixpQkFBQUEsSUFBSSxDQUFDZSxTQUFTLGNBQWRmLHFDQUFBQSxjQUFnQixDQUFDZ0IsYUFBYTtnQkFDM0MsSUFBSUMsT0FBTyxHQUFHO29CQUNaM0IsUUFBUUMsR0FBRyxDQUFDLHFDQUErQ3NCLE9BQVZELFFBQU8sS0FBbUJLLE9BQWhCSixZQUFXLE9BQVUsT0FBTEk7b0JBQzNFLE9BQU9BO2dCQUNUO1lBQ0Y7UUFDRjtRQUVBLDJDQUEyQztRQUMzQyxNQUFNQyxpQkFBaUJDLFlBQVlQO1FBQ25DLE1BQU1RLHFCQUFxQkQsWUFBWU47UUFDdkMsTUFBTUksT0FBT0MsaUJBQWlCRTtRQUU5QjlCLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBMENzQixPQUFWRCxRQUFPLEtBQW1CSyxPQUFoQkosWUFBVyxPQUFVLE9BQUxJLE1BQUs7UUFDM0UsT0FBT0E7SUFFVCxFQUFFLE9BQU9QLE9BQU87UUFDZHBCLFFBQVFvQixLQUFLLENBQUMsNENBQTRDQTtRQUMxRCxpQkFBaUI7UUFDakIsTUFBTVEsaUJBQWlCQyxZQUFZUDtRQUNuQyxNQUFNUSxxQkFBcUJELFlBQVlOO1FBQ3ZDLE9BQU9LLGlCQUFpQkU7SUFDMUI7QUFDRjtBQUVBLGlGQUFpRjtBQUNqRixNQUFNRCxjQUFjLENBQUNQO0lBQ25CLE1BQU1TLFlBQXVDO1FBQzNDLHlCQUF5QjtRQUN6QixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsUUFBUTtRQUNSLFFBQVE7UUFDUixTQUFTO1FBQ1QsT0FBTztRQUNQLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsY0FBYztRQUNkLE9BQU87UUFDUCxRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBQ1IsT0FBTztRQUNQLFNBQVM7UUFDVCxTQUFTO1FBQ1QsT0FBTztRQUNQLE9BQU87UUFFUCxzQkFBc0I7UUFDdEIsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLFFBQVE7UUFDUixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsdUJBQXVCO1FBQ3ZCLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBRVIscUJBQXFCO1FBQ3JCLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsMkJBQTJCO1FBQzNCLFNBQVM7UUFDVCxRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUVQLGdCQUFnQjtRQUNoQixPQUFPO1FBQ1AsUUFBUTtRQUVSLGFBQWE7UUFDYixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCxlQUFlO1FBQ2YsT0FBTztRQUNQLFFBQVE7UUFDUixTQUFTO1FBQ1QsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCxxQkFBcUI7UUFDckIsUUFBUTtRQUNSLE9BQU87UUFDUCxPQUFPO1FBQ1AsUUFBUTtRQUNSLE9BQU87UUFDUCxTQUFTO1FBQ1QsT0FBTztRQUNQLFFBQVE7UUFDUixNQUFNO1FBQ04sT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBQ1IsUUFBUTtRQUNSLE9BQU87UUFDUCxRQUFRO1FBRVIsY0FBYztRQUNkLFFBQVE7UUFDUixRQUFRO1FBQ1IsU0FBUztRQUNULFFBQVE7UUFDUixPQUFPO0lBQ1Q7SUFDQSxPQUFPQSxTQUFTLENBQUNULE9BQU9oQixXQUFXLEdBQUcsSUFBSTtBQUM1QztBQUVBLHVGQUF1RjtBQUN2RixNQUFNZCwrQkFBK0IsQ0FBQ0g7SUFDcEMsTUFBTTJDLGtCQUFrQkgsWUFBWXhDLE9BQU9DLE9BQU87SUFDbEQsTUFBTTJDLGtCQUFrQkosWUFBWXhDLE9BQU9FLE9BQU87SUFFbEQsSUFBSTJDO0lBRUosSUFBSTdDLE9BQU9LLFdBQVcsS0FBSyxrQkFBa0I7UUFDM0MsbUVBQW1FO1FBQ25FLGdFQUFnRTtRQUNoRXdDLFlBQVlGLGtCQUFrQkM7UUFFOUJqQyxRQUFRQyxHQUFHLENBQUMsa0RBQTREK0IsT0FBcEIzQyxPQUFPQyxPQUFPLEVBQUMsT0FBMkJELE9BQXRCMkMsaUJBQWdCLFFBQTBCQyxPQUFwQjVDLE9BQU9FLE9BQU8sRUFBQyxPQUEyQjJDLE9BQXRCRCxpQkFBZ0IsUUFBMkIsT0FBckJDLFVBQVVoQyxPQUFPLENBQUM7SUFDNUosT0FBTztRQUNMLHVGQUF1RjtRQUN2RmdDLFlBQVlGLGtCQUFrQkM7UUFFOUJqQyxRQUFRQyxHQUFHLENBQUMsOENBQXdEK0IsT0FBcEIzQyxPQUFPQyxPQUFPLEVBQUMsT0FBMkJELE9BQXRCMkMsaUJBQWdCLFFBQTBCQyxPQUFwQjVDLE9BQU9FLE9BQU8sRUFBQyxPQUEyQjJDLE9BQXRCRCxpQkFBZ0IsUUFBMkIsT0FBckJDLFVBQVVoQyxPQUFPLENBQUM7SUFDeEo7SUFFQSwrQkFBK0I7SUFDL0IsTUFBTWlDLGNBQWMsQ0FBQ0MsS0FBS0MsTUFBTSxLQUFLLEdBQUUsSUFBSyxNQUFNLE1BQU07SUFDeEQsTUFBTUMsYUFBYUosWUFBYSxLQUFJQyxXQUFVO0lBRTlDLE9BQU9HO0FBQ1Q7QUFFQSxxRUFBcUU7QUFFckUsTUFBTUMsb0JBQW1DO0lBQ3ZDN0MsYUFBYTtJQUNiSixTQUFTO0lBQ1RDLFNBQVM7SUFDVGlELFNBQVM7SUFDVEMsWUFBWTtJQUNaQyxXQUFXO0lBQ1hDLGlCQUFpQjtJQUNqQkMsMkJBQTJCO0lBQzNCQywyQkFBMkI7SUFDM0JsRCxxQkFBcUJoQiw2REFBcUIsQ0FBQyxFQUFFO0FBQy9DO0FBRUEsaUZBQWlGO0FBQ2pGLE1BQU1tRSxzQkFBb0M7SUFDeEN6RCxRQUFRa0Q7SUFDUlEsaUJBQWlCLEVBQUU7SUFDbkJDLGNBQWMsRUFBRTtJQUNoQkMsYUFBYXZFLDREQUFvQkE7SUFDakN3RSxvQkFBb0I5RCw0QkFBNEJtRDtJQUNoRFksaUJBQWlCO0lBQ2pCQyxnQkFBZ0I7SUFDaEJDLGdCQUFnQjtJQUNoQkMsbUJBQW1CO0lBQ25CQyxlQUFlO0FBQ2pCO0FBRUEsTUFBTUMsZ0NBQWdDLElBQUlDO0FBRTFDLHFDQUFxQztBQUNyQyxNQUFNQyxjQUFjO0FBQ3BCLE1BQU1DLHFCQUFxQjtBQUMzQixNQUFNQyxpQkFBaUI7QUFFdkIsTUFBTUMsMEJBQTBCLENBQUNDO0lBQy9CLElBQUk7UUFDRixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsY0FBYztnQkFDbEIxRSxRQUFReUUsTUFBTXpFLE1BQU07Z0JBQ3BCMEQsaUJBQWlCZSxNQUFNZixlQUFlO2dCQUN0Q0MsY0FBY2MsTUFBTWQsWUFBWTtnQkFDaENDLGFBQWFhLE1BQU1iLFdBQVc7Z0JBQzlCQyxvQkFBb0JZLE1BQU1aLGtCQUFrQjtnQkFDNUNFLGdCQUFnQlUsTUFBTVYsY0FBYztnQkFDcENDLGdCQUFnQlMsTUFBTVQsY0FBYztnQkFDcENDLG1CQUFtQlEsTUFBTVIsaUJBQWlCO2dCQUMxQ0gsaUJBQWlCVyxNQUFNWCxlQUFlO2dCQUN0Q2EsV0FBV0MsS0FBS0MsR0FBRztZQUNyQjtZQUNBQyxhQUFhQyxPQUFPLENBQUNWLGFBQWFXLEtBQUtDLFNBQVMsQ0FBQ1A7UUFDbkQ7SUFDRixFQUFFLE9BQU8zQyxPQUFPO1FBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLHlDQUF5Q0E7SUFDekQ7QUFDRjtBQUVBLE1BQU1tRCw0QkFBNEI7SUFDaEMsSUFBSTtRQUNGLElBQUksSUFBNkIsRUFBRTtZQUNqQyxNQUFNQyxhQUFhTCxhQUFhTSxPQUFPLENBQUNmO1lBQ3hDLElBQUljLFlBQVk7Z0JBQ2QsTUFBTUUsU0FBU0wsS0FBS00sS0FBSyxDQUFDSDtnQkFDMUIsMkNBQTJDO2dCQUMzQyxJQUFJRSxPQUFPVixTQUFTLElBQUksS0FBTUUsR0FBRyxLQUFLUSxPQUFPVixTQUFTLEdBQUksS0FBSyxLQUFLLEtBQUssTUFBTTtvQkFDN0UsT0FBT1U7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0YsRUFBRSxPQUFPdEQsT0FBTztRQUNkcEIsUUFBUW9CLEtBQUssQ0FBQywyQ0FBMkNBO0lBQzNEO0lBQ0EsT0FBTztBQUNUO0FBRUEsdUNBQXVDO0FBQ3ZDLE1BQU13RCxxQkFBcUIsQ0FBQ3hCLGdCQUF3QkMsZ0JBQXdCQztJQUMxRSxJQUFJO1FBQ0YsSUFBSSxJQUE2QixFQUFFO1lBQ2pDLE1BQU11QixXQUFXO2dCQUNmekI7Z0JBQ0FDO2dCQUNBQztnQkFDQVUsV0FBV0MsS0FBS0MsR0FBRztZQUNyQjtZQUNBQyxhQUFhQyxPQUFPLENBQUNULG9CQUFvQlUsS0FBS0MsU0FBUyxDQUFDTztZQUN4RDdFLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkI0RTtRQUMzQztJQUNGLEVBQUUsT0FBT3pELE9BQU87UUFDZHBCLFFBQVFvQixLQUFLLENBQUMsbUNBQW1DQTtJQUNuRDtBQUNGO0FBRUEsTUFBTTBELHFCQUFxQjtJQUN6QixJQUFJO1FBQ0YsSUFBSSxJQUE2QixFQUFFO1lBQ2pDLE1BQU1DLGdCQUFnQlosYUFBYU0sT0FBTyxDQUFDZDtZQUMzQyxJQUFJb0IsZUFBZTtnQkFDakIsTUFBTUwsU0FBU0wsS0FBS00sS0FBSyxDQUFDSTtnQkFDMUIvRSxRQUFRQyxHQUFHLENBQUMsOEJBQThCeUU7Z0JBQzFDLE9BQU87b0JBQ0x0QixnQkFBZ0JzQixPQUFPdEIsY0FBYyxJQUFJO29CQUN6Q0MsZ0JBQWdCcUIsT0FBT3JCLGNBQWMsSUFBSTtvQkFDekNDLG1CQUFtQm9CLE9BQU9wQixpQkFBaUIsSUFBSTtnQkFDakQ7WUFDRjtRQUNGO0lBQ0YsRUFBRSxPQUFPbEMsT0FBTztRQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyxtQ0FBbUNBO0lBQ25EO0lBQ0EsT0FBTztRQUNMZ0MsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtJQUNyQjtBQUNGO0FBRUEsTUFBTTBCLGdCQUFnQixDQUFDQztJQUNyQixJQUFJLElBQTZCLEVBQUU7UUFDakMsSUFBSTtZQUNGZCxhQUFhQyxPQUFPLENBQUNSLGdCQUFnQnFCO1FBQ3ZDLEVBQUUsT0FBTzdELE9BQU87WUFDZHBCLFFBQVFvQixLQUFLLENBQUMsOENBQThDQTtRQUM5RDtJQUNGO0FBQ0Y7QUFFQSxNQUFNOEQsZ0JBQWdCO0lBQ3BCLElBQUksSUFBNkIsRUFBRTtRQUNqQyxJQUFJO1lBQ0YsTUFBTUMsUUFBUWhCLGFBQWFNLE9BQU8sQ0FBQ2I7WUFDbkMsSUFBSXVCLFNBQVVBLENBQUFBLFVBQVUsYUFBYUEsVUFBVSxlQUFlQSxVQUFVLFNBQVEsR0FBSTtnQkFDbEYsT0FBT0E7WUFDVDtRQUNGLEVBQUUsT0FBTy9ELE9BQU87WUFDZHBCLFFBQVFvQixLQUFLLENBQUMsZ0RBQWdEQTtRQUNoRTtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLE9BQU87QUFDVDtBQUVBLHlGQUF5RjtBQUN6RixNQUFNZ0UsNEJBQTRCO0lBQ2hDLE1BQU1DLGlCQUFpQlA7SUFDdkIsT0FBTztRQUNMekYsUUFBUWtEO1FBQ1JRLGlCQUFpQixFQUFFO1FBQ25CQyxjQUFjLEVBQUU7UUFDaEJDLGFBQWF2RSw0REFBb0JBO1FBQ2pDd0Usb0JBQW9COUQsNEJBQTRCbUQ7UUFDaERZLGlCQUFpQjtRQUNqQkMsZ0JBQWdCaUMsZUFBZWpDLGNBQWM7UUFDN0NDLGdCQUFnQmdDLGVBQWVoQyxjQUFjO1FBQzdDQyxtQkFBbUIrQixlQUFlL0IsaUJBQWlCO1FBQ25EQyxlQUFlO0lBQ2pCO0FBQ0Y7QUFFQSxNQUFNK0IsaUJBQWlCLENBQUN4QixPQUFxQnlCO0lBQzNDLE9BQVFBLE9BQU9DLElBQUk7UUFDakIsS0FBSztZQUNILE1BQU1DLFlBQVk7Z0JBQUUsR0FBRzNCLE1BQU16RSxNQUFNO2dCQUFFLEdBQUdrRyxPQUFPRyxPQUFPO1lBQUM7WUFDdkQsbUZBQW1GO1lBQ25GLElBQUlILE9BQU9HLE9BQU8sQ0FBQ3BHLE9BQU8sSUFBSWlHLE9BQU9HLE9BQU8sQ0FBQ25HLE9BQU8sRUFBRTtnQkFDcEQsT0FBTztvQkFBRSxHQUFHdUUsS0FBSztvQkFBRXpFLFFBQVFvRztvQkFBV3ZDLG9CQUFvQjlELDRCQUE0QnFHO2dCQUFXO1lBQ25HO1lBQ0EsT0FBTztnQkFBRSxHQUFHM0IsS0FBSztnQkFBRXpFLFFBQVFvRztZQUFVO1FBQ3ZDLEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUczQixLQUFLO2dCQUFFZixpQkFBaUJ3QyxPQUFPRyxPQUFPLENBQUNDLElBQUksQ0FBQyxDQUFDQyxHQUFtQkMsSUFBc0JELEVBQUVFLFdBQVcsR0FBR0QsRUFBRUMsV0FBVyxFQUFFQyxHQUFHLENBQUMsQ0FBQ0MsS0FBcUJDLFFBQW1CO3dCQUFDLEdBQUdELEdBQUc7d0JBQUVFLFNBQVNELFFBQVE7b0JBQUM7WUFBSTtRQUMvTSxLQUFLO1lBQXdCO2dCQUMzQixNQUFNRSxVQUFVO3VCQUFJckMsTUFBTWYsZUFBZTtvQkFBRXdDLE9BQU9HLE9BQU87aUJBQUMsQ0FBQ0MsSUFBSSxDQUFDLENBQUNDLEdBQW1CQyxJQUFzQkQsRUFBRUUsV0FBVyxHQUFHRCxFQUFFQyxXQUFXLEVBQUVDLEdBQUcsQ0FBQyxDQUFDQyxLQUFxQkMsUUFBbUI7d0JBQUMsR0FBR0QsR0FBRzt3QkFBRUUsU0FBU0QsUUFBUTtvQkFBQztnQkFDak4sT0FBTztvQkFBRSxHQUFHbkMsS0FBSztvQkFBRWYsaUJBQWlCb0Q7Z0JBQVE7WUFDOUM7UUFDQSxLQUFLO1lBQTJCO2dCQUM5QixNQUFNQyxjQUFjdEMsTUFBTWYsZUFBZSxDQUFDZ0QsR0FBRyxDQUFDLENBQUNDLE1BQzdDQSxJQUFJSyxFQUFFLEtBQUtkLE9BQU9HLE9BQU8sQ0FBQ1csRUFBRSxHQUFHZCxPQUFPRyxPQUFPLEdBQUdNLEtBQ2hETCxJQUFJLENBQUMsQ0FBQ0MsR0FBbUJDLElBQXNCRCxFQUFFRSxXQUFXLEdBQUdELEVBQUVDLFdBQVcsRUFBRUMsR0FBRyxDQUFDLENBQUNDLEtBQXFCQyxRQUFtQjt3QkFBQyxHQUFHRCxHQUFHO3dCQUFFRSxTQUFTRCxRQUFRO29CQUFDO2dCQUN4SixPQUFPO29CQUFFLEdBQUduQyxLQUFLO29CQUFFZixpQkFBaUJxRDtnQkFBWTtZQUNsRDtRQUNBLEtBQUs7WUFBMkI7Z0JBQzlCLE1BQU1FLGVBQWV4QyxNQUFNZixlQUFlLENBQUN3RCxNQUFNLENBQUMsQ0FBQ1AsTUFBd0JBLElBQUlLLEVBQUUsS0FBS2QsT0FBT0csT0FBTyxFQUFFQyxJQUFJLENBQUMsQ0FBQ0MsR0FBbUJDLElBQXNCRCxFQUFFRSxXQUFXLEdBQUdELEVBQUVDLFdBQVcsRUFBRUMsR0FBRyxDQUFDLENBQUNDLEtBQXFCQyxRQUFtQjt3QkFBQyxHQUFHRCxHQUFHO3dCQUFFRSxTQUFTRCxRQUFRO29CQUFDO2dCQUM1UCxPQUFPO29CQUFFLEdBQUduQyxLQUFLO29CQUFFZixpQkFBaUJ1RDtnQkFBYTtZQUNuRDtRQUNBLEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUd4QyxLQUFLO2dCQUFFZCxjQUFjO29CQUFDdUMsT0FBT0csT0FBTzt1QkFBSzVCLE1BQU1kLFlBQVk7aUJBQUM7WUFBQztRQUMzRSxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHYyxLQUFLO2dCQUFFZCxjQUFjLEVBQUU7WUFBQztRQUN0QyxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHYyxLQUFLO2dCQUFFYixhQUFhO29CQUFFLEdBQUdhLE1BQU1iLFdBQVc7b0JBQUUsR0FBR3NDLE9BQU9HLE9BQU87Z0JBQUM7WUFBRTtRQUM5RSxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHNUIsS0FBSztnQkFBRVosb0JBQW9CcUMsT0FBT0csT0FBTztZQUFDO1FBQ3hELEtBQUs7WUFBMEI7Z0JBQzdCLElBQUk1QixNQUFNWixrQkFBa0IsSUFBSSxHQUFHLE9BQU9ZO2dCQUMxQyw2REFBNkQ7Z0JBQzdELE1BQU0wQyxvQkFBb0IsQ0FBQ3BFLEtBQUtDLE1BQU0sS0FBSyxHQUFFLElBQUssT0FBTyw2QkFBNkI7Z0JBQ3RGLE1BQU1vRSxXQUFXM0MsTUFBTVosa0JBQWtCLEdBQUksS0FBSXNELGlCQUFnQjtnQkFDakUsT0FBTztvQkFBRSxHQUFHMUMsS0FBSztvQkFBRVosb0JBQW9CdUQsV0FBVyxJQUFJQSxXQUFXM0MsTUFBTVosa0JBQWtCO2dCQUFDO1lBQzVGO1FBQ0Esb0NBQW9DO1FBQ3BDLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdZLEtBQUs7Z0JBQ1JWLGdCQUFnQm1DLE9BQU9HLE9BQU8sQ0FBQ3BHLE9BQU87Z0JBQ3RDK0QsZ0JBQWdCa0MsT0FBT0csT0FBTyxDQUFDbkcsT0FBTztZQUN4QztRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUd1RSxLQUFLO2dCQUNSVixnQkFBZ0JtQyxPQUFPRyxPQUFPLENBQUNwRyxPQUFPLEtBQUtvSCxZQUFZbkIsT0FBT0csT0FBTyxDQUFDcEcsT0FBTyxHQUFHd0UsTUFBTVYsY0FBYztnQkFDcEdDLGdCQUFnQmtDLE9BQU9HLE9BQU8sQ0FBQ25HLE9BQU8sS0FBS21ILFlBQVluQixPQUFPRyxPQUFPLENBQUNuRyxPQUFPLEdBQUd1RSxNQUFNVCxjQUFjO2dCQUNwR0MsbUJBQW1CaUMsT0FBT0csT0FBTyxDQUFDbkUsVUFBVSxLQUFLbUYsWUFBWW5CLE9BQU9HLE9BQU8sQ0FBQ25FLFVBQVUsR0FBR3VDLE1BQU1SLGlCQUFpQjtZQUNsSDtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdRLEtBQUs7Z0JBQ1JSLG1CQUFtQmlDLE9BQU9HLE9BQU87WUFDbkM7UUFDRixLQUFLO1lBQ0gsTUFBTWlCLGlCQUFpQjtnQkFBRSxHQUFHN0MsTUFBTXpFLE1BQU07WUFBQztZQUN6QyxPQUFPO2dCQUNMLEdBQUd5RCxtQkFBbUI7Z0JBQ3RCekQsUUFBUXNIO2dCQUNSMUQsYUFBYTtvQkFBRSxHQUFHYSxNQUFNYixXQUFXO2dCQUFDO2dCQUNwQ0Msb0JBQW9COUQsNEJBQTRCdUg7WUFDbEQ7UUFDRixLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHN0MsS0FBSztnQkFBRVAsZUFBZWdDLE9BQU9HLE9BQU87WUFBQztRQUNuRCxLQUFLO1lBQ0gsb0RBQW9EO1lBQ3BEVixjQUFjO1lBQ2QsT0FBTztnQkFDTCxHQUFHbEIsS0FBSztnQkFDUlgsaUJBQWlCO1lBRW5CO1FBQ0YsS0FBSztZQUNINkIsY0FBYztZQUNkLE9BQU87Z0JBQUUsR0FBR2xCLEtBQUs7Z0JBQUVYLGlCQUFpQjtZQUFVO1FBQ2hELEtBQUs7WUFDSDZCLGNBQWM7WUFDZCxPQUFPO2dCQUFFLEdBQUdsQixLQUFLO2dCQUFFWCxpQkFBaUI7WUFBVTtRQUNoRCxLQUFLO1lBQ0gsd0VBQXdFO1lBQ3hFSyw4QkFBOEJvRCxLQUFLO1lBQ25DLDZEQUE2RDtZQUM3RCxNQUFNQyxpQkFBaUI3SCxnRUFBY0EsQ0FBQzhILFdBQVc7WUFDakRELGVBQWVFLG1CQUFtQjtZQUNsQy9CLGNBQWM7WUFDZCxPQUFPO2dCQUNMLEdBQUdsQixLQUFLO2dCQUNSWCxpQkFBaUI7Z0JBQ2pCSixpQkFBaUIsRUFBRTtnQkFDbkJDLGNBQWMsRUFBRTtZQUNsQjtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUdjLEtBQUs7Z0JBQUVmLGlCQUFpQndDLE9BQU9HLE9BQU87WUFBQztRQUNyRCxLQUFLO1lBQ0hWLGNBQWM7WUFDZCxPQUFPO2dCQUNMLEdBQUdsQyxtQkFBbUI7Z0JBQ3RCekQsUUFBUXlFLE1BQU16RSxNQUFNO2dCQUNwQmtFLGVBQWVPLE1BQU1QLGFBQWE7Z0JBQ2xDSixpQkFBaUI7Z0JBQ2pCRCxvQkFBb0I7WUFDdEI7UUFDRjtZQUNFLE9BQU9ZO0lBQ1g7QUFDRjtBQWlCQSxNQUFNa0QsK0JBQWlCNUksb0RBQWFBLENBQWlDc0k7QUFFckUsMENBQTBDO0FBRW5DLE1BQU1PLGtCQUFrQjtRQUFDLEVBQUVDLFFBQVEsRUFBMkI7O0lBQ25FLHNEQUFzRDtJQUN0RCxnQ0FBZ0M7SUFFaEMsdURBQXVEO0lBQ3ZELE1BQU1DLGtCQUFrQjtRQUN0Qm5ILFFBQVFDLEdBQUcsQ0FBQztRQUVaLG9EQUFvRDtRQUNwRCxJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTW1ILFlBQVksSUFBSUMsZ0JBQWdCQyxPQUFPQyxRQUFRLENBQUNDLE1BQU07WUFDNUQsTUFBTUMsZUFBZUwsVUFBVU0sR0FBRyxDQUFDLGtCQUFrQjtZQUVyRCxJQUFJRCxjQUFjO2dCQUNoQnpILFFBQVFDLEdBQUcsQ0FBQztnQkFDWixxRkFBcUY7Z0JBQ3JGMEgsV0FBVztvQkFDVCxNQUFNQyxTQUFTTixPQUFPQyxRQUFRLENBQUNNLFFBQVE7b0JBQ3ZDUCxPQUFPUSxPQUFPLENBQUNDLFlBQVksQ0FBQyxDQUFDLEdBQUcsSUFBSUg7Z0JBQ3RDLEdBQUc7Z0JBQ0gsT0FBT3hDLDZCQUE2QiwrQ0FBK0M7WUFDckY7UUFDRjtRQUVBLDhFQUE4RTtRQUM5RSxJQUFJeUI7UUFDSixJQUFJbUI7UUFFSixJQUFJO1lBQ0ZuQixpQkFBaUI3SCxnRUFBY0EsQ0FBQzhILFdBQVc7WUFDM0NrQixtQkFBbUJuQixlQUFlb0IsbUJBQW1CO1lBQ3JEakksUUFBUUMsR0FBRyxDQUFDLGlFQUFpRStIO1FBQy9FLEVBQUUsT0FBTzVHLE9BQU87WUFDZHBCLFFBQVFDLEdBQUcsQ0FBQztZQUNaNEcsaUJBQWlCO1lBQ2pCbUIsbUJBQW1CO1FBQ3JCO1FBRUEsNERBQTREO1FBQzVELElBQUksQ0FBQ0Esa0JBQWtCO1lBQ3JCaEksUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBT21GLDZCQUE2QiwrQ0FBK0M7UUFDckY7UUFFQSx3RUFBd0U7UUFDeEUsSUFBSXlCLGtCQUFrQm1CLGtCQUFrQjtZQUN0QyxJQUFJO2dCQUNGLE1BQU1FLGlCQUFpQnJCLGVBQWVzQixXQUFXLENBQUNIO2dCQUNsRCxJQUFJRSxnQkFBZ0I7b0JBQ2xCLE1BQU1FLGlCQUFpQmxEO29CQUN2QmxGLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBOENpSSxlQUFlRyxJQUFJO29CQUM3RXJJLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJtSTtvQkFDMUMsT0FBTzt3QkFDTCxHQUFHdEYsbUJBQW1CO3dCQUN0QnpELFFBQVE2SSxlQUFlN0ksTUFBTTt3QkFDN0IwRCxpQkFBaUJtRixlQUFlbkYsZUFBZTt3QkFDL0NDLGNBQWNrRixlQUFlbEYsWUFBWTt3QkFDekNFLG9CQUFvQmdGLGVBQWVoRixrQkFBa0I7d0JBQ3JERSxnQkFBZ0I4RSxlQUFlOUUsY0FBYzt3QkFDN0NDLGdCQUFnQjZFLGVBQWU3RSxjQUFjO3dCQUM3Q0MsbUJBQW1CNEUsZUFBZTVFLGlCQUFpQjt3QkFDbkRILGlCQUFpQmlGLGVBQWUscURBQXFEO29CQUN2RjtnQkFDRjtZQUNGLEVBQUUsT0FBT2hILE9BQU87Z0JBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLGtEQUFrREE7WUFDbEU7UUFDRjtRQUVBLG9EQUFvRDtRQUNwRCxNQUFNb0QsYUFBYUQ7UUFDbkIsSUFBSUMsWUFBWTtZQUNkLE1BQU00RCxpQkFBaUJsRDtZQUN2QmxGLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsOEJBQThCbUk7WUFDMUMsTUFBTS9DLGlCQUFpQlA7WUFDdkIsT0FBTztnQkFDTCxHQUFHTSwyQkFBMkI7Z0JBQzlCLEdBQUdaLFVBQVU7Z0JBQ2IscUNBQXFDO2dCQUNyQ3BCLGdCQUFnQmlDLGVBQWVqQyxjQUFjO2dCQUM3Q0MsZ0JBQWdCZ0MsZUFBZWhDLGNBQWM7Z0JBQzdDQyxtQkFBbUIrQixlQUFlL0IsaUJBQWlCO2dCQUNuRCxxREFBcUQ7Z0JBQ3JESCxpQkFBaUJpRjtZQUNuQjtRQUNGO1FBRUFwSSxRQUFRQyxHQUFHLENBQUM7UUFDWixPQUFPbUY7SUFDVDtJQUVBLE1BQU0sQ0FBQ3RCLE9BQU93RSxTQUFTLEdBQUdoSyxpREFBVUEsQ0FBQ2dILGdCQUFnQjZCO0lBQ3JELE1BQU1vQixXQUFXOUosNkNBQU1BLENBQTBCO0lBQ2pELGdFQUFnRTtJQUVoRSxzRkFBc0Y7SUFDdEYsTUFBTStKLGdDQUFnQ2hLLGtEQUFXQTtzRUFBQyxPQUFPaUssV0FBbUJDLGNBQXNCQztZQUNoRyxJQUFJO2dCQUNGLE1BQU1DLGdCQUFnQnpFLGFBQWFNLE9BQU8sQ0FBQztnQkFDM0MsTUFBTW9FLGlCQUFpQjFFLGFBQWFNLE9BQU8sQ0FBQztnQkFFNUMsSUFBSSxDQUFDbUUsaUJBQWlCLENBQUNDLGdCQUFnQjtvQkFDckM3SSxRQUFRQyxHQUFHLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBRUEsZ0RBQWdEO2dCQUNoRCxJQUFJNkksVUFBVztnQkFDZkEsV0FBVyxnQkFBMEIsT0FBVkwsV0FBVTtnQkFDckNLLFdBQVcsaUJBQThCLE9BQWJKLGNBQWE7Z0JBRXpDLElBQUlDLFNBQVM7b0JBQ1hHLFdBQVcsbUJBQTJCLE9BQVJILFNBQVE7Z0JBQ3hDO2dCQUVBLElBQUk3RSxNQUFNekUsTUFBTSxDQUFDQyxPQUFPLElBQUl3RSxNQUFNekUsTUFBTSxDQUFDRSxPQUFPLEVBQUU7b0JBQ2hEdUosV0FBVyx3QkFBZ0RoRixPQUF4QkEsTUFBTXpFLE1BQU0sQ0FBQ0MsT0FBTyxFQUFDLEtBQXdCLE9BQXJCd0UsTUFBTXpFLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDO2dCQUNsRjtnQkFFQXVKLFdBQVcsZ0JBQTRDLE9BQTVCLElBQUk3RSxPQUFPOEUsY0FBYyxJQUFHO2dCQUV2RCxNQUFNeEksV0FBVyxNQUFNQyxNQUFNLCtCQUE2QyxPQUFkb0ksZUFBYyxpQkFBZTtvQkFDdkZJLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQUUsZ0JBQWdCO29CQUFtQjtvQkFDOUNDLE1BQU03RSxLQUFLQyxTQUFTLENBQUM7d0JBQ25CNkUsU0FBU047d0JBQ1RPLE1BQU1OO3dCQUNOTyxZQUFZO29CQUNkO2dCQUNGO2dCQUVBLElBQUksQ0FBQzlJLFNBQVNFLEVBQUUsRUFBRTtvQkFDaEJULFFBQVFvQixLQUFLLENBQUMsK0NBQStDYixTQUFTK0ksVUFBVTtnQkFDbEYsT0FBTztvQkFDTHRKLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLEVBQUUsT0FBT21CLE9BQU87Z0JBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLHdDQUF3Q0E7WUFDeEQ7UUFDRjtxRUFBRztRQUFDMEMsTUFBTXpFLE1BQU0sQ0FBQ0MsT0FBTztRQUFFd0UsTUFBTXpFLE1BQU0sQ0FBQ0UsT0FBTztLQUFDO0lBRS9DLG9DQUFvQztJQUNwQyxNQUFNZ0ssbUJBQW1CL0ssa0RBQVdBO3lEQUFDO1lBQ25DLElBQUk7Z0JBQ0YscURBQXFEO2dCQUNyRCxJQUFJLENBQUNzRixNQUFNekUsTUFBTSxDQUFDQyxPQUFPLElBQUksQ0FBQ3dFLE1BQU16RSxNQUFNLENBQUNFLE9BQU8sRUFBRTtvQkFDbEQrSSxTQUFTO3dCQUFFOUMsTUFBTTt3QkFBb0JFLFNBQVM7b0JBQUU7b0JBQ2hEO2dCQUNGO2dCQUNBLE1BQU05RSxRQUFRLE1BQU1uQixzQkFBc0JxRSxNQUFNekUsTUFBTTtnQkFDdERpSixTQUFTO29CQUFFOUMsTUFBTTtvQkFBb0JFLFNBQVM5RTtnQkFBTTtZQUN0RCxFQUFFLE9BQU9RLE9BQU87Z0JBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLGlDQUFpQ0E7Z0JBQy9DLHNEQUFzRDtnQkFDdERvSCw4QkFDRSxxQkFDQSxpQ0FBMEYsT0FBekRwSCxpQkFBaUJvSSxRQUFRcEksTUFBTTBILE9BQU8sR0FBRyxrQkFDMUUsaUJBQXlDaEYsT0FBeEJBLE1BQU16RSxNQUFNLENBQUNDLE9BQU8sRUFBQyxLQUF3QixPQUFyQndFLE1BQU16RSxNQUFNLENBQUNFLE9BQU87WUFFakU7UUFDRjt3REFBRztRQUFDdUUsTUFBTXpFLE1BQU07UUFBRWlKO1FBQVVFO0tBQThCO0lBRTFELHNFQUFzRTtJQUN0RWpLLGdEQUFTQTtxQ0FBQztZQUNSLGdCQUFnQjtZQUNoQmdMO1lBRUEsbURBQW1EO1lBQ25ELE1BQU1FLDJCQUEyQkM7c0VBQVk7b0JBQzNDLGlDQUFpQztvQkFDakMsTUFBTUMsaUJBQWlCMUssZ0VBQWNBLENBQUM2SCxXQUFXO29CQUNqRCxJQUFJNkMsZUFBZUMsU0FBUyxHQUFHQyxRQUFRLEVBQUU7d0JBQ3ZDdkIsU0FBUzs0QkFBRTlDLE1BQU07d0JBQXlCO29CQUM1QztnQkFDRjtxRUFBRyxPQUFPLGtEQUFrRDtZQUU1RDs2Q0FBTztvQkFDTHNFLGNBQWNMO2dCQUNoQjs7UUFDRjtvQ0FBRztRQUFDRjtRQUFrQmpCO0tBQVM7SUFFL0IsZ0JBQWdCO0lBQ2hCL0osZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSSxJQUE2QixFQUFFO2dCQUMvQmdLLFNBQVN3QixPQUFPLEdBQUcsSUFBSUM7WUFDM0I7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsdUVBQXVFO0lBQ3ZFLE1BQU1DLGdCQUFnQnhMLDZDQUFNQSxDQUFnRixFQUFFO0lBQzlHLE1BQU15TCxvQkFBb0J6TCw2Q0FBTUEsQ0FBQztJQUVqQyxNQUFNMEwsb0JBQW9CM0wsa0RBQVdBOzBEQUFDO1lBQ3BDLElBQUkwTCxrQkFBa0JILE9BQU8sSUFBSUUsY0FBY0YsT0FBTyxDQUFDSyxNQUFNLEtBQUssS0FBSyxDQUFDN0IsU0FBU3dCLE9BQU8sRUFBRTtZQUUxRkcsa0JBQWtCSCxPQUFPLEdBQUc7WUFDNUIsTUFBTSxFQUFFTSxRQUFRLEVBQUVDLFNBQVMsRUFBRSxHQUFHTCxjQUFjRixPQUFPLENBQUNRLEtBQUs7WUFFM0QsSUFBSTtnQkFDRixzQ0FBc0M7Z0JBQ3RDLE1BQU0xRCxpQkFBaUI3SCxnRUFBY0EsQ0FBQzhILFdBQVc7Z0JBQ2pELE1BQU0wRCxrQkFBa0JGLGFBQWF6RCxlQUFlb0IsbUJBQW1CO2dCQUN2RSxNQUFNd0MsZ0JBQWdCRCxrQkFBa0IzRCxlQUFlc0IsV0FBVyxDQUFDcUMsbUJBQW1CO2dCQUN0RixNQUFNRSxnQkFBZ0JELENBQUFBLDBCQUFBQSxvQ0FBQUEsY0FBZUMsYUFBYSxLQUFJNUcsTUFBTWIsV0FBVztnQkFFdkUsSUFBSSxDQUFDeUgsY0FBY0Msa0JBQWtCLEVBQUU7b0JBQ3JDLFFBQVEsNEJBQTRCO2dCQUN0QztnQkFFQSxJQUFJQztnQkFDSixJQUFJUCxhQUFhLHlCQUF5QkssY0FBY0cscUJBQXFCLEVBQUU7b0JBQzdFRCxZQUFZRixjQUFjSSxtQkFBbUI7Z0JBQy9DLE9BQU8sSUFBSVQsYUFBYSxnQkFBZ0JLLGNBQWNLLFlBQVksRUFBRTtvQkFDbEVILFlBQVlGLGNBQWNNLFVBQVU7Z0JBQ3RDO2dCQUVBLElBQUlKLFdBQVc7b0JBQ2Isd0NBQXdDO29CQUN4QyxJQUFJSyxpQkFBaUJMO29CQUVyQix3Q0FBd0M7b0JBQ3hDLElBQUlBLFVBQVVNLFVBQVUsQ0FBQyxhQUFhO3dCQUNwQ0QsaUJBQWlCTCxVQUFVTyxPQUFPLENBQUMsWUFBWTt3QkFDL0NuTCxRQUFRSSxJQUFJLENBQUMsZ0NBQWdENkssT0FBaEJMLFdBQVUsUUFBcUIsT0FBZks7b0JBQy9EO29CQUVBLCtEQUErRDtvQkFDL0QsSUFBSUEsZUFBZUMsVUFBVSxDQUFDLGtCQUFrQixDQUFDRCxlQUFlRyxRQUFRLENBQUMsZUFBZTt3QkFDdEYsTUFBTUMsaUJBQWlCOzRCQUNyQjs0QkFBYTs0QkFBYzs0QkFBYzs0QkFBYTs0QkFBYTs0QkFDbkU7NEJBQWdCOzRCQUFxQjs0QkFBa0I7NEJBQWU7NEJBQ3RFOzRCQUFxQjs0QkFBcUI7NEJBQXdCOzRCQUNsRTs0QkFBa0I7NEJBQWM7NEJBQWM7NEJBQWE7NEJBQzNEOzRCQUFZO3lCQUNiO3dCQUVELE1BQU1DLFdBQVdMLGVBQWVNLEtBQUssQ0FBQyxLQUFLQyxHQUFHO3dCQUM5QyxJQUFJRixZQUFZLENBQUNELGVBQWVELFFBQVEsQ0FBQ0UsV0FBVzs0QkFDbERMLGlCQUFpQix3QkFBd0IsbUJBQW1COzRCQUM1RGpMLFFBQVFJLElBQUksQ0FBQywwQkFBdUQ2SyxPQUE3QkwsV0FBVSxxQkFBa0MsT0FBZks7d0JBQ3RFO29CQUNGO29CQUVBLHdEQUF3RDtvQkFDeEQxQyxTQUFTd0IsT0FBTyxDQUFDMEIsS0FBSztvQkFDdEJsRCxTQUFTd0IsT0FBTyxDQUFDMkIsV0FBVyxHQUFHO29CQUMvQm5ELFNBQVN3QixPQUFPLENBQUM0QixHQUFHLEdBQUdWO29CQUV2Qix3Q0FBd0M7b0JBQ3hDLE1BQU0sSUFBSVc7MEVBQWMsQ0FBQ0MsU0FBU0M7Z0NBYWhDdkQsbUJBQ0FBLG9CQUNBQTs0QkFkQSxNQUFNd0Q7NEZBQVk7d0NBQ2hCeEQsbUJBQ0FBO3FDQURBQSxvQkFBQUEsU0FBU3dCLE9BQU8sY0FBaEJ4Qix3Q0FBQUEsa0JBQWtCeUQsbUJBQW1CLENBQUMsa0JBQWtCRDtxQ0FDeER4RCxxQkFBQUEsU0FBU3dCLE9BQU8sY0FBaEJ4Qix5Q0FBQUEsbUJBQWtCeUQsbUJBQW1CLENBQUMsU0FBU0M7b0NBQy9DSjtnQ0FDRjs7NEJBRUEsTUFBTUk7MEZBQVUsQ0FBQ0M7d0NBQ2YzRCxtQkFDQUE7cUNBREFBLG9CQUFBQSxTQUFTd0IsT0FBTyxjQUFoQnhCLHdDQUFBQSxrQkFBa0J5RCxtQkFBbUIsQ0FBQyxrQkFBa0JEO3FDQUN4RHhELHFCQUFBQSxTQUFTd0IsT0FBTyxjQUFoQnhCLHlDQUFBQSxtQkFBa0J5RCxtQkFBbUIsQ0FBQyxTQUFTQztvQ0FDL0NILE9BQU9JO2dDQUNUOzs2QkFFQTNELG9CQUFBQSxTQUFTd0IsT0FBTyxjQUFoQnhCLHdDQUFBQSxrQkFBa0I0RCxnQkFBZ0IsQ0FBQyxrQkFBa0JKLFdBQVc7Z0NBQUVLLE1BQU07NEJBQUs7NkJBQzdFN0QscUJBQUFBLFNBQVN3QixPQUFPLGNBQWhCeEIseUNBQUFBLG1CQUFrQjRELGdCQUFnQixDQUFDLFNBQVNGLFNBQVM7Z0NBQUVHLE1BQU07NEJBQUs7NkJBQ2xFN0QscUJBQUFBLFNBQVN3QixPQUFPLGNBQWhCeEIseUNBQUFBLG1CQUFrQjhELElBQUk7d0JBQ3hCOztvQkFFQSw4QkFBOEI7b0JBQzlCLE1BQU05RCxTQUFTd0IsT0FBTyxDQUFDdUMsSUFBSTtvQkFFM0IsNENBQTRDO29CQUM1QzNFOzBFQUFXOzRCQUNULElBQUlZLFNBQVN3QixPQUFPLEVBQUU7Z0NBQ3BCeEIsU0FBU3dCLE9BQU8sQ0FBQzBCLEtBQUs7Z0NBQ3RCbEQsU0FBU3dCLE9BQU8sQ0FBQzJCLFdBQVcsR0FBRzs0QkFDakM7d0JBQ0Y7eUVBQUc7Z0JBQ0w7WUFDRixFQUFFLE9BQU90SyxPQUFPO2dCQUNkLGlDQUFpQztnQkFDakMsSUFBSUEsaUJBQWlCb0ksU0FDbkJwSSxDQUFBQSxNQUFNaUgsSUFBSSxLQUFLLGdCQUNmakgsTUFBTTBILE9BQU8sQ0FBQ3NDLFFBQVEsQ0FBQyxrQkFDdkJoSyxNQUFNMEgsT0FBTyxDQUFDc0MsUUFBUSxDQUFDLGlCQUFnQixHQUN0QztvQkFDRHBMLFFBQVF1TSxLQUFLLENBQUMsMENBQTBDbkwsTUFBTTBILE9BQU87Z0JBQ3ZFLE9BQU87b0JBQ0w5SSxRQUFRb0IsS0FBSyxDQUFDLHdCQUF3QkE7b0JBQ3RDLHlDQUF5QztvQkFDekMsSUFBSW1ILFNBQVN3QixPQUFPLElBQUl4QixTQUFTd0IsT0FBTyxDQUFDNEIsR0FBRyxLQUFLLHdCQUF3Qjt3QkFDdkUsSUFBSTs0QkFDRnBELFNBQVN3QixPQUFPLENBQUM0QixHQUFHLEdBQUc7NEJBQ3ZCLE1BQU1wRCxTQUFTd0IsT0FBTyxDQUFDdUMsSUFBSTs0QkFDM0IzRTtrRkFBVztvQ0FDVCxJQUFJWSxTQUFTd0IsT0FBTyxFQUFFO3dDQUNwQnhCLFNBQVN3QixPQUFPLENBQUMwQixLQUFLO3dDQUN0QmxELFNBQVN3QixPQUFPLENBQUMyQixXQUFXLEdBQUc7b0NBQ2pDO2dDQUNGO2lGQUFHO3dCQUNMLEVBQUUsT0FBT2MsZUFBZTs0QkFDdEJ4TSxRQUFRb0IsS0FBSyxDQUFDLCtCQUErQm9MO3dCQUMvQztvQkFDRjtnQkFDRjtZQUNGLFNBQVU7Z0JBQ1J0QyxrQkFBa0JILE9BQU8sR0FBRztnQkFDNUIsaURBQWlEO2dCQUNqRHBDO3NFQUFXLElBQU13QztxRUFBcUI7WUFDeEM7UUFDRjt5REFBRztRQUFDckcsTUFBTWIsV0FBVztLQUFDO0lBRXRCLE1BQU13SixZQUFZak8sa0RBQVdBO2tEQUFDLENBQUM2TCxVQUFnREM7WUFDN0UsOENBQThDO1lBQzlDTCxjQUFjRixPQUFPLENBQUMyQyxJQUFJLENBQUM7Z0JBQUVyQztnQkFBVUM7WUFBVTtZQUNqREg7UUFDRjtpREFBRztRQUFDQTtLQUFrQjtJQUV0QixpQ0FBaUM7SUFDakMsTUFBTXdDLDJCQUEyQm5PLGtEQUFXQTtpRUFBQyxPQUFPc0s7WUFDbEQsSUFBSTtnQkFDRixNQUFNRixnQkFBZ0J6RSxhQUFhTSxPQUFPLENBQUM7Z0JBQzNDLE1BQU1vRSxpQkFBaUIxRSxhQUFhTSxPQUFPLENBQUM7Z0JBRTVDLElBQUksQ0FBQ21FLGlCQUFpQixDQUFDQyxnQkFBZ0I7b0JBQ3JDN0ksUUFBUUMsR0FBRyxDQUFDO29CQUNaO2dCQUNGO2dCQUVBLE1BQU1NLFdBQVcsTUFBTUMsTUFBTSwrQkFBNkMsT0FBZG9JLGVBQWMsaUJBQWU7b0JBQ3ZGSSxRQUFRO29CQUNSQyxTQUFTO3dCQUFFLGdCQUFnQjtvQkFBbUI7b0JBQzlDQyxNQUFNN0UsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQjZFLFNBQVNOO3dCQUNUTyxNQUFNTjt3QkFDTk8sWUFBWTtvQkFDZDtnQkFDRjtnQkFFQSxJQUFJLENBQUM5SSxTQUFTRSxFQUFFLEVBQUU7b0JBQ2hCVCxRQUFRb0IsS0FBSyxDQUFDLHlDQUF5Q2IsU0FBUytJLFVBQVU7Z0JBQzVFO1lBQ0YsRUFBRSxPQUFPbEksT0FBTztnQkFDZHBCLFFBQVFvQixLQUFLLENBQUMsd0NBQXdDQTtZQUN4RDtRQUNGO2dFQUFHLEVBQUU7SUFJTCwwREFBMEQ7SUFDMUQ3QyxnREFBU0E7cUNBQUM7UUFDUiwwREFBMEQ7UUFDMUQseURBQXlEO1FBQ3pELDBEQUEwRDtRQUMxRCxrRkFBa0Y7UUFDbEYsNkJBQTZCO1FBQzdCLDJGQUEyRjtRQUMzRiw2Q0FBNkM7UUFDL0M7b0NBQUc7UUFBQ3VGLE1BQU16RSxNQUFNLENBQUNDLE9BQU87UUFBRXdFLE1BQU16RSxNQUFNLENBQUNFLE9BQU87S0FBQyxHQUFHLGtEQUFrRDtJQUVwRyxNQUFNcU4sa0JBQWtCcE8sa0RBQVdBO3dEQUFDLENBQUNxTztZQUNuQyxJQUFJLENBQUNBLFVBQVUsQ0FBQ0MsTUFBTUMsT0FBTyxDQUFDRixTQUFTO1lBRXZDLG1FQUFtRTtZQUNuRSxNQUFNRyxlQUFlO21CQUFJSDthQUFPLENBQUN0RyxNQUFNOzZFQUFDLENBQUMzRixRQUFrQixDQUFDcU0sTUFBTXJNLFVBQVVBLFFBQVE7NEVBQUcrRSxJQUFJOzZFQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDOztZQUUxRyxNQUFNTSxVQUE0QjZHLGFBQWFqSCxHQUFHO3dFQUFDLENBQUNuRixPQUFlcUY7b0JBQy9ELE1BQU1pSCxjQUFjcEosTUFBTWYsZUFBZSxDQUFDb0ssSUFBSTs0RkFBQyxDQUFDQyxJQUFzQkEsRUFBRXRILFdBQVcsS0FBS2xGOztvQkFDeEYsSUFBSXNNLGFBQWE7d0JBQ2YsMkRBQTJEO3dCQUMzRCxPQUFPOzRCQUFFLEdBQUdBLFdBQVc7NEJBQUVoSCxTQUFTRCxRQUFRO3dCQUFFO29CQUM5QztvQkFFQSxPQUFPO3dCQUNMSSxJQUFJeEgsZ0RBQU1BO3dCQUNWcUgsU0FBU0QsUUFBUTt3QkFDakJoQixRQUFRO3dCQUNSb0ksWUFBWTt3QkFDWkMsWUFBWXhKLE1BQU16RSxNQUFNLENBQUNtRCxPQUFPO3dCQUNoQ3NELGFBQWFsRjtvQkFDZjtnQkFDRjs7WUFDRjBILFNBQVM7Z0JBQUU5QyxNQUFNO2dCQUF5QkUsU0FBU1M7WUFBUTtRQUM3RDt1REFBRztRQUFDckMsTUFBTWYsZUFBZTtRQUFFZSxNQUFNekUsTUFBTSxDQUFDbUQsT0FBTztRQUFFOEY7S0FBUztJQUUxRCw4REFBOEQ7SUFDOUQvSixnREFBU0E7cUNBQUM7WUFDUiw2QkFBNkI7WUFDN0IsTUFBTW9MLGlCQUFpQjFLLGdFQUFjQSxDQUFDNkgsV0FBVztZQUNqRCxNQUFNK0MsV0FBV0YsZUFBZUMsU0FBUyxHQUFHQyxRQUFRO1lBRXBELHFEQUFxRDtZQUNyRCxJQUFJL0YsTUFBTVgsZUFBZSxLQUFLLGFBQzFCVyxNQUFNZixlQUFlLENBQUNxSCxNQUFNLEtBQUssS0FDakN0RyxNQUFNWixrQkFBa0IsSUFBSSxLQUM1QixDQUFDMkcsVUFBVTtnQkFFYixJQUFJLENBQUNBLFlBQVkvRixNQUFNWCxlQUFlLEtBQUssV0FBVztvQkFDcERuRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7Z0JBQ0E7WUFDRjtZQUVBLHdFQUF3RTtZQUN4RSxNQUFNLEVBQUVaLE1BQU0sRUFBRTZELGtCQUFrQixFQUFFSCxlQUFlLEVBQUVLLGNBQWMsRUFBRUMsY0FBYyxFQUFFLEdBQUdTO1lBQ3hGLE1BQU15SixxQkFBcUI7bUJBQUl4SzthQUFnQixDQUFDNEMsSUFBSTtnRUFBQyxDQUFDQyxHQUFtQkMsSUFBc0JELEVBQUVFLFdBQVcsR0FBR0QsRUFBRUMsV0FBVzs7WUFFNUgsK0RBQStEO1lBQy9ELElBQUkwSCx3QkFBd0JwSztZQUM1QixJQUFJcUssd0JBQXdCcEs7WUFDNUIsSUFBSXFLLGVBQWU7WUFFbkIxTixRQUFRQyxHQUFHLENBQUMsMkNBQTZFc04sT0FBNUNySyxtQkFBbUJoRCxPQUFPLENBQUMsSUFBRyxnQkFBdUR1TixPQUF6Q0YsbUJBQW1CbkQsTUFBTSxFQUFDLGlCQUF3Qy9LLE9BQXpCb08sdUJBQXNCLEtBQWtCLE9BQWZwTyxPQUFPRSxPQUFPO1lBRXpLLGtDQUFrQztZQUNsQyxNQUFNb08saUJBQWlCSixtQkFBbUJoSCxNQUFNOzREQUFDUCxDQUFBQTtvQkFDL0MsTUFBTTRILGNBQWMsS0FBTUMsR0FBRyxDQUFDM0sscUJBQXFCOEMsSUFBSUYsV0FBVyxJQUFJNUMscUJBQXNCO29CQUM1RixPQUFPMEssZUFBZXZPLE9BQU9zRCxlQUFlO2dCQUM5Qzs7WUFFQSxJQUFJZ0wsZUFBZXZELE1BQU0sR0FBRyxHQUFHO2dCQUM3QnBLLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBZ0QsT0FBdkJaLE9BQU9zRCxlQUFlLEVBQUMsUUFBTWdMLGVBQWU1SCxHQUFHO2lEQUFDQyxDQUFBQSxNQUFPLFdBQTJCQSxPQUFoQkEsSUFBSUUsT0FBTyxFQUFDLE1BQWUsT0FBWEYsSUFBSWYsTUFBTSxFQUFDOztZQUNwSTtZQUVBLDREQUE0RDtZQUM1RCxJQUFLLElBQUk2SSxJQUFJLEdBQUdBLElBQUlQLG1CQUFtQm5ELE1BQU0sRUFBRTBELElBQUs7Z0JBQ2xELE1BQU1DLFlBQVlSLGtCQUFrQixDQUFDTyxFQUFFO2dCQUN2QyxNQUFNRSxtQkFBbUI1TCxLQUFLeUwsR0FBRyxDQUFDM0sscUJBQXFCNkssVUFBVWpJLFdBQVcsSUFBSTVDLHFCQUFxQjtnQkFFckcsbUVBQW1FO2dCQUNuRSxJQUFJOEssb0JBQW9CM08sT0FBT3NELGVBQWUsRUFBRTtvQkFFOUMsSUFBSXRELE9BQU9LLFdBQVcsS0FBSyxjQUFjO3dCQUN2QyxpREFBaUQ7d0JBQ2pELElBQUlxTyxVQUFVOUksTUFBTSxLQUFLLFFBQVE7NEJBQy9CLHFDQUFxQzs0QkFDckMsTUFBTWdKLGNBQWNGLFVBQVVULFVBQVU7NEJBRXhDLElBQUlHLHlCQUF5QlEsYUFBYTtnQ0FDeEMsTUFBTUMsc0JBQXNCRCxjQUFjL0s7Z0NBQzFDLE1BQU1pTCxhQUE2QjtvQ0FDakMsR0FBR0osU0FBUztvQ0FDWjlJLFFBQVE7b0NBQ1JvSSxZQUFZVSxVQUFVVixVQUFVLEdBQUc7b0NBQ25DQyxZQUFZak8sT0FBT21ELE9BQU8sR0FBR0osS0FBS2dNLEdBQUcsQ0FBQy9PLE9BQU9vRCxVQUFVLEVBQUVzTCxVQUFVVixVQUFVLEdBQUc7b0NBQ2hGZ0IsbUJBQW1CSDtvQ0FDbkJJLHFCQUFxQkw7b0NBQ3JCTSxZQUFZTDtvQ0FDWk0sWUFBWSxDQUFDUDtnQ0FDZjtnQ0FDQTNGLFNBQVM7b0NBQUU5QyxNQUFNO29DQUEyQkUsU0FBU3lJO2dDQUFXO2dDQUNoRTdGLFNBQVM7b0NBQUU5QyxNQUFNO29DQUFtQkUsU0FBUzt3Q0FBRXBHLFNBQVNrTyx3QkFBd0JVO3dDQUFxQjNPLFNBQVNrTyx3QkFBd0JRO29DQUFZO2dDQUFFO2dDQUNwSjNGLFNBQVM7b0NBQ1A5QyxNQUFNO29DQUNORSxTQUFTO3dDQUNQVyxJQUFJeEgsZ0RBQU1BO3dDQUNWbUYsV0FBV0MsS0FBS0MsR0FBRzt3Q0FDbkJ1SyxNQUFNLEdBQXFCcFAsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUFrQixPQUFmRCxPQUFPRSxPQUFPO3dDQUN6Q0QsU0FBU0QsT0FBT0MsT0FBTzt3Q0FDdkJvUCxXQUFXO3dDQUNYQyxlQUFlVDt3Q0FDZlUsVUFBVTFMO3dDQUNWMkwsY0FBY1o7d0NBQ2RhLFFBQVE1TDt3Q0FDUjZMLGVBQWUxUCxPQUFPQyxPQUFPLElBQUk7d0NBQ2pDMFAsZUFBZTNQLE9BQU9FLE9BQU8sSUFBSTtvQ0FDbkM7Z0NBQ0Y7Z0NBQ0FTLFFBQVFDLEdBQUcsQ0FBQyxrQkFBOENpTyxPQUE1QkgsVUFBVTdILE9BQU8sRUFBQyxZQUE0QzdHLE9BQWxDNk8sb0JBQW9CaE8sT0FBTyxDQUFDLElBQUcsS0FBeUJnRCxPQUF0QjdELE9BQU9DLE9BQU8sRUFBQyxTQUFxQyxPQUE5QjRELG1CQUFtQmhELE9BQU8sQ0FBQztnQ0FDN0l1TSxVQUFVO2dDQUVWLHlEQUF5RDtnQ0FDekQ7Ozs7O2lCQUtHLEdBRUgscUNBQXFDO2dDQUNyQ0UseUJBQ0UsdUNBQ0EseUJBQWlDLE9BQWxCb0IsVUFBVTdILE9BQU8sRUFBQyxRQUNqQyx3QkFBZ0Q3RyxPQUFsQzZPLG9CQUFvQmhPLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZiLE9BQU9DLE9BQU8sRUFBQyxRQUMvRCx3QkFBNEMsT0FBOUI0RCxtQkFBbUJoRCxPQUFPLENBQUMsSUFBRyxRQUM1Qyx1QkFBdUNiLE9BQTFCNE8sWUFBWS9OLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZiLE9BQU9FLE9BQU8sRUFBQyxRQUNyRDtnQ0FHSG1PO2dDQUVBLDJEQUEyRDtnQ0FDM0RELHlCQUF5QlE7Z0NBQ3pCVCx5QkFBeUJVOzRCQUMzQixPQUFPO2dDQUNMLG1EQUFtRDtnQ0FDbkRsTyxRQUFRQyxHQUFHLENBQUMsa0JBQWlDLE9BQWZaLE9BQU9FLE9BQU8sRUFBQztnQ0FDN0NpSiw4QkFDRSx3QkFDQSwyQ0FBMEQsT0FBZm5KLE9BQU9FLE9BQU8sRUFBQyxhQUMxRCxhQUF1Q0YsT0FBMUI0TyxZQUFZL04sT0FBTyxDQUFDLElBQUcsS0FBaUN1TixPQUE5QnBPLE9BQU9FLE9BQU8sRUFBQyxpQkFBbURGLE9BQXBDb08sc0JBQXNCdk4sT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZmIsT0FBT0UsT0FBTzs0QkFFM0g7d0JBQ0Y7d0JBRUEsZ0dBQWdHO3dCQUNoRyxNQUFNMFAsaUJBQWlCbEIsVUFBVTdILE9BQU87d0JBQ3hDLE1BQU1nSixjQUFjM0IsbUJBQW1CSixJQUFJO3FFQUFDbkgsQ0FBQUEsTUFBT0EsSUFBSUUsT0FBTyxLQUFLK0ksaUJBQWlCOzt3QkFFcEYsSUFBSUMsZUFBZUEsWUFBWWpLLE1BQU0sS0FBSyxVQUFVaUssWUFBWWIsaUJBQWlCLElBQUlhLFlBQVlaLG1CQUFtQixFQUFFOzRCQUNwSCxNQUFNYSxzQkFBc0JELFlBQVliLGlCQUFpQjs0QkFDekQsTUFBTWUsa0JBQWtCRCxzQkFBc0JqTTs0QkFDOUMsTUFBTW1NLGlCQUFpQkQsa0JBQWtCRixZQUFZWixtQkFBbUI7NEJBRXhFLHdFQUF3RTs0QkFDeEUsTUFBTWdCLHdCQUF3QnBNLHFCQUFxQixJQUFJbU0saUJBQWlCbk0scUJBQXFCOzRCQUU3RixNQUFNcU0scUJBQXFDO2dDQUN6QyxHQUFHTCxXQUFXO2dDQUNkakssUUFBUTtnQ0FDUm9KLG1CQUFtQjNIO2dDQUNuQjRILHFCQUFxQjVIO2dDQUNyQjRHLFlBQVlqTyxPQUFPbUQsT0FBTyxHQUFHSixLQUFLZ00sR0FBRyxDQUFDL08sT0FBT29ELFVBQVUsRUFBRXlNLFlBQVk3QixVQUFVO2dDQUMvRWtCLFlBQVksQ0FBQ1k7Z0NBQ2JYLFlBQVlZOzRCQUNkOzRCQUNBOUcsU0FBUztnQ0FBRTlDLE1BQU07Z0NBQTJCRSxTQUFTNko7NEJBQW1COzRCQUN4RWpILFNBQVM7Z0NBQUU5QyxNQUFNO2dDQUFtQkUsU0FBUztvQ0FBRXBHLFNBQVNrTyx3QkFBd0IyQjtvQ0FBcUI1UCxTQUFTa08sd0JBQXdCMkI7Z0NBQWdCOzRCQUFFOzRCQUN4SjlHLFNBQVM7Z0NBQ1A5QyxNQUFNO2dDQUNORSxTQUFTO29DQUNQVyxJQUFJeEgsZ0RBQU1BO29DQUNWbUYsV0FBV0MsS0FBS0MsR0FBRztvQ0FDbkJ1SyxNQUFNLEdBQXFCcFAsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUFrQixPQUFmRCxPQUFPRSxPQUFPO29DQUN6Q0QsU0FBU0QsT0FBT0MsT0FBTztvQ0FDdkJvUCxXQUFXO29DQUNYQyxlQUFlUTtvQ0FDZlAsVUFBVTFMO29DQUNWMkwsY0FBY087b0NBQ2ROLFFBQVE1TDtvQ0FDUjZMLGVBQWUxUCxPQUFPQyxPQUFPLElBQUk7b0NBQ2pDMFAsZUFBZTNQLE9BQU9FLE9BQU8sSUFBSTtvQ0FDakNpUSwyQkFBMkJIO29DQUMzQkksMkJBQTJCSDtnQ0FDN0I7NEJBQ0Y7NEJBRUF0UCxRQUFRQyxHQUFHLENBQUMsd0NBQW9FcVAsT0FBdENELGVBQWVuUCxPQUFPLENBQUMsSUFBRyxjQUE2QyxPQUFqQ29QLHNCQUFzQnBQLE9BQU8sQ0FBQzs0QkFDOUdGLFFBQVFDLEdBQUcsQ0FBQyxtQkFBOENrUCxPQUEzQkYsaUJBQWlCLEdBQUUsVUFBMEM1UCxPQUFsQzhQLG9CQUFvQmpQLE9BQU8sQ0FBQyxJQUFHLEtBQStCbVAsT0FBNUJoUSxPQUFPQyxPQUFPLEVBQUMsZUFBdUMsT0FBMUIrUCxlQUFlblAsT0FBTyxDQUFDOzRCQUMvSXVNLFVBQVU7NEJBRVYsMERBQTBEOzRCQUMxRCxNQUFNaUQsY0FBY0wsaUJBQWlCLElBQUksT0FBT0EsaUJBQWlCLElBQUksT0FBTzs0QkFDNUU7Ozs7O2VBS0csR0FFSCxzQ0FBc0M7NEJBQ3RDMUMseUJBQ0Usd0NBQ0EseUJBQWtDLE9BQW5Cc0MsaUJBQWlCLEdBQUUsUUFDbEMsd0JBQWdENVAsT0FBbEM4UCxvQkFBb0JqUCxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmYixPQUFPQyxPQUFPLEVBQUMsUUFDL0Qsd0JBQTRDLE9BQTlCNEQsbUJBQW1CaEQsT0FBTyxDQUFDLElBQUcsUUFDNUMsMkJBQStDYixPQUE5QitQLGdCQUFnQmxQLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZiLE9BQU9FLE9BQU8sRUFBQyxRQUM5RCxHQUEyQjhQLE9BQXhCSyxhQUFZLGNBQXlDclEsT0FBN0JnUSxlQUFlblAsT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZmIsT0FBT0UsT0FBTyxFQUFDLFFBQ3RFOzRCQUdIbU87NEJBRUEsMkRBQTJEOzRCQUMzREYseUJBQXlCMkI7NEJBQ3pCMUIseUJBQXlCMkI7d0JBQzNCO29CQUNGLE9BQU8sSUFBSS9QLE9BQU9LLFdBQVcsS0FBSyxrQkFBa0I7d0JBQ2xELHdFQUF3RTt3QkFDeEUsSUFBSXFPLFVBQVU5SSxNQUFNLEtBQUssUUFBUTs0QkFDL0IsdUVBQXVFOzRCQUN2RSxNQUFNMEsscUJBQXFCNUIsVUFBVVQsVUFBVSxFQUFFLHlDQUF5Qzs0QkFFMUYsSUFBSUcseUJBQXlCa0Msb0JBQW9CO2dDQUMvQywrQ0FBK0M7Z0NBQy9DLDBFQUEwRTtnQ0FDMUUsTUFBTTdQLHlCQUF5QitCLFlBQVl4QyxPQUFPRSxPQUFPLElBQUksVUFBVXNDLFlBQVl4QyxPQUFPTSxtQkFBbUIsSUFBSTtnQ0FDakgsTUFBTWlRLHFCQUFxQkQscUJBQXFCN1A7Z0NBRWhELHNDQUFzQztnQ0FDdEMsb0RBQW9EO2dDQUNwRCxNQUFNRix5QkFBeUJpQyxZQUFZeEMsT0FBT0MsT0FBTyxJQUFJLFNBQVN1QyxZQUFZeEMsT0FBT00sbUJBQW1CLElBQUk7Z0NBQ2hILE1BQU1rUSxnQkFBZ0JELHFCQUFxQmhRO2dDQUUzQyx5REFBeUQ7Z0NBQ3pELE1BQU1rUSxXQUFXL0IsVUFBVVYsVUFBVSxHQUFHO2dDQUN4QyxNQUFNMEMsV0FBVzFRLE9BQU9tRCxPQUFPLEdBQUdKLEtBQUtnTSxHQUFHLENBQUMvTyxPQUFPb0QsVUFBVSxFQUFFcU47Z0NBRTlELE1BQU0zQixhQUE2QjtvQ0FDakMsR0FBR0osU0FBUztvQ0FDWjlJLFFBQVE7b0NBQ1JvSSxZQUFZeUM7b0NBQ1p4QyxZQUFZeUM7b0NBQ1oxQixtQkFBbUJ3QjtvQ0FDbkJ2QixxQkFBcUJxQjtvQ0FDckJwQixZQUFZc0I7b0NBQ1pyQixZQUFZLENBQUNtQjtnQ0FDZjtnQ0FFQXJILFNBQVM7b0NBQUU5QyxNQUFNO29DQUEyQkUsU0FBU3lJO2dDQUFXO2dDQUNoRTdGLFNBQVM7b0NBQUU5QyxNQUFNO29DQUFtQkUsU0FBUzt3Q0FBRXBHLFNBQVNrTyx3QkFBd0JxQzt3Q0FBZXRRLFNBQVNrTyx3QkFBd0JrQztvQ0FBbUI7Z0NBQUU7Z0NBRXJKLGtFQUFrRTtnQ0FDbEUsTUFBTUssbUJBQW1CTDtnQ0FDekIsTUFBTU0sc0JBQXNCTDtnQ0FFNUIsNERBQTREO2dDQUM1RHRILFNBQVM7b0NBQ1A5QyxNQUFNO29DQUNORSxTQUFTO3dDQUNQVyxJQUFJeEgsZ0RBQU1BO3dDQUNWbUYsV0FBV0MsS0FBS0MsR0FBRzt3Q0FDbkJ1SyxNQUFNLEdBQXFCcFAsT0FBbEJBLE9BQU9FLE9BQU8sRUFBQyxLQUE4QixPQUEzQkYsT0FBT00sbUJBQW1CO3dDQUNyREwsU0FBU0QsT0FBT0UsT0FBTzt3Q0FDdkJtUCxXQUFXO3dDQUNYQyxlQUFlZ0I7d0NBQ2ZmLFVBQVU5Tzt3Q0FDVitPLGNBQWNlO3dDQUNkZCxRQUFRaFA7d0NBQ1JpUCxlQUFlMVAsT0FBT0UsT0FBTyxJQUFJO3dDQUNqQ3lQLGVBQWUzUCxPQUFPTSxtQkFBbUIsSUFBSTt3Q0FDN0MsNEVBQTRFO3dDQUM1RTZQLDJCQUEyQixDQUFDUTt3Q0FDNUJQLDJCQUEyQjdQLHlCQUF5QixJQUFJLENBQUNvUSxtQkFBbUJwUSx5QkFBeUI7b0NBQ3ZHO2dDQUNGO2dDQUVBMEksU0FBUztvQ0FDUDlDLE1BQU07b0NBQ05FLFNBQVM7d0NBQ1BXLElBQUl4SCxnREFBTUE7d0NBQ1ZtRixXQUFXQyxLQUFLQyxHQUFHO3dDQUNuQnVLLE1BQU0sR0FBcUJwUCxPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQThCLE9BQTNCRCxPQUFPTSxtQkFBbUI7d0NBQ3JETCxTQUFTRCxPQUFPQyxPQUFPO3dDQUN2Qm9QLFdBQVc7d0NBQ1hDLGVBQWVrQjt3Q0FDZmpCLFVBQVVoUDt3Q0FDVmlQLGNBQWNlO3dDQUNkZCxRQUFRbFA7d0NBQ1JtUCxlQUFlMVAsT0FBT0MsT0FBTyxJQUFJO3dDQUNqQzBQLGVBQWUzUCxPQUFPTSxtQkFBbUIsSUFBSTt3Q0FDN0MsbURBQW1EO3dDQUNuRDZQLDJCQUEyQlE7d0NBQzNCUCwyQkFBMkJJLGNBQWMsNkJBQTZCO29DQUN4RTtnQ0FDRjtnQ0FFQTdQLFFBQVFDLEdBQUcsQ0FBQyw2QkFBaUUwUCxPQUFwQzVCLFVBQVU3SCxPQUFPLEVBQUMsb0JBQXdDN0csT0FBdEJzUSxvQkFBbUIsS0FBdUJDLE9BQXBCdlEsT0FBT0UsT0FBTyxFQUFDLE9BQXNDRixPQUFqQ3VRLG1CQUFtQjFQLE9BQU8sQ0FBQyxJQUFHLEtBQWtEMlAsT0FBL0N4USxPQUFPTSxtQkFBbUIsRUFBQyxzQkFBZ0ROLE9BQTVCd1EsY0FBYzNQLE9BQU8sQ0FBQyxJQUFHLEtBQThCNk4sT0FBM0IxTyxPQUFPQyxPQUFPLEVBQUMsY0FBc0N3USxPQUExQi9CLFVBQVVWLFVBQVUsRUFBQyxPQUFjLE9BQVR5QztnQ0FDeFJyRCxVQUFVO2dDQUVWLG9FQUFvRTtnQ0FDcEU7Ozs7O2lCQUtHLEdBRUgsZ0RBQWdEO2dDQUNoREUseUJBQ0UseURBQ0EseUJBQWlDLE9BQWxCb0IsVUFBVTdILE9BQU8sRUFBQyxRQUNqQyw2QkFBb0Q3RyxPQUFqQ3NRLG1CQUFtQnpQLE9BQU8sQ0FBQyxJQUFHLEtBQXVCMFAsT0FBcEJ2USxPQUFPRSxPQUFPLEVBQUMsT0FBc0NGLE9BQWpDdVEsbUJBQW1CMVAsT0FBTyxDQUFDLElBQUcsS0FBOEIsT0FBM0JiLE9BQU9NLG1CQUFtQixFQUFDLFFBQ3BJLCtCQUFpRE4sT0FBNUJ3USxjQUFjM1AsT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZmIsT0FBT0MsT0FBTyxFQUFDLFFBQ2hFLHVCQUF1Q3dRLE9BQTFCL0IsVUFBVVYsVUFBVSxFQUFDLE9BQWMsT0FBVHlDLFVBQVMsUUFDL0M7Z0NBR0hwQztnQ0FFQSwyREFBMkQ7Z0NBQzNERCx5QkFBeUJrQztnQ0FDekJuQyx5QkFBeUJxQzs0QkFDM0I7d0JBQ0Y7d0JBRUEsZ0dBQWdHO3dCQUNoRyxNQUFNWixpQkFBaUJsQixVQUFVN0gsT0FBTzt3QkFDeEMsTUFBTWdKLGNBQWMzQixtQkFBbUJKLElBQUk7cUVBQUNuSCxDQUFBQSxNQUFPQSxJQUFJRSxPQUFPLEtBQUsrSSxpQkFBaUI7O3dCQUVwRixJQUFJQyxlQUFlQSxZQUFZakssTUFBTSxLQUFLLFVBQVVpSyxZQUFZYixpQkFBaUIsSUFBSWEsWUFBWVosbUJBQW1CLEVBQUU7NEJBQ3BILDRGQUE0Rjs0QkFDNUYsTUFBTWEsc0JBQXNCRCxZQUFZYixpQkFBaUI7NEJBRXpELCtDQUErQzs0QkFDL0MsTUFBTXpPLHlCQUF5QmlDLFlBQVl4QyxPQUFPQyxPQUFPLElBQUksU0FBU3VDLFlBQVl4QyxPQUFPTSxtQkFBbUIsSUFBSTs0QkFDaEgsTUFBTXVRLHVCQUF1QmYsc0JBQXNCdlA7NEJBRW5ELHNDQUFzQzs0QkFDdEMsTUFBTUUseUJBQXlCK0IsWUFBWXhDLE9BQU9FLE9BQU8sSUFBSSxVQUFVc0MsWUFBWXhDLE9BQU9NLG1CQUFtQixJQUFJOzRCQUNqSCxNQUFNd1Esb0JBQW9CRCx1QkFBdUJwUTs0QkFFakQsdUNBQXVDOzRCQUN2QyxNQUFNc1EsMEJBQTBCRCxvQkFBb0JqQixZQUFZWixtQkFBbUI7NEJBRW5GLCtFQUErRTs0QkFDL0UsTUFBTWdCLHdCQUF3QjFQLHlCQUF5QixJQUFJd1EsMEJBQTBCeFEseUJBQXlCOzRCQUU5Ryw2REFBNkQ7NEJBQzdELE1BQU0yUCxxQkFBcUM7Z0NBQ3pDLEdBQUdMLFdBQVc7Z0NBQ2RqSyxRQUFRO2dDQUNSLGtEQUFrRDtnQ0FDbERvSixtQkFBbUIzSDtnQ0FDbkI0SCxxQkFBcUI1SDtnQ0FDckI0RyxZQUFZak8sT0FBT21ELE9BQU8sR0FBR0osS0FBS2dNLEdBQUcsQ0FBQy9PLE9BQU9vRCxVQUFVLEVBQUV5TSxZQUFZN0IsVUFBVTtnQ0FDL0VrQixZQUFZO2dDQUNaQyxZQUFZOzRCQUNkOzRCQUVBbEcsU0FBUztnQ0FBRTlDLE1BQU07Z0NBQTJCRSxTQUFTNko7NEJBQW1COzRCQUN4RWpILFNBQVM7Z0NBQUU5QyxNQUFNO2dDQUFtQkUsU0FBUztvQ0FBRXBHLFNBQVNrTyx3QkFBd0IyQjtvQ0FBcUI1UCxTQUFTa08sd0JBQXdCMEM7Z0NBQWtCOzRCQUFFOzRCQUUxSixnRUFBZ0U7NEJBQ2hFN0gsU0FBUztnQ0FDUDlDLE1BQU07Z0NBQ05FLFNBQVM7b0NBQ1BXLElBQUl4SCxnREFBTUE7b0NBQ1ZtRixXQUFXQyxLQUFLQyxHQUFHO29DQUNuQnVLLE1BQU0sR0FBcUJwUCxPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQThCLE9BQTNCRCxPQUFPTSxtQkFBbUI7b0NBQ3JETCxTQUFTRCxPQUFPQyxPQUFPO29DQUN2Qm9QLFdBQVc7b0NBQ1hDLGVBQWVRO29DQUNmUCxVQUFVaFA7b0NBQ1ZpUCxjQUFjcUI7b0NBQ2RwQixRQUFRbFA7b0NBQ1JtUCxlQUFlMVAsT0FBT0MsT0FBTyxJQUFJO29DQUNqQzBQLGVBQWUzUCxPQUFPTSxtQkFBbUIsSUFBSTtvQ0FDN0MsK0RBQStEO29DQUMvRDZQLDJCQUEyQlUsdUJBQXVCcFE7b0NBQ2xEMlAsMkJBQTJCTixvQkFBb0IsdURBQXVEO2dDQUN4Rzs0QkFDRjs0QkFFQTdHLFNBQVM7Z0NBQ1A5QyxNQUFNO2dDQUNORSxTQUFTO29DQUNQVyxJQUFJeEgsZ0RBQU1BO29DQUNWbUYsV0FBV0MsS0FBS0MsR0FBRztvQ0FDbkJ1SyxNQUFNLEdBQXFCcFAsT0FBbEJBLE9BQU9FLE9BQU8sRUFBQyxLQUE4QixPQUEzQkYsT0FBT00sbUJBQW1CO29DQUNyREwsU0FBU0QsT0FBT0UsT0FBTztvQ0FDdkJtUCxXQUFXO29DQUNYQyxlQUFld0I7b0NBQ2Z2QixVQUFVOU87b0NBQ1YrTyxjQUFjcUI7b0NBQ2RwQixRQUFRaFA7b0NBQ1JpUCxlQUFlMVAsT0FBT0UsT0FBTyxJQUFJO29DQUNqQ3lQLGVBQWUzUCxPQUFPTSxtQkFBbUIsSUFBSTtvQ0FDN0MscUVBQXFFO29DQUNyRTZQLDJCQUEyQlk7b0NBQzNCWCwyQkFBMkJIO2dDQUM3Qjs0QkFDRjs0QkFFQXRQLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBa0ZxUCxPQUEvQ2Msd0JBQXdCbFEsT0FBTyxDQUFDLElBQUcsY0FBNkMsT0FBakNvUCxzQkFBc0JwUCxPQUFPLENBQUM7NEJBRTVIRixRQUFRQyxHQUFHLENBQUMsOEJBQW1Fa1AsT0FBckNGLGlCQUFpQixHQUFFLG9CQUFvRDVQLE9BQWxDOFAsb0JBQW9CalAsT0FBTyxDQUFDLElBQUcsS0FBdUJnUSxPQUFwQjdRLE9BQU9DLE9BQU8sRUFBQyxPQUF3Q0QsT0FBbkM2USxxQkFBcUJoUSxPQUFPLENBQUMsSUFBRyxLQUFrRGlRLE9BQS9DOVEsT0FBT00sbUJBQW1CLEVBQUMsc0JBQW9ETixPQUFoQzhRLGtCQUFrQmpRLE9BQU8sQ0FBQyxJQUFHLEtBQStCa1EsT0FBNUIvUSxPQUFPRSxPQUFPLEVBQUMsZUFBbURGLE9BQXRDK1Esd0JBQXdCbFEsT0FBTyxDQUFDLElBQUcsS0FBOEJnUCxPQUEzQjdQLE9BQU9FLE9BQU8sRUFBQyxjQUFtQyxPQUF2QjJQLFlBQVk3QixVQUFVLEVBQUM7NEJBQzNXWixVQUFVOzRCQUVWLHFFQUFxRTs0QkFDckUsTUFBTWlELGNBQWNVLDBCQUEwQixJQUFJLE9BQU9BLDBCQUEwQixJQUFJLE9BQU87NEJBQzlGOzs7OztlQUtHLEdBRUgsaURBQWlEOzRCQUNqRHpELHlCQUNFLDBEQUNBLHlCQUFrQyxPQUFuQnNDLGlCQUFpQixHQUFFLFFBQ2xDLDZCQUFxRDVQLE9BQWxDOFAsb0JBQW9CalAsT0FBTyxDQUFDLElBQUcsS0FBdUJnUSxPQUFwQjdRLE9BQU9DLE9BQU8sRUFBQyxPQUF3Q0QsT0FBbkM2USxxQkFBcUJoUSxPQUFPLENBQUMsSUFBRyxLQUE4QixPQUEzQmIsT0FBT00sbUJBQW1CLEVBQUMsUUFDdkksK0JBQXFETixPQUFoQzhRLGtCQUFrQmpRLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZiLE9BQU9FLE9BQU8sRUFBQyxRQUNwRSxHQUEwQjZRLE9BQXZCVixhQUFZLGFBQWlEclEsT0FBdEMrUSx3QkFBd0JsUSxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmYixPQUFPRSxPQUFPLEVBQUMsUUFDL0UsdUJBQW9DLE9BQXZCMlAsWUFBWTdCLFVBQVUsRUFBQyxvQkFDbkM7NEJBR0hLOzRCQUVBLDJEQUEyRDs0QkFDM0RGLHlCQUF5QjJCOzRCQUN6QjFCLHlCQUF5QjBDO3dCQUMzQjtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsSUFBSXpDLGVBQWUsR0FBRztnQkFDcEIxTixRQUFRQyxHQUFHLENBQUMsZ0NBQThEaUQsT0FBeEN3SyxjQUFhLDZCQUF5RCxPQUE5QnhLLG1CQUFtQmhELE9BQU8sQ0FBQztZQUN2RztRQUVGO29DQUFHO1FBQUM0RCxNQUFNWCxlQUFlO1FBQUVXLE1BQU1aLGtCQUFrQjtRQUFFWSxNQUFNZixlQUFlO1FBQUVlLE1BQU16RSxNQUFNO1FBQUV5RSxNQUFNVixjQUFjO1FBQUVVLE1BQU1ULGNBQWM7UUFBRVMsTUFBTVIsaUJBQWlCO1FBQUVnRjtRQUFVbUU7UUFBV0U7S0FBeUI7SUFHN00sTUFBTTBELG1CQUFtQjdSLGtEQUFXQTt5REFBQztZQUNuQyxJQUFJLENBQUNzRixNQUFNZixlQUFlLElBQUksQ0FBQytKLE1BQU1DLE9BQU8sQ0FBQ2pKLE1BQU1mLGVBQWUsR0FBRztnQkFDbkUsT0FBTyxFQUFFO1lBQ1g7WUFFQSxPQUFPZSxNQUFNZixlQUFlLENBQUNnRCxHQUFHO2lFQUFDLENBQUNDO29CQUNoQyxNQUFNc0ssZUFBZXhNLE1BQU1aLGtCQUFrQixJQUFJO29CQUNqRCxNQUFNNEMsY0FBY0UsSUFBSUYsV0FBVyxJQUFJO29CQUN2QyxNQUFNeUsseUJBQXlCRCxnQkFBZ0J4SyxjQUMzQyxDQUFDLGVBQWdCQSxjQUFlLEtBQUssTUFDckM7b0JBRUosSUFBSTBLO29CQUNKLElBQUlDO29CQUVKLElBQUl6SyxJQUFJZixNQUFNLEtBQUssVUFBVWUsSUFBSXFJLGlCQUFpQixJQUFJckksSUFBSXNJLG1CQUFtQixFQUFFO3dCQUM3RSxrRkFBa0Y7d0JBQ2xGLElBQUl4SyxNQUFNekUsTUFBTSxDQUFDSyxXQUFXLEtBQUssa0JBQWtCOzRCQUNqRCxzRkFBc0Y7NEJBQ3RGLDZEQUE2RDs0QkFDN0QsMkRBQTJEOzRCQUMzRCxNQUFNb0csY0FBY0UsSUFBSUYsV0FBVyxJQUFJOzRCQUN2QyxNQUFNNEssa0JBQWtCSixlQUFleEs7NEJBQ3ZDLE1BQU02SyxtQkFBbUI3SyxjQUFjLElBQUs0SyxrQkFBa0I1SyxjQUFlOzRCQUU3RSxzRUFBc0U7NEJBQ3RFLE1BQU04SyxpQ0FBaUM1SyxJQUFJcUksaUJBQWlCLEdBQUdxQzs0QkFDL0RELGdCQUFnQkc7NEJBQ2hCLElBQUlOLGVBQWUsR0FBRztnQ0FDcEJFLGdCQUFnQkksaUNBQWlDTjs0QkFDbkQ7d0JBQ0YsT0FBTzs0QkFDTCx5QkFBeUI7NEJBQ3pCLE1BQU1NLGlDQUFpQyxlQUFnQjVLLElBQUlxSSxpQkFBaUIsR0FBSXJJLElBQUlzSSxtQkFBbUI7NEJBQ3ZHbUMsZ0JBQWdCRzs0QkFDaEIsSUFBSU4sZUFBZSxHQUFHO2dDQUNwQkUsZ0JBQWdCSSxpQ0FBaUNOOzRCQUNuRDt3QkFDRjtvQkFDRjtvQkFFQSxPQUFPO3dCQUNMLEdBQUd0SyxHQUFHO3dCQUNOc0s7d0JBQ0FJLGlCQUFpQjVLLGNBQWN3Szt3QkFDL0JPLHdCQUF3QlAsZUFBZSxJQUFJLENBQUV4SyxjQUFjd0ssWUFBVyxJQUFLQSxlQUFnQixNQUFNO3dCQUNqR1Esd0JBQXdCLE1BQU96UixNQUFNLENBQUN1RCx5QkFBeUIsR0FBRyxNQUFPb0QsSUFBSXNILFVBQVUsR0FBSXhILENBQUFBLGVBQWU7d0JBQzFHaUwsd0JBQXdCLE1BQU8xUixNQUFNLENBQUN3RCx5QkFBeUIsR0FBRyxNQUFPbUQsSUFBSXNILFVBQVU7d0JBQ3ZGaUQ7d0JBQ0FDO3dCQUNBQztvQkFDRjtnQkFDRjtnRUFBRzlLLElBQUk7aUVBQUMsQ0FBQ0MsR0FBb0JDLElBQXVCQSxFQUFFQyxXQUFXLEdBQUdGLEVBQUVFLFdBQVc7O1FBQ25GO3dEQUFHO1FBQUNoQyxNQUFNZixlQUFlO1FBQUVlLE1BQU1aLGtCQUFrQjtRQUFFWSxNQUFNekUsTUFBTSxDQUFDdUQseUJBQXlCO1FBQUVrQixNQUFNekUsTUFBTSxDQUFDd0QseUJBQXlCO1FBQUVpQixNQUFNekUsTUFBTSxDQUFDbUQsT0FBTztRQUFFc0IsTUFBTXpFLE1BQU0sQ0FBQ29ELFVBQVU7S0FBQztJQUduTCxnQ0FBZ0M7SUFDaEMsTUFBTXVPLHNCQUFzQnhTLGtEQUFXQTs0REFBQyxPQUFPYTtZQUM3QyxJQUFJO29CQWtCS2tCO2dCQWpCUCxNQUFNMFEsYUFBYTtvQkFDakI1SSxNQUFNLEdBQXFCaEosT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUFxQkQsT0FBbEJBLE9BQU9FLE9BQU8sRUFBQyxLQUFzQixPQUFuQkYsT0FBT0ssV0FBVztvQkFDL0RBLGFBQWFMLE9BQU9LLFdBQVc7b0JBQy9CSixTQUFTRCxPQUFPQyxPQUFPO29CQUN2QkMsU0FBU0YsT0FBT0UsT0FBTztvQkFDdkJpRCxTQUFTbkQsT0FBT21ELE9BQU87b0JBQ3ZCQyxZQUFZcEQsT0FBT29ELFVBQVU7b0JBQzdCQyxXQUFXckQsT0FBT3FELFNBQVM7b0JBQzNCQyxpQkFBaUJ0RCxPQUFPc0QsZUFBZTtvQkFDdkNDLDJCQUEyQnZELE9BQU91RCx5QkFBeUI7b0JBQzNEQywyQkFBMkJ4RCxPQUFPd0QseUJBQXlCO29CQUMzRGxELHFCQUFxQk4sT0FBT00sbUJBQW1CO29CQUMvQ3VSLGNBQWNwTixNQUFNZixlQUFlLENBQUNnRCxHQUFHOzRFQUFDQyxDQUFBQSxNQUFPQSxJQUFJRixXQUFXOztnQkFDaEU7Z0JBRUEsTUFBTXZGLFdBQVcsTUFBTXhCLGdEQUFVQSxDQUFDb1MsVUFBVSxDQUFDRjtnQkFDN0NqUixRQUFRQyxHQUFHLENBQUMsOEJBQThCTTtnQkFDMUMsT0FBT0EsRUFBQUEsbUJBQUFBLFNBQVNsQixNQUFNLGNBQWZrQix1Q0FBQUEsaUJBQWlCOEYsRUFBRSxLQUFJO1lBQ2hDLEVBQUUsT0FBT2pGLE9BQU87Z0JBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLHVDQUF1Q0E7Z0JBQ3JELE9BQU87WUFDVDtRQUNGOzJEQUFHO1FBQUMwQyxNQUFNZixlQUFlO0tBQUM7SUFFMUIsTUFBTXFPLGtCQUFrQjVTLGtEQUFXQTt3REFBQyxPQUFPNlM7WUFDekMsSUFBSTtnQkFDRixNQUFNOVEsV0FBVyxNQUFNeEIsZ0RBQVVBLENBQUN1UyxRQUFRLENBQUNEO2dCQUMzQ3JSLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJNO2dCQUN6QyxPQUFPO1lBQ1QsRUFBRSxPQUFPYSxPQUFPO2dCQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyxxQ0FBcUNBO2dCQUNuRCxPQUFPO1lBQ1Q7UUFDRjt1REFBRyxFQUFFO0lBRUwsTUFBTW1RLGlCQUFpQi9TLGtEQUFXQTt1REFBQyxPQUFPNlM7WUFDeEMsSUFBSTtnQkFDRixNQUFNOVEsV0FBVyxNQUFNeEIsZ0RBQVVBLENBQUN5UyxPQUFPLENBQUNIO2dCQUMxQ3JSLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJNO2dCQUN6QyxPQUFPO1lBQ1QsRUFBRSxPQUFPYSxPQUFPO2dCQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyxvQ0FBb0NBO2dCQUNsRCxPQUFPO1lBQ1Q7UUFDRjtzREFBRyxFQUFFO0lBRUwsTUFBTXFRLHFCQUFxQmpULGtEQUFXQTsyREFBQztZQUNyQyxNQUFNa1QsU0FBU0MsdUJBQStCO1lBQzlDLElBQUksQ0FBQ0QsUUFBUTtnQkFDWDFSLFFBQVFvQixLQUFLLENBQUM7Z0JBQ2RrSCxTQUFTO29CQUFFOUMsTUFBTTtvQkFBc0JFLFNBQVM7Z0JBQVU7Z0JBQzFEO1lBQ0Y7WUFDQSxJQUFJO2dCQUNGLE1BQU1vTSxpQkFBaUIsTUFBTXRSLE1BQU0sR0FBVSxPQUFQa1IsUUFBTztnQkFDN0MsSUFBSSxDQUFDSSxlQUFlclIsRUFBRSxFQUFFO29CQUN0QiwyREFBMkQ7b0JBQzNEVCxRQUFRb0IsS0FBSyxDQUFDLDRDQUFxRTBRLE9BQXpCQSxlQUFlN00sTUFBTSxFQUFDLEtBQTZCLE9BQTFCNk0sZUFBZXhJLFVBQVU7b0JBQzVHLE1BQU15SSxlQUFlLE1BQU1ELGVBQWUxSSxJQUFJLEdBQUc0SSxLQUFLOzJFQUFDLElBQU07O29CQUM3RGhTLFFBQVFvQixLQUFLLENBQUMsdUNBQXVDMlE7Z0JBQ3ZEO2dCQUNBekosU0FBUztvQkFBRTlDLE1BQU07b0JBQXNCRSxTQUFTb00sZUFBZXJSLEVBQUUsR0FBRyxXQUFXO2dCQUFVO1lBQzNGLEVBQUUsT0FBT1csT0FBWTtnQkFDbkJrSCxTQUFTO29CQUFFOUMsTUFBTTtvQkFBc0JFLFNBQVM7Z0JBQVU7Z0JBQzFEMUYsUUFBUW9CLEtBQUssQ0FBQyxxREFBcURBO2dCQUNuRSxJQUFJQSxNQUFNNlEsS0FBSyxFQUFFO29CQUNmalMsUUFBUW9CLEtBQUssQ0FBQyxzQkFBc0JBLE1BQU02USxLQUFLO2dCQUNqRDtnQkFDQSxtRUFBbUU7Z0JBQ25FalMsUUFBUW9CLEtBQUssQ0FBQywrQkFBK0IsR0FBVSxPQUFQc1EsUUFBTztZQUN6RDtRQUNGOzBEQUFHO1FBQUNwSjtLQUFTO0lBRWIsa0RBQWtEO0lBQ2xEL0osZ0RBQVNBO3FDQUFDO1lBQ1Isd0NBQXdDO1lBQ3hDa1Q7UUFDRjtvQ0FBRztRQUFDQTtLQUFtQjtJQUV2QixpREFBaUQ7SUFDakRsVCxnREFBU0E7cUNBQUM7WUFDUnNGLHdCQUF3QkM7UUFDMUI7b0NBQUc7UUFBQ0E7S0FBTTtJQUdWLDREQUE0RDtJQUM1RHZGLGdEQUFTQTtxQ0FBQztZQUNSLElBQUl1RixNQUFNWCxlQUFlLEtBQUssYUFBYTtnQkFDekNuRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osb0RBQW9EO2dCQUNwRHFJLFNBQVM7b0JBQUU5QyxNQUFNO2dCQUF5QjtnQkFDMUN4RixRQUFRQyxHQUFHLENBQUM7WUFDZDtRQUNGO29DQUFHO1FBQUM2RCxNQUFNWCxlQUFlO1FBQUVtRjtLQUFTO0lBRXBDLDRDQUE0QztJQUM1Qy9KLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU1zSSxpQkFBaUI3SCxnRUFBY0EsQ0FBQzhILFdBQVc7WUFDakQsTUFBTWtCLG1CQUFtQm5CLGVBQWVvQixtQkFBbUI7WUFFM0QsSUFBSUQsa0JBQWtCO2dCQUNwQixJQUFJbEUsTUFBTVgsZUFBZSxLQUFLLFdBQVc7b0JBQ3ZDLDhDQUE4QztvQkFDOUMwRCxlQUFlcUwsbUJBQW1CLENBQUNsSztvQkFDbkNoSSxRQUFRQyxHQUFHLENBQUMsMkNBQTJDK0g7Z0JBQ3pELE9BQU8sSUFBSWxFLE1BQU1YLGVBQWUsS0FBSyxXQUFXO29CQUM5QyxxQ0FBcUM7b0JBQ3JDMEQsZUFBZXNMLGtCQUFrQixDQUFDbks7b0JBQ2xDaEksUUFBUUMsR0FBRyxDQUFDLDRDQUE0QytIO2dCQUMxRDtZQUNGO1FBQ0Y7b0NBQUc7UUFBQ2xFLE1BQU1YLGVBQWU7S0FBQztJQUUxQixpRkFBaUY7SUFDakY1RSxnREFBU0E7cUNBQUM7WUFDUixNQUFNc0ksaUJBQWlCN0gsZ0VBQWNBLENBQUM4SCxXQUFXO1lBRWpELHVEQUF1RDtZQUN2RCxJQUFJaEQsTUFBTVgsZUFBZSxLQUFLLGVBQWUsQ0FBQzBELGVBQWVvQixtQkFBbUIsSUFBSTtnQkFDbEYsMEVBQTBFO2dCQUMxRSxnREFBZ0Q7Z0JBQ2hELElBQUluRSxNQUFNekUsTUFBTSxDQUFDQyxPQUFPLElBQUl3RSxNQUFNekUsTUFBTSxDQUFDRSxPQUFPLElBQUl1RSxNQUFNZixlQUFlLENBQUNxSCxNQUFNLEdBQUcsR0FBRztvQkFDcEZ2RCxlQUFldUwsNEJBQTRCLENBQ3pDdE8sTUFBTXpFLE1BQU0sRUFDWnFILFdBQ0E7d0JBQUVwSCxTQUFTd0UsTUFBTVYsY0FBYzt3QkFBRTdELFNBQVN1RSxNQUFNVCxjQUFjO3dCQUFFOUIsWUFBWXVDLE1BQU1SLGlCQUFpQjtvQkFBQyxHQUVuRytPLElBQUk7cURBQUMsQ0FBQy9IOzRCQUNMekQsZUFBZXlMLGlCQUFpQixDQUFDaEk7NEJBQ2pDdEssUUFBUUMsR0FBRyxDQUFDLDJCQUEyQnFLO3dCQUN6QztvREFDQzBILEtBQUs7cURBQUMsQ0FBQzVROzRCQUNOcEIsUUFBUW9CLEtBQUssQ0FBQyxvQ0FBb0NBO3dCQUNwRDs7Z0JBQ0o7WUFDRjtZQUVBLDhDQUE4QztZQUM5QyxNQUFNNEcsbUJBQW1CbkIsZUFBZW9CLG1CQUFtQjtZQUMzRCxJQUFJRCxrQkFBa0I7Z0JBQ3BCLElBQUlsRSxNQUFNWCxlQUFlLEtBQUssV0FBVztvQkFDdkMsaURBQWlEO29CQUNqRDBELGVBQWVxTCxtQkFBbUIsQ0FBQ2xLO29CQUNuQ2hJLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBNEMrSDtnQkFDMUQsT0FBTyxJQUFJbEUsTUFBTVgsZUFBZSxLQUFLLFdBQVc7b0JBQzlDLHVDQUF1QztvQkFDdkMwRCxlQUFlc0wsa0JBQWtCLENBQUNuSztvQkFDbENoSSxRQUFRQyxHQUFHLENBQUMsNENBQTRDK0g7Z0JBQzFEO1lBQ0Y7UUFDRjtvQ0FBRztRQUFDbEUsTUFBTVgsZUFBZTtRQUFFVyxNQUFNekUsTUFBTSxDQUFDQyxPQUFPO1FBQUV3RSxNQUFNekUsTUFBTSxDQUFDRSxPQUFPO0tBQUM7SUFFdEUsbURBQW1EO0lBQ25EaEIsZ0RBQVNBO3FDQUFDO1lBQ1IsTUFBTXNJLGlCQUFpQjdILGdFQUFjQSxDQUFDOEgsV0FBVztZQUNqRCxNQUFNa0IsbUJBQW1CbkIsZUFBZW9CLG1CQUFtQjtZQUUzRCxJQUFJRCxrQkFBa0I7Z0JBQ3BCLE1BQU1FLGlCQUFpQnJCLGVBQWVzQixXQUFXLENBQUNIO2dCQUVsRCw0REFBNEQ7Z0JBQzVELElBQUlFLGtCQUNDQSxDQUFBQSxlQUFlN0ksTUFBTSxDQUFDQyxPQUFPLEtBQUt3RSxNQUFNekUsTUFBTSxDQUFDQyxPQUFPLElBQ3RENEksZUFBZTdJLE1BQU0sQ0FBQ0UsT0FBTyxLQUFLdUUsTUFBTXpFLE1BQU0sQ0FBQ0UsT0FBTyxHQUFHO29CQUU1RFMsUUFBUUMsR0FBRyxDQUFDO29CQUVaLDJEQUEyRDtvQkFDM0QsSUFBSTZELE1BQU1YLGVBQWUsS0FBSyxhQUMxQlcsTUFBTWYsZUFBZSxDQUFDcUgsTUFBTSxHQUFHLEtBQy9CdEcsTUFBTWQsWUFBWSxDQUFDb0gsTUFBTSxHQUFHLEdBQUc7d0JBRWpDLDhEQUE4RDt3QkFDOUQsMERBQTBEO3dCQUMxRCxpRUFBaUU7d0JBRWpFLHNDQUFzQzt3QkFDdEMsTUFBTXBHLFlBQVksSUFBSUMsT0FBTzhFLGNBQWMsQ0FBQyxTQUFTOzRCQUNuRHdKLE9BQU87NEJBQ1BDLEtBQUs7NEJBQ0xDLE1BQU07NEJBQ05DLFFBQVE7NEJBQ1JDLFFBQVE7d0JBQ1Y7d0JBQ0EsTUFBTUMsWUFBWSxHQUFxQzVPLE9BQWxDa0UsZUFBZUcsSUFBSSxFQUFDLGdCQUF3QixPQUFWckUsV0FBVTt3QkFFakU2QyxlQUFlZ00sZ0JBQWdCLENBQzdCRCxXQUNBMUssZUFBZTdJLE1BQU0sRUFDckI7NEJBQUVDLFNBQVN3RSxNQUFNVixjQUFjOzRCQUFFN0QsU0FBU3VFLE1BQU1ULGNBQWM7NEJBQUU5QixZQUFZdUMsTUFBTVIsaUJBQWlCO3dCQUFDLEdBRW5HK08sSUFBSTt5REFBQyxDQUFDUztnQ0FDTGpNLGVBQWVrTSxXQUFXLENBQ3hCRCxnQkFDQTVLLGVBQWU3SSxNQUFNLEVBQ3JCeUUsTUFBTWYsZUFBZSxFQUNyQmUsTUFBTWQsWUFBWSxFQUNsQmMsTUFBTVosa0JBQWtCLEVBQ3hCWSxNQUFNVixjQUFjLEVBQ3BCVSxNQUFNVCxjQUFjLEVBQ3BCUyxNQUFNUixpQkFBaUIsRUFDdkI7Z0NBRUZ0RCxRQUFRQyxHQUFHLENBQUMseUJBQXlCMlM7NEJBQ3ZDO3dEQUNDWixLQUFLO3lEQUFDLENBQUM1UTtnQ0FDTnBCLFFBQVFvQixLQUFLLENBQUMsNERBQTREQTs0QkFDNUU7O29CQUNKO29CQUVBLDRCQUE0QjtvQkFDNUJrSCxTQUFTO3dCQUFFOUMsTUFBTTtvQkFBdUI7b0JBRXhDLHVEQUF1RDtvQkFDdkQsa0VBQWtFO29CQUNsRXhGLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGO1FBQ0Y7b0NBQUc7UUFBQzZELE1BQU16RSxNQUFNLENBQUNDLE9BQU87UUFBRXdFLE1BQU16RSxNQUFNLENBQUNFLE9BQU87S0FBQztJQUUvQyw4Q0FBOEM7SUFDOUNoQixnREFBU0E7cUNBQUM7WUFDUixNQUFNb0wsaUJBQWlCMUssZ0VBQWNBLENBQUM2SCxXQUFXO1lBQ2pELE1BQU1rTSxrQkFBa0I5VCxpRUFBZUEsQ0FBQzRILFdBQVc7WUFDbkQsTUFBTW1NLGdCQUFnQjlULCtEQUFhQSxDQUFDMkgsV0FBVztZQUMvQyxNQUFNRCxpQkFBaUI3SCxnRUFBY0EsQ0FBQzhILFdBQVc7WUFFakQsaUNBQWlDO1lBQ2pDLE1BQU1vTSxxQkFBcUJ2SixlQUFld0osV0FBVztnRUFBQyxDQUFDdEosVUFBVXVKO29CQUMvRHBULFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEQsT0FBaEM0SixXQUFXLFdBQVc7b0JBRWhFLG1EQUFtRDtvQkFDbkQsSUFBSSxDQUFDQSxZQUFZLENBQUN1SixXQUFXO3dCQUMzQiwrQkFBK0I7d0JBQy9CLElBQUl0UCxNQUFNWCxlQUFlLEtBQUssV0FBVzs0QkFDdkNuRCxRQUFRQyxHQUFHLENBQUM7NEJBQ1pxSSxTQUFTO2dDQUFFOUMsTUFBTTs0QkFBa0I7NEJBRW5DLDRCQUE0Qjs0QkFDNUIsTUFBTXFCLGlCQUFpQjdILGdFQUFjQSxDQUFDOEgsV0FBVzs0QkFDakQsTUFBTWtCLG1CQUFtQm5CLGVBQWVvQixtQkFBbUI7NEJBQzNELElBQUlELGtCQUFrQjtnQ0FDcEIsTUFBTUUsaUJBQWlCckIsZUFBZXNCLFdBQVcsQ0FBQ0g7Z0NBQ2xELElBQUlFLGdCQUFnQjtvQ0FDbEIsZ0NBQWdDO29DQUNoQyxNQUFNbEUsWUFBWSxJQUFJQyxPQUFPOEUsY0FBYyxDQUFDLFNBQVM7d0NBQ25Ed0osT0FBTzt3Q0FDUEMsS0FBSzt3Q0FDTEMsTUFBTTt3Q0FDTkMsUUFBUTt3Q0FDUkMsUUFBUTtvQ0FDVjtvQ0FDQSxNQUFNVSxjQUFjLEdBQTBDclAsT0FBdkNrRSxlQUFlRyxJQUFJLEVBQUMscUJBQTZCLE9BQVZyRSxXQUFVO29DQUV4RTZDLGVBQWVnTSxnQkFBZ0IsQ0FDN0JRLGFBQ0FuTCxlQUFlN0ksTUFBTSxFQUNyQjt3Q0FBRUMsU0FBU3dFLE1BQU1WLGNBQWM7d0NBQUU3RCxTQUFTdUUsTUFBTVQsY0FBYzt3Q0FBRTlCLFlBQVl1QyxNQUFNUixpQkFBaUI7b0NBQUMsR0FFbkcrTyxJQUFJO3dGQUFDLENBQUNpQjs0Q0FDTHpNLGVBQWVrTSxXQUFXLENBQ3hCTyxpQkFDQXhQLE1BQU16RSxNQUFNLEVBQ1p5RSxNQUFNZixlQUFlLEVBQ3JCZSxNQUFNZCxZQUFZLEVBQ2xCYyxNQUFNWixrQkFBa0IsRUFDeEJZLE1BQU1WLGNBQWMsRUFDcEJVLE1BQU1ULGNBQWMsRUFDcEJTLE1BQU1SLGlCQUFpQixFQUN2Qjs0Q0FFRnRELFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NvVDt3Q0FDcEQ7dUZBQ0NyQixLQUFLO3dGQUFDLENBQUM1UTs0Q0FDTnBCLFFBQVFvQixLQUFLLENBQUMsOENBQThDQTt3Q0FDOUQ7O2dDQUNKOzRCQUNGO3dCQUNGO3dCQUVBcEIsUUFBUUMsR0FBRyxDQUFDO29CQUNkLE9BQU8sSUFBSTRKLFlBQVksQ0FBQ3VKLFdBQVc7d0JBQ2pDcFQsUUFBUUMsR0FBRyxDQUFDO29CQUNkO2dCQUNGOztZQUVBLDJCQUEyQjtZQUMzQixNQUFNc1Qsb0JBQW9CTixjQUFjRSxXQUFXOytEQUFDLENBQUNLO29CQUNuRCxNQUFNQyxTQUFTRCxPQUFPRSxjQUFjLEdBQUcsT0FBTztvQkFDOUMsSUFBSUQsU0FBUyxLQUFLO3dCQUNoQnpULFFBQVFJLElBQUksQ0FBQyxtQ0FBMkMsT0FBbEJxVCxPQUFPdlQsT0FBTyxDQUFDLElBQUc7b0JBQzFEO2dCQUNGOztZQUVBLG1CQUFtQjtZQUNuQixNQUFNeVQ7MERBQWU7b0JBQ25CLElBQUk7d0JBQ0YsdURBQXVEO3dCQUN2RCxNQUFNM0wsbUJBQW1CbkIsZUFBZW9CLG1CQUFtQjt3QkFDM0QsSUFBSUQsa0JBQWtCOzRCQUNwQm5CLGVBQWVrTSxXQUFXLENBQ3hCL0ssa0JBQ0FsRSxNQUFNekUsTUFBTSxFQUNaeUUsTUFBTWYsZUFBZSxFQUNyQmUsTUFBTWQsWUFBWSxFQUNsQmMsTUFBTVosa0JBQWtCLEVBQ3hCWSxNQUFNVixjQUFjLEVBQ3BCVSxNQUFNVCxjQUFjLEVBQ3BCUyxNQUFNUixpQkFBaUIsRUFDdkIsS0FBSyxnREFBZ0Q7O3dCQUV6RDt3QkFFQSxzQ0FBc0M7d0JBQ3RDTyx3QkFBd0JDO29CQUMxQixFQUFFLE9BQU8xQyxPQUFPO3dCQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyxxQkFBcUJBO29CQUNyQztnQkFDRjs7WUFFQTRSLGdCQUFnQlksTUFBTSxDQUFDRCxjQUFjLFFBQVEsNkJBQTZCO1lBRTFFLHFFQUFxRTtZQUNyRSxNQUFNRTtnRUFBcUIsQ0FBQ0M7b0JBQzFCLGlDQUFpQztvQkFDakNIO29CQUVBLGtDQUFrQztvQkFDbEMsSUFBSTdQLE1BQU1YLGVBQWUsS0FBSyxXQUFXO3dCQUN2QyxNQUFNMkYsVUFBVTt3QkFDaEJnTCxNQUFNQyxXQUFXLEdBQUdqTDt3QkFDcEIsT0FBT0E7b0JBQ1Q7Z0JBQ0Y7O1lBRUF4QixPQUFPNkUsZ0JBQWdCLENBQUMsZ0JBQWdCMEg7WUFFeEMsbUJBQW1CO1lBQ25COzZDQUFPO29CQUNMWDtvQkFDQUs7b0JBQ0FQLGdCQUFnQmdCLE9BQU87b0JBQ3ZCMU0sT0FBTzBFLG1CQUFtQixDQUFDLGdCQUFnQjZIO2dCQUM3Qzs7UUFDRjtvQ0FBRztRQUFDL1A7S0FBTTtJQUVWLHFDQUFxQztJQUNyQ3ZGLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU15VSxrQkFBa0I5VCxpRUFBZUEsQ0FBQzRILFdBQVc7WUFDbkRrTSxnQkFBZ0JpQixPQUFPO1FBQ3pCO29DQUFHO1FBQUNuUSxNQUFNWCxlQUFlO0tBQUM7SUFFMUIsNENBQTRDO0lBQzVDNUUsZ0RBQVNBO3FDQUFDO1lBQ1JxRyxtQkFBbUJkLE1BQU1WLGNBQWMsRUFBRVUsTUFBTVQsY0FBYyxFQUFFUyxNQUFNUixpQkFBaUI7UUFDeEY7b0NBQUc7UUFBQ1EsTUFBTVYsY0FBYztRQUFFVSxNQUFNVCxjQUFjO1FBQUVTLE1BQU1SLGlCQUFpQjtLQUFDO0lBRXhFLDRFQUE0RTtJQUM1RS9FLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU0yVixvQkFBb0JwUSxNQUFNVixjQUFjLEtBQUssTUFBTVUsTUFBTVQsY0FBYyxLQUFLLFVBQVVTLE1BQU1SLGlCQUFpQixLQUFLO1lBQ3hILElBQUk0USxtQkFBbUI7Z0JBQ3JCLE1BQU03TyxpQkFBaUJQO2dCQUN2QixJQUFJTyxlQUFlakMsY0FBYyxLQUFLLE1BQU1pQyxlQUFlaEMsY0FBYyxLQUFLLFVBQVVnQyxlQUFlL0IsaUJBQWlCLEtBQUssR0FBRztvQkFDOUhnRixTQUFTO3dCQUNQOUMsTUFBTTt3QkFDTkUsU0FBUzs0QkFDUHBHLFNBQVMrRixlQUFlakMsY0FBYzs0QkFDdEM3RCxTQUFTOEYsZUFBZWhDLGNBQWM7NEJBQ3RDOUIsWUFBWThELGVBQWUvQixpQkFBaUI7d0JBQzlDO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtvQ0FBRyxFQUFFLEdBQUcseUJBQXlCO0lBRWpDLCtCQUErQjtJQUMvQixNQUFNNlEscUJBQXFCM1Ysa0RBQVdBOzJEQUFDO1lBQ3JDLElBQUk7Z0JBQ0YsTUFBTXFJLGlCQUFpQjdILGdFQUFjQSxDQUFDOEgsV0FBVztnQkFDakQsTUFBTWtCLG1CQUFtQm5CLGVBQWVvQixtQkFBbUI7Z0JBRTNELElBQUksQ0FBQ0Qsa0JBQWtCO29CQUNyQix1Q0FBdUM7b0JBQ3ZDLElBQUlsRSxNQUFNekUsTUFBTSxDQUFDQyxPQUFPLElBQUl3RSxNQUFNekUsTUFBTSxDQUFDRSxPQUFPLEVBQUU7d0JBQ2hEc0gsZUFBZXVMLDRCQUE0QixDQUN6Q3RPLE1BQU16RSxNQUFNLEVBQ1pxSCxXQUNBOzRCQUFFcEgsU0FBU3dFLE1BQU1WLGNBQWM7NEJBQUU3RCxTQUFTdUUsTUFBTVQsY0FBYzs0QkFBRTlCLFlBQVl1QyxNQUFNUixpQkFBaUI7d0JBQUMsR0FFbkcrTyxJQUFJOytFQUFDLENBQUMvSDtnQ0FDTHpELGVBQWV5TCxpQkFBaUIsQ0FBQ2hJO2dDQUNqQ3pELGVBQWVrTSxXQUFXLENBQ3hCekksV0FDQXhHLE1BQU16RSxNQUFNLEVBQ1p5RSxNQUFNZixlQUFlLEVBQ3JCZSxNQUFNZCxZQUFZLEVBQ2xCYyxNQUFNWixrQkFBa0IsRUFDeEJZLE1BQU1WLGNBQWMsRUFDcEJVLE1BQU1ULGNBQWMsRUFDcEJTLE1BQU1SLGlCQUFpQixFQUN2QixLQUFLLHFDQUFxQzs7NEJBRTlDOzhFQUNDME8sS0FBSzsrRUFBQyxDQUFDNVE7Z0NBQ05wQixRQUFRb0IsS0FBSyxDQUFDLGlDQUFpQ0E7Z0NBQy9Db0gsOEJBQ0UsMEJBQ0Esd0NBQ0EsVUFBbUUsT0FBekRwSCxpQkFBaUJvSSxRQUFRcEksTUFBTTBILE9BQU8sR0FBRzs0QkFFdkQ7O3dCQUNGLE9BQU87b0JBQ1Q7b0JBQ0FOLDhCQUNFLHNCQUNBLGtEQUNBO29CQUVGLE9BQU87Z0JBQ1Q7Z0JBRUEsd0JBQXdCO2dCQUN4QixPQUFPM0IsZUFBZWtNLFdBQVcsQ0FDL0IvSyxrQkFDQWxFLE1BQU16RSxNQUFNLEVBQ1p5RSxNQUFNZixlQUFlLEVBQ3JCZSxNQUFNZCxZQUFZLEVBQ2xCYyxNQUFNWixrQkFBa0IsRUFDeEJZLE1BQU1WLGNBQWMsRUFDcEJVLE1BQU1ULGNBQWMsRUFDcEJTLE1BQU1SLGlCQUFpQixFQUN2QixLQUFLLDRCQUE0Qjs7WUFFckMsRUFBRSxPQUFPbEMsT0FBTztnQkFDZHBCLFFBQVFvQixLQUFLLENBQUMsbUNBQW1DQTtnQkFDakRvSCw4QkFDRSxzQkFDQSx5Q0FDQSxVQUFtRSxPQUF6RHBILGlCQUFpQm9JLFFBQVFwSSxNQUFNMEgsT0FBTyxHQUFHO2dCQUVyRCxPQUFPO1lBQ1Q7UUFDRjswREFBRztRQUFDaEY7UUFBTzBFO0tBQThCO0lBRXpDLGdCQUFnQjtJQUNoQixNQUFNNEwsZUFBbUM7UUFDdkMsR0FBR3RRLEtBQUs7UUFDUndFO1FBQ0FzRTtRQUNBeUQ7UUFDQW9CO1FBQ0FsSTtRQUNBNkg7UUFDQUc7UUFDQVA7UUFDQW1EO1FBQ0E1USxlQUFlTyxNQUFNUCxhQUFhO1FBQ2xDSixpQkFBaUJXLE1BQU1YLGVBQWU7UUFDdENrUixhQUFhdlEsTUFBTVgsZUFBZSxLQUFLO0lBQ3pDO0lBRUEscUJBQ0UsOERBQUM2RCxlQUFlc04sUUFBUTtRQUFDQyxPQUFPSDtrQkFDN0JsTjs7Ozs7O0FBR1AsRUFBRTtHQW56Q1dEO0tBQUFBO0FBcXpDYix5Q0FBeUM7QUFDbEMsTUFBTXVOLG9CQUFvQjs7SUFDL0IsTUFBTTdMLFVBQVV0SyxpREFBVUEsQ0FBQzJJO0lBQzNCLElBQUkyQixZQUFZakMsV0FBVztRQUN6QixNQUFNLElBQUk4QyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT2I7QUFDVCxFQUFFO0lBTlc2TCIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXFRyYWRpbmdDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB0eXBlIHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVJlZHVjZXIsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgVHJhZGluZ0NvbmZpZywgVGFyZ2V0UHJpY2VSb3csIE9yZGVySGlzdG9yeUVudHJ5LCBBcHBTZXR0aW5ncywgT3JkZXJTdGF0dXMsIERpc3BsYXlPcmRlclJvdyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IERFRkFVTFRfQVBQX1NFVFRJTkdTLCBBVkFJTEFCTEVfQ1JZUFRPUywgQVZBSUxBQkxFX1FVT1RFU19TSU1QTEUsIEFWQUlMQUJMRV9TVEFCTEVDT0lOUyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnO1xuXG4vLyBEZWZpbmUgbG9jYWxseSB0byBhdm9pZCBpbXBvcnQgaXNzdWVzXG5jb25zdCBERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMgPSBbXCJVU0RUXCIsIFwiVVNEQ1wiLCBcIkJUQ1wiXTtcbmltcG9ydCB7IHRyYWRpbmdBcGkgfSBmcm9tICdAL2xpYi9hcGknO1xuaW1wb3J0IHsgU2Vzc2lvbk1hbmFnZXIgfSBmcm9tICdAL2xpYi9zZXNzaW9uLW1hbmFnZXInO1xuaW1wb3J0IHsgTmV0d29ya01vbml0b3IsIEF1dG9TYXZlTWFuYWdlciwgTWVtb3J5TW9uaXRvciB9IGZyb20gJ0AvbGliL25ldHdvcmstbW9uaXRvcic7XG4vLyBpbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90b2FzdCc7IC8vIFRlbXBvcmFyaWx5IGRpc2FibGVkIHRvIGZpeCBtb2R1bGUgbG9hZGluZ1xuXG4vLyBEZWZpbmUgQm90IFN5c3RlbSBTdGF0dXNcbmV4cG9ydCB0eXBlIEJvdFN5c3RlbVN0YXR1cyA9ICdTdG9wcGVkJyB8ICdXYXJtaW5nVXAnIHwgJ1J1bm5pbmcnO1xuXG4vLyBVcGRhdGUgVGFyZ2V0UHJpY2VSb3cgdHlwZVxuLy8gKEFzc3VtaW5nIFRhcmdldFByaWNlUm93IGlzIGRlZmluZWQgaW4gQC9saWIvdHlwZXMsIHRoaXMgaXMgY29uY2VwdHVhbClcbi8vIGV4cG9ydCBpbnRlcmZhY2UgVGFyZ2V0UHJpY2VSb3cge1xuLy8gICAvLyAuLi4gZXhpc3RpbmcgZmllbGRzXG4vLyAgIGxhc3RBY3Rpb25UaW1lc3RhbXA/OiBudW1iZXI7XG4vLyB9XG5cbi8vIERlZmluZSBhY3Rpb24gdHlwZXNcbnR5cGUgVHJhZGluZ0FjdGlvbiA9XG4gIHwgeyB0eXBlOiAnU0VUX0NPTkZJRyc7IHBheWxvYWQ6IFBhcnRpYWw8VHJhZGluZ0NvbmZpZz4gfVxuICB8IHsgdHlwZTogJ1NFVF9UQVJHRVRfUFJJQ0VfUk9XUyc7IHBheWxvYWQ6IFRhcmdldFByaWNlUm93W10gfVxuICB8IHsgdHlwZTogJ0FERF9UQVJHRVRfUFJJQ0VfUk9XJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3cgfVxuICB8IHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3cgfVxuICB8IHsgdHlwZTogJ1JFTU9WRV9UQVJHRVRfUFJJQ0VfUk9XJzsgcGF5bG9hZDogc3RyaW5nIH1cbiAgfCB7IHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWSc7IHBheWxvYWQ6IE9yZGVySGlzdG9yeUVudHJ5IH1cbiAgfCB7IHR5cGU6ICdVUERBVEVfT1JERVJfSElTVE9SWV9FTlRSWSc7IHBheWxvYWQ6IE9yZGVySGlzdG9yeUVudHJ5IH1cbiAgfCB7IHR5cGU6ICdSRU1PVkVfT1JERVJfSElTVE9SWV9FTlRSWSc7IHBheWxvYWQ6IHN0cmluZyB9XG4gIHwgeyB0eXBlOiAnU0VUX0FQUF9TRVRUSU5HUyc7IHBheWxvYWQ6IFBhcnRpYWw8QXBwU2V0dGluZ3M+IH1cbiAgfCB7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnOyBwYXlsb2FkOiAnb25saW5lJyB8ICdvZmZsaW5lJyB8ICd1bmtub3duJyB9XG4gIHwgeyB0eXBlOiAnU0VUX01BUktFVF9QUklDRSc7IHBheWxvYWQ6IG51bWJlciB9XG4gIHwgeyB0eXBlOiAnRkxVQ1RVQVRFX01BUktFVF9QUklDRScgfVxuICB8IHsgdHlwZTogJ1NFVF9CQUxBTkNFUyc7IHBheWxvYWQ6IHsgY3J5cHRvMTogbnVtYmVyOyBjcnlwdG8yOiBudW1iZXIgfSB9XG4gIHwgeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJzsgcGF5bG9hZDogeyBjcnlwdG8xPzogbnVtYmVyOyBjcnlwdG8yPzogbnVtYmVyOyBzdGFibGVjb2luPzogbnVtYmVyIH0gfVxuICB8IHsgdHlwZTogJ1VQREFURV9TVEFCTEVDT0lOX0JBTEFOQ0UnOyBwYXlsb2FkOiBudW1iZXIgfVxuICB8IHsgdHlwZTogJ0NMRUFSX09SREVSX0hJU1RPUlknIH1cbiAgfCB7IHR5cGU6ICdTWVNURU1fU1RBUlRfQk9UX0lOSVRJQVRFJyB9XG4gIHwgeyB0eXBlOiAnU1lTVEVNX0NPTVBMRVRFX1dBUk1VUCcgfVxuICB8IHsgdHlwZTogJ1NZU1RFTV9TVE9QX0JPVCcgfVxuICB8IHsgdHlwZTogJ1NZU1RFTV9SRVNFVF9CT1QnIH1cbiAgfCB7IHR5cGU6ICdSRVNFVF9TRVNTSU9OJyB9XG4gIHwgeyB0eXBlOiAnUkVTRVRfRk9SX05FV19DUllQVE8nIH07XG5cbi8vIERlZmluZSBzdGF0ZSB0eXBlXG5pbnRlcmZhY2UgVHJhZGluZ1N0YXRlIHtcbiAgY29uZmlnOiBUcmFkaW5nQ29uZmlnO1xuICB0YXJnZXRQcmljZVJvd3M6IFRhcmdldFByaWNlUm93W107XG4gIG9yZGVySGlzdG9yeTogT3JkZXJIaXN0b3J5RW50cnlbXTtcbiAgYXBwU2V0dGluZ3M6IEFwcFNldHRpbmdzO1xuICBiYWNrZW5kU3RhdHVzOiAnb25saW5lJyB8ICdvZmZsaW5lJyB8ICd1bmtub3duJztcbiAgY3VycmVudE1hcmtldFByaWNlOiBudW1iZXI7XG4gIGNyeXB0bzFCYWxhbmNlOiBudW1iZXI7XG4gIGNyeXB0bzJCYWxhbmNlOiBudW1iZXI7XG4gIC8vIGlzQm90QWN0aXZlOiBib29sZWFuOyAvLyBUbyBiZSByZXBsYWNlZCBieSBib3RTeXN0ZW1TdGF0dXNcbiAgYm90U3lzdGVtU3RhdHVzOiBCb3RTeXN0ZW1TdGF0dXM7IC8vIE5ldyBzdGF0ZSBmb3IgYm90IGxpZmVjeWNsZVxuICBzdGFibGVjb2luQmFsYW5jZTogbnVtYmVyOyAvLyBGb3IgU3RhYmxlY29pbiBTd2FwIG1vZGVcbn1cblxuLy8gQWRkIHRoaXMgZnVuY3Rpb24gdG8gY2FsY3VsYXRlIHRoZSBpbml0aWFsIG1hcmtldCBwcmljZVxuY29uc3QgY2FsY3VsYXRlSW5pdGlhbE1hcmtldFByaWNlID0gKGNvbmZpZzogVHJhZGluZ0NvbmZpZyk6IG51bWJlciA9PiB7XG4gIC8vIFJldHVybiAwIGlmIGVpdGhlciBjcnlwdG8gaXMgbm90IHNlbGVjdGVkXG4gIGlmICghY29uZmlnLmNyeXB0bzEgfHwgIWNvbmZpZy5jcnlwdG8yKSB7XG4gICAgcmV0dXJuIDA7XG4gIH1cbiAgLy8gRGVmYXVsdCBmYWxsYmFjayB2YWx1ZSBmb3IgdmFsaWQgcGFpcnNcbiAgcmV0dXJuIGNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UoY29uZmlnKTtcbn07XG5cbi8vIEVuaGFuY2VkIEFQSSBmdW5jdGlvbiB0byBnZXQgbWFya2V0IHByaWNlIGZvciBhbnkgdHJhZGluZyBwYWlyXG5jb25zdCBnZXRNYXJrZXRQcmljZUZyb21BUEkgPSBhc3luYyAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBSZXR1cm4gMCBpZiBlaXRoZXIgY3J5cHRvIGlzIG5vdCBzZWxlY3RlZFxuICAgIGlmICghY29uZmlnLmNyeXB0bzEgfHwgIWNvbmZpZy5jcnlwdG8yKSB7XG4gICAgICByZXR1cm4gMDtcbiAgICB9XG5cbiAgICAvLyBGb3IgU3RhYmxlY29pblN3YXAgbW9kZSwgd2UgbmVlZCB0byBjYWxjdWxhdGUgdGhlIHJhdGlvIHZpYSBzdGFibGVjb2luIHByaWNlc1xuICAgIGlmIChjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIiAmJiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gR2V0IGJvdGggY3J5cHRvIHByaWNlcyBpbiB0ZXJtcyBvZiB0aGUgcHJlZmVycmVkIHN0YWJsZWNvaW5cbiAgICAgICAgY29uc3QgY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA9IGF3YWl0IGdldFN0YWJsZWNvaW5FeGNoYW5nZVJhdGUoY29uZmlnLmNyeXB0bzEsIGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luKTtcbiAgICAgICAgY29uc3QgY3J5cHRvMlN0YWJsZWNvaW5QcmljZSA9IGF3YWl0IGdldFN0YWJsZWNvaW5FeGNoYW5nZVJhdGUoY29uZmlnLmNyeXB0bzIsIGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luKTtcblxuICAgICAgICBpZiAoY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA+IDAgJiYgY3J5cHRvMlN0YWJsZWNvaW5QcmljZSA+IDApIHtcbiAgICAgICAgICAvLyBDYWxjdWxhdGUgQ3J5cHRvMS9DcnlwdG8yIHJhdGlvIHZpYSBzdGFibGVjb2luXG4gICAgICAgICAgY29uc3QgcmF0aW8gPSBjcnlwdG8xU3RhYmxlY29pblByaWNlIC8gY3J5cHRvMlN0YWJsZWNvaW5QcmljZTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFN0YWJsZWNvaW5Td2FwIHByaWNlIHZpYSAke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufTogJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn0gPSAke3JhdGlvLnRvRml4ZWQoNil9YCk7XG4gICAgICAgICAgY29uc29sZS5sb2coYCAgICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59ID0gJHtjcnlwdG8xU3RhYmxlY29pblByaWNlfWApO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGAgICAke2NvbmZpZy5jcnlwdG8yfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufSA9ICR7Y3J5cHRvMlN0YWJsZWNvaW5QcmljZX1gKTtcbiAgICAgICAgICByZXR1cm4gcmF0aW87XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKHN0YWJsZWNvaW5FcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ1N0YWJsZWNvaW4gcHJpY2UgY2FsY3VsYXRpb24gZmFpbGVkLCBmYWxsaW5nIGJhY2sgdG8gZGlyZWN0IHBhaXIuLi4nLCBzdGFibGVjb2luRXJyb3IpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZvciBTaW1wbGVTcG90IG1vZGUgb3IgZmFsbGJhY2s6IHRyeSBkaXJlY3QgcGFpciBmZXRjaGluZ1xuICAgIGNvbnN0IHN5bWJvbCA9IGAke2NvbmZpZy5jcnlwdG8xfSR7Y29uZmlnLmNyeXB0bzJ9YC50b1VwcGVyQ2FzZSgpO1xuXG4gICAgLy8gRmlyc3QgdHJ5IEJpbmFuY2UgQVBJXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHBzOi8vYXBpLmJpbmFuY2UuY29tL2FwaS92My90aWNrZXIvcHJpY2U/c3ltYm9sPSR7c3ltYm9sfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnN0IHByaWNlID0gcGFyc2VGbG9hdChkYXRhLnByaWNlKTtcbiAgICAgICAgaWYgKHByaWNlID4gMCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgUHJpY2UgZmV0Y2hlZCBmcm9tIEJpbmFuY2U6ICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ID0gJHtwcmljZX1gKTtcbiAgICAgICAgICByZXR1cm4gcHJpY2U7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChiaW5hbmNlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignQmluYW5jZSBBUEkgZmFpbGVkLCB0cnlpbmcgYWx0ZXJuYXRpdmUuLi4nLCBiaW5hbmNlRXJyb3IpO1xuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIHRvIENvaW5HZWNrbyBBUEkgZm9yIGJyb2FkZXIgcGFpciBzdXBwb3J0XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNyeXB0bzFJZCA9IGdldENvaW5HZWNrb0lkKGNvbmZpZy5jcnlwdG8xKTtcbiAgICAgIGNvbnN0IGNyeXB0bzJJZCA9IGdldENvaW5HZWNrb0lkKGNvbmZpZy5jcnlwdG8yKTtcblxuICAgICAgLy8gRm9yIGJvdGggbW9kZXMsIHdlIHdhbnQgQ3J5cHRvMS9DcnlwdG8yIHJhdGlvXG4gICAgICBpZiAoY3J5cHRvMUlkICYmIGNyeXB0bzJJZCkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS5jb2luZ2Vja28uY29tL2FwaS92My9zaW1wbGUvcHJpY2U/aWRzPSR7Y3J5cHRvMUlkfSZ2c19jdXJyZW5jaWVzPSR7Y3J5cHRvMklkfWApO1xuICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgIGNvbnN0IHByaWNlID0gZGF0YVtjcnlwdG8xSWRdPy5bY3J5cHRvMklkXTtcbiAgICAgICAgICBpZiAocHJpY2UgPiAwKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFByaWNlIGZldGNoZWQgZnJvbSBDb2luR2Vja286ICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ID0gJHtwcmljZX1gKTtcbiAgICAgICAgICAgIHJldHVybiBwcmljZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChnZWNrb0Vycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ0NvaW5HZWNrbyBBUEkgZmFpbGVkLCB1c2luZyBtb2NrIHByaWNlLi4uJywgZ2Vja29FcnJvcik7XG4gICAgfVxuXG4gICAgLy8gRmluYWwgZmFsbGJhY2sgdG8gbW9jayBwcmljZVxuICAgIGNvbnN0IG1vY2tQcmljZSA9IGNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UoY29uZmlnKTtcbiAgICBjb25zb2xlLmxvZyhg4pqg77iPIFVzaW5nIG1vY2sgcHJpY2U6ICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ID0gJHttb2NrUHJpY2V9YCk7XG4gICAgcmV0dXJuIG1vY2tQcmljZTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1hcmtldCBwcmljZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIGNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UoY29uZmlnKTtcbiAgfVxufTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIG1hcCBjcnlwdG8gc3ltYm9scyB0byBDb2luR2Vja28gSURzXG5jb25zdCBnZXRDb2luR2Vja29JZCA9IChzeW1ib2w6IHN0cmluZyk6IHN0cmluZyB8IG51bGwgPT4ge1xuICBjb25zdCBtYXBwaW5nOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgICdCVEMnOiAnYml0Y29pbicsXG4gICAgJ0VUSCc6ICdldGhlcmV1bScsXG4gICAgJ1NPTCc6ICdzb2xhbmEnLFxuICAgICdBREEnOiAnY2FyZGFubycsXG4gICAgJ0RPVCc6ICdwb2xrYWRvdCcsXG4gICAgJ01BVElDJzogJ21hdGljLW5ldHdvcmsnLFxuICAgICdBVkFYJzogJ2F2YWxhbmNoZS0yJyxcbiAgICAnTElOSyc6ICdjaGFpbmxpbmsnLFxuICAgICdVTkknOiAndW5pc3dhcCcsXG4gICAgJ1VTRFQnOiAndGV0aGVyJyxcbiAgICAnVVNEQyc6ICd1c2QtY29pbicsXG4gICAgJ0JVU0QnOiAnYmluYW5jZS11c2QnLFxuICAgICdEQUknOiAnZGFpJ1xuICB9O1xuICByZXR1cm4gbWFwcGluZ1tzeW1ib2wudG9VcHBlckNhc2UoKV0gfHwgbnVsbDtcbn07XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgc3RhYmxlY29pbiBleGNoYW5nZSByYXRlcyBmb3IgcmVhbCBtYXJrZXQgZGF0YVxuY29uc3QgZ2V0U3RhYmxlY29pbkV4Y2hhbmdlUmF0ZSA9IGFzeW5jIChjcnlwdG86IHN0cmluZywgc3RhYmxlY29pbjogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBGb3Igc3RhYmxlY29pbi10by1zdGFibGVjb2luLCBhc3N1bWUgMToxIHJhdGVcbiAgICBpZiAoY3J5cHRvLnRvVXBwZXJDYXNlKCkgPT09IHN0YWJsZWNvaW4udG9VcHBlckNhc2UoKSkgcmV0dXJuIDEuMDtcblxuICAgIC8vIEZpcnN0IHRyeSBCaW5hbmNlIEFQSSBmb3IgZGlyZWN0IHBhaXJcbiAgICBjb25zdCBiaW5hbmNlU3ltYm9sID0gYCR7Y3J5cHRvLnRvVXBwZXJDYXNlKCl9JHtzdGFibGVjb2luLnRvVXBwZXJDYXNlKCl9YDtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9hcGkuYmluYW5jZS5jb20vYXBpL3YzL3RpY2tlci9wcmljZT9zeW1ib2w9JHtiaW5hbmNlU3ltYm9sfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnN0IHByaWNlID0gcGFyc2VGbG9hdChkYXRhLnByaWNlKTtcbiAgICAgICAgaWYgKHByaWNlID4gMCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgU3RhYmxlY29pbiByYXRlIGZyb20gQmluYW5jZTogJHtjcnlwdG99LyR7c3RhYmxlY29pbn0gPSAke3ByaWNlfWApO1xuICAgICAgICAgIHJldHVybiBwcmljZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGJpbmFuY2VFcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKGBCaW5hbmNlIEFQSSBmYWlsZWQgZm9yICR7YmluYW5jZVN5bWJvbH0sIHRyeWluZyBDb2luR2Vja28uLi5gKTtcbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBDb2luR2Vja28gQVBJXG4gICAgY29uc3QgY3J5cHRvSWQgPSBnZXRDb2luR2Vja29JZChjcnlwdG8pO1xuICAgIGNvbnN0IHN0YWJsZWNvaW5JZCA9IGdldENvaW5HZWNrb0lkKHN0YWJsZWNvaW4pO1xuXG4gICAgaWYgKGNyeXB0b0lkICYmIHN0YWJsZWNvaW5JZCkge1xuICAgICAgLy8gVHJ5IGRpcmVjdCBjb252ZXJzaW9uXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS5jb2luZ2Vja28uY29tL2FwaS92My9zaW1wbGUvcHJpY2U/aWRzPSR7Y3J5cHRvSWR9JnZzX2N1cnJlbmNpZXM9JHtzdGFibGVjb2luSWR9YCk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgY29uc3QgcmF0ZSA9IGRhdGFbY3J5cHRvSWRdPy5bc3RhYmxlY29pbklkXTtcbiAgICAgICAgaWYgKHJhdGUgPiAwKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYOKchSBTdGFibGVjb2luIHJhdGUgZnJvbSBDb2luR2Vja286ICR7Y3J5cHRvfS8ke3N0YWJsZWNvaW59ID0gJHtyYXRlfWApO1xuICAgICAgICAgIHJldHVybiByYXRlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRmluYWwgZmFsbGJhY2s6IGNhbGN1bGF0ZSB2aWEgVVNEIHByaWNlc1xuICAgIGNvbnN0IGNyeXB0b1VTRFByaWNlID0gZ2V0VVNEUHJpY2UoY3J5cHRvKTtcbiAgICBjb25zdCBzdGFibGVjb2luVVNEUHJpY2UgPSBnZXRVU0RQcmljZShzdGFibGVjb2luKTtcbiAgICBjb25zdCByYXRlID0gY3J5cHRvVVNEUHJpY2UgLyBzdGFibGVjb2luVVNEUHJpY2U7XG5cbiAgICBjb25zb2xlLmxvZyhg4pqg77iPIEZhbGxiYWNrIHN0YWJsZWNvaW4gcmF0ZTogJHtjcnlwdG99LyR7c3RhYmxlY29pbn0gPSAke3JhdGV9ICh2aWEgVVNEKWApO1xuICAgIHJldHVybiByYXRlO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc3RhYmxlY29pbiBleGNoYW5nZSByYXRlOicsIGVycm9yKTtcbiAgICAvLyBGaW5hbCBmYWxsYmFja1xuICAgIGNvbnN0IGNyeXB0b1VTRFByaWNlID0gZ2V0VVNEUHJpY2UoY3J5cHRvKTtcbiAgICBjb25zdCBzdGFibGVjb2luVVNEUHJpY2UgPSBnZXRVU0RQcmljZShzdGFibGVjb2luKTtcbiAgICByZXR1cm4gY3J5cHRvVVNEUHJpY2UgLyBzdGFibGVjb2luVVNEUHJpY2U7XG4gIH1cbn07XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgVVNEIHByaWNlIChleHRyYWN0ZWQgZnJvbSBjYWxjdWxhdGVGYWxsYmFja01hcmtldFByaWNlKVxuY29uc3QgZ2V0VVNEUHJpY2UgPSAoY3J5cHRvOiBzdHJpbmcpOiBudW1iZXIgPT4ge1xuICBjb25zdCB1c2RQcmljZXM6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XG4gICAgLy8gTWFqb3IgY3J5cHRvY3VycmVuY2llc1xuICAgICdCVEMnOiAxMDgwMDAsXG4gICAgJ0VUSCc6IDIxMDAsXG4gICAgJ1NPTCc6IDI0MCxcbiAgICAnQURBJzogMS4yLFxuICAgICdET0dFJzogMC40LFxuICAgICdMSU5LJzogMjUsXG4gICAgJ01BVElDJzogMC41LFxuICAgICdET1QnOiA4LFxuICAgICdBVkFYJzogNDUsXG4gICAgJ1NISUInOiAwLjAwMDAzMCxcbiAgICAnWFJQJzogMi41LFxuICAgICdMVEMnOiAxMTAsXG4gICAgJ0JDSCc6IDUwMCxcblxuICAgIC8vIERlRmkgdG9rZW5zXG4gICAgJ1VOSSc6IDE1LFxuICAgICdBQVZFJzogMTgwLFxuICAgICdNS1InOiAxODAwLFxuICAgICdTTlgnOiAzLjUsXG4gICAgJ0NPTVAnOiA4NSxcbiAgICAnWUZJJzogODUwMCxcbiAgICAnU1VTSEknOiAyLjEsXG4gICAgJzFJTkNIJzogMC42NSxcbiAgICAnQ1JWJzogMC44NSxcbiAgICAnVU1BJzogMy4yLFxuXG4gICAgLy8gTGF5ZXIgMSBibG9ja2NoYWluc1xuICAgICdBVE9NJzogMTIsXG4gICAgJ05FQVInOiA2LjUsXG4gICAgJ0FMR08nOiAwLjM1LFxuICAgICdJQ1AnOiAxNCxcbiAgICAnSEJBUic6IDAuMjgsXG4gICAgJ0FQVCc6IDEyLjUsXG4gICAgJ1RPTic6IDUuOCxcbiAgICAnRlRNJzogMC45NSxcbiAgICAnT05FJzogMC4wMjUsXG5cbiAgICAvLyBPdGhlciBwb3B1bGFyIHRva2Vuc1xuICAgICdGSUwnOiA4LjUsXG4gICAgJ1RSWCc6IDAuMjUsXG4gICAgJ0VUQyc6IDM1LFxuICAgICdWRVQnOiAwLjA1NSxcbiAgICAnUU5UJzogMTI1LFxuICAgICdMRE8nOiAyLjgsXG4gICAgJ0NSTyc6IDAuMTgsXG4gICAgJ0xVTkMnOiAwLjAwMDE1LFxuXG4gICAgLy8gR2FtaW5nICYgTWV0YXZlcnNlXG4gICAgJ01BTkEnOiAwLjg1LFxuICAgICdTQU5EJzogMC43NSxcbiAgICAnQVhTJzogOC41LFxuICAgICdFTkonOiAwLjQ1LFxuICAgICdDSFonOiAwLjEyLFxuXG4gICAgLy8gSW5mcmFzdHJ1Y3R1cmUgJiBVdGlsaXR5XG4gICAgJ1RIRVRBJzogMi4xLFxuICAgICdGTE9XJzogMS4yLFxuICAgICdYVFonOiAxLjgsXG4gICAgJ0VPUyc6IDEuMSxcbiAgICAnR1JUJzogMC4yOCxcbiAgICAnQkFUJzogMC4zNSxcblxuICAgIC8vIFByaXZhY3kgY29pbnNcbiAgICAnWkVDJzogNDUsXG4gICAgJ0RBU0gnOiAzNSxcblxuICAgIC8vIERFWCB0b2tlbnNcbiAgICAnTFJDJzogMC40NSxcbiAgICAnWlJYJzogMC42NSxcbiAgICAnS05DJzogMC44NSxcblxuICAgIC8vIE90aGVyIHRva2Vuc1xuICAgICdSRU4nOiAwLjE1LFxuICAgICdCQU5EJzogMi41LFxuICAgICdTVE9SSic6IDAuODUsXG4gICAgJ05NUic6IDI1LFxuICAgICdBTlQnOiA4LjUsXG4gICAgJ0JOVCc6IDAuOTUsXG4gICAgJ01MTic6IDM1LFxuICAgICdSRVAnOiAxNSxcblxuICAgIC8vIFNtYWxsZXIgY2FwIHRva2Vuc1xuICAgICdJT1RYJzogMC4wNjUsXG4gICAgJ1pJTCc6IDAuMDQ1LFxuICAgICdJQ1gnOiAwLjM1LFxuICAgICdRVFVNJzogNC41LFxuICAgICdPTlQnOiAwLjQ1LFxuICAgICdXQVZFUyc6IDMuMixcbiAgICAnTFNLJzogMS44LFxuICAgICdOQU5PJzogMS41LFxuICAgICdTQyc6IDAuMDA4LFxuICAgICdER0InOiAwLjAyNSxcbiAgICAnUlZOJzogMC4wMzUsXG4gICAgJ0JUVCc6IDAuMDAwMDAxNSxcbiAgICAnV0lOJzogMC4wMDAxNSxcbiAgICAnSE9UJzogMC4wMDM1LFxuICAgICdERU5UJzogMC4wMDE4LFxuICAgICdOUFhTJzogMC4wMDA4NSxcbiAgICAnRlVOJzogMC4wMDg1LFxuICAgICdDRUxSJzogMC4wMjUsXG5cbiAgICAvLyBTdGFibGVjb2luc1xuICAgICdVU0RUJzogMS4wLFxuICAgICdVU0RDJzogMS4wLFxuICAgICdGRFVTRCc6IDEuMCxcbiAgICAnQlVTRCc6IDEuMCxcbiAgICAnREFJJzogMS4wXG4gIH07XG4gIHJldHVybiB1c2RQcmljZXNbY3J5cHRvLnRvVXBwZXJDYXNlKCldIHx8IDEwMDtcbn07XG5cbi8vIEVuaGFuY2VkIGZhbGxiYWNrIGZ1bmN0aW9uIGZvciBtYXJrZXQgcHJpY2UgY2FsY3VsYXRpb24gc3VwcG9ydGluZyBhbGwgdHJhZGluZyBwYWlyc1xuY29uc3QgY2FsY3VsYXRlRmFsbGJhY2tNYXJrZXRQcmljZSA9IChjb25maWc6IFRyYWRpbmdDb25maWcpOiBudW1iZXIgPT4ge1xuICBjb25zdCBjcnlwdG8xVVNEUHJpY2UgPSBnZXRVU0RQcmljZShjb25maWcuY3J5cHRvMSk7XG4gIGNvbnN0IGNyeXB0bzJVU0RQcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8yKTtcblxuICBsZXQgYmFzZVByaWNlOiBudW1iZXI7XG5cbiAgaWYgKGNvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiKSB7XG4gICAgLy8gRm9yIHN0YWJsZWNvaW4gc3dhcCBtb2RlOiBDcnlwdG8xL0NyeXB0bzIgPSBDdXJyZW50IG1hcmtldCBQcmljZVxuICAgIC8vIFRoaXMgc2hvd3MgaG93IG1hbnkgdW5pdHMgb2YgQ3J5cHRvMiBlcXVhbHMgMSB1bml0IG9mIENyeXB0bzFcbiAgICBiYXNlUHJpY2UgPSBjcnlwdG8xVVNEUHJpY2UgLyBjcnlwdG8yVVNEUHJpY2U7XG5cbiAgICBjb25zb2xlLmxvZyhg8J+TiiBTdGFibGVjb2luU3dhcCBwcmljZSBjYWxjdWxhdGlvbjogJHtjb25maWcuY3J5cHRvMX0gKCQke2NyeXB0bzFVU0RQcmljZX0pIC8gJHtjb25maWcuY3J5cHRvMn0gKCQke2NyeXB0bzJVU0RQcmljZX0pID0gJHtiYXNlUHJpY2UudG9GaXhlZCg2KX1gKTtcbiAgfSBlbHNlIHtcbiAgICAvLyBTaW1wbGUgU3BvdCBtb2RlOiBDYWxjdWxhdGUgdGhlIHJhdGlvOiBob3cgbWFueSB1bml0cyBvZiBjcnlwdG8yID0gMSB1bml0IG9mIGNyeXB0bzFcbiAgICBiYXNlUHJpY2UgPSBjcnlwdG8xVVNEUHJpY2UgLyBjcnlwdG8yVVNEUHJpY2U7XG5cbiAgICBjb25zb2xlLmxvZyhg8J+TiiBTaW1wbGVTcG90IHByaWNlIGNhbGN1bGF0aW9uOiAke2NvbmZpZy5jcnlwdG8xfSAoJCR7Y3J5cHRvMVVTRFByaWNlfSkgLyAke2NvbmZpZy5jcnlwdG8yfSAoJCR7Y3J5cHRvMlVTRFByaWNlfSkgPSAke2Jhc2VQcmljZS50b0ZpeGVkKDYpfWApO1xuICB9XG5cbiAgLy8gQWRkIHNtYWxsIHJhbmRvbSBmbHVjdHVhdGlvblxuICBjb25zdCBmbHVjdHVhdGlvbiA9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDAuMDI7IC8vIMKxMSVcbiAgY29uc3QgZmluYWxQcmljZSA9IGJhc2VQcmljZSAqICgxICsgZmx1Y3R1YXRpb24pO1xuXG4gIHJldHVybiBmaW5hbFByaWNlO1xufTtcblxuLy8gRHVwbGljYXRlIFRyYWRpbmdBY3Rpb24gdHlwZSByZW1vdmVkIC0gdXNpbmcgdGhlIG9uZSBkZWZpbmVkIGFib3ZlXG5cbmNvbnN0IGluaXRpYWxCYXNlQ29uZmlnOiBUcmFkaW5nQ29uZmlnID0ge1xuICB0cmFkaW5nTW9kZTogXCJTaW1wbGVTcG90XCIsXG4gIGNyeXB0bzE6IFwiXCIsIC8vIFN0YXJ0IHdpdGggZW1wdHkgY3J5cHRvMVxuICBjcnlwdG8yOiBcIlwiLCAvLyBTdGFydCB3aXRoIGVtcHR5IGNyeXB0bzJcbiAgYmFzZUJpZDogMTAwLFxuICBtdWx0aXBsaWVyOiAxLjAwNSxcbiAgbnVtRGlnaXRzOiA0LFxuICBzbGlwcGFnZVBlcmNlbnQ6IDAuMiwgLy8gRGVmYXVsdCAwLjIlIHNsaXBwYWdlIHJhbmdlXG4gIGluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQ6IDUwLFxuICBpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50OiA1MCxcbiAgcHJlZmVycmVkU3RhYmxlY29pbjogQVZBSUxBQkxFX1NUQUJMRUNPSU5TWzBdLFxufTtcblxuLy8gSW5pdGlhbCBzdGF0ZSB3aXRoIGRlZmF1bHQgYmFsYW5jZXMgKHdpbGwgYmUgdXBkYXRlZCBieSBnbG9iYWwgYmFsYW5jZXMgbGF0ZXIpXG5jb25zdCBpbml0aWFsVHJhZGluZ1N0YXRlOiBUcmFkaW5nU3RhdGUgPSB7XG4gIGNvbmZpZzogaW5pdGlhbEJhc2VDb25maWcsXG4gIHRhcmdldFByaWNlUm93czogW10sXG4gIG9yZGVySGlzdG9yeTogW10sXG4gIGFwcFNldHRpbmdzOiBERUZBVUxUX0FQUF9TRVRUSU5HUyxcbiAgY3VycmVudE1hcmtldFByaWNlOiBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UoaW5pdGlhbEJhc2VDb25maWcpLFxuICBib3RTeXN0ZW1TdGF0dXM6ICdTdG9wcGVkJyxcbiAgY3J5cHRvMUJhbGFuY2U6IDEwLCAvLyBEZWZhdWx0IGJhbGFuY2VcbiAgY3J5cHRvMkJhbGFuY2U6IDEwMDAwMCwgLy8gRGVmYXVsdCBiYWxhbmNlXG4gIHN0YWJsZWNvaW5CYWxhbmNlOiAwLCAvLyBEZWZhdWx0IGJhbGFuY2VcbiAgYmFja2VuZFN0YXR1czogJ3Vua25vd24nLFxufTtcblxuY29uc3QgbGFzdEFjdGlvblRpbWVzdGFtcFBlckNvdW50ZXIgPSBuZXcgTWFwPG51bWJlciwgbnVtYmVyPigpO1xuXG4vLyBMb2NhbFN0b3JhZ2UgcGVyc2lzdGVuY2UgZnVuY3Rpb25zXG5jb25zdCBTVE9SQUdFX0tFWSA9ICdwbHV0b190cmFkaW5nX3N0YXRlJztcbmNvbnN0IEdMT0JBTF9CQUxBTkNFX0tFWSA9ICdwbHV0b19nbG9iYWxfYmFsYW5jZXMnO1xuY29uc3QgQk9UX1NUQVRVU19LRVkgPSAncGx1dG9fYm90X3N0YXR1cyc7XG5cbmNvbnN0IHNhdmVTdGF0ZVRvTG9jYWxTdG9yYWdlID0gKHN0YXRlOiBUcmFkaW5nU3RhdGUpID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IHN0YXRlVG9TYXZlID0ge1xuICAgICAgICBjb25maWc6IHN0YXRlLmNvbmZpZyxcbiAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgIG9yZGVySGlzdG9yeTogc3RhdGUub3JkZXJIaXN0b3J5LFxuICAgICAgICBhcHBTZXR0aW5nczogc3RhdGUuYXBwU2V0dGluZ3MsXG4gICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogc3RhdGUuY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICBjcnlwdG8xQmFsYW5jZTogc3RhdGUuY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgIGNyeXB0bzJCYWxhbmNlOiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICBib3RTeXN0ZW1TdGF0dXM6IHN0YXRlLmJvdFN5c3RlbVN0YXR1cyxcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgICB9O1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oU1RPUkFHRV9LRVksIEpTT04uc3RyaW5naWZ5KHN0YXRlVG9TYXZlKSk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIHN0YXRlIHRvIGxvY2FsU3RvcmFnZTonLCBlcnJvcik7XG4gIH1cbn07XG5cbmNvbnN0IGxvYWRTdGF0ZUZyb21Mb2NhbFN0b3JhZ2UgPSAoKTogUGFydGlhbDxUcmFkaW5nU3RhdGU+IHwgbnVsbCA9PiB7XG4gIHRyeSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBzYXZlZFN0YXRlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oU1RPUkFHRV9LRVkpO1xuICAgICAgaWYgKHNhdmVkU3RhdGUpIHtcbiAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShzYXZlZFN0YXRlKTtcbiAgICAgICAgLy8gQ2hlY2sgaWYgc3RhdGUgaXMgbm90IHRvbyBvbGQgKDI0IGhvdXJzKVxuICAgICAgICBpZiAocGFyc2VkLnRpbWVzdGFtcCAmJiAoRGF0ZS5ub3coKSAtIHBhcnNlZC50aW1lc3RhbXApIDwgMjQgKiA2MCAqIDYwICogMTAwMCkge1xuICAgICAgICAgIHJldHVybiBwYXJzZWQ7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgc3RhdGUgZnJvbSBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xuICB9XG4gIHJldHVybiBudWxsO1xufTtcblxuLy8gR2xvYmFsIGJhbGFuY2UgcGVyc2lzdGVuY2UgZnVuY3Rpb25zXG5jb25zdCBzYXZlR2xvYmFsQmFsYW5jZXMgPSAoY3J5cHRvMUJhbGFuY2U6IG51bWJlciwgY3J5cHRvMkJhbGFuY2U6IG51bWJlciwgc3RhYmxlY29pbkJhbGFuY2U6IG51bWJlcikgPT4ge1xuICB0cnkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgYmFsYW5jZXMgPSB7XG4gICAgICAgIGNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKEdMT0JBTF9CQUxBTkNFX0tFWSwgSlNPTi5zdHJpbmdpZnkoYmFsYW5jZXMpKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5KwIEdsb2JhbCBiYWxhbmNlcyBzYXZlZDonLCBiYWxhbmNlcyk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGdsb2JhbCBiYWxhbmNlczonLCBlcnJvcik7XG4gIH1cbn07XG5cbmNvbnN0IGxvYWRHbG9iYWxCYWxhbmNlcyA9ICgpID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IHNhdmVkQmFsYW5jZXMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShHTE9CQUxfQkFMQU5DRV9LRVkpO1xuICAgICAgaWYgKHNhdmVkQmFsYW5jZXMpIHtcbiAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShzYXZlZEJhbGFuY2VzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/CfkrAgR2xvYmFsIGJhbGFuY2VzIGxvYWRlZDonLCBwYXJzZWQpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGNyeXB0bzFCYWxhbmNlOiBwYXJzZWQuY3J5cHRvMUJhbGFuY2UgfHwgMTAsXG4gICAgICAgICAgY3J5cHRvMkJhbGFuY2U6IHBhcnNlZC5jcnlwdG8yQmFsYW5jZSB8fCAxMDAwMDAsXG4gICAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IHBhcnNlZC5zdGFibGVjb2luQmFsYW5jZSB8fCAwXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGdsb2JhbCBiYWxhbmNlczonLCBlcnJvcik7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBjcnlwdG8xQmFsYW5jZTogMTAsXG4gICAgY3J5cHRvMkJhbGFuY2U6IDEwMDAwMCxcbiAgICBzdGFibGVjb2luQmFsYW5jZTogMFxuICB9O1xufTtcblxuY29uc3Qgc2F2ZUJvdFN0YXR1cyA9IChzdGF0dXM6IEJvdFN5c3RlbVN0YXR1cykgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICB0cnkge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oQk9UX1NUQVRVU19LRVksIHN0YXR1cyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGJvdCBzdGF0dXMgdG8gbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcbiAgICB9XG4gIH1cbn07XG5cbmNvbnN0IGxvYWRCb3RTdGF0dXMgPSAoKTogQm90U3lzdGVtU3RhdHVzID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNhdmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oQk9UX1NUQVRVU19LRVkpO1xuICAgICAgaWYgKHNhdmVkICYmIChzYXZlZCA9PT0gJ1N0b3BwZWQnIHx8IHNhdmVkID09PSAnV2FybWluZ1VwJyB8fCBzYXZlZCA9PT0gJ1J1bm5pbmcnKSkge1xuICAgICAgICByZXR1cm4gc2F2ZWQgYXMgQm90U3lzdGVtU3RhdHVzO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBib3Qgc3RhdHVzIGZyb20gbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvLyBSZXR1cm4gZGVmYXVsdCBzdGF0dXMgaWYgbG9hZGluZyBmYWlsc1xuICByZXR1cm4gJ1N0b3BwZWQnO1xufTtcblxuLy8gQ3JlYXRlIGluaXRpYWwgc3RhdGUgd2l0aCBnbG9iYWwgYmFsYW5jZXMgLSBjYWxsZWQgYWZ0ZXIgbG9hZEdsb2JhbEJhbGFuY2VzIGlzIGRlZmluZWRcbmNvbnN0IGNyZWF0ZUluaXRpYWxUcmFkaW5nU3RhdGUgPSAoKTogVHJhZGluZ1N0YXRlID0+IHtcbiAgY29uc3QgZ2xvYmFsQmFsYW5jZXMgPSBsb2FkR2xvYmFsQmFsYW5jZXMoKTtcbiAgcmV0dXJuIHtcbiAgICBjb25maWc6IGluaXRpYWxCYXNlQ29uZmlnLFxuICAgIHRhcmdldFByaWNlUm93czogW10sXG4gICAgb3JkZXJIaXN0b3J5OiBbXSxcbiAgICBhcHBTZXR0aW5nczogREVGQVVMVF9BUFBfU0VUVElOR1MsXG4gICAgY3VycmVudE1hcmtldFByaWNlOiBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UoaW5pdGlhbEJhc2VDb25maWcpLFxuICAgIGJvdFN5c3RlbVN0YXR1czogJ1N0b3BwZWQnLFxuICAgIGNyeXB0bzFCYWxhbmNlOiBnbG9iYWxCYWxhbmNlcy5jcnlwdG8xQmFsYW5jZSxcbiAgICBjcnlwdG8yQmFsYW5jZTogZ2xvYmFsQmFsYW5jZXMuY3J5cHRvMkJhbGFuY2UsXG4gICAgc3RhYmxlY29pbkJhbGFuY2U6IGdsb2JhbEJhbGFuY2VzLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgIGJhY2tlbmRTdGF0dXM6ICd1bmtub3duJyxcbiAgfTtcbn07XG5cbmNvbnN0IHRyYWRpbmdSZWR1Y2VyID0gKHN0YXRlOiBUcmFkaW5nU3RhdGUsIGFjdGlvbjogVHJhZGluZ0FjdGlvbik6IFRyYWRpbmdTdGF0ZSA9PiB7XG4gIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICBjYXNlICdTRVRfQ09ORklHJzpcbiAgICAgIGNvbnN0IG5ld0NvbmZpZyA9IHsgLi4uc3RhdGUuY29uZmlnLCAuLi5hY3Rpb24ucGF5bG9hZCB9O1xuICAgICAgLy8gSWYgdHJhZGluZyBwYWlyIGNoYW5nZXMsIHJlc2V0IG1hcmtldCBwcmljZSAoaXQgd2lsbCBiZSByZS1jYWxjdWxhdGVkIGJ5IGVmZmVjdClcbiAgICAgIGlmIChhY3Rpb24ucGF5bG9hZC5jcnlwdG8xIHx8IGFjdGlvbi5wYXlsb2FkLmNyeXB0bzIpIHtcbiAgICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGNvbmZpZzogbmV3Q29uZmlnLCBjdXJyZW50TWFya2V0UHJpY2U6IGNhbGN1bGF0ZUluaXRpYWxNYXJrZXRQcmljZShuZXdDb25maWcpIH07XG4gICAgICB9XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgY29uZmlnOiBuZXdDb25maWcgfTtcbiAgICBjYXNlICdTRVRfVEFSR0VUX1BSSUNFX1JPV1MnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIHRhcmdldFByaWNlUm93czogYWN0aW9uLnBheWxvYWQuc29ydCgoYTogVGFyZ2V0UHJpY2VSb3csIGI6IFRhcmdldFByaWNlUm93KSA9PiBhLnRhcmdldFByaWNlIC0gYi50YXJnZXRQcmljZSkubWFwKChyb3c6IFRhcmdldFByaWNlUm93LCBpbmRleDogbnVtYmVyKSA9PiAoey4uLnJvdywgY291bnRlcjogaW5kZXggKyAxfSkpIH07XG4gICAgY2FzZSAnQUREX1RBUkdFVF9QUklDRV9ST1cnOiB7XG4gICAgICBjb25zdCBuZXdSb3dzID0gWy4uLnN0YXRlLnRhcmdldFByaWNlUm93cywgYWN0aW9uLnBheWxvYWRdLnNvcnQoKGE6IFRhcmdldFByaWNlUm93LCBiOiBUYXJnZXRQcmljZVJvdykgPT4gYS50YXJnZXRQcmljZSAtIGIudGFyZ2V0UHJpY2UpLm1hcCgocm93OiBUYXJnZXRQcmljZVJvdywgaW5kZXg6IG51bWJlcikgPT4gKHsuLi5yb3csIGNvdW50ZXI6IGluZGV4ICsgMX0pKTtcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCB0YXJnZXRQcmljZVJvd3M6IG5ld1Jvd3MgfTtcbiAgICB9XG4gICAgY2FzZSAnVVBEQVRFX1RBUkdFVF9QUklDRV9ST1cnOiB7XG4gICAgICBjb25zdCB1cGRhdGVkUm93cyA9IHN0YXRlLnRhcmdldFByaWNlUm93cy5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3cpID0+XG4gICAgICAgIHJvdy5pZCA9PT0gYWN0aW9uLnBheWxvYWQuaWQgPyBhY3Rpb24ucGF5bG9hZCA6IHJvd1xuICAgICAgKS5zb3J0KChhOiBUYXJnZXRQcmljZVJvdywgYjogVGFyZ2V0UHJpY2VSb3cpID0+IGEudGFyZ2V0UHJpY2UgLSBiLnRhcmdldFByaWNlKS5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3csIGluZGV4OiBudW1iZXIpID0+ICh7Li4ucm93LCBjb3VudGVyOiBpbmRleCArIDF9KSk7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgdGFyZ2V0UHJpY2VSb3dzOiB1cGRhdGVkUm93cyB9O1xuICAgIH1cbiAgICBjYXNlICdSRU1PVkVfVEFSR0VUX1BSSUNFX1JPVyc6IHtcbiAgICAgIGNvbnN0IGZpbHRlcmVkUm93cyA9IHN0YXRlLnRhcmdldFByaWNlUm93cy5maWx0ZXIoKHJvdzogVGFyZ2V0UHJpY2VSb3cpID0+IHJvdy5pZCAhPT0gYWN0aW9uLnBheWxvYWQpLnNvcnQoKGE6IFRhcmdldFByaWNlUm93LCBiOiBUYXJnZXRQcmljZVJvdykgPT4gYS50YXJnZXRQcmljZSAtIGIudGFyZ2V0UHJpY2UpLm1hcCgocm93OiBUYXJnZXRQcmljZVJvdywgaW5kZXg6IG51bWJlcikgPT4gKHsuLi5yb3csIGNvdW50ZXI6IGluZGV4ICsgMX0pKTtcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCB0YXJnZXRQcmljZVJvd3M6IGZpbHRlcmVkUm93cyB9O1xuICAgIH1cbiAgICBjYXNlICdBRERfT1JERVJfSElTVE9SWV9FTlRSWSc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgb3JkZXJIaXN0b3J5OiBbYWN0aW9uLnBheWxvYWQsIC4uLnN0YXRlLm9yZGVySGlzdG9yeV0gfTtcbiAgICBjYXNlICdDTEVBUl9PUkRFUl9ISVNUT1JZJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBvcmRlckhpc3Rvcnk6IFtdIH07XG4gICAgY2FzZSAnU0VUX0FQUF9TRVRUSU5HUyc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgYXBwU2V0dGluZ3M6IHsgLi4uc3RhdGUuYXBwU2V0dGluZ3MsIC4uLmFjdGlvbi5wYXlsb2FkIH0gfTtcbiAgICBjYXNlICdTRVRfTUFSS0VUX1BSSUNFJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBjdXJyZW50TWFya2V0UHJpY2U6IGFjdGlvbi5wYXlsb2FkIH07XG4gICAgY2FzZSAnRkxVQ1RVQVRFX01BUktFVF9QUklDRSc6IHtcbiAgICAgIGlmIChzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UgPD0gMCkgcmV0dXJuIHN0YXRlO1xuICAgICAgLy8gTW9yZSByZWFsaXN0aWMgZmx1Y3R1YXRpb246IHNtYWxsZXIsIG1vcmUgZnJlcXVlbnQgY2hhbmdlc1xuICAgICAgY29uc3QgZmx1Y3R1YXRpb25GYWN0b3IgPSAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAwLjAwNjsgLy8gQXBwcm94ICsvLSAwLjMlIHBlciB1cGRhdGVcbiAgICAgIGNvbnN0IG5ld1ByaWNlID0gc3RhdGUuY3VycmVudE1hcmtldFByaWNlICogKDEgKyBmbHVjdHVhdGlvbkZhY3Rvcik7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgY3VycmVudE1hcmtldFByaWNlOiBuZXdQcmljZSA+IDAgPyBuZXdQcmljZSA6IHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSB9O1xuICAgIH1cbiAgICAvLyBjYXNlICdTRVRfQk9UX1NUQVRVUyc6IC8vIFJlbW92ZWRcbiAgICBjYXNlICdTRVRfQkFMQU5DRVMnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIGNyeXB0bzFCYWxhbmNlOiBhY3Rpb24ucGF5bG9hZC5jcnlwdG8xLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuY3J5cHRvMixcbiAgICAgIH07XG4gICAgY2FzZSAnVVBEQVRFX0JBTEFOQ0VTJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBjcnlwdG8xQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuY3J5cHRvMSAhPT0gdW5kZWZpbmVkID8gYWN0aW9uLnBheWxvYWQuY3J5cHRvMSA6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuY3J5cHRvMiAhPT0gdW5kZWZpbmVkID8gYWN0aW9uLnBheWxvYWQuY3J5cHRvMiA6IHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuc3RhYmxlY29pbiAhPT0gdW5kZWZpbmVkID8gYWN0aW9uLnBheWxvYWQuc3RhYmxlY29pbiA6IHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgfTtcbiAgICBjYXNlICdVUERBVEVfU1RBQkxFQ09JTl9CQUxBTkNFJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogYWN0aW9uLnBheWxvYWQsXG4gICAgICB9O1xuICAgIGNhc2UgJ1JFU0VUX1NFU1NJT04nOlxuICAgICAgY29uc3QgY29uZmlnRm9yUmVzZXQgPSB7IC4uLnN0YXRlLmNvbmZpZyB9O1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uaW5pdGlhbFRyYWRpbmdTdGF0ZSxcbiAgICAgICAgY29uZmlnOiBjb25maWdGb3JSZXNldCxcbiAgICAgICAgYXBwU2V0dGluZ3M6IHsgLi4uc3RhdGUuYXBwU2V0dGluZ3MgfSxcbiAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UoY29uZmlnRm9yUmVzZXQpLFxuICAgICAgfTtcbiAgICBjYXNlICdTRVRfQkFDS0VORF9TVEFUVVMnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGJhY2tlbmRTdGF0dXM6IGFjdGlvbi5wYXlsb2FkIH07XG4gICAgY2FzZSAnU1lTVEVNX1NUQVJUX0JPVF9JTklUSUFURSc6XG4gICAgICAvLyBDb250aW51ZSBmcm9tIHByZXZpb3VzIHN0YXRlIGluc3RlYWQgb2YgcmVzZXR0aW5nXG4gICAgICBzYXZlQm90U3RhdHVzKCdXYXJtaW5nVXAnKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBib3RTeXN0ZW1TdGF0dXM6ICdXYXJtaW5nVXAnLFxuICAgICAgICAvLyBLZWVwIGV4aXN0aW5nIHRhcmdldFByaWNlUm93cyBhbmQgb3JkZXJIaXN0b3J5IC0gZG9uJ3QgcmVzZXRcbiAgICAgIH07XG4gICAgY2FzZSAnU1lTVEVNX0NPTVBMRVRFX1dBUk1VUCc6XG4gICAgICBzYXZlQm90U3RhdHVzKCdSdW5uaW5nJyk7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgYm90U3lzdGVtU3RhdHVzOiAnUnVubmluZycgfTtcbiAgICBjYXNlICdTWVNURU1fU1RPUF9CT1QnOlxuICAgICAgc2F2ZUJvdFN0YXR1cygnU3RvcHBlZCcpO1xuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGJvdFN5c3RlbVN0YXR1czogJ1N0b3BwZWQnIH07XG4gICAgY2FzZSAnU1lTVEVNX1JFU0VUX0JPVCc6XG4gICAgICAvLyBGcmVzaCBTdGFydDogQ2xlYXIgYWxsIHRhcmdldCBwcmljZSByb3dzIGFuZCBvcmRlciBoaXN0b3J5IGNvbXBsZXRlbHlcbiAgICAgIGxhc3RBY3Rpb25UaW1lc3RhbXBQZXJDb3VudGVyLmNsZWFyKCk7XG4gICAgICAvLyBDbGVhciBjdXJyZW50IHNlc3Npb24gdG8gZm9yY2UgbmV3IHNlc3Npb24gbmFtZSBnZW5lcmF0aW9uXG4gICAgICBjb25zdCBzZXNzaW9uTWFuYWdlciA9IFNlc3Npb25NYW5hZ2VyLmdldEluc3RhbmNlKCk7XG4gICAgICBzZXNzaW9uTWFuYWdlci5jbGVhckN1cnJlbnRTZXNzaW9uKCk7XG4gICAgICBzYXZlQm90U3RhdHVzKCdTdG9wcGVkJyk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgYm90U3lzdGVtU3RhdHVzOiAnU3RvcHBlZCcsXG4gICAgICAgIHRhcmdldFByaWNlUm93czogW10sIC8vIENvbXBsZXRlbHkgY2xlYXIgYWxsIHRhcmdldCBwcmljZSByb3dzXG4gICAgICAgIG9yZGVySGlzdG9yeTogW10sXG4gICAgICB9O1xuICAgIGNhc2UgJ1NFVF9UQVJHRVRfUFJJQ0VfUk9XUyc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgdGFyZ2V0UHJpY2VSb3dzOiBhY3Rpb24ucGF5bG9hZCB9O1xuICAgIGNhc2UgJ1JFU0VUX0ZPUl9ORVdfQ1JZUFRPJzpcbiAgICAgIHNhdmVCb3RTdGF0dXMoJ1N0b3BwZWQnKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLmluaXRpYWxUcmFkaW5nU3RhdGUsXG4gICAgICAgIGNvbmZpZzogc3RhdGUuY29uZmlnLCAvLyBLZWVwIHRoZSBuZXcgY3J5cHRvIGNvbmZpZ3VyYXRpb25cbiAgICAgICAgYmFja2VuZFN0YXR1czogc3RhdGUuYmFja2VuZFN0YXR1cywgLy8gUHJlc2VydmUgYmFja2VuZCBzdGF0dXNcbiAgICAgICAgYm90U3lzdGVtU3RhdHVzOiAnU3RvcHBlZCcsIC8vIEVuc3VyZSBib3QgaXMgc3RvcHBlZFxuICAgICAgICBjdXJyZW50TWFya2V0UHJpY2U6IDAsIC8vIFJlc2V0IG1hcmtldCBwcmljZSBmb3IgbmV3IGNyeXB0b1xuICAgICAgfTtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIHN0YXRlO1xuICB9XG59O1xuXG5pbnRlcmZhY2UgVHJhZGluZ0NvbnRleHRUeXBlIGV4dGVuZHMgT21pdDxUcmFkaW5nU3RhdGUsICdiYWNrZW5kU3RhdHVzJyB8ICdib3RTeXN0ZW1TdGF0dXMnPiB7XG4gIGRpc3BhdGNoOiBSZWFjdC5EaXNwYXRjaDxUcmFkaW5nQWN0aW9uPjtcbiAgc2V0VGFyZ2V0UHJpY2VzOiAocHJpY2VzOiBudW1iZXJbXSkgPT4gdm9pZDtcbiAgZ2V0RGlzcGxheU9yZGVyczogKCkgPT4gRGlzcGxheU9yZGVyUm93W107XG4gIGJhY2tlbmRTdGF0dXM6ICdvbmxpbmUnIHwgJ29mZmxpbmUnIHwgJ3Vua25vd24nO1xuICBmZXRjaE1hcmtldFByaWNlOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBjaGVja0JhY2tlbmRTdGF0dXM6ICgpID0+IFByb21pc2U8dm9pZD47XG4gIGJvdFN5c3RlbVN0YXR1czogQm90U3lzdGVtU3RhdHVzO1xuICBpc0JvdEFjdGl2ZTogYm9vbGVhbjsgLy8gRGVyaXZlZCBmcm9tIGJvdFN5c3RlbVN0YXR1c1xuICBzdGFydEJhY2tlbmRCb3Q6IChjb25maWdJZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xuICBzdG9wQmFja2VuZEJvdDogKGNvbmZpZ0lkOiBzdHJpbmcpID0+IFByb21pc2U8Ym9vbGVhbj47XG4gIHNhdmVDb25maWdUb0JhY2tlbmQ6IChjb25maWc6IFRyYWRpbmdDb25maWcpID0+IFByb21pc2U8c3RyaW5nIHwgbnVsbD47XG4gIHNhdmVDdXJyZW50U2Vzc2lvbjogKCkgPT4gYm9vbGVhbjtcbn1cblxuY29uc3QgVHJhZGluZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRyYWRpbmdDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuLy8gU0lNUExJRklFRCBMT0dJQyAtIE5PIENPTVBMRVggQ09PTERPV05TXG5cbmV4cG9ydCBjb25zdCBUcmFkaW5nUHJvdmlkZXIgPSAoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkgPT4ge1xuICAvLyBUb2FzdCBob29rIGZvciBub3RpZmljYXRpb25zIC0gdGVtcG9yYXJpbHkgZGlzYWJsZWRcbiAgLy8gY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcblxuICAvLyBJbml0aWFsaXplIHN0YXRlIHdpdGggbG9jYWxTdG9yYWdlIGRhdGEgaWYgYXZhaWxhYmxlXG4gIGNvbnN0IGluaXRpYWxpemVTdGF0ZSA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBUcmFkaW5nQ29udGV4dDogSW5pdGlhbGl6aW5nIHN0YXRlLi4uJyk7XG5cbiAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgbmV3IHNlc3Npb24gZnJvbSBVUkwgcGFyYW1ldGVyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCB1cmxQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5zZWFyY2gpO1xuICAgICAgY29uc3QgaXNOZXdTZXNzaW9uID0gdXJsUGFyYW1zLmdldCgnbmV3U2Vzc2lvbicpID09PSAndHJ1ZSc7XG5cbiAgICAgIGlmIChpc05ld1Nlc3Npb24pIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CfhpUgTmV3IHNlc3Npb24gcmVxdWVzdGVkIC0gc3RhcnRpbmcgd2l0aCBmcmVzaCBzdGF0ZSBidXQga2VlcGluZyBnbG9iYWwgYmFsYW5jZXMnKTtcbiAgICAgICAgLy8gQ2xlYXIgdGhlIFVSTCBwYXJhbWV0ZXIgdG8gYXZvaWQgY29uZnVzaW9uIC0gdXNlIHNldFRpbWVvdXQgdG8gYXZvaWQgcmVuZGVyIGlzc3Vlc1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdVcmwgPSB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWU7XG4gICAgICAgICAgd2luZG93Lmhpc3RvcnkucmVwbGFjZVN0YXRlKHt9LCAnJywgbmV3VXJsKTtcbiAgICAgICAgfSwgMCk7XG4gICAgICAgIHJldHVybiBjcmVhdGVJbml0aWFsVHJhZGluZ1N0YXRlKCk7IC8vIFVzZSBmcmVzaCBzdGF0ZSB3aXRoIGN1cnJlbnQgZ2xvYmFsIGJhbGFuY2VzXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVHJ5IHRvIGdldCBzZXNzaW9uIG1hbmFnZXIsIGJ1dCBoYW5kbGUgY2FzZSB3aGVyZSBpdCBtaWdodCBub3QgYmUgcmVhZHkgeWV0XG4gICAgbGV0IHNlc3Npb25NYW5hZ2VyO1xuICAgIGxldCBjdXJyZW50U2Vzc2lvbklkO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICAgIGN1cnJlbnRTZXNzaW9uSWQgPSBzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBUcmFkaW5nQ29udGV4dDogU2Vzc2lvbk1hbmFnZXIgYXZhaWxhYmxlLCBjdXJyZW50IHNlc3Npb246JywgY3VycmVudFNlc3Npb25JZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFRyYWRpbmdDb250ZXh0OiBTZXNzaW9uTWFuYWdlciBub3QgcmVhZHkgeWV0LCBmYWxsaW5nIGJhY2sgdG8gbG9jYWxTdG9yYWdlJyk7XG4gICAgICBzZXNzaW9uTWFuYWdlciA9IG51bGw7XG4gICAgICBjdXJyZW50U2Vzc2lvbklkID0gbnVsbDtcbiAgICB9XG5cbiAgICAvLyBJZiBubyBjdXJyZW50IHNlc3Npb24gZXhpc3RzIGZvciB0aGlzIHdpbmRvdywgc3RhcnQgZnJlc2hcbiAgICBpZiAoIWN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4aVIE5vIGN1cnJlbnQgc2Vzc2lvbiAtIHN0YXJ0aW5nIHdpdGggZnJlc2ggc3RhdGUgYnV0IGtlZXBpbmcgZ2xvYmFsIGJhbGFuY2VzJyk7XG4gICAgICByZXR1cm4gY3JlYXRlSW5pdGlhbFRyYWRpbmdTdGF0ZSgpOyAvLyBVc2UgZnJlc2ggc3RhdGUgd2l0aCBjdXJyZW50IGdsb2JhbCBiYWxhbmNlc1xuICAgIH1cblxuICAgIC8vIElmIHdlIGhhdmUgYSBzZXNzaW9uIGFuZCBzZXNzaW9uIG1hbmFnZXIgaXMgYXZhaWxhYmxlLCB0cnkgdG8gbG9hZCBpdFxuICAgIGlmIChzZXNzaW9uTWFuYWdlciAmJiBjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjdXJyZW50U2Vzc2lvbiA9IHNlc3Npb25NYW5hZ2VyLmxvYWRTZXNzaW9uKGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgICBpZiAoY3VycmVudFNlc3Npb24pIHtcbiAgICAgICAgICBjb25zdCBzYXZlZEJvdFN0YXR1cyA9IGxvYWRCb3RTdGF0dXMoKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBSZXN0b3Jpbmcgc2Vzc2lvbiBmcm9tIHNlc3Npb24gbWFuYWdlcjonLCBjdXJyZW50U2Vzc2lvbi5uYW1lKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBCb3Qgc3RhdHVzIHJlc3RvcmVkIHRvOicsIHNhdmVkQm90U3RhdHVzKTtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uaW5pdGlhbFRyYWRpbmdTdGF0ZSxcbiAgICAgICAgICAgIGNvbmZpZzogY3VycmVudFNlc3Npb24uY29uZmlnLFxuICAgICAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBjdXJyZW50U2Vzc2lvbi50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgICBvcmRlckhpc3Rvcnk6IGN1cnJlbnRTZXNzaW9uLm9yZGVySGlzdG9yeSxcbiAgICAgICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogY3VycmVudFNlc3Npb24uY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgY3J5cHRvMUJhbGFuY2U6IGN1cnJlbnRTZXNzaW9uLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICAgICAgY3J5cHRvMkJhbGFuY2U6IGN1cnJlbnRTZXNzaW9uLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IGN1cnJlbnRTZXNzaW9uLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgICAgYm90U3lzdGVtU3RhdHVzOiBzYXZlZEJvdFN0YXR1cyAvLyBSZXN0b3JlIHByZXZpb3VzIGJvdCBzdGF0dXMgdG8gbWFpbnRhaW4gY29udGludWl0eVxuICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflIQgRXJyb3IgbG9hZGluZyBzZXNzaW9uIGZyb20gc2Vzc2lvbiBtYW5hZ2VyOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBsb2NhbFN0b3JhZ2UgaWYgc2Vzc2lvbiBtYW5hZ2VyIGZhaWxzXG4gICAgY29uc3Qgc2F2ZWRTdGF0ZSA9IGxvYWRTdGF0ZUZyb21Mb2NhbFN0b3JhZ2UoKTtcbiAgICBpZiAoc2F2ZWRTdGF0ZSkge1xuICAgICAgY29uc3Qgc2F2ZWRCb3RTdGF0dXMgPSBsb2FkQm90U3RhdHVzKCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBSZXN0b3JpbmcgZnJvbSBsb2NhbFN0b3JhZ2UgYmFja3VwJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBCb3Qgc3RhdHVzIHJlc3RvcmVkIHRvOicsIHNhdmVkQm90U3RhdHVzKTtcbiAgICAgIGNvbnN0IGdsb2JhbEJhbGFuY2VzID0gbG9hZEdsb2JhbEJhbGFuY2VzKCk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5jcmVhdGVJbml0aWFsVHJhZGluZ1N0YXRlKCksXG4gICAgICAgIC4uLnNhdmVkU3RhdGUsXG4gICAgICAgIC8vIEFsd2F5cyB1c2UgY3VycmVudCBnbG9iYWwgYmFsYW5jZXNcbiAgICAgICAgY3J5cHRvMUJhbGFuY2U6IGdsb2JhbEJhbGFuY2VzLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogZ2xvYmFsQmFsYW5jZXMuY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBnbG9iYWxCYWxhbmNlcy5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgLy8gUmVzdG9yZSBwcmV2aW91cyBib3Qgc3RhdHVzIHRvIG1haW50YWluIGNvbnRpbnVpdHlcbiAgICAgICAgYm90U3lzdGVtU3RhdHVzOiBzYXZlZEJvdFN0YXR1c1xuICAgICAgfTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn4pqg77iPIE5vIHNlc3Npb24gZGF0YSBmb3VuZCwgc3RhcnRpbmcgZnJlc2ggd2l0aCBnbG9iYWwgYmFsYW5jZXMnKTtcbiAgICByZXR1cm4gY3JlYXRlSW5pdGlhbFRyYWRpbmdTdGF0ZSgpO1xuICB9O1xuXG4gIGNvbnN0IFtzdGF0ZSwgZGlzcGF0Y2hdID0gdXNlUmVkdWNlcih0cmFkaW5nUmVkdWNlciwgaW5pdGlhbGl6ZVN0YXRlKCkgYXMgVHJhZGluZ1N0YXRlKTtcbiAgY29uc3QgYXVkaW9SZWYgPSB1c2VSZWY8SFRNTEF1ZGlvRWxlbWVudCB8IG51bGw+KG51bGwpO1xuICAvLyBSZW1vdmVkIHByb2Nlc3NpbmcgbG9ja3MgYW5kIGNvb2xkb3ducyBmb3IgY29udGludW91cyB0cmFkaW5nXG5cbiAgLy8gVGVsZWdyYW0gZXJyb3Igbm90aWZpY2F0aW9uIGZ1bmN0aW9uIC0gZGVmaW5lZCBlYXJseSB0byBhdm9pZCBpbml0aWFsaXphdGlvbiBpc3N1ZXNcbiAgY29uc3Qgc2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoZXJyb3JUeXBlOiBzdHJpbmcsIGVycm9yTWVzc2FnZTogc3RyaW5nLCBjb250ZXh0Pzogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRlbGVncmFtVG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGVsZWdyYW1fYm90X3Rva2VuJyk7XG4gICAgICBjb25zdCB0ZWxlZ3JhbUNoYXRJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0ZWxlZ3JhbV9jaGF0X2lkJyk7XG5cbiAgICAgIGlmICghdGVsZWdyYW1Ub2tlbiB8fCAhdGVsZWdyYW1DaGF0SWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1RlbGVncmFtIG5vdCBjb25maWd1cmVkIC0gc2tpcHBpbmcgZXJyb3Igbm90aWZpY2F0aW9uJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gRm9ybWF0IGVycm9yIG1lc3NhZ2Ugd2l0aCBlbW9qaSBhbmQgc3RydWN0dXJlXG4gICAgICBsZXQgbWVzc2FnZSA9IGDimqDvuI8gPGI+RXJyb3IgQWxlcnQ8L2I+XFxuXFxuYDtcbiAgICAgIG1lc3NhZ2UgKz0gYDxiPlR5cGU6PC9iPiAke2Vycm9yVHlwZX1cXG5gO1xuICAgICAgbWVzc2FnZSArPSBgPGI+RXJyb3I6PC9iPiAke2Vycm9yTWVzc2FnZX1cXG5gO1xuXG4gICAgICBpZiAoY29udGV4dCkge1xuICAgICAgICBtZXNzYWdlICs9IGA8Yj5Db250ZXh0OjwvYj4gJHtjb250ZXh0fVxcbmA7XG4gICAgICB9XG5cbiAgICAgIGlmIChzdGF0ZS5jb25maWcuY3J5cHRvMSAmJiBzdGF0ZS5jb25maWcuY3J5cHRvMikge1xuICAgICAgICBtZXNzYWdlICs9IGA8Yj5UcmFkaW5nIFBhaXI6PC9iPiAke3N0YXRlLmNvbmZpZy5jcnlwdG8xfS8ke3N0YXRlLmNvbmZpZy5jcnlwdG8yfVxcbmA7XG4gICAgICB9XG5cbiAgICAgIG1lc3NhZ2UgKz0gYDxiPlRpbWU6PC9iPiAke25ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKX1cXG5gO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS50ZWxlZ3JhbS5vcmcvYm90JHt0ZWxlZ3JhbVRva2VufS9zZW5kTWVzc2FnZWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgY2hhdF9pZDogdGVsZWdyYW1DaGF0SWQsXG4gICAgICAgICAgdGV4dDogbWVzc2FnZSxcbiAgICAgICAgICBwYXJzZV9tb2RlOiAnSFRNTCdcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzZW5kIFRlbGVncmFtIGVycm9yIG5vdGlmaWNhdGlvbjonLCByZXNwb25zZS5zdGF0dXNUZXh0KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgVGVsZWdyYW0gZXJyb3Igbm90aWZpY2F0aW9uIHNlbnQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgVGVsZWdyYW0gbm90aWZpY2F0aW9uOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtzdGF0ZS5jb25maWcuY3J5cHRvMSwgc3RhdGUuY29uZmlnLmNyeXB0bzJdKTtcblxuICAvLyBJbml0aWFsaXplIGZldGNoTWFya2V0UHJpY2UgZmlyc3RcbiAgY29uc3QgZmV0Y2hNYXJrZXRQcmljZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gRG9uJ3QgZmV0Y2ggcHJpY2UgaWYgZWl0aGVyIGNyeXB0byBpcyBub3Qgc2VsZWN0ZWRcbiAgICAgIGlmICghc3RhdGUuY29uZmlnLmNyeXB0bzEgfHwgIXN0YXRlLmNvbmZpZy5jcnlwdG8yKSB7XG4gICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9NQVJLRVRfUFJJQ0UnLCBwYXlsb2FkOiAwIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBjb25zdCBwcmljZSA9IGF3YWl0IGdldE1hcmtldFByaWNlRnJvbUFQSShzdGF0ZS5jb25maWcpO1xuICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX01BUktFVF9QUklDRScsIHBheWxvYWQ6IHByaWNlIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggbWFya2V0IHByaWNlOicsIGVycm9yKTtcbiAgICAgIC8vIFNlbmQgZXJyb3Igbm90aWZpY2F0aW9uIGZvciBwcmljZSBmZXRjaGluZyBmYWlsdXJlc1xuICAgICAgc2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24oXG4gICAgICAgICdQcmljZSBGZXRjaCBFcnJvcicsXG4gICAgICAgIGBGYWlsZWQgdG8gZmV0Y2ggbWFya2V0IHByaWNlOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWAsXG4gICAgICAgIGBUcmFkaW5nIHBhaXI6ICR7c3RhdGUuY29uZmlnLmNyeXB0bzF9LyR7c3RhdGUuY29uZmlnLmNyeXB0bzJ9YFxuICAgICAgKTtcbiAgICB9XG4gIH0sIFtzdGF0ZS5jb25maWcsIGRpc3BhdGNoLCBzZW5kVGVsZWdyYW1FcnJvck5vdGlmaWNhdGlvbl0pO1xuXG4gIC8vIE1hcmtldCBwcmljZSBmbHVjdHVhdGlvbiBlZmZlY3QgLSBzaW11bGF0ZXMgcmVhbC10aW1lIHByaWNlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsIGZldGNoXG4gICAgZmV0Y2hNYXJrZXRQcmljZSgpO1xuXG4gICAgLy8gU2V0IHVwIHByaWNlIGZsdWN0dWF0aW9uIGludGVydmFsIGZvciBzaW11bGF0aW9uXG4gICAgY29uc3QgcHJpY2VGbHVjdHVhdGlvbkludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgLy8gT25seSBmbHVjdHVhdGUgcHJpY2UgaWYgb25saW5lXG4gICAgICBjb25zdCBuZXR3b3JrTW9uaXRvciA9IE5ldHdvcmtNb25pdG9yLmdldEluc3RhbmNlKCk7XG4gICAgICBpZiAobmV0d29ya01vbml0b3IuZ2V0U3RhdHVzKCkuaXNPbmxpbmUpIHtcbiAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnRkxVQ1RVQVRFX01BUktFVF9QUklDRScgfSk7XG4gICAgICB9XG4gICAgfSwgMjAwMCk7IC8vIFVwZGF0ZSBldmVyeSAyIHNlY29uZHMgZm9yIHJlYWxpc3RpYyBzaW11bGF0aW9uXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJJbnRlcnZhbChwcmljZUZsdWN0dWF0aW9uSW50ZXJ2YWwpO1xuICAgIH07XG4gIH0sIFtmZXRjaE1hcmtldFByaWNlLCBkaXNwYXRjaF0pO1xuXG4gIC8vIE90aGVyIGVmZmVjdHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICBhdWRpb1JlZi5jdXJyZW50ID0gbmV3IEF1ZGlvKCk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gQXVkaW8gcXVldWUgc3lzdGVtIGZvciBoYW5kbGluZyBtdWx0aXBsZSBzaW11bHRhbmVvdXMgc291bmQgcmVxdWVzdHNcbiAgY29uc3QgYXVkaW9RdWV1ZVJlZiA9IHVzZVJlZjxBcnJheTx7IHNvdW5kS2V5OiAnc291bmRPcmRlckV4ZWN1dGlvbicgfCAnc291bmRFcnJvcic7IHNlc3Npb25JZD86IHN0cmluZyB9Pj4oW10pO1xuICBjb25zdCBpc1BsYXlpbmdBdWRpb1JlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgY29uc3QgcHJvY2Vzc0F1ZGlvUXVldWUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKGlzUGxheWluZ0F1ZGlvUmVmLmN1cnJlbnQgfHwgYXVkaW9RdWV1ZVJlZi5jdXJyZW50Lmxlbmd0aCA9PT0gMCB8fCAhYXVkaW9SZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgaXNQbGF5aW5nQXVkaW9SZWYuY3VycmVudCA9IHRydWU7XG4gICAgY29uc3QgeyBzb3VuZEtleSwgc2Vzc2lvbklkIH0gPSBhdWRpb1F1ZXVlUmVmLmN1cnJlbnQuc2hpZnQoKSE7XG5cbiAgICB0cnkge1xuICAgICAgLy8gR2V0IHNlc3Npb24tc3BlY2lmaWMgYWxhcm0gc2V0dGluZ3NcbiAgICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICAgIGNvbnN0IHRhcmdldFNlc3Npb25JZCA9IHNlc3Npb25JZCB8fCBzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCk7XG4gICAgICBjb25zdCB0YXJnZXRTZXNzaW9uID0gdGFyZ2V0U2Vzc2lvbklkID8gc2Vzc2lvbk1hbmFnZXIubG9hZFNlc3Npb24odGFyZ2V0U2Vzc2lvbklkKSA6IG51bGw7XG4gICAgICBjb25zdCBhbGFybVNldHRpbmdzID0gdGFyZ2V0U2Vzc2lvbj8uYWxhcm1TZXR0aW5ncyB8fCBzdGF0ZS5hcHBTZXR0aW5ncztcblxuICAgICAgaWYgKCFhbGFybVNldHRpbmdzLnNvdW5kQWxlcnRzRW5hYmxlZCkge1xuICAgICAgICByZXR1cm47IC8vIFNraXAgaWYgc291bmQgaXMgZGlzYWJsZWRcbiAgICAgIH1cblxuICAgICAgbGV0IHNvdW5kUGF0aDogc3RyaW5nIHwgdW5kZWZpbmVkO1xuICAgICAgaWYgKHNvdW5kS2V5ID09PSAnc291bmRPcmRlckV4ZWN1dGlvbicgJiYgYWxhcm1TZXR0aW5ncy5hbGVydE9uT3JkZXJFeGVjdXRpb24pIHtcbiAgICAgICAgc291bmRQYXRoID0gYWxhcm1TZXR0aW5ncy5zb3VuZE9yZGVyRXhlY3V0aW9uO1xuICAgICAgfSBlbHNlIGlmIChzb3VuZEtleSA9PT0gJ3NvdW5kRXJyb3InICYmIGFsYXJtU2V0dGluZ3MuYWxlcnRPbkVycm9yKSB7XG4gICAgICAgIHNvdW5kUGF0aCA9IGFsYXJtU2V0dGluZ3Muc291bmRFcnJvcjtcbiAgICAgIH1cblxuICAgICAgaWYgKHNvdW5kUGF0aCkge1xuICAgICAgICAvLyBWYWxpZGF0ZSBhbmQgZml4IHNvdW5kIHBhdGggaWYgbmVlZGVkXG4gICAgICAgIGxldCB2YWxpZFNvdW5kUGF0aCA9IHNvdW5kUGF0aDtcblxuICAgICAgICAvLyBGaXggb2xkIC9zb3VuZHMvIHBhdGhzIHRvIC9yaW5ndG9uZXMvXG4gICAgICAgIGlmIChzb3VuZFBhdGguc3RhcnRzV2l0aCgnL3NvdW5kcy8nKSkge1xuICAgICAgICAgIHZhbGlkU291bmRQYXRoID0gc291bmRQYXRoLnJlcGxhY2UoJy9zb3VuZHMvJywgJy9yaW5ndG9uZXMvJyk7XG4gICAgICAgICAgY29uc29sZS53YXJuKGBGaXhlZCBkZXByZWNhdGVkIHNvdW5kIHBhdGg6ICR7c291bmRQYXRofSAtPiAke3ZhbGlkU291bmRQYXRofWApO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gRmFsbGJhY2sgdG8gZGVmYXVsdCBzb3VuZCBpZiBwYXRoIGRvZXNuJ3QgZXhpc3QgaW4gcmluZ3RvbmVzXG4gICAgICAgIGlmICh2YWxpZFNvdW5kUGF0aC5zdGFydHNXaXRoKCcvcmluZ3RvbmVzLycpICYmICF2YWxpZFNvdW5kUGF0aC5pbmNsdWRlcygnZGF0YTphdWRpbycpKSB7XG4gICAgICAgICAgY29uc3Qga25vd25SaW5ndG9uZXMgPSBbXG4gICAgICAgICAgICAnY2hlZXIud2F2JywgJ2NoZXN0MS53YXYnLCAnY2hpbWUyLndhdicsICdiZWxscy53YXYnLCAnYmlyZDEud2F2JywgJ2JpcmQ3LndhdicsXG4gICAgICAgICAgICAnc3BhcnJvdzEud2F2JywgJ3NwYWNlX2JlbGxzNGEud2F2JywgJ3NhbmN0dWFyeTEud2F2JywgJ21hcmJsZTEud2F2JywgJ2ZvdW5kcnkyLndhdicsXG4gICAgICAgICAgICAnR19oYWRlc19jdXJzZS53YXYnLCAnR19oYWRlc19kZW1hdC53YXYnLCAnR19oYWRlc19zYW5jdGlmeS53YXYnLCAnZGFyazIud2F2JyxcbiAgICAgICAgICAgICdTYXR5cl9hdGs0LndhdicsICdTX21vbjEubXAzJywgJ1NfbW9uMi5tcDMnLCAnd29sZjQud2F2JywgJ2dvYXRoZXJkMS53YXYnLFxuICAgICAgICAgICAgJ3RheDMud2F2JywgJ0dfaGFkZXNfbWF0LndhdidcbiAgICAgICAgICBdO1xuXG4gICAgICAgICAgY29uc3QgZmlsZW5hbWUgPSB2YWxpZFNvdW5kUGF0aC5zcGxpdCgnLycpLnBvcCgpO1xuICAgICAgICAgIGlmIChmaWxlbmFtZSAmJiAha25vd25SaW5ndG9uZXMuaW5jbHVkZXMoZmlsZW5hbWUpKSB7XG4gICAgICAgICAgICB2YWxpZFNvdW5kUGF0aCA9ICcvcmluZ3RvbmVzL2NoZWVyLndhdic7IC8vIERlZmF1bHQgZmFsbGJhY2tcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgVW5rbm93biByaW5ndG9uZSBmaWxlOiAke3NvdW5kUGF0aH0sIHVzaW5nIGRlZmF1bHQ6ICR7dmFsaWRTb3VuZFBhdGh9YCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gU3RvcCBhbnkgY3VycmVudGx5IHBsYXlpbmcgYXVkaW8gdG8gcHJldmVudCBjb25mbGljdHNcbiAgICAgICAgYXVkaW9SZWYuY3VycmVudC5wYXVzZSgpO1xuICAgICAgICBhdWRpb1JlZi5jdXJyZW50LmN1cnJlbnRUaW1lID0gMDtcbiAgICAgICAgYXVkaW9SZWYuY3VycmVudC5zcmMgPSB2YWxpZFNvdW5kUGF0aDtcblxuICAgICAgICAvLyBXYWl0IGZvciBhdWRpbyB0byBsb2FkIGJlZm9yZSBwbGF5aW5nXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlPHZvaWQ+KChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICBjb25zdCBvbkNhblBsYXkgPSAoKSA9PiB7XG4gICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50Py5yZW1vdmVFdmVudExpc3RlbmVyKCdjYW5wbGF5dGhyb3VnaCcsIG9uQ2FuUGxheSk7XG4gICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50Py5yZW1vdmVFdmVudExpc3RlbmVyKCdlcnJvcicsIG9uRXJyb3IpO1xuICAgICAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICAgIH07XG5cbiAgICAgICAgICBjb25zdCBvbkVycm9yID0gKGU6IEV2ZW50KSA9PiB7XG4gICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50Py5yZW1vdmVFdmVudExpc3RlbmVyKCdjYW5wbGF5dGhyb3VnaCcsIG9uQ2FuUGxheSk7XG4gICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50Py5yZW1vdmVFdmVudExpc3RlbmVyKCdlcnJvcicsIG9uRXJyb3IpO1xuICAgICAgICAgICAgcmVqZWN0KGUpO1xuICAgICAgICAgIH07XG5cbiAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50Py5hZGRFdmVudExpc3RlbmVyKCdjYW5wbGF5dGhyb3VnaCcsIG9uQ2FuUGxheSwgeyBvbmNlOiB0cnVlIH0pO1xuICAgICAgICAgIGF1ZGlvUmVmLmN1cnJlbnQ/LmFkZEV2ZW50TGlzdGVuZXIoJ2Vycm9yJywgb25FcnJvciwgeyBvbmNlOiB0cnVlIH0pO1xuICAgICAgICAgIGF1ZGlvUmVmLmN1cnJlbnQ/LmxvYWQoKTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gUGxheSB0aGUgc291bmQgd2l0aCB0aW1lb3V0XG4gICAgICAgIGF3YWl0IGF1ZGlvUmVmLmN1cnJlbnQucGxheSgpO1xuXG4gICAgICAgIC8vIFNldCB0aW1lb3V0IHRvIHN0b3AgYXVkaW8gYWZ0ZXIgMiBzZWNvbmRzXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIGlmIChhdWRpb1JlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LnBhdXNlKCk7XG4gICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LmN1cnJlbnRUaW1lID0gMDtcbiAgICAgICAgICB9XG4gICAgICAgIH0sIDIwMDApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBIYW5kbGUgYXVkaW8gZXJyb3JzIGdyYWNlZnVsbHlcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIChcbiAgICAgICAgZXJyb3IubmFtZSA9PT0gJ0Fib3J0RXJyb3InIHx8XG4gICAgICAgIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2ludGVycnVwdGVkJykgfHxcbiAgICAgICAgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygncGxheSgpIHJlcXVlc3QnKVxuICAgICAgKSkge1xuICAgICAgICBjb25zb2xlLmRlYnVnKCdBdWRpbyBwbGF5IGludGVycnVwdGVkIChub24tY3JpdGljYWwpOicsIGVycm9yLm1lc3NhZ2UpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHBsYXlpbmcgc291bmQ6XCIsIGVycm9yKTtcbiAgICAgICAgLy8gVHJ5IGZhbGxiYWNrIHNvdW5kIGZvciBjcml0aWNhbCBlcnJvcnNcbiAgICAgICAgaWYgKGF1ZGlvUmVmLmN1cnJlbnQgJiYgYXVkaW9SZWYuY3VycmVudC5zcmMgIT09ICcvcmluZ3RvbmVzL2NoZWVyLndhdicpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5zcmMgPSAnL3Jpbmd0b25lcy9jaGVlci53YXYnO1xuICAgICAgICAgICAgYXdhaXQgYXVkaW9SZWYuY3VycmVudC5wbGF5KCk7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgaWYgKGF1ZGlvUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LnBhdXNlKCk7XG4gICAgICAgICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5jdXJyZW50VGltZSA9IDA7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIDIwMDApO1xuICAgICAgICAgIH0gY2F0Y2ggKGZhbGxiYWNrRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWxsYmFjayBzb3VuZCBhbHNvIGZhaWxlZDpcIiwgZmFsbGJhY2tFcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGlzUGxheWluZ0F1ZGlvUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgIC8vIFByb2Nlc3MgbmV4dCBpdGVtIGluIHF1ZXVlIGFmdGVyIGEgc2hvcnQgZGVsYXlcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gcHJvY2Vzc0F1ZGlvUXVldWUoKSwgMTUwKTtcbiAgICB9XG4gIH0sIFtzdGF0ZS5hcHBTZXR0aW5nc10pO1xuXG4gIGNvbnN0IHBsYXlTb3VuZCA9IHVzZUNhbGxiYWNrKChzb3VuZEtleTogJ3NvdW5kT3JkZXJFeGVjdXRpb24nIHwgJ3NvdW5kRXJyb3InLCBzZXNzaW9uSWQ/OiBzdHJpbmcpID0+IHtcbiAgICAvLyBBZGQgdG8gcXVldWUgaW5zdGVhZCBvZiBwbGF5aW5nIGltbWVkaWF0ZWx5XG4gICAgYXVkaW9RdWV1ZVJlZi5jdXJyZW50LnB1c2goeyBzb3VuZEtleSwgc2Vzc2lvbklkIH0pO1xuICAgIHByb2Nlc3NBdWRpb1F1ZXVlKCk7XG4gIH0sIFtwcm9jZXNzQXVkaW9RdWV1ZV0pO1xuXG4gIC8vIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmdW5jdGlvblxuICBjb25zdCBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAobWVzc2FnZTogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRlbGVncmFtVG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGVsZWdyYW1fYm90X3Rva2VuJyk7XG4gICAgICBjb25zdCB0ZWxlZ3JhbUNoYXRJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0ZWxlZ3JhbV9jaGF0X2lkJyk7XG5cbiAgICAgIGlmICghdGVsZWdyYW1Ub2tlbiB8fCAhdGVsZWdyYW1DaGF0SWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1RlbGVncmFtIG5vdCBjb25maWd1cmVkIC0gc2tpcHBpbmcgbm90aWZpY2F0aW9uJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9hcGkudGVsZWdyYW0ub3JnL2JvdCR7dGVsZWdyYW1Ub2tlbn0vc2VuZE1lc3NhZ2VgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGNoYXRfaWQ6IHRlbGVncmFtQ2hhdElkLFxuICAgICAgICAgIHRleHQ6IG1lc3NhZ2UsXG4gICAgICAgICAgcGFyc2VfbW9kZTogJ0hUTUwnXG4gICAgICAgIH0pXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb246JywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgVGVsZWdyYW0gbm90aWZpY2F0aW9uOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuXG5cbiAgLy8gRWZmZWN0IHRvIHVwZGF0ZSBtYXJrZXQgcHJpY2Ugd2hlbiB0cmFkaW5nIHBhaXIgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFdoZW4gY3J5cHRvMSBvciBjcnlwdG8yIChwYXJ0cyBvZiBzdGF0ZS5jb25maWcpIGNoYW5nZSxcbiAgICAvLyB0aGUgZmV0Y2hNYXJrZXRQcmljZSB1c2VDYWxsYmFjayBnZXRzIGEgbmV3IHJlZmVyZW5jZS5cbiAgICAvLyBUaGUgdXNlRWZmZWN0IGFib3ZlICh3aGljaCBkZXBlbmRzIG9uIGZldGNoTWFya2V0UHJpY2UpXG4gICAgLy8gd2lsbCByZS1ydW4sIGNsZWFyIHRoZSBvbGQgaW50ZXJ2YWwsIG1ha2UgYW4gaW5pdGlhbCBmZXRjaCB3aXRoIHRoZSBuZXcgY29uZmlnLFxuICAgIC8vIGFuZCBzZXQgdXAgYSBuZXcgaW50ZXJ2YWwuXG4gICAgLy8gVGhlIHJlZHVjZXIgZm9yIFNFVF9DT05GSUcgYWxzbyBzZXRzIGFuIGluaXRpYWwgbWFya2V0IHByaWNlIGlmIGNyeXB0bzEvY3J5cHRvMiBjaGFuZ2VzLlxuICAgIC8vIFRodXMsIG5vIGV4cGxpY2l0IGRpc3BhdGNoIGlzIG5lZWRlZCBoZXJlLlxuICB9LCBbc3RhdGUuY29uZmlnLmNyeXB0bzEsIHN0YXRlLmNvbmZpZy5jcnlwdG8yXSk7IC8vIERlcGVuZGVuY2llcyBlbnN1cmUgdGhpcyByZWFjdHMgdG8gcGFpciBjaGFuZ2VzXG5cbiAgY29uc3Qgc2V0VGFyZ2V0UHJpY2VzID0gdXNlQ2FsbGJhY2soKHByaWNlczogbnVtYmVyW10pID0+IHtcbiAgICBpZiAoIXByaWNlcyB8fCAhQXJyYXkuaXNBcnJheShwcmljZXMpKSByZXR1cm47XG5cbiAgICAvLyBTb3J0IHByaWNlcyBmcm9tIGxvd2VzdCB0byBoaWdoZXN0IGZvciBwcm9wZXIgY291bnRlciBhc3NpZ25tZW50XG4gICAgY29uc3Qgc29ydGVkUHJpY2VzID0gWy4uLnByaWNlc10uZmlsdGVyKChwcmljZTogbnVtYmVyKSA9PiAhaXNOYU4ocHJpY2UpICYmIHByaWNlID4gMCkuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xuXG4gICAgY29uc3QgbmV3Um93czogVGFyZ2V0UHJpY2VSb3dbXSA9IHNvcnRlZFByaWNlcy5tYXAoKHByaWNlOiBudW1iZXIsIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgY29uc3QgZXhpc3RpbmdSb3cgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MuZmluZCgocjogVGFyZ2V0UHJpY2VSb3cpID0+IHIudGFyZ2V0UHJpY2UgPT09IHByaWNlKTtcbiAgICAgICAgaWYgKGV4aXN0aW5nUm93KSB7XG4gICAgICAgICAgLy8gVXBkYXRlIGNvdW50ZXIgZm9yIGV4aXN0aW5nIHJvdyBiYXNlZCBvbiBzb3J0ZWQgcG9zaXRpb25cbiAgICAgICAgICByZXR1cm4geyAuLi5leGlzdGluZ1JvdywgY291bnRlcjogaW5kZXggKyAxIH07XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICBjb3VudGVyOiBpbmRleCArIDEsIC8vIENvdW50ZXIgc3RhcnRzIGZyb20gMSwgbG93ZXN0IHByaWNlIGdldHMgY291bnRlciAxXG4gICAgICAgICAgc3RhdHVzOiAnRnJlZScgYXMgT3JkZXJTdGF0dXMsXG4gICAgICAgICAgb3JkZXJMZXZlbDogMCxcbiAgICAgICAgICB2YWx1ZUxldmVsOiBzdGF0ZS5jb25maWcuYmFzZUJpZCwgLy8gVXNlIGJhc2VCaWQgZnJvbSBjb25maWdcbiAgICAgICAgICB0YXJnZXRQcmljZTogcHJpY2UsXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfVEFSR0VUX1BSSUNFX1JPV1MnLCBwYXlsb2FkOiBuZXdSb3dzIH0pO1xuICB9LCBbc3RhdGUudGFyZ2V0UHJpY2VSb3dzLCBzdGF0ZS5jb25maWcuYmFzZUJpZCwgZGlzcGF0Y2hdKTtcblxuICAvLyBDb3JlIFRyYWRpbmcgTG9naWMgKFNpbXVsYXRlZCkgLSBDT05USU5VT1VTIFRSQURJTkcgVkVSU0lPTlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIENoZWNrIG5ldHdvcmsgc3RhdHVzIGZpcnN0XG4gICAgY29uc3QgbmV0d29ya01vbml0b3IgPSBOZXR3b3JrTW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IGlzT25saW5lID0gbmV0d29ya01vbml0b3IuZ2V0U3RhdHVzKCkuaXNPbmxpbmU7XG5cbiAgICAvLyBPbmx5IGNoZWNrIGVzc2VudGlhbCBjb25kaXRpb25zIEFORCBuZXR3b3JrIHN0YXR1c1xuICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgIT09ICdSdW5uaW5nJyB8fFxuICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MubGVuZ3RoID09PSAwIHx8XG4gICAgICAgIHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSA8PSAwIHx8XG4gICAgICAgICFpc09ubGluZSkge1xuXG4gICAgICBpZiAoIWlzT25saW5lICYmIHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5S0IFRyYWRpbmcgcGF1c2VkIC0gbmV0d29yayBvZmZsaW5lJyk7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gRXhlY3V0ZSB0cmFkaW5nIGxvZ2ljIGltbWVkaWF0ZWx5IC0gbm8gbG9ja3MsIG5vIGNvb2xkb3ducywgbm8gZGVsYXlzXG4gICAgY29uc3QgeyBjb25maWcsIGN1cnJlbnRNYXJrZXRQcmljZSwgdGFyZ2V0UHJpY2VSb3dzLCBjcnlwdG8xQmFsYW5jZSwgY3J5cHRvMkJhbGFuY2UgfSA9IHN0YXRlO1xuICAgIGNvbnN0IHNvcnRlZFJvd3NGb3JMb2dpYyA9IFsuLi50YXJnZXRQcmljZVJvd3NdLnNvcnQoKGE6IFRhcmdldFByaWNlUm93LCBiOiBUYXJnZXRQcmljZVJvdykgPT4gYS50YXJnZXRQcmljZSAtIGIudGFyZ2V0UHJpY2UpO1xuXG4gICAgLy8gVXNlIG11dGFibGUgdmFyaWFibGVzIGZvciBiYWxhbmNlIHRyYWNraW5nIHdpdGhpbiB0aGlzIGN5Y2xlXG4gICAgbGV0IGN1cnJlbnRDcnlwdG8xQmFsYW5jZSA9IGNyeXB0bzFCYWxhbmNlO1xuICAgIGxldCBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgPSBjcnlwdG8yQmFsYW5jZTtcbiAgICBsZXQgYWN0aW9uc1Rha2VuID0gMDtcblxuICAgIGNvbnNvbGUubG9nKGDwn5qAIENPTlRJTlVPVVMgVFJBRElORzogUHJpY2UgJCR7Y3VycmVudE1hcmtldFByaWNlLnRvRml4ZWQoMil9IHwgVGFyZ2V0czogJHtzb3J0ZWRSb3dzRm9yTG9naWMubGVuZ3RofSB8IEJhbGFuY2U6ICQke2N1cnJlbnRDcnlwdG8yQmFsYW5jZX0gJHtjb25maWcuY3J5cHRvMn1gKTtcblxuICAgIC8vIFNob3cgd2hpY2ggdGFyZ2V0cyBhcmUgaW4gcmFuZ2VcbiAgICBjb25zdCB0YXJnZXRzSW5SYW5nZSA9IHNvcnRlZFJvd3NGb3JMb2dpYy5maWx0ZXIocm93ID0+IHtcbiAgICAgIGNvbnN0IGRpZmZQZXJjZW50ID0gKE1hdGguYWJzKGN1cnJlbnRNYXJrZXRQcmljZSAtIHJvdy50YXJnZXRQcmljZSkgLyBjdXJyZW50TWFya2V0UHJpY2UpICogMTAwO1xuICAgICAgcmV0dXJuIGRpZmZQZXJjZW50IDw9IGNvbmZpZy5zbGlwcGFnZVBlcmNlbnQ7XG4gICAgfSk7XG5cbiAgICBpZiAodGFyZ2V0c0luUmFuZ2UubGVuZ3RoID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYPCfjq8gVEFSR0VUUyBJTiBSQU5HRSAowrEke2NvbmZpZy5zbGlwcGFnZVBlcmNlbnR9JSk6YCwgdGFyZ2V0c0luUmFuZ2UubWFwKHJvdyA9PiBgQ291bnRlciAke3Jvdy5jb3VudGVyfSAoJHtyb3cuc3RhdHVzfSlgKSk7XG4gICAgfVxuXG4gICAgLy8gQ09OVElOVU9VUyBUUkFESU5HIExPR0lDOiBQcm9jZXNzIGFsbCB0YXJnZXRzIGltbWVkaWF0ZWx5XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzb3J0ZWRSb3dzRm9yTG9naWMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGFjdGl2ZVJvdyA9IHNvcnRlZFJvd3NGb3JMb2dpY1tpXTtcbiAgICAgIGNvbnN0IHByaWNlRGlmZlBlcmNlbnQgPSBNYXRoLmFicyhjdXJyZW50TWFya2V0UHJpY2UgLSBhY3RpdmVSb3cudGFyZ2V0UHJpY2UpIC8gY3VycmVudE1hcmtldFByaWNlICogMTAwO1xuXG4gICAgICAvLyBTVEVQIDE6IENoZWNrIGlmIFRhcmdldFJvd04gaXMgdHJpZ2dlcmVkICh3aXRoaW4gc2xpcHBhZ2UgcmFuZ2UpXG4gICAgICBpZiAocHJpY2VEaWZmUGVyY2VudCA8PSBjb25maWcuc2xpcHBhZ2VQZXJjZW50KSB7XG5cbiAgICAgICAgaWYgKGNvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTaW1wbGVTcG90XCIpIHtcbiAgICAgICAgICAvLyBTVEVQIDI6IEV2YWx1YXRlIFRyaWdnZXJlZCBUYXJnZXRSb3dOJ3MgU3RhdHVzXG4gICAgICAgICAgaWYgKGFjdGl2ZVJvdy5zdGF0dXMgPT09IFwiRnJlZVwiKSB7XG4gICAgICAgICAgICAvLyBTVEVQIDJhOiBFeGVjdXRlIEJVWSBvbiBUYXJnZXRSb3dOXG4gICAgICAgICAgICBjb25zdCBjb3N0Q3J5cHRvMiA9IGFjdGl2ZVJvdy52YWx1ZUxldmVsO1xuXG4gICAgICAgICAgICBpZiAoY3VycmVudENyeXB0bzJCYWxhbmNlID49IGNvc3RDcnlwdG8yKSB7XG4gICAgICAgICAgICAgIGNvbnN0IGFtb3VudENyeXB0bzFCb3VnaHQgPSBjb3N0Q3J5cHRvMiAvIGN1cnJlbnRNYXJrZXRQcmljZTtcbiAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZFJvdzogVGFyZ2V0UHJpY2VSb3cgPSB7XG4gICAgICAgICAgICAgICAgLi4uYWN0aXZlUm93LFxuICAgICAgICAgICAgICAgIHN0YXR1czogXCJGdWxsXCIsXG4gICAgICAgICAgICAgICAgb3JkZXJMZXZlbDogYWN0aXZlUm93Lm9yZGVyTGV2ZWwgKyAxLFxuICAgICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIGFjdGl2ZVJvdy5vcmRlckxldmVsICsgMSksXG4gICAgICAgICAgICAgICAgY3J5cHRvMUFtb3VudEhlbGQ6IGFtb3VudENyeXB0bzFCb3VnaHQsXG4gICAgICAgICAgICAgICAgb3JpZ2luYWxDb3N0Q3J5cHRvMjogY29zdENyeXB0bzIsXG4gICAgICAgICAgICAgICAgY3J5cHRvMVZhcjogYW1vdW50Q3J5cHRvMUJvdWdodCxcbiAgICAgICAgICAgICAgICBjcnlwdG8yVmFyOiAtY29zdENyeXB0bzIsXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZFJvdyB9KTtcbiAgICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgKyBhbW91bnRDcnlwdG8xQm91Z2h0LCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgLSBjb3N0Q3J5cHRvMiB9IH0pO1xuICAgICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgICBwYWlyOiBgJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn1gLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzEsXG4gICAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiQlVZXCIsXG4gICAgICAgICAgICAgICAgICBhbW91bnRDcnlwdG8xOiBhbW91bnRDcnlwdG8xQm91Z2h0LFxuICAgICAgICAgICAgICAgICAgYXZnUHJpY2U6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogY29zdENyeXB0bzIsXG4gICAgICAgICAgICAgICAgICBwcmljZTE6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8xIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMlN5bWJvbDogY29uZmlnLmNyeXB0bzIgfHwgJydcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEJVWTogQ291bnRlciAke2FjdGl2ZVJvdy5jb3VudGVyfSBib3VnaHQgJHthbW91bnRDcnlwdG8xQm91Z2h0LnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IGF0ICQke2N1cnJlbnRNYXJrZXRQcmljZS50b0ZpeGVkKDIpfWApO1xuICAgICAgICAgICAgICBwbGF5U291bmQoJ3NvdW5kT3JkZXJFeGVjdXRpb24nKTtcblxuICAgICAgICAgICAgICAvLyBTaG93IHRvYXN0IG5vdGlmaWNhdGlvbiBmb3IgQlVZIC0gdGVtcG9yYXJpbHkgZGlzYWJsZWRcbiAgICAgICAgICAgICAgLyp0b2FzdCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgICAgICAgIHRpdGxlOiAn8J+foiBCVVkgRVhFQ1VURUQnLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBgQm91Z2h0ICR7YW1vdW50Q3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfSBhdCAkJHtjdXJyZW50TWFya2V0UHJpY2UudG9GaXhlZCgyKX1gLFxuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwXG4gICAgICAgICAgICAgIH0pOyovXG5cbiAgICAgICAgICAgICAgLy8gU2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb24gZm9yIEJVWVxuICAgICAgICAgICAgICBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24oXG4gICAgICAgICAgICAgICAgYPCfn6IgPGI+QlVZIEVYRUNVVEVEPC9iPlxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5OKIENvdW50ZXI6ICR7YWN0aXZlUm93LmNvdW50ZXJ9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfkrAgQW1vdW50OiAke2Ftb3VudENyeXB0bzFCb3VnaHQudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+StSBQcmljZTogJCR7Y3VycmVudE1hcmtldFByaWNlLnRvRml4ZWQoMil9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfkrggQ29zdDogJCR7Y29zdENyeXB0bzIudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+TiCBNb2RlOiBTaW1wbGUgU3BvdGBcbiAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICBhY3Rpb25zVGFrZW4rKztcblxuICAgICAgICAgICAgICAvLyBVcGRhdGUgbG9jYWwgYmFsYW5jZSBmb3Igc3Vic2VxdWVudCBjaGVja3MgaW4gdGhpcyBjeWNsZVxuICAgICAgICAgICAgICBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgLT0gY29zdENyeXB0bzI7XG4gICAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8xQmFsYW5jZSArPSBhbW91bnRDcnlwdG8xQm91Z2h0O1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgLy8gU2VuZCBlcnJvciBub3RpZmljYXRpb24gZm9yIGluc3VmZmljaWVudCBiYWxhbmNlXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinYwgSW5zdWZmaWNpZW50ICR7Y29uZmlnLmNyeXB0bzJ9IGJhbGFuY2UgZm9yIEJVWSBvcmRlcmApO1xuICAgICAgICAgICAgICBzZW5kVGVsZWdyYW1FcnJvck5vdGlmaWNhdGlvbihcbiAgICAgICAgICAgICAgICAnSW5zdWZmaWNpZW50IEJhbGFuY2UnLFxuICAgICAgICAgICAgICAgIGBDYW5ub3QgZXhlY3V0ZSBCVVkgb3JkZXIgLSBpbnN1ZmZpY2llbnQgJHtjb25maWcuY3J5cHRvMn0gYmFsYW5jZWAsXG4gICAgICAgICAgICAgICAgYFJlcXVpcmVkOiAke2Nvc3RDcnlwdG8yLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9LCBBdmFpbGFibGU6ICR7Y3VycmVudENyeXB0bzJCYWxhbmNlLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9YFxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFNURVAgMzogQ2hlY2sgYW5kIFByb2Nlc3MgSW5mZXJpb3IgVGFyZ2V0Um93IChUYXJnZXRSb3dOX21pbnVzXzEpIC0gQUxXQVlTLCByZWdhcmRsZXNzIG9mIEJVWVxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRDb3VudGVyID0gYWN0aXZlUm93LmNvdW50ZXI7XG4gICAgICAgICAgY29uc3QgaW5mZXJpb3JSb3cgPSBzb3J0ZWRSb3dzRm9yTG9naWMuZmluZChyb3cgPT4gcm93LmNvdW50ZXIgPT09IGN1cnJlbnRDb3VudGVyIC0gMSk7XG5cbiAgICAgICAgICBpZiAoaW5mZXJpb3JSb3cgJiYgaW5mZXJpb3JSb3cuc3RhdHVzID09PSBcIkZ1bGxcIiAmJiBpbmZlcmlvclJvdy5jcnlwdG8xQW1vdW50SGVsZCAmJiBpbmZlcmlvclJvdy5vcmlnaW5hbENvc3RDcnlwdG8yKSB7XG4gICAgICAgICAgICBjb25zdCBhbW91bnRDcnlwdG8xVG9TZWxsID0gaW5mZXJpb3JSb3cuY3J5cHRvMUFtb3VudEhlbGQ7XG4gICAgICAgICAgICBjb25zdCBjcnlwdG8yUmVjZWl2ZWQgPSBhbW91bnRDcnlwdG8xVG9TZWxsICogY3VycmVudE1hcmtldFByaWNlO1xuICAgICAgICAgICAgY29uc3QgcmVhbGl6ZWRQcm9maXQgPSBjcnlwdG8yUmVjZWl2ZWQgLSBpbmZlcmlvclJvdy5vcmlnaW5hbENvc3RDcnlwdG8yO1xuXG4gICAgICAgICAgICAvLyBDYWxjdWxhdGUgQ3J5cHRvMSBwcm9maXQvbG9zcyAtIGNvbnZlcnQgVVNEVCBwcm9maXQgdG8gQlRDIGVxdWl2YWxlbnRcbiAgICAgICAgICAgIGNvbnN0IHJlYWxpemVkUHJvZml0Q3J5cHRvMSA9IGN1cnJlbnRNYXJrZXRQcmljZSA+IDAgPyByZWFsaXplZFByb2ZpdCAvIGN1cnJlbnRNYXJrZXRQcmljZSA6IDA7XG5cbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRJbmZlcmlvclJvdzogVGFyZ2V0UHJpY2VSb3cgPSB7XG4gICAgICAgICAgICAgIC4uLmluZmVyaW9yUm93LFxuICAgICAgICAgICAgICBzdGF0dXM6IFwiRnJlZVwiLFxuICAgICAgICAgICAgICBjcnlwdG8xQW1vdW50SGVsZDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICBvcmlnaW5hbENvc3RDcnlwdG8yOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIGluZmVyaW9yUm93Lm9yZGVyTGV2ZWwpLFxuICAgICAgICAgICAgICBjcnlwdG8xVmFyOiAtYW1vdW50Q3J5cHRvMVRvU2VsbCxcbiAgICAgICAgICAgICAgY3J5cHRvMlZhcjogY3J5cHRvMlJlY2VpdmVkLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZEluZmVyaW9yUm93IH0pO1xuICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgLSBhbW91bnRDcnlwdG8xVG9TZWxsLCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgKyBjcnlwdG8yUmVjZWl2ZWQgfSB9KTtcbiAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9YCxcbiAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMSxcbiAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiU0VMTFwiLFxuICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGFtb3VudENyeXB0bzFUb1NlbGwsXG4gICAgICAgICAgICAgICAgYXZnUHJpY2U6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICB2YWx1ZUNyeXB0bzI6IGNyeXB0bzJSZWNlaXZlZCxcbiAgICAgICAgICAgICAgICBwcmljZTE6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICBjcnlwdG8xU3ltYm9sOiBjb25maWcuY3J5cHRvMSB8fCAnJyxcbiAgICAgICAgICAgICAgICBjcnlwdG8yU3ltYm9sOiBjb25maWcuY3J5cHRvMiB8fCAnJyxcbiAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yOiByZWFsaXplZFByb2ZpdCxcbiAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8xOiByZWFsaXplZFByb2ZpdENyeXB0bzFcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5KwIFNFTEwgVHJhZGUgUC9MOiBDcnlwdG8yPSR7cmVhbGl6ZWRQcm9maXQudG9GaXhlZCg2KX0sIENyeXB0bzE9JHtyZWFsaXplZFByb2ZpdENyeXB0bzEudG9GaXhlZCg2KX1gKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgU0VMTDogQ291bnRlciAke2N1cnJlbnRDb3VudGVyIC0gMX0gc29sZCAke2Ftb3VudENyeXB0bzFUb1NlbGwudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX0uIFByb2ZpdDogJCR7cmVhbGl6ZWRQcm9maXQudG9GaXhlZCgyKX1gKTtcbiAgICAgICAgICAgIHBsYXlTb3VuZCgnc291bmRPcmRlckV4ZWN1dGlvbicpO1xuXG4gICAgICAgICAgICAvLyBTaG93IHRvYXN0IG5vdGlmaWNhdGlvbiBmb3IgU0VMTCAtIHRlbXBvcmFyaWx5IGRpc2FibGVkXG4gICAgICAgICAgICBjb25zdCBwcm9maXRFbW9qaSA9IHJlYWxpemVkUHJvZml0ID4gMCA/ICfwn5OIJyA6IHJlYWxpemVkUHJvZml0IDwgMCA/ICfwn5OJJyA6ICfinpYnO1xuICAgICAgICAgICAgLyp0b2FzdCh7XG4gICAgICAgICAgICAgIHR5cGU6IHJlYWxpemVkUHJvZml0ID4gMCA/ICdzdWNjZXNzJyA6IHJlYWxpemVkUHJvZml0IDwgMCA/ICd3YXJuaW5nJyA6ICdpbmZvJyxcbiAgICAgICAgICAgICAgdGl0bGU6ICfwn5S0IFNFTEwgRVhFQ1VURUQnLFxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYFNvbGQgJHthbW91bnRDcnlwdG8xVG9TZWxsLnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IHwgJHtwcm9maXRFbW9qaX0gUHJvZml0OiAkJHtyZWFsaXplZFByb2ZpdC50b0ZpeGVkKDIpfWAsXG4gICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwXG4gICAgICAgICAgICB9KTsqL1xuXG4gICAgICAgICAgICAvLyBTZW5kIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgU0VMTFxuICAgICAgICAgICAgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uKFxuICAgICAgICAgICAgICBg8J+UtCA8Yj5TRUxMIEVYRUNVVEVEPC9iPlxcbmAgK1xuICAgICAgICAgICAgICBg8J+TiiBDb3VudGVyOiAke2N1cnJlbnRDb3VudGVyIC0gMX1cXG5gICtcbiAgICAgICAgICAgICAgYPCfkrAgQW1vdW50OiAke2Ftb3VudENyeXB0bzFUb1NlbGwudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX1cXG5gICtcbiAgICAgICAgICAgICAgYPCfkrUgUHJpY2U6ICQke2N1cnJlbnRNYXJrZXRQcmljZS50b0ZpeGVkKDIpfVxcbmAgK1xuICAgICAgICAgICAgICBg8J+SuCBSZWNlaXZlZDogJCR7Y3J5cHRvMlJlY2VpdmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9XFxuYCArXG4gICAgICAgICAgICAgIGAke3Byb2ZpdEVtb2ppfSBQcm9maXQ6ICQke3JlYWxpemVkUHJvZml0LnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9XFxuYCArXG4gICAgICAgICAgICAgIGDwn5OIIE1vZGU6IFNpbXBsZSBTcG90YFxuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgYWN0aW9uc1Rha2VuKys7XG5cbiAgICAgICAgICAgIC8vIFVwZGF0ZSBsb2NhbCBiYWxhbmNlIGZvciBzdWJzZXF1ZW50IGNoZWNrcyBpbiB0aGlzIGN5Y2xlXG4gICAgICAgICAgICBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgLT0gYW1vdW50Q3J5cHRvMVRvU2VsbDtcbiAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8yQmFsYW5jZSArPSBjcnlwdG8yUmVjZWl2ZWQ7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKGNvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiKSB7XG4gICAgICAgICAgLy8gU1RFUCAyOiBFdmFsdWF0ZSBUcmlnZ2VyZWQgVGFyZ2V0Um93TidzIFN0YXR1cyAoU3RhYmxlY29pbiBTd2FwIE1vZGUpXG4gICAgICAgICAgaWYgKGFjdGl2ZVJvdy5zdGF0dXMgPT09IFwiRnJlZVwiKSB7XG4gICAgICAgICAgICAvLyBTVEVQIDJhOiBFeGVjdXRlIFR3by1TdGVwIFwiQnV5IENyeXB0bzEgdmlhIFN0YWJsZWNvaW5cIiBvbiBUYXJnZXRSb3dOXG4gICAgICAgICAgICBjb25zdCBhbW91bnRDcnlwdG8yVG9Vc2UgPSBhY3RpdmVSb3cudmFsdWVMZXZlbDsgLy8gVmFsdWUgPSBCYXNlQmlkICogKE11bHRpcGxpZXIgXiBMZXZlbClcblxuICAgICAgICAgICAgaWYgKGN1cnJlbnRDcnlwdG8yQmFsYW5jZSA+PSBhbW91bnRDcnlwdG8yVG9Vc2UpIHtcbiAgICAgICAgICAgICAgLy8gU3RlcCAxOiBTZWxsIENyeXB0bzIgZm9yIFByZWZlcnJlZFN0YWJsZWNvaW5cbiAgICAgICAgICAgICAgLy8gR2V0IHJlYWwgbWFya2V0IHByaWNlIGZvciBDcnlwdG8yL1N0YWJsZWNvaW4gcGFpciAoc3luY2hyb25vdXMgZm9yIG5vdylcbiAgICAgICAgICAgICAgY29uc3QgY3J5cHRvMlN0YWJsZWNvaW5QcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8yIHx8ICdVU0RUJykgLyBnZXRVU0RQcmljZShjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnVVNEVCcpO1xuICAgICAgICAgICAgICBjb25zdCBzdGFibGVjb2luT2J0YWluZWQgPSBhbW91bnRDcnlwdG8yVG9Vc2UgKiBjcnlwdG8yU3RhYmxlY29pblByaWNlO1xuXG4gICAgICAgICAgICAgIC8vIFN0ZXAgMjogQnV5IENyeXB0bzEgd2l0aCBTdGFibGVjb2luXG4gICAgICAgICAgICAgIC8vIEdldCByZWFsIG1hcmtldCBwcmljZSBmb3IgQ3J5cHRvMS9TdGFibGVjb2luIHBhaXJcbiAgICAgICAgICAgICAgY29uc3QgY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8xIHx8ICdCVEMnKSAvIGdldFVTRFByaWNlKGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICAgIGNvbnN0IGNyeXB0bzFCb3VnaHQgPSBzdGFibGVjb2luT2J0YWluZWQgLyBjcnlwdG8xU3RhYmxlY29pblByaWNlO1xuXG4gICAgICAgICAgICAgIC8vIFVwZGF0ZSBSb3cgTjogRnJlZSDihpIgRnVsbCwgTGV2ZWwrKywgVmFsdWUgcmVjYWxjdWxhdGVkXG4gICAgICAgICAgICAgIGNvbnN0IG5ld0xldmVsID0gYWN0aXZlUm93Lm9yZGVyTGV2ZWwgKyAxO1xuICAgICAgICAgICAgICBjb25zdCBuZXdWYWx1ZSA9IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIG5ld0xldmVsKTtcblxuICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkUm93OiBUYXJnZXRQcmljZVJvdyA9IHtcbiAgICAgICAgICAgICAgICAuLi5hY3RpdmVSb3csXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBcIkZ1bGxcIiwgLy8gRnJlZSDihpIgRnVsbFxuICAgICAgICAgICAgICAgIG9yZGVyTGV2ZWw6IG5ld0xldmVsLCAvLyBMZXZlbCA9IExldmVsICsgMVxuICAgICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IG5ld1ZhbHVlLCAvLyBWYWx1ZSA9IEJhc2VCaWQgKiAoTXVsdGlwbGllciBeIExldmVsKVxuICAgICAgICAgICAgICAgIGNyeXB0bzFBbW91bnRIZWxkOiBjcnlwdG8xQm91Z2h0LCAvLyBTdG9yZSBhbW91bnQgaGVsZCBmb3IgTi0xIGxvZ2ljXG4gICAgICAgICAgICAgICAgb3JpZ2luYWxDb3N0Q3J5cHRvMjogYW1vdW50Q3J5cHRvMlRvVXNlLCAvLyBTdG9yZSBvcmlnaW5hbCBjb3N0IGZvciBwcm9maXQgY2FsY3VsYXRpb25cbiAgICAgICAgICAgICAgICBjcnlwdG8xVmFyOiBjcnlwdG8xQm91Z2h0LCAvLyBQb3NpdGl2ZSBhbW91bnQgb2YgQ3J5cHRvMSBhY3F1aXJlZFxuICAgICAgICAgICAgICAgIGNyeXB0bzJWYXI6IC1hbW91bnRDcnlwdG8yVG9Vc2UsIC8vIE5lZ2F0aXZlIGFtb3VudCBvZiBDcnlwdG8yIHNvbGRcbiAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfVEFSR0VUX1BSSUNFX1JPVycsIHBheWxvYWQ6IHVwZGF0ZWRSb3cgfSk7XG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9CQUxBTkNFUycsIHBheWxvYWQ6IHsgY3J5cHRvMTogY3VycmVudENyeXB0bzFCYWxhbmNlICsgY3J5cHRvMUJvdWdodCwgY3J5cHRvMjogY3VycmVudENyeXB0bzJCYWxhbmNlIC0gYW1vdW50Q3J5cHRvMlRvVXNlIH0gfSk7XG5cbiAgICAgICAgICAgICAgLy8gQ2FsY3VsYXRlIGNvc3QgYmFzaXMgZm9yIHRoaXMgQlVZIG9wZXJhdGlvbiBpbiB0ZXJtcyBvZiBjcnlwdG8yXG4gICAgICAgICAgICAgIGNvbnN0IGNvc3RCYXNpc0NyeXB0bzIgPSBhbW91bnRDcnlwdG8yVG9Vc2U7XG4gICAgICAgICAgICAgIGNvbnN0IGNvc3RCYXNpc1N0YWJsZWNvaW4gPSBzdGFibGVjb2luT2J0YWluZWQ7XG5cbiAgICAgICAgICAgICAgLy8gQWRkIGhpc3RvcnkgZW50cmllcyBmb3IgYm90aCBzdGVwcyBvZiB0aGUgc3RhYmxlY29pbiBzd2FwXG4gICAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnQUREX09SREVSX0hJU1RPUllfRU5UUlknLFxuICAgICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICAgIHBhaXI6IGAke2NvbmZpZy5jcnlwdG8yfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMixcbiAgICAgICAgICAgICAgICAgIG9yZGVyVHlwZTogXCJTRUxMXCIsXG4gICAgICAgICAgICAgICAgICBhbW91bnRDcnlwdG8xOiBhbW91bnRDcnlwdG8yVG9Vc2UsXG4gICAgICAgICAgICAgICAgICBhdmdQcmljZTogY3J5cHRvMlN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbk9idGFpbmVkLFxuICAgICAgICAgICAgICAgICAgcHJpY2UxOiBjcnlwdG8yU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMVN5bWJvbDogY29uZmlnLmNyeXB0bzIgfHwgJycsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8yU3ltYm9sOiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnJyxcbiAgICAgICAgICAgICAgICAgIC8vIEZvciBTRUxMIHN0ZXAgb2YgQlVZIG9wZXJhdGlvbjogc2hvdyBjb3N0IGJhc2lzIGFzIG5lZ2F0aXZlIChtb25leSBzcGVudClcbiAgICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzI6IC1jb3N0QmFzaXNDcnlwdG8yLCAvLyBDb3N0IG9mIHRoaXMgb3BlcmF0aW9uXG4gICAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8xOiBjcnlwdG8xU3RhYmxlY29pblByaWNlID4gMCA/IC1jb3N0QmFzaXNDcnlwdG8yIC8gY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA6IDBcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnQUREX09SREVSX0hJU1RPUllfRU5UUlknLFxuICAgICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICAgIHBhaXI6IGAke2NvbmZpZy5jcnlwdG8xfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMSxcbiAgICAgICAgICAgICAgICAgIG9yZGVyVHlwZTogXCJCVVlcIixcbiAgICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGNyeXB0bzFCb3VnaHQsXG4gICAgICAgICAgICAgICAgICBhdmdQcmljZTogY3J5cHRvMVN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbk9idGFpbmVkLFxuICAgICAgICAgICAgICAgICAgcHJpY2UxOiBjcnlwdG8xU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMVN5bWJvbDogY29uZmlnLmNyeXB0bzEgfHwgJycsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8yU3ltYm9sOiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnJyxcbiAgICAgICAgICAgICAgICAgIC8vIEZvciBCVVkgc3RlcDogc2hvdyB0aGUgdmFsdWUgYWNxdWlyZWQgKHBvc2l0aXZlKVxuICAgICAgICAgICAgICAgICAgcmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMjogY29zdEJhc2lzQ3J5cHRvMiwgLy8gVmFsdWUgYWNxdWlyZWQgaW4gY3J5cHRvMiB0ZXJtc1xuICAgICAgICAgICAgICAgICAgcmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMTogY3J5cHRvMUJvdWdodCAvLyBBbW91bnQgb2YgY3J5cHRvMSBhY3F1aXJlZFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYOKchSBTVEFCTEVDT0lOIEJVWTogQ291bnRlciAke2FjdGl2ZVJvdy5jb3VudGVyfSB8IFN0ZXAgMTogU29sZCAke2Ftb3VudENyeXB0bzJUb1VzZX0gJHtjb25maWcuY3J5cHRvMn0g4oaSICR7c3RhYmxlY29pbk9idGFpbmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59IHwgU3RlcCAyOiBCb3VnaHQgJHtjcnlwdG8xQm91Z2h0LnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IHwgTGV2ZWw6ICR7YWN0aXZlUm93Lm9yZGVyTGV2ZWx9IOKGkiAke25ld0xldmVsfWApO1xuICAgICAgICAgICAgICBwbGF5U291bmQoJ3NvdW5kT3JkZXJFeGVjdXRpb24nKTtcblxuICAgICAgICAgICAgICAvLyBTaG93IHRvYXN0IG5vdGlmaWNhdGlvbiBmb3IgU3RhYmxlY29pbiBCVVkgLSB0ZW1wb3JhcmlseSBkaXNhYmxlZFxuICAgICAgICAgICAgICAvKnRvYXN0KHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsXG4gICAgICAgICAgICAgICAgdGl0bGU6ICfwn5+iIEJVWSBFWEVDVVRFRCAoU3RhYmxlY29pbiknLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBgQm91Z2h0ICR7Y3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfSB2aWEgJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1gLFxuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwXG4gICAgICAgICAgICAgIH0pOyovXG5cbiAgICAgICAgICAgICAgLy8gU2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb24gZm9yIFN0YWJsZWNvaW4gQlVZXG4gICAgICAgICAgICAgIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbihcbiAgICAgICAgICAgICAgICBg8J+foiA8Yj5CVVkgRVhFQ1VURUQgKFN0YWJsZWNvaW4gU3dhcCk8L2I+XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfk4ogQ291bnRlcjogJHthY3RpdmVSb3cuY291bnRlcn1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+UhCBTdGVwIDE6IFNvbGQgJHthbW91bnRDcnlwdG8yVG9Vc2UudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn0g4oaSICR7c3RhYmxlY29pbk9idGFpbmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59XFxuYCArXG4gICAgICAgICAgICAgICAgYPCflIQgU3RlcCAyOiBCb3VnaHQgJHtjcnlwdG8xQm91Z2h0LnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfk4ogTGV2ZWw6ICR7YWN0aXZlUm93Lm9yZGVyTGV2ZWx9IOKGkiAke25ld0xldmVsfVxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5OIIE1vZGU6IFN0YWJsZWNvaW4gU3dhcGBcbiAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICBhY3Rpb25zVGFrZW4rKztcblxuICAgICAgICAgICAgICAvLyBVcGRhdGUgbG9jYWwgYmFsYW5jZSBmb3Igc3Vic2VxdWVudCBjaGVja3MgaW4gdGhpcyBjeWNsZVxuICAgICAgICAgICAgICBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgLT0gYW1vdW50Q3J5cHRvMlRvVXNlO1xuICAgICAgICAgICAgICBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgKz0gY3J5cHRvMUJvdWdodDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBTVEVQIDM6IENoZWNrIGFuZCBQcm9jZXNzIEluZmVyaW9yIFRhcmdldFJvdyAoVGFyZ2V0Um93Tl9taW51c18xKSAtIEFMV0FZUywgcmVnYXJkbGVzcyBvZiBCVVlcbiAgICAgICAgICBjb25zdCBjdXJyZW50Q291bnRlciA9IGFjdGl2ZVJvdy5jb3VudGVyO1xuICAgICAgICAgIGNvbnN0IGluZmVyaW9yUm93ID0gc29ydGVkUm93c0ZvckxvZ2ljLmZpbmQocm93ID0+IHJvdy5jb3VudGVyID09PSBjdXJyZW50Q291bnRlciAtIDEpO1xuXG4gICAgICAgICAgaWYgKGluZmVyaW9yUm93ICYmIGluZmVyaW9yUm93LnN0YXR1cyA9PT0gXCJGdWxsXCIgJiYgaW5mZXJpb3JSb3cuY3J5cHRvMUFtb3VudEhlbGQgJiYgaW5mZXJpb3JSb3cub3JpZ2luYWxDb3N0Q3J5cHRvMikge1xuICAgICAgICAgICAgLy8gRXhlY3V0ZSBUd28tU3RlcCBcIlNlbGwgQ3J5cHRvMSAmIFJlYWNxdWlyZSBDcnlwdG8yIHZpYSBTdGFibGVjb2luXCIgZm9yIFRhcmdldFJvd05fbWludXNfMVxuICAgICAgICAgICAgY29uc3QgYW1vdW50Q3J5cHRvMVRvU2VsbCA9IGluZmVyaW9yUm93LmNyeXB0bzFBbW91bnRIZWxkO1xuXG4gICAgICAgICAgICAvLyBTdGVwIEE6IFNlbGwgQ3J5cHRvMSBmb3IgUHJlZmVycmVkU3RhYmxlY29pblxuICAgICAgICAgICAgY29uc3QgY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8xIHx8ICdCVEMnKSAvIGdldFVTRFByaWNlKGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICBjb25zdCBzdGFibGVjb2luRnJvbUMxU2VsbCA9IGFtb3VudENyeXB0bzFUb1NlbGwgKiBjcnlwdG8xU3RhYmxlY29pblByaWNlO1xuXG4gICAgICAgICAgICAvLyBTdGVwIEI6IEJ1eSBDcnlwdG8yIHdpdGggU3RhYmxlY29pblxuICAgICAgICAgICAgY29uc3QgY3J5cHRvMlN0YWJsZWNvaW5QcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8yIHx8ICdVU0RUJykgLyBnZXRVU0RQcmljZShjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnVVNEVCcpO1xuICAgICAgICAgICAgY29uc3QgY3J5cHRvMlJlYWNxdWlyZWQgPSBzdGFibGVjb2luRnJvbUMxU2VsbCAvIGNyeXB0bzJTdGFibGVjb2luUHJpY2U7XG5cbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSByZWFsaXplZCBwcm9maXQgaW4gQ3J5cHRvMlxuICAgICAgICAgICAgY29uc3QgcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPSBjcnlwdG8yUmVhY3F1aXJlZCAtIGluZmVyaW9yUm93Lm9yaWdpbmFsQ29zdENyeXB0bzI7XG5cbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBDcnlwdG8xIHByb2ZpdC9sb3NzIC0gY29udmVydCBDcnlwdG8yIHByb2ZpdCB0byBDcnlwdG8xIGVxdWl2YWxlbnRcbiAgICAgICAgICAgIGNvbnN0IHJlYWxpemVkUHJvZml0Q3J5cHRvMSA9IGNyeXB0bzFTdGFibGVjb2luUHJpY2UgPiAwID8gcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgLyBjcnlwdG8xU3RhYmxlY29pblByaWNlIDogMDtcblxuICAgICAgICAgICAgLy8gVXBkYXRlIFJvdyBOLTE6IEZ1bGwg4oaSIEZyZWUsIExldmVsIFVOQ0hBTkdFRCwgVmFycyBjbGVhcmVkXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkSW5mZXJpb3JSb3c6IFRhcmdldFByaWNlUm93ID0ge1xuICAgICAgICAgICAgICAuLi5pbmZlcmlvclJvdyxcbiAgICAgICAgICAgICAgc3RhdHVzOiBcIkZyZWVcIiwgLy8gRnVsbCDihpIgRnJlZVxuICAgICAgICAgICAgICAvLyBvcmRlckxldmVsOiBSRU1BSU5TIFVOQ0hBTkdFRCBwZXIgc3BlY2lmaWNhdGlvblxuICAgICAgICAgICAgICBjcnlwdG8xQW1vdW50SGVsZDogdW5kZWZpbmVkLCAvLyBDbGVhciBoZWxkIGFtb3VudFxuICAgICAgICAgICAgICBvcmlnaW5hbENvc3RDcnlwdG8yOiB1bmRlZmluZWQsIC8vIENsZWFyIG9yaWdpbmFsIGNvc3RcbiAgICAgICAgICAgICAgdmFsdWVMZXZlbDogY29uZmlnLmJhc2VCaWQgKiBNYXRoLnBvdyhjb25maWcubXVsdGlwbGllciwgaW5mZXJpb3JSb3cub3JkZXJMZXZlbCksIC8vIFJlY2FsY3VsYXRlIHZhbHVlIGJhc2VkIG9uIGN1cnJlbnQgbGV2ZWxcbiAgICAgICAgICAgICAgY3J5cHRvMVZhcjogMCwgLy8gQ2xlYXIgdG8gMC4wMCAoRnJlZSBzdGF0dXMpXG4gICAgICAgICAgICAgIGNyeXB0bzJWYXI6IDAsIC8vIENsZWFyIHRvIDAuMDAgKEZyZWUgc3RhdHVzKVxuICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX1RBUkdFVF9QUklDRV9ST1cnLCBwYXlsb2FkOiB1cGRhdGVkSW5mZXJpb3JSb3cgfSk7XG4gICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfQkFMQU5DRVMnLCBwYXlsb2FkOiB7IGNyeXB0bzE6IGN1cnJlbnRDcnlwdG8xQmFsYW5jZSAtIGFtb3VudENyeXB0bzFUb1NlbGwsIGNyeXB0bzI6IGN1cnJlbnRDcnlwdG8yQmFsYW5jZSArIGNyeXB0bzJSZWFjcXVpcmVkIH0gfSk7XG5cbiAgICAgICAgICAgIC8vIEFkZCBoaXN0b3J5IGVudHJpZXMgZm9yIGJvdGggc3RlcHMgb2YgdGhlIE4tMSBzdGFibGVjb2luIHN3YXBcbiAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59YCxcbiAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMSxcbiAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiU0VMTFwiLFxuICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGFtb3VudENyeXB0bzFUb1NlbGwsXG4gICAgICAgICAgICAgICAgYXZnUHJpY2U6IGNyeXB0bzFTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgdmFsdWVDcnlwdG8yOiBzdGFibGVjb2luRnJvbUMxU2VsbCxcbiAgICAgICAgICAgICAgICBwcmljZTE6IGNyeXB0bzFTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgY3J5cHRvMVN5bWJvbDogY29uZmlnLmNyeXB0bzEgfHwgJycsXG4gICAgICAgICAgICAgICAgY3J5cHRvMlN5bWJvbDogY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4gfHwgJycsXG4gICAgICAgICAgICAgICAgLy8gRm9yIFNFTEwgc3RlcDogc2hvdyB0aGUgc3RhYmxlY29pbiB2YWx1ZSByZWNlaXZlZCAocG9zaXRpdmUpXG4gICAgICAgICAgICAgICAgcmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMjogc3RhYmxlY29pbkZyb21DMVNlbGwgLyBjcnlwdG8yU3RhYmxlY29pblByaWNlLCAvLyBDb252ZXJ0IHN0YWJsZWNvaW4gdG8gY3J5cHRvMiBlcXVpdmFsZW50XG4gICAgICAgICAgICAgICAgcmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMTogYW1vdW50Q3J5cHRvMVRvU2VsbCAvLyBBbW91bnQgb2YgY3J5cHRvMSBzb2xkIChuZWdhdGl2ZSBpbXBhY3Qgb24gaG9sZGluZ3MpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgIHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWScsXG4gICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICAgIHBhaXI6IGAke2NvbmZpZy5jcnlwdG8yfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsXG4gICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzIsXG4gICAgICAgICAgICAgICAgb3JkZXJUeXBlOiBcIkJVWVwiLFxuICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGNyeXB0bzJSZWFjcXVpcmVkLFxuICAgICAgICAgICAgICAgIGF2Z1ByaWNlOiBjcnlwdG8yU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbkZyb21DMVNlbGwsXG4gICAgICAgICAgICAgICAgcHJpY2UxOiBjcnlwdG8yU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8yIHx8ICcnLFxuICAgICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICcnLFxuICAgICAgICAgICAgICAgIC8vIEZvciBCVVkgc3RlcDogc2hvdyB0aGUgbmV0IHByb2ZpdC9sb3NzIGZyb20gdGhlIGNvbXBsZXRlIG9wZXJhdGlvblxuICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzI6IHJlYWxpemVkUHJvZml0SW5DcnlwdG8yLFxuICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzE6IHJlYWxpemVkUHJvZml0Q3J5cHRvMVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfkrAgU1RBQkxFQ09JTiBTV0FQIFAvTDogQ3J5cHRvMj0ke3JlYWxpemVkUHJvZml0SW5DcnlwdG8yLnRvRml4ZWQoNil9LCBDcnlwdG8xPSR7cmVhbGl6ZWRQcm9maXRDcnlwdG8xLnRvRml4ZWQoNil9YCk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgU1RBQkxFQ09JTiBTRUxMOiBDb3VudGVyICR7Y3VycmVudENvdW50ZXIgLSAxfSB8IFN0ZXAgQTogU29sZCAke2Ftb3VudENyeXB0bzFUb1NlbGwudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX0g4oaSICR7c3RhYmxlY29pbkZyb21DMVNlbGwudG9GaXhlZCgyKX0gJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn0gfCBTdGVwIEI6IEJvdWdodCAke2NyeXB0bzJSZWFjcXVpcmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9IHwgUHJvZml0OiAke3JlYWxpemVkUHJvZml0SW5DcnlwdG8yLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9IHwgTGV2ZWw6ICR7aW5mZXJpb3JSb3cub3JkZXJMZXZlbH0gKHVuY2hhbmdlZClgKTtcbiAgICAgICAgICAgIHBsYXlTb3VuZCgnc291bmRPcmRlckV4ZWN1dGlvbicpO1xuXG4gICAgICAgICAgICAvLyBTaG93IHRvYXN0IG5vdGlmaWNhdGlvbiBmb3IgU3RhYmxlY29pbiBTRUxMIC0gdGVtcG9yYXJpbHkgZGlzYWJsZWRcbiAgICAgICAgICAgIGNvbnN0IHByb2ZpdEVtb2ppID0gcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPiAwID8gJ/Cfk4gnIDogcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPCAwID8gJ/Cfk4knIDogJ+Kelic7XG4gICAgICAgICAgICAvKnRvYXN0KHtcbiAgICAgICAgICAgICAgdHlwZTogcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPiAwID8gJ3N1Y2Nlc3MnIDogcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPCAwID8gJ3dhcm5pbmcnIDogJ2luZm8nLFxuICAgICAgICAgICAgICB0aXRsZTogJ/CflLQgU0VMTCBFWEVDVVRFRCAoU3RhYmxlY29pbiknLFxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYFNvbGQgJHthbW91bnRDcnlwdG8xVG9TZWxsLnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IHwgJHtwcm9maXRFbW9qaX0gUHJvZml0OiAkJHtyZWFsaXplZFByb2ZpdEluQ3J5cHRvMi50b0ZpeGVkKDIpfWAsXG4gICAgICAgICAgICAgIGR1cmF0aW9uOiAyMDAwXG4gICAgICAgICAgICB9KTsqL1xuXG4gICAgICAgICAgICAvLyBTZW5kIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgU3RhYmxlY29pbiBTRUxMXG4gICAgICAgICAgICBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24oXG4gICAgICAgICAgICAgIGDwn5S0IDxiPlNFTEwgRVhFQ1VURUQgKFN0YWJsZWNvaW4gU3dhcCk8L2I+XFxuYCArXG4gICAgICAgICAgICAgIGDwn5OKIENvdW50ZXI6ICR7Y3VycmVudENvdW50ZXIgLSAxfVxcbmAgK1xuICAgICAgICAgICAgICBg8J+UhCBTdGVwIEE6IFNvbGQgJHthbW91bnRDcnlwdG8xVG9TZWxsLnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IOKGkiAke3N0YWJsZWNvaW5Gcm9tQzFTZWxsLnRvRml4ZWQoMil9ICR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59XFxuYCArXG4gICAgICAgICAgICAgIGDwn5SEIFN0ZXAgQjogQm91Z2h0ICR7Y3J5cHRvMlJlYWNxdWlyZWQudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn1cXG5gICtcbiAgICAgICAgICAgICAgYCR7cHJvZml0RW1vaml9IFByb2ZpdDogJHtyZWFsaXplZFByb2ZpdEluQ3J5cHRvMi50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfVxcbmAgK1xuICAgICAgICAgICAgICBg8J+TiiBMZXZlbDogJHtpbmZlcmlvclJvdy5vcmRlckxldmVsfSAodW5jaGFuZ2VkKVxcbmAgK1xuICAgICAgICAgICAgICBg8J+TiCBNb2RlOiBTdGFibGVjb2luIFN3YXBgXG4gICAgICAgICAgICApO1xuXG4gICAgICAgICAgICBhY3Rpb25zVGFrZW4rKztcblxuICAgICAgICAgICAgLy8gVXBkYXRlIGxvY2FsIGJhbGFuY2UgZm9yIHN1YnNlcXVlbnQgY2hlY2tzIGluIHRoaXMgY3ljbGVcbiAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8xQmFsYW5jZSAtPSBhbW91bnRDcnlwdG8xVG9TZWxsO1xuICAgICAgICAgICAgY3VycmVudENyeXB0bzJCYWxhbmNlICs9IGNyeXB0bzJSZWFjcXVpcmVkO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChhY3Rpb25zVGFrZW4gPiAwKSB7XG4gICAgICBjb25zb2xlLmxvZyhg8J+OryBDWUNMRSBDT01QTEVURTogJHthY3Rpb25zVGFrZW59IGFjdGlvbnMgdGFrZW4gYXQgcHJpY2UgJCR7Y3VycmVudE1hcmtldFByaWNlLnRvRml4ZWQoMil9YCk7XG4gICAgfVxuXG4gIH0sIFtzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMsIHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSwgc3RhdGUudGFyZ2V0UHJpY2VSb3dzLCBzdGF0ZS5jb25maWcsIHN0YXRlLmNyeXB0bzFCYWxhbmNlLCBzdGF0ZS5jcnlwdG8yQmFsYW5jZSwgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsIGRpc3BhdGNoLCBwbGF5U291bmQsIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbl0pO1xuXG5cbiAgY29uc3QgZ2V0RGlzcGxheU9yZGVycyA9IHVzZUNhbGxiYWNrKCgpOiBEaXNwbGF5T3JkZXJSb3dbXSA9PiB7XG4gICAgaWYgKCFzdGF0ZS50YXJnZXRQcmljZVJvd3MgfHwgIUFycmF5LmlzQXJyYXkoc3RhdGUudGFyZ2V0UHJpY2VSb3dzKSkge1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIHJldHVybiBzdGF0ZS50YXJnZXRQcmljZVJvd3MubWFwKChyb3c6IFRhcmdldFByaWNlUm93KSA9PiB7XG4gICAgICBjb25zdCBjdXJyZW50UHJpY2UgPSBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UgfHwgMDtcbiAgICAgIGNvbnN0IHRhcmdldFByaWNlID0gcm93LnRhcmdldFByaWNlIHx8IDA7XG4gICAgICBjb25zdCBwZXJjZW50RnJvbUFjdHVhbFByaWNlID0gY3VycmVudFByaWNlICYmIHRhcmdldFByaWNlXG4gICAgICAgID8gKChjdXJyZW50UHJpY2UgLyB0YXJnZXRQcmljZSkgLSAxKSAqIDEwMFxuICAgICAgICA6IDA7XG5cbiAgICAgIGxldCBpbmNvbWVDcnlwdG8xOiBudW1iZXIgfCB1bmRlZmluZWQ7XG4gICAgICBsZXQgaW5jb21lQ3J5cHRvMjogbnVtYmVyIHwgdW5kZWZpbmVkO1xuXG4gICAgICBpZiAocm93LnN0YXR1cyA9PT0gXCJGdWxsXCIgJiYgcm93LmNyeXB0bzFBbW91bnRIZWxkICYmIHJvdy5vcmlnaW5hbENvc3RDcnlwdG8yKSB7XG4gICAgICAgIC8vIENhbGN1bGF0ZSB1bnJlYWxpemVkIHByb2ZpdC9sb3NzIGJhc2VkIG9uIGN1cnJlbnQgbWFya2V0IHByaWNlIHZzIG9yaWdpbmFsIGNvc3RcbiAgICAgICAgaWYgKHN0YXRlLmNvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiKSB7XG4gICAgICAgICAgLy8gRm9yIHN0YWJsZWNvaW4gc3dhcDogQ2FsY3VsYXRlIHByb2ZpdCBiYXNlZCBvbiBjdXJyZW50IG1hcmtldCBwcmljZSB2cyB0YXJnZXQgcHJpY2VcbiAgICAgICAgICAvLyBJZiBjdXJyZW50IHByaWNlID4gdGFyZ2V0IHByaWNlID0gcHJvZml0IChwb3NpdGl2ZSBpbmNvbWUpXG4gICAgICAgICAgLy8gSWYgY3VycmVudCBwcmljZSA8IHRhcmdldCBwcmljZSA9IGxvc3MgKG5lZ2F0aXZlIGluY29tZSlcbiAgICAgICAgICBjb25zdCB0YXJnZXRQcmljZSA9IHJvdy50YXJnZXRQcmljZSB8fCAwO1xuICAgICAgICAgIGNvbnN0IHByaWNlRGlmZmVyZW5jZSA9IGN1cnJlbnRQcmljZSAtIHRhcmdldFByaWNlO1xuICAgICAgICAgIGNvbnN0IHByb2ZpdFBlcmNlbnRhZ2UgPSB0YXJnZXRQcmljZSA+IDAgPyAocHJpY2VEaWZmZXJlbmNlIC8gdGFyZ2V0UHJpY2UpIDogMDtcblxuICAgICAgICAgIC8vIENhbGN1bGF0ZSBwcm9maXQvbG9zcyBiYXNlZCBvbiB0aGUgYW1vdW50IGhlbGQgYW5kIHByaWNlIGRpZmZlcmVuY2VcbiAgICAgICAgICBjb25zdCB0b3RhbFVucmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPSByb3cuY3J5cHRvMUFtb3VudEhlbGQgKiBwcmljZURpZmZlcmVuY2U7XG4gICAgICAgICAgaW5jb21lQ3J5cHRvMiA9IHRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMjtcbiAgICAgICAgICBpZiAoY3VycmVudFByaWNlID4gMCkge1xuICAgICAgICAgICAgaW5jb21lQ3J5cHRvMSA9IHRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMiAvIGN1cnJlbnRQcmljZTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gU2ltcGxlIHNwb3QgbW9kZSBsb2dpY1xuICAgICAgICAgIGNvbnN0IHRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMiA9IChjdXJyZW50UHJpY2UgKiByb3cuY3J5cHRvMUFtb3VudEhlbGQpIC0gcm93Lm9yaWdpbmFsQ29zdENyeXB0bzI7XG4gICAgICAgICAgaW5jb21lQ3J5cHRvMiA9IHRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMjtcbiAgICAgICAgICBpZiAoY3VycmVudFByaWNlID4gMCkge1xuICAgICAgICAgICAgaW5jb21lQ3J5cHRvMSA9IHRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMiAvIGN1cnJlbnRQcmljZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4ucm93LFxuICAgICAgICBjdXJyZW50UHJpY2UsXG4gICAgICAgIHByaWNlRGlmZmVyZW5jZTogdGFyZ2V0UHJpY2UgLSBjdXJyZW50UHJpY2UsXG4gICAgICAgIHByaWNlRGlmZmVyZW5jZVBlcmNlbnQ6IGN1cnJlbnRQcmljZSA+IDAgPyAoKHRhcmdldFByaWNlIC0gY3VycmVudFByaWNlKSAvIGN1cnJlbnRQcmljZSkgKiAxMDAgOiAwLFxuICAgICAgICBwb3RlbnRpYWxQcm9maXRDcnlwdG8xOiAoc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQgLyAxMDApICogcm93LnZhbHVlTGV2ZWwgLyAodGFyZ2V0UHJpY2UgfHwgMSksXG4gICAgICAgIHBvdGVudGlhbFByb2ZpdENyeXB0bzI6IChzdGF0ZS5jb25maWcuaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCAvIDEwMCkgKiByb3cudmFsdWVMZXZlbCxcbiAgICAgICAgcGVyY2VudEZyb21BY3R1YWxQcmljZSxcbiAgICAgICAgaW5jb21lQ3J5cHRvMSxcbiAgICAgICAgaW5jb21lQ3J5cHRvMixcbiAgICAgIH07XG4gICAgfSkuc29ydCgoYTogRGlzcGxheU9yZGVyUm93LCBiOiBEaXNwbGF5T3JkZXJSb3cpID0+IGIudGFyZ2V0UHJpY2UgLSBhLnRhcmdldFByaWNlKTtcbiAgfSwgW3N0YXRlLnRhcmdldFByaWNlUm93cywgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLCBzdGF0ZS5jb25maWcuaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudCwgc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQsIHN0YXRlLmNvbmZpZy5iYXNlQmlkLCBzdGF0ZS5jb25maWcubXVsdGlwbGllcl0pO1xuXG5cbiAgLy8gQmFja2VuZCBJbnRlZ3JhdGlvbiBGdW5jdGlvbnNcbiAgY29uc3Qgc2F2ZUNvbmZpZ1RvQmFja2VuZCA9IHVzZUNhbGxiYWNrKGFzeW5jIChjb25maWc6IFRyYWRpbmdDb25maWcpOiBQcm9taXNlPHN0cmluZyB8IG51bGw+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY29uZmlnRGF0YSA9IHtcbiAgICAgICAgbmFtZTogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ICR7Y29uZmlnLnRyYWRpbmdNb2RlfWAsXG4gICAgICAgIHRyYWRpbmdNb2RlOiBjb25maWcudHJhZGluZ01vZGUsXG4gICAgICAgIGNyeXB0bzE6IGNvbmZpZy5jcnlwdG8xLFxuICAgICAgICBjcnlwdG8yOiBjb25maWcuY3J5cHRvMixcbiAgICAgICAgYmFzZUJpZDogY29uZmlnLmJhc2VCaWQsXG4gICAgICAgIG11bHRpcGxpZXI6IGNvbmZpZy5tdWx0aXBsaWVyLFxuICAgICAgICBudW1EaWdpdHM6IGNvbmZpZy5udW1EaWdpdHMsXG4gICAgICAgIHNsaXBwYWdlUGVyY2VudDogY29uZmlnLnNsaXBwYWdlUGVyY2VudCxcbiAgICAgICAgaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudDogY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQsXG4gICAgICAgIGluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQ6IGNvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50LFxuICAgICAgICBwcmVmZXJyZWRTdGFibGVjb2luOiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbixcbiAgICAgICAgdGFyZ2V0UHJpY2VzOiBzdGF0ZS50YXJnZXRQcmljZVJvd3MubWFwKHJvdyA9PiByb3cudGFyZ2V0UHJpY2UpXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRyYWRpbmdBcGkuc2F2ZUNvbmZpZyhjb25maWdEYXRhKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQ29uZmlnIHNhdmVkIHRvIGJhY2tlbmQ6JywgcmVzcG9uc2UpO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLmNvbmZpZz8uaWQgfHwgbnVsbDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBzYXZlIGNvbmZpZyB0byBiYWNrZW5kOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfSwgW3N0YXRlLnRhcmdldFByaWNlUm93c10pO1xuXG4gIGNvbnN0IHN0YXJ0QmFja2VuZEJvdCA9IHVzZUNhbGxiYWNrKGFzeW5jIChjb25maWdJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdHJhZGluZ0FwaS5zdGFydEJvdChjb25maWdJZCk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIEJvdCBzdGFydGVkIG9uIGJhY2tlbmQ6JywgcmVzcG9uc2UpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gc3RhcnQgYm90IG9uIGJhY2tlbmQ6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IHN0b3BCYWNrZW5kQm90ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbmZpZ0lkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0cmFkaW5nQXBpLnN0b3BCb3QoY29uZmlnSWQpO1xuICAgICAgY29uc29sZS5sb2coJ+KchSBCb3Qgc3RvcHBlZCBvbiBiYWNrZW5kOicsIHJlc3BvbnNlKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIHN0b3AgYm90IG9uIGJhY2tlbmQ6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IGNoZWNrQmFja2VuZFN0YXR1cyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBjb25zdCBhcGlVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMO1xuICAgIGlmICghYXBpVXJsKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvcjogTkVYVF9QVUJMSUNfQVBJX1VSTCBpcyBub3QgZGVmaW5lZC4gQmFja2VuZCBjb25uZWN0aXZpdHkgY2hlY2sgY2Fubm90IGJlIHBlcmZvcm1lZC4nKTtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9CQUNLRU5EX1NUQVRVUycsIHBheWxvYWQ6ICdvZmZsaW5lJyB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGhlYWx0aFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YXBpVXJsfS9oZWFsdGgvYCk7XG4gICAgICBpZiAoIWhlYWx0aFJlc3BvbnNlLm9rKSB7XG4gICAgICAgIC8vIExvZyBtb3JlIGRldGFpbHMgaWYgdGhlIHJlc3BvbnNlIHdhcyByZWNlaXZlZCBidXQgbm90IE9LXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEJhY2tlbmQgaGVhbHRoIGNoZWNrIGZhaWxlZCB3aXRoIHN0YXR1czogJHtoZWFsdGhSZXNwb25zZS5zdGF0dXN9ICR7aGVhbHRoUmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2VUZXh0ID0gYXdhaXQgaGVhbHRoUmVzcG9uc2UudGV4dCgpLmNhdGNoKCgpID0+ICdDb3VsZCBub3QgcmVhZCByZXNwb25zZSB0ZXh0LicpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdCYWNrZW5kIGhlYWx0aCBjaGVjayByZXNwb25zZSBib2R5OicsIHJlc3BvbnNlVGV4dCk7XG4gICAgICB9XG4gICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnLCBwYXlsb2FkOiBoZWFsdGhSZXNwb25zZS5vayA/ICdvbmxpbmUnIDogJ29mZmxpbmUnIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9CQUNLRU5EX1NUQVRVUycsIHBheWxvYWQ6ICdvZmZsaW5lJyB9KTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0JhY2tlbmQgY29ubmVjdGl2aXR5IGNoZWNrIGZhaWxlZC4gRXJyb3IgZGV0YWlsczonLCBlcnJvcik7XG4gICAgICBpZiAoZXJyb3IuY2F1c2UpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmV0Y2ggZXJyb3IgY2F1c2U6JywgZXJyb3IuY2F1c2UpO1xuICAgICAgfVxuICAgICAgLy8gSXQncyBhbHNvIHVzZWZ1bCB0byBsb2cgdGhlIGFwaVVybCB0byBlbnN1cmUgaXQncyB3aGF0IHdlIGV4cGVjdFxuICAgICAgY29uc29sZS5lcnJvcignQXR0ZW1wdGVkIHRvIGZldGNoIEFQSSBVUkw6JywgYCR7YXBpVXJsfS9oZWFsdGgvYCk7XG4gICAgfVxuICB9LCBbZGlzcGF0Y2hdKTtcblxuICAvLyBJbml0aWFsaXplIGJhY2tlbmQgc3RhdHVzIGNoZWNrIChvbmUtdGltZSBvbmx5KVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWwgY2hlY2sgZm9yIGJhY2tlbmQgc3RhdHVzIG9ubHlcbiAgICBjaGVja0JhY2tlbmRTdGF0dXMoKTtcbiAgfSwgW2NoZWNrQmFja2VuZFN0YXR1c10pO1xuXG4gIC8vIFNhdmUgc3RhdGUgdG8gbG9jYWxTdG9yYWdlIHdoZW5ldmVyIGl0IGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzYXZlU3RhdGVUb0xvY2FsU3RvcmFnZShzdGF0ZSk7XG4gIH0sIFtzdGF0ZV0pO1xuXG5cbiAgLy8gRWZmZWN0IHRvIGhhbmRsZSBib3Qgd2FybS11cCBwZXJpb2QgKGltbWVkaWF0ZSBleGVjdXRpb24pXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1dhcm1pbmdVcCcpIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiQm90IGlzIFdhcm1pbmcgVXAuLi4gSW1tZWRpYXRlIGV4ZWN1dGlvbiBlbmFibGVkLlwiKTtcbiAgICAgIC8vIEltbWVkaWF0ZSB0cmFuc2l0aW9uIHRvIFJ1bm5pbmcgc3RhdGUgLSBubyBkZWxheXNcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NZU1RFTV9DT01QTEVURV9XQVJNVVAnIH0pO1xuICAgICAgY29uc29sZS5sb2coXCJCb3QgaXMgbm93IFJ1bm5pbmcgaW1tZWRpYXRlbHkuXCIpO1xuICAgIH1cbiAgfSwgW3N0YXRlLmJvdFN5c3RlbVN0YXR1cywgZGlzcGF0Y2hdKTtcblxuICAvLyBFZmZlY3QgdG8gaGFuZGxlIHNlc3Npb24gcnVudGltZSB0cmFja2luZ1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICBjb25zdCBjdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpO1xuXG4gICAgaWYgKGN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJykge1xuICAgICAgICAvLyBCb3Qgc3RhcnRlZCBydW5uaW5nLCBzdGFydCBydW50aW1lIHRyYWNraW5nXG4gICAgICAgIHNlc3Npb25NYW5hZ2VyLnN0YXJ0U2Vzc2lvblJ1bnRpbWUoY3VycmVudFNlc3Npb25JZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU3RhcnRlZCBydW50aW1lIHRyYWNraW5nIGZvciBzZXNzaW9uOicsIGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgfSBlbHNlIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdTdG9wcGVkJykge1xuICAgICAgICAvLyBCb3Qgc3RvcHBlZCwgc3RvcCBydW50aW1lIHRyYWNraW5nXG4gICAgICAgIHNlc3Npb25NYW5hZ2VyLnN0b3BTZXNzaW9uUnVudGltZShjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KPue+4jyBTdG9wcGVkIHJ1bnRpbWUgdHJhY2tpbmcgZm9yIHNlc3Npb246JywgY3VycmVudFNlc3Npb25JZCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdGUuYm90U3lzdGVtU3RhdHVzXSk7XG5cbiAgLy8gQXV0by1jcmVhdGUgc2Vzc2lvbiB3aGVuIGJvdCBzdGFydHMgaWYgbm9uZSBleGlzdHMgYW5kIGhhbmRsZSBydW50aW1lIHRyYWNraW5nXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuXG4gICAgLy8gQ2hlY2sgaWYgd2UgbmVlZCB0byBjcmVhdGUgYSBzZXNzaW9uIHdoZW4gYm90IHN0YXJ0c1xuICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdXYXJtaW5nVXAnICYmICFzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCkpIHtcbiAgICAgIC8vIE9ubHkgY3JlYXRlIGlmIHdlIGhhdmUgdmFsaWQgY3J5cHRvIGNvbmZpZ3VyYXRpb24gQU5EIHRhcmdldCBwcmljZXMgc2V0XG4gICAgICAvLyBUaGlzIHByZXZlbnRzIGF1dG8tY3JlYXRpb24gZm9yIGZyZXNoIHdpbmRvd3NcbiAgICAgIGlmIChzdGF0ZS5jb25maWcuY3J5cHRvMSAmJiBzdGF0ZS5jb25maWcuY3J5cHRvMiAmJiBzdGF0ZS50YXJnZXRQcmljZVJvd3MubGVuZ3RoID4gMCkge1xuICAgICAgICBzZXNzaW9uTWFuYWdlci5jcmVhdGVOZXdTZXNzaW9uV2l0aEF1dG9OYW1lKFxuICAgICAgICAgIHN0YXRlLmNvbmZpZyxcbiAgICAgICAgICB1bmRlZmluZWQsXG4gICAgICAgICAgeyBjcnlwdG8xOiBzdGF0ZS5jcnlwdG8xQmFsYW5jZSwgY3J5cHRvMjogc3RhdGUuY3J5cHRvMkJhbGFuY2UsIHN0YWJsZWNvaW46IHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlIH1cbiAgICAgICAgKVxuICAgICAgICAgIC50aGVuKChzZXNzaW9uSWQpID0+IHtcbiAgICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLnNldEN1cnJlbnRTZXNzaW9uKHNlc3Npb25JZCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEF1dG8tY3JlYXRlZCBzZXNzaW9uOicsIHNlc3Npb25JZCk7XG4gICAgICAgICAgfSlcbiAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGF1dG8tY3JlYXRlIHNlc3Npb246JywgZXJyb3IpO1xuICAgICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEhhbmRsZSBydW50aW1lIHRyYWNraW5nIGJhc2VkIG9uIGJvdCBzdGF0dXNcbiAgICBjb25zdCBjdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpO1xuICAgIGlmIChjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycpIHtcbiAgICAgICAgLy8gU3RhcnQgcnVudGltZSB0cmFja2luZyB3aGVuIGJvdCBiZWNvbWVzIGFjdGl2ZVxuICAgICAgICBzZXNzaW9uTWFuYWdlci5zdGFydFNlc3Npb25SdW50aW1lKGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgICBjb25zb2xlLmxvZygn4o+x77iPIFN0YXJ0ZWQgcnVudGltZSB0cmFja2luZyBmb3Igc2Vzc2lvbjonLCBjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgIH0gZWxzZSBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnU3RvcHBlZCcpIHtcbiAgICAgICAgLy8gU3RvcCBydW50aW1lIHRyYWNraW5nIHdoZW4gYm90IHN0b3BzXG4gICAgICAgIHNlc3Npb25NYW5hZ2VyLnN0b3BTZXNzaW9uUnVudGltZShjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KPue+4jyBTdG9wcGVkIHJ1bnRpbWUgdHJhY2tpbmcgZm9yIHNlc3Npb246JywgY3VycmVudFNlc3Npb25JZCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdGUuYm90U3lzdGVtU3RhdHVzLCBzdGF0ZS5jb25maWcuY3J5cHRvMSwgc3RhdGUuY29uZmlnLmNyeXB0bzJdKTtcblxuICAvLyBIYW5kbGUgY3J5cHRvIHBhaXIgY2hhbmdlcyBkdXJpbmcgYWN0aXZlIHRyYWRpbmdcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzZXNzaW9uTWFuYWdlciA9IFNlc3Npb25NYW5hZ2VyLmdldEluc3RhbmNlKCk7XG4gICAgY29uc3QgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25NYW5hZ2VyLmdldEN1cnJlbnRTZXNzaW9uSWQoKTtcblxuICAgIGlmIChjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICBjb25zdCBjdXJyZW50U2Vzc2lvbiA9IHNlc3Npb25NYW5hZ2VyLmxvYWRTZXNzaW9uKGN1cnJlbnRTZXNzaW9uSWQpO1xuXG4gICAgICAvLyBDaGVjayBpZiBjcnlwdG8gcGFpciBoYXMgY2hhbmdlZCBmcm9tIHRoZSBjdXJyZW50IHNlc3Npb25cbiAgICAgIGlmIChjdXJyZW50U2Vzc2lvbiAmJlxuICAgICAgICAgIChjdXJyZW50U2Vzc2lvbi5jb25maWcuY3J5cHRvMSAhPT0gc3RhdGUuY29uZmlnLmNyeXB0bzEgfHxcbiAgICAgICAgICAgY3VycmVudFNlc3Npb24uY29uZmlnLmNyeXB0bzIgIT09IHN0YXRlLmNvbmZpZy5jcnlwdG8yKSkge1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIENyeXB0byBwYWlyIGNoYW5nZWQgZHVyaW5nIHNlc3Npb24sIGF1dG8tc2F2aW5nIGFuZCByZXNldHRpbmcuLi4nKTtcblxuICAgICAgICAvLyBBdXRvLXNhdmUgY3VycmVudCBzZXNzaW9uIGlmIGJvdCB3YXMgcnVubmluZyBvciBoYXMgZGF0YVxuICAgICAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycgfHxcbiAgICAgICAgICAgIHN0YXRlLnRhcmdldFByaWNlUm93cy5sZW5ndGggPiAwIHx8XG4gICAgICAgICAgICBzdGF0ZS5vcmRlckhpc3RvcnkubGVuZ3RoID4gMCkge1xuXG4gICAgICAgICAgLy8gTm90ZTogTm8gbmVlZCB0byBzdG9wIGJhY2tlbmQgYm90IGR1cmluZyBjcnlwdG8gcGFpciBjaGFuZ2VcbiAgICAgICAgICAvLyBGcm9udGVuZCBib3Qgc3lzdGVtIGlzIHNlcGFyYXRlIGZyb20gYmFja2VuZCBib3Qgc3lzdGVtXG4gICAgICAgICAgLy8gVGhlIGZyb250ZW5kIGJvdCBzdGF0dXMgd2lsbCBiZSByZXNldCB3aXRoIHRoZSBuZXcgY3J5cHRvIHBhaXJcblxuICAgICAgICAgIC8vIFNhdmUgY3VycmVudCBzZXNzaW9uIHdpdGggdGltZXN0YW1wXG4gICAgICAgICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgICAgICAgbWludXRlOiAnMi1kaWdpdCcsXG4gICAgICAgICAgICBob3VyMTI6IGZhbHNlXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc3Qgc2F2ZWROYW1lID0gYCR7Y3VycmVudFNlc3Npb24ubmFtZX0gKEF1dG9TYXZlZCAke3RpbWVzdGFtcH0pYDtcblxuICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLmNyZWF0ZU5ld1Nlc3Npb24oXG4gICAgICAgICAgICBzYXZlZE5hbWUsXG4gICAgICAgICAgICBjdXJyZW50U2Vzc2lvbi5jb25maWcsXG4gICAgICAgICAgICB7IGNyeXB0bzE6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLCBjcnlwdG8yOiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSwgc3RhYmxlY29pbjogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UgfVxuICAgICAgICAgIClcbiAgICAgICAgICAgIC50aGVuKChzYXZlZFNlc3Npb25JZCkgPT4ge1xuICAgICAgICAgICAgICBzZXNzaW9uTWFuYWdlci5zYXZlU2Vzc2lvbihcbiAgICAgICAgICAgICAgICBzYXZlZFNlc3Npb25JZCxcbiAgICAgICAgICAgICAgICBjdXJyZW50U2Vzc2lvbi5jb25maWcsXG4gICAgICAgICAgICAgICAgc3RhdGUudGFyZ2V0UHJpY2VSb3dzLFxuICAgICAgICAgICAgICAgIHN0YXRlLm9yZGVySGlzdG9yeSxcbiAgICAgICAgICAgICAgICBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICAgICAgc3RhdGUuY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgICAgICAgICAgc3RhdGUuY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgICAgICAgICAgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgICAgICAgICAgZmFsc2VcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gQXV0b1NhdmVkIHNlc3Npb246Jywgc2F2ZWROYW1lKTtcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gYXV0by1zYXZlIHNlc3Npb24gZHVyaW5nIGNyeXB0byBwYWlyIGNoYW5nZTonLCBlcnJvcik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFJlc2V0IGZvciBuZXcgY3J5cHRvIHBhaXJcbiAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnUkVTRVRfRk9SX05FV19DUllQVE8nIH0pO1xuXG4gICAgICAgIC8vIERvbid0IGF1dG8tY3JlYXRlIG5ldyBzZXNzaW9uIGZvciBjcnlwdG8gcGFpciBjaGFuZ2VcbiAgICAgICAgLy8gVXNlciBzaG91bGQgbWFudWFsbHkgY3JlYXRlIHNlc3Npb24gYWZ0ZXIgc2V0dGluZyB0YXJnZXQgcHJpY2VzXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIENyeXB0byBwYWlyIGNoYW5nZWQsIHNlc3Npb24gY2xlYXJlZC4gU2V0IHRhcmdldCBwcmljZXMgYW5kIHN0YXJ0IGJvdCB0byBjcmVhdGUgbmV3IHNlc3Npb24uJyk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdGUuY29uZmlnLmNyeXB0bzEsIHN0YXRlLmNvbmZpZy5jcnlwdG8yXSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBuZXR3b3JrIG1vbml0b3JpbmcgYW5kIGF1dG8tc2F2ZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG5ldHdvcmtNb25pdG9yID0gTmV0d29ya01vbml0b3IuZ2V0SW5zdGFuY2UoKTtcbiAgICBjb25zdCBhdXRvU2F2ZU1hbmFnZXIgPSBBdXRvU2F2ZU1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICBjb25zdCBtZW1vcnlNb25pdG9yID0gTWVtb3J5TW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcblxuICAgIC8vIFNldCB1cCBuZXR3b3JrIHN0YXR1cyBsaXN0ZW5lclxuICAgIGNvbnN0IHVuc3Vic2NyaWJlTmV0d29yayA9IG5ldHdvcmtNb25pdG9yLmFkZExpc3RlbmVyKChpc09ubGluZSwgaXNJbml0aWFsKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZyhg8J+MkCBOZXR3b3JrIHN0YXR1cyBjaGFuZ2VkOiAke2lzT25saW5lID8gJ09ubGluZScgOiAnT2ZmbGluZSd9YCk7XG5cbiAgICAgIC8vIEhhbmRsZSBvZmZsaW5lIHN0YXRlIC0gc3RvcCBib3QgYW5kIHNhdmUgc2Vzc2lvblxuICAgICAgaWYgKCFpc09ubGluZSAmJiAhaXNJbml0aWFsKSB7XG4gICAgICAgIC8vIFN0b3AgdGhlIGJvdCBpZiBpdCdzIHJ1bm5pbmdcbiAgICAgICAgaWYgKHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflLQgSW50ZXJuZXQgbG9zdCAtIHN0b3BwaW5nIGJvdCBhbmQgc2F2aW5nIHNlc3Npb24nKTtcbiAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTWVNURU1fU1RPUF9CT1QnIH0pO1xuXG4gICAgICAgICAgLy8gQXV0by1zYXZlIGN1cnJlbnQgc2Vzc2lvblxuICAgICAgICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICAgICAgICBjb25zdCBjdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpO1xuICAgICAgICAgIGlmIChjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICAgICAgICBjb25zdCBjdXJyZW50U2Vzc2lvbiA9IHNlc3Npb25NYW5hZ2VyLmxvYWRTZXNzaW9uKGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgICAgICAgaWYgKGN1cnJlbnRTZXNzaW9uKSB7XG4gICAgICAgICAgICAgIC8vIENyZWF0ZSBvZmZsaW5lIGJhY2t1cCBzZXNzaW9uXG4gICAgICAgICAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywge1xuICAgICAgICAgICAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICBob3VyMTI6IGZhbHNlXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zdCBvZmZsaW5lTmFtZSA9IGAke2N1cnJlbnRTZXNzaW9uLm5hbWV9IChPZmZsaW5lIEJhY2t1cCAke3RpbWVzdGFtcH0pYDtcblxuICAgICAgICAgICAgICBzZXNzaW9uTWFuYWdlci5jcmVhdGVOZXdTZXNzaW9uKFxuICAgICAgICAgICAgICAgIG9mZmxpbmVOYW1lLFxuICAgICAgICAgICAgICAgIGN1cnJlbnRTZXNzaW9uLmNvbmZpZyxcbiAgICAgICAgICAgICAgICB7IGNyeXB0bzE6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLCBjcnlwdG8yOiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSwgc3RhYmxlY29pbjogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UgfVxuICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgLnRoZW4oKGJhY2t1cFNlc3Npb25JZCkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2F2ZVNlc3Npb24oXG4gICAgICAgICAgICAgICAgICAgIGJhY2t1cFNlc3Npb25JZCxcbiAgICAgICAgICAgICAgICAgICAgc3RhdGUuY29uZmlnLFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgICAgICAgICAgIHN0YXRlLm9yZGVySGlzdG9yeSxcbiAgICAgICAgICAgICAgICAgICAgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICAgICAgICAgICAgc3RhdGUuY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgICAgICAgICAgICAgIHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgICAgICAgICAgICBmYWxzZVxuICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IENyZWF0ZWQgb2ZmbGluZSBiYWNrdXAgc2Vzc2lvbjonLCBvZmZsaW5lTmFtZSk7XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNyZWF0ZSBvZmZsaW5lIGJhY2t1cCBzZXNzaW9uOicsIGVycm9yKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZyhcIk5ldHdvcmsgRGlzY29ubmVjdGVkOiBCb3Qgc3RvcHBlZCBhbmQgc2Vzc2lvbiBzYXZlZC4gVHJhZGluZyBwYXVzZWQgdW50aWwgY29ubmVjdGlvbiByZXN0b3JlZC5cIik7XG4gICAgICB9IGVsc2UgaWYgKGlzT25saW5lICYmICFpc0luaXRpYWwpIHtcbiAgICAgICAgY29uc29sZS5sb2coXCJOZXR3b3JrIFJlY29ubmVjdGVkOiBDb25uZWN0aW9uIHJlc3RvcmVkLiBZb3UgY2FuIHJlc3VtZSB0cmFkaW5nLlwiKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFNldCB1cCBtZW1vcnkgbW9uaXRvcmluZ1xuICAgIGNvbnN0IHVuc3Vic2NyaWJlTWVtb3J5ID0gbWVtb3J5TW9uaXRvci5hZGRMaXN0ZW5lcigobWVtb3J5KSA9PiB7XG4gICAgICBjb25zdCB1c2VkTUIgPSBtZW1vcnkudXNlZEpTSGVhcFNpemUgLyAxMDI0IC8gMTAyNDtcbiAgICAgIGlmICh1c2VkTUIgPiAxNTApIHsgLy8gMTUwTUIgdGhyZXNob2xkXG4gICAgICAgIGNvbnNvbGUud2Fybihg8J+noCBIaWdoIG1lbW9yeSB1c2FnZTogJHt1c2VkTUIudG9GaXhlZCgyKX1NQmApO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gU2V0IHVwIGF1dG8tc2F2ZVxuICAgIGNvbnN0IHNhdmVGdW5jdGlvbiA9ICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIFNhdmUgdG8gc2Vzc2lvbiBtYW5hZ2VyIGlmIHdlIGhhdmUgYSBjdXJyZW50IHNlc3Npb25cbiAgICAgICAgY29uc3QgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25NYW5hZ2VyLmdldEN1cnJlbnRTZXNzaW9uSWQoKTtcbiAgICAgICAgaWYgKGN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgICAgICBzZXNzaW9uTWFuYWdlci5zYXZlU2Vzc2lvbihcbiAgICAgICAgICAgIGN1cnJlbnRTZXNzaW9uSWQsXG4gICAgICAgICAgICBzdGF0ZS5jb25maWcsXG4gICAgICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgICBzdGF0ZS5vcmRlckhpc3RvcnksXG4gICAgICAgICAgICBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICAgIHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgICAgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgICAgICB0cnVlIC8vIFNlc3Npb24gaXMgYWN0aXZlIGlmIGl0J3MgdGhlIGN1cnJlbnQgc2Vzc2lvblxuICAgICAgICAgICk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBbHNvIHNhdmUgdG8gbG9jYWxTdG9yYWdlIGFzIGJhY2t1cFxuICAgICAgICBzYXZlU3RhdGVUb0xvY2FsU3RvcmFnZShzdGF0ZSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRvLXNhdmUgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgYXV0b1NhdmVNYW5hZ2VyLmVuYWJsZShzYXZlRnVuY3Rpb24sIDMwMDAwKTsgLy8gQXV0by1zYXZlIGV2ZXJ5IDMwIHNlY29uZHNcblxuICAgIC8vIEFkZCBiZWZvcmV1bmxvYWQgbGlzdGVuZXIgdG8gc2F2ZSBzZXNzaW9uIG9uIGJyb3dzZXIgY2xvc2UvcmVmcmVzaFxuICAgIGNvbnN0IGhhbmRsZUJlZm9yZVVubG9hZCA9IChldmVudDogQmVmb3JlVW5sb2FkRXZlbnQpID0+IHtcbiAgICAgIC8vIFNhdmUgaW1tZWRpYXRlbHkgYmVmb3JlIHVubG9hZFxuICAgICAgc2F2ZUZ1bmN0aW9uKCk7XG5cbiAgICAgIC8vIElmIGJvdCBpcyBydW5uaW5nLCBzaG93IHdhcm5pbmdcbiAgICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJykge1xuICAgICAgICBjb25zdCBtZXNzYWdlID0gJ1RyYWRpbmcgYm90IGlzIGN1cnJlbnRseSBydW5uaW5nLiBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gbGVhdmU/JztcbiAgICAgICAgZXZlbnQucmV0dXJuVmFsdWUgPSBtZXNzYWdlO1xuICAgICAgICByZXR1cm4gbWVzc2FnZTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZCk7XG5cbiAgICAvLyBDbGVhbnVwIGZ1bmN0aW9uXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHVuc3Vic2NyaWJlTmV0d29yaygpO1xuICAgICAgdW5zdWJzY3JpYmVNZW1vcnkoKTtcbiAgICAgIGF1dG9TYXZlTWFuYWdlci5kaXNhYmxlKCk7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKTtcbiAgICB9O1xuICB9LCBbc3RhdGVdKTtcblxuICAvLyBGb3JjZSBzYXZlIHdoZW4gYm90IHN0YXR1cyBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgYXV0b1NhdmVNYW5hZ2VyID0gQXV0b1NhdmVNYW5hZ2VyLmdldEluc3RhbmNlKCk7XG4gICAgYXV0b1NhdmVNYW5hZ2VyLnNhdmVOb3coKTtcbiAgfSwgW3N0YXRlLmJvdFN5c3RlbVN0YXR1c10pO1xuXG4gIC8vIFNhdmUgZ2xvYmFsIGJhbGFuY2VzIHdoZW5ldmVyIHRoZXkgY2hhbmdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2F2ZUdsb2JhbEJhbGFuY2VzKHN0YXRlLmNyeXB0bzFCYWxhbmNlLCBzdGF0ZS5jcnlwdG8yQmFsYW5jZSwgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UpO1xuICB9LCBbc3RhdGUuY3J5cHRvMUJhbGFuY2UsIHN0YXRlLmNyeXB0bzJCYWxhbmNlLCBzdGF0ZS5zdGFibGVjb2luQmFsYW5jZV0pO1xuXG4gIC8vIEluaXRpYWxpemUgd2l0aCBnbG9iYWwgYmFsYW5jZXMgb24gbW91bnQgKG9ubHkgaWYgdXNpbmcgZGVmYXVsdCBiYWxhbmNlcylcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpc0RlZmF1bHRCYWxhbmNlcyA9IHN0YXRlLmNyeXB0bzFCYWxhbmNlID09PSAxMCAmJiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSA9PT0gMTAwMDAwICYmIHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlID09PSAwO1xuICAgIGlmIChpc0RlZmF1bHRCYWxhbmNlcykge1xuICAgICAgY29uc3QgZ2xvYmFsQmFsYW5jZXMgPSBsb2FkR2xvYmFsQmFsYW5jZXMoKTtcbiAgICAgIGlmIChnbG9iYWxCYWxhbmNlcy5jcnlwdG8xQmFsYW5jZSAhPT0gMTAgfHwgZ2xvYmFsQmFsYW5jZXMuY3J5cHRvMkJhbGFuY2UgIT09IDEwMDAwMCB8fCBnbG9iYWxCYWxhbmNlcy5zdGFibGVjb2luQmFsYW5jZSAhPT0gMCkge1xuICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgdHlwZTogJ1VQREFURV9CQUxBTkNFUycsXG4gICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgY3J5cHRvMTogZ2xvYmFsQmFsYW5jZXMuY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgICAgICBjcnlwdG8yOiBnbG9iYWxCYWxhbmNlcy5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgIHN0YWJsZWNvaW46IGdsb2JhbEJhbGFuY2VzLnN0YWJsZWNvaW5CYWxhbmNlXG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtdKTsgLy8gT25seSBydW4gb25jZSBvbiBtb3VudFxuXG4gIC8vIE1hbnVhbCBzYXZlIHNlc3Npb24gZnVuY3Rpb25cbiAgY29uc3Qgc2F2ZUN1cnJlbnRTZXNzaW9uID0gdXNlQ2FsbGJhY2soKCk6IGJvb2xlYW4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXNzaW9uTWFuYWdlciA9IFNlc3Npb25NYW5hZ2VyLmdldEluc3RhbmNlKCk7XG4gICAgICBjb25zdCBjdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpO1xuXG4gICAgICBpZiAoIWN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgICAgLy8gTm8gY3VycmVudCBzZXNzaW9uLCBjcmVhdGUgb25lIGZpcnN0XG4gICAgICAgIGlmIChzdGF0ZS5jb25maWcuY3J5cHRvMSAmJiBzdGF0ZS5jb25maWcuY3J5cHRvMikge1xuICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLmNyZWF0ZU5ld1Nlc3Npb25XaXRoQXV0b05hbWUoXG4gICAgICAgICAgICBzdGF0ZS5jb25maWcsXG4gICAgICAgICAgICB1bmRlZmluZWQsXG4gICAgICAgICAgICB7IGNyeXB0bzE6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLCBjcnlwdG8yOiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSwgc3RhYmxlY29pbjogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UgfVxuICAgICAgICAgIClcbiAgICAgICAgICAgIC50aGVuKChzZXNzaW9uSWQpID0+IHtcbiAgICAgICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2V0Q3VycmVudFNlc3Npb24oc2Vzc2lvbklkKTtcbiAgICAgICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2F2ZVNlc3Npb24oXG4gICAgICAgICAgICAgICAgc2Vzc2lvbklkLFxuICAgICAgICAgICAgICAgIHN0YXRlLmNvbmZpZyxcbiAgICAgICAgICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgICAgICAgc3RhdGUub3JkZXJIaXN0b3J5LFxuICAgICAgICAgICAgICAgIHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICAgICAgICBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgICAgICBzdGF0ZS5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgICAgICAgICB0cnVlIC8vIE5ldyBzZXNzaW9uIGlzIGFjdGl2ZSB3aGVuIGNyZWF0ZWRcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjcmVhdGUgbmV3IHNlc3Npb246JywgZXJyb3IpO1xuICAgICAgICAgICAgICBzZW5kVGVsZWdyYW1FcnJvck5vdGlmaWNhdGlvbihcbiAgICAgICAgICAgICAgICAnU2Vzc2lvbiBDcmVhdGlvbiBFcnJvcicsXG4gICAgICAgICAgICAgICAgJ0ZhaWxlZCB0byBjcmVhdGUgbmV3IHRyYWRpbmcgc2Vzc2lvbicsXG4gICAgICAgICAgICAgICAgYEVycm9yOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWBcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHNlbmRUZWxlZ3JhbUVycm9yTm90aWZpY2F0aW9uKFxuICAgICAgICAgICdTZXNzaW9uIFNhdmUgRXJyb3InLFxuICAgICAgICAgICdDYW5ub3Qgc2F2ZSBzZXNzaW9uIC0gbm8gdHJhZGluZyBwYWlyIHNlbGVjdGVkJyxcbiAgICAgICAgICAnUGxlYXNlIHNlbGVjdCBib3RoIGNyeXB0bzEgYW5kIGNyeXB0bzIgYmVmb3JlIHNhdmluZydcbiAgICAgICAgKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICAvLyBTYXZlIGV4aXN0aW5nIHNlc3Npb25cbiAgICAgIHJldHVybiBzZXNzaW9uTWFuYWdlci5zYXZlU2Vzc2lvbihcbiAgICAgICAgY3VycmVudFNlc3Npb25JZCxcbiAgICAgICAgc3RhdGUuY29uZmlnLFxuICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgIHN0YXRlLm9yZGVySGlzdG9yeSxcbiAgICAgICAgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgc3RhdGUuY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgIHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICB0cnVlIC8vIEN1cnJlbnQgc2Vzc2lvbiBpcyBhY3RpdmVcbiAgICAgICk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGN1cnJlbnQgc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICBzZW5kVGVsZWdyYW1FcnJvck5vdGlmaWNhdGlvbihcbiAgICAgICAgJ1Nlc3Npb24gU2F2ZSBFcnJvcicsXG4gICAgICAgICdVbmV4cGVjdGVkIGVycm9yIHdoaWxlIHNhdmluZyBzZXNzaW9uJyxcbiAgICAgICAgYEVycm9yOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWBcbiAgICAgICk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9LCBbc3RhdGUsIHNlbmRUZWxlZ3JhbUVycm9yTm90aWZpY2F0aW9uXSk7XG5cbiAgLy8gQ29udGV4dCB2YWx1ZVxuICBjb25zdCBjb250ZXh0VmFsdWU6IFRyYWRpbmdDb250ZXh0VHlwZSA9IHtcbiAgICAuLi5zdGF0ZSxcbiAgICBkaXNwYXRjaCxcbiAgICBzZXRUYXJnZXRQcmljZXMsXG4gICAgZ2V0RGlzcGxheU9yZGVycyxcbiAgICBjaGVja0JhY2tlbmRTdGF0dXMsXG4gICAgZmV0Y2hNYXJrZXRQcmljZSxcbiAgICBzdGFydEJhY2tlbmRCb3QsXG4gICAgc3RvcEJhY2tlbmRCb3QsXG4gICAgc2F2ZUNvbmZpZ1RvQmFja2VuZCxcbiAgICBzYXZlQ3VycmVudFNlc3Npb24sXG4gICAgYmFja2VuZFN0YXR1czogc3RhdGUuYmFja2VuZFN0YXR1cyBhcyAnb25saW5lJyB8ICdvZmZsaW5lJyB8ICd1bmtub3duJyxcbiAgICBib3RTeXN0ZW1TdGF0dXM6IHN0YXRlLmJvdFN5c3RlbVN0YXR1cyxcbiAgICBpc0JvdEFjdGl2ZTogc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycsXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8VHJhZGluZ0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9UcmFkaW5nQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbi8vIEN1c3RvbSBob29rIHRvIHVzZSB0aGUgdHJhZGluZyBjb250ZXh0XG5leHBvcnQgY29uc3QgdXNlVHJhZGluZ0NvbnRleHQgPSAoKTogVHJhZGluZ0NvbnRleHRUeXBlID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVHJhZGluZ0NvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUcmFkaW5nQ29udGV4dCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVHJhZGluZ1Byb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVJlZHVjZXIiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsIkRFRkFVTFRfQVBQX1NFVFRJTkdTIiwiQVZBSUxBQkxFX1NUQUJMRUNPSU5TIiwidjQiLCJ1dWlkdjQiLCJERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMiLCJ0cmFkaW5nQXBpIiwiU2Vzc2lvbk1hbmFnZXIiLCJOZXR3b3JrTW9uaXRvciIsIkF1dG9TYXZlTWFuYWdlciIsIk1lbW9yeU1vbml0b3IiLCJjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UiLCJjb25maWciLCJjcnlwdG8xIiwiY3J5cHRvMiIsImNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UiLCJnZXRNYXJrZXRQcmljZUZyb21BUEkiLCJ0cmFkaW5nTW9kZSIsInByZWZlcnJlZFN0YWJsZWNvaW4iLCJjcnlwdG8xU3RhYmxlY29pblByaWNlIiwiZ2V0U3RhYmxlY29pbkV4Y2hhbmdlUmF0ZSIsImNyeXB0bzJTdGFibGVjb2luUHJpY2UiLCJyYXRpbyIsImNvbnNvbGUiLCJsb2ciLCJ0b0ZpeGVkIiwic3RhYmxlY29pbkVycm9yIiwid2FybiIsInN5bWJvbCIsInRvVXBwZXJDYXNlIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiZGF0YSIsImpzb24iLCJwcmljZSIsInBhcnNlRmxvYXQiLCJiaW5hbmNlRXJyb3IiLCJjcnlwdG8xSWQiLCJnZXRDb2luR2Vja29JZCIsImNyeXB0bzJJZCIsImdlY2tvRXJyb3IiLCJtb2NrUHJpY2UiLCJlcnJvciIsIm1hcHBpbmciLCJjcnlwdG8iLCJzdGFibGVjb2luIiwiYmluYW5jZVN5bWJvbCIsImNyeXB0b0lkIiwic3RhYmxlY29pbklkIiwicmF0ZSIsImNyeXB0b1VTRFByaWNlIiwiZ2V0VVNEUHJpY2UiLCJzdGFibGVjb2luVVNEUHJpY2UiLCJ1c2RQcmljZXMiLCJjcnlwdG8xVVNEUHJpY2UiLCJjcnlwdG8yVVNEUHJpY2UiLCJiYXNlUHJpY2UiLCJmbHVjdHVhdGlvbiIsIk1hdGgiLCJyYW5kb20iLCJmaW5hbFByaWNlIiwiaW5pdGlhbEJhc2VDb25maWciLCJiYXNlQmlkIiwibXVsdGlwbGllciIsIm51bURpZ2l0cyIsInNsaXBwYWdlUGVyY2VudCIsImluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQiLCJpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50IiwiaW5pdGlhbFRyYWRpbmdTdGF0ZSIsInRhcmdldFByaWNlUm93cyIsIm9yZGVySGlzdG9yeSIsImFwcFNldHRpbmdzIiwiY3VycmVudE1hcmtldFByaWNlIiwiYm90U3lzdGVtU3RhdHVzIiwiY3J5cHRvMUJhbGFuY2UiLCJjcnlwdG8yQmFsYW5jZSIsInN0YWJsZWNvaW5CYWxhbmNlIiwiYmFja2VuZFN0YXR1cyIsImxhc3RBY3Rpb25UaW1lc3RhbXBQZXJDb3VudGVyIiwiTWFwIiwiU1RPUkFHRV9LRVkiLCJHTE9CQUxfQkFMQU5DRV9LRVkiLCJCT1RfU1RBVFVTX0tFWSIsInNhdmVTdGF0ZVRvTG9jYWxTdG9yYWdlIiwic3RhdGUiLCJzdGF0ZVRvU2F2ZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwiSlNPTiIsInN0cmluZ2lmeSIsImxvYWRTdGF0ZUZyb21Mb2NhbFN0b3JhZ2UiLCJzYXZlZFN0YXRlIiwiZ2V0SXRlbSIsInBhcnNlZCIsInBhcnNlIiwic2F2ZUdsb2JhbEJhbGFuY2VzIiwiYmFsYW5jZXMiLCJsb2FkR2xvYmFsQmFsYW5jZXMiLCJzYXZlZEJhbGFuY2VzIiwic2F2ZUJvdFN0YXR1cyIsInN0YXR1cyIsImxvYWRCb3RTdGF0dXMiLCJzYXZlZCIsImNyZWF0ZUluaXRpYWxUcmFkaW5nU3RhdGUiLCJnbG9iYWxCYWxhbmNlcyIsInRyYWRpbmdSZWR1Y2VyIiwiYWN0aW9uIiwidHlwZSIsIm5ld0NvbmZpZyIsInBheWxvYWQiLCJzb3J0IiwiYSIsImIiLCJ0YXJnZXRQcmljZSIsIm1hcCIsInJvdyIsImluZGV4IiwiY291bnRlciIsIm5ld1Jvd3MiLCJ1cGRhdGVkUm93cyIsImlkIiwiZmlsdGVyZWRSb3dzIiwiZmlsdGVyIiwiZmx1Y3R1YXRpb25GYWN0b3IiLCJuZXdQcmljZSIsInVuZGVmaW5lZCIsImNvbmZpZ0ZvclJlc2V0IiwiY2xlYXIiLCJzZXNzaW9uTWFuYWdlciIsImdldEluc3RhbmNlIiwiY2xlYXJDdXJyZW50U2Vzc2lvbiIsIlRyYWRpbmdDb250ZXh0IiwiVHJhZGluZ1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJpbml0aWFsaXplU3RhdGUiLCJ1cmxQYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInNlYXJjaCIsImlzTmV3U2Vzc2lvbiIsImdldCIsInNldFRpbWVvdXQiLCJuZXdVcmwiLCJwYXRobmFtZSIsImhpc3RvcnkiLCJyZXBsYWNlU3RhdGUiLCJjdXJyZW50U2Vzc2lvbklkIiwiZ2V0Q3VycmVudFNlc3Npb25JZCIsImN1cnJlbnRTZXNzaW9uIiwibG9hZFNlc3Npb24iLCJzYXZlZEJvdFN0YXR1cyIsIm5hbWUiLCJkaXNwYXRjaCIsImF1ZGlvUmVmIiwic2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24iLCJlcnJvclR5cGUiLCJlcnJvck1lc3NhZ2UiLCJjb250ZXh0IiwidGVsZWdyYW1Ub2tlbiIsInRlbGVncmFtQ2hhdElkIiwibWVzc2FnZSIsInRvTG9jYWxlU3RyaW5nIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJjaGF0X2lkIiwidGV4dCIsInBhcnNlX21vZGUiLCJzdGF0dXNUZXh0IiwiZmV0Y2hNYXJrZXRQcmljZSIsIkVycm9yIiwicHJpY2VGbHVjdHVhdGlvbkludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJuZXR3b3JrTW9uaXRvciIsImdldFN0YXR1cyIsImlzT25saW5lIiwiY2xlYXJJbnRlcnZhbCIsImN1cnJlbnQiLCJBdWRpbyIsImF1ZGlvUXVldWVSZWYiLCJpc1BsYXlpbmdBdWRpb1JlZiIsInByb2Nlc3NBdWRpb1F1ZXVlIiwibGVuZ3RoIiwic291bmRLZXkiLCJzZXNzaW9uSWQiLCJzaGlmdCIsInRhcmdldFNlc3Npb25JZCIsInRhcmdldFNlc3Npb24iLCJhbGFybVNldHRpbmdzIiwic291bmRBbGVydHNFbmFibGVkIiwic291bmRQYXRoIiwiYWxlcnRPbk9yZGVyRXhlY3V0aW9uIiwic291bmRPcmRlckV4ZWN1dGlvbiIsImFsZXJ0T25FcnJvciIsInNvdW5kRXJyb3IiLCJ2YWxpZFNvdW5kUGF0aCIsInN0YXJ0c1dpdGgiLCJyZXBsYWNlIiwiaW5jbHVkZXMiLCJrbm93blJpbmd0b25lcyIsImZpbGVuYW1lIiwic3BsaXQiLCJwb3AiLCJwYXVzZSIsImN1cnJlbnRUaW1lIiwic3JjIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJvbkNhblBsYXkiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwib25FcnJvciIsImUiLCJhZGRFdmVudExpc3RlbmVyIiwib25jZSIsImxvYWQiLCJwbGF5IiwiZGVidWciLCJmYWxsYmFja0Vycm9yIiwicGxheVNvdW5kIiwicHVzaCIsInNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbiIsInNldFRhcmdldFByaWNlcyIsInByaWNlcyIsIkFycmF5IiwiaXNBcnJheSIsInNvcnRlZFByaWNlcyIsImlzTmFOIiwiZXhpc3RpbmdSb3ciLCJmaW5kIiwiciIsIm9yZGVyTGV2ZWwiLCJ2YWx1ZUxldmVsIiwic29ydGVkUm93c0ZvckxvZ2ljIiwiY3VycmVudENyeXB0bzFCYWxhbmNlIiwiY3VycmVudENyeXB0bzJCYWxhbmNlIiwiYWN0aW9uc1Rha2VuIiwidGFyZ2V0c0luUmFuZ2UiLCJkaWZmUGVyY2VudCIsImFicyIsImkiLCJhY3RpdmVSb3ciLCJwcmljZURpZmZQZXJjZW50IiwiY29zdENyeXB0bzIiLCJhbW91bnRDcnlwdG8xQm91Z2h0IiwidXBkYXRlZFJvdyIsInBvdyIsImNyeXB0bzFBbW91bnRIZWxkIiwib3JpZ2luYWxDb3N0Q3J5cHRvMiIsImNyeXB0bzFWYXIiLCJjcnlwdG8yVmFyIiwicGFpciIsIm9yZGVyVHlwZSIsImFtb3VudENyeXB0bzEiLCJhdmdQcmljZSIsInZhbHVlQ3J5cHRvMiIsInByaWNlMSIsImNyeXB0bzFTeW1ib2wiLCJjcnlwdG8yU3ltYm9sIiwiY3VycmVudENvdW50ZXIiLCJpbmZlcmlvclJvdyIsImFtb3VudENyeXB0bzFUb1NlbGwiLCJjcnlwdG8yUmVjZWl2ZWQiLCJyZWFsaXplZFByb2ZpdCIsInJlYWxpemVkUHJvZml0Q3J5cHRvMSIsInVwZGF0ZWRJbmZlcmlvclJvdyIsInJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIiLCJyZWFsaXplZFByb2ZpdExvc3NDcnlwdG8xIiwicHJvZml0RW1vamkiLCJhbW91bnRDcnlwdG8yVG9Vc2UiLCJzdGFibGVjb2luT2J0YWluZWQiLCJjcnlwdG8xQm91Z2h0IiwibmV3TGV2ZWwiLCJuZXdWYWx1ZSIsImNvc3RCYXNpc0NyeXB0bzIiLCJjb3N0QmFzaXNTdGFibGVjb2luIiwic3RhYmxlY29pbkZyb21DMVNlbGwiLCJjcnlwdG8yUmVhY3F1aXJlZCIsInJlYWxpemVkUHJvZml0SW5DcnlwdG8yIiwiZ2V0RGlzcGxheU9yZGVycyIsImN1cnJlbnRQcmljZSIsInBlcmNlbnRGcm9tQWN0dWFsUHJpY2UiLCJpbmNvbWVDcnlwdG8xIiwiaW5jb21lQ3J5cHRvMiIsInByaWNlRGlmZmVyZW5jZSIsInByb2ZpdFBlcmNlbnRhZ2UiLCJ0b3RhbFVucmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIiLCJwcmljZURpZmZlcmVuY2VQZXJjZW50IiwicG90ZW50aWFsUHJvZml0Q3J5cHRvMSIsInBvdGVudGlhbFByb2ZpdENyeXB0bzIiLCJzYXZlQ29uZmlnVG9CYWNrZW5kIiwiY29uZmlnRGF0YSIsInRhcmdldFByaWNlcyIsInNhdmVDb25maWciLCJzdGFydEJhY2tlbmRCb3QiLCJjb25maWdJZCIsInN0YXJ0Qm90Iiwic3RvcEJhY2tlbmRCb3QiLCJzdG9wQm90IiwiY2hlY2tCYWNrZW5kU3RhdHVzIiwiYXBpVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJoZWFsdGhSZXNwb25zZSIsInJlc3BvbnNlVGV4dCIsImNhdGNoIiwiY2F1c2UiLCJzdGFydFNlc3Npb25SdW50aW1lIiwic3RvcFNlc3Npb25SdW50aW1lIiwiY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZSIsInRoZW4iLCJzZXRDdXJyZW50U2Vzc2lvbiIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsImhvdXIxMiIsInNhdmVkTmFtZSIsImNyZWF0ZU5ld1Nlc3Npb24iLCJzYXZlZFNlc3Npb25JZCIsInNhdmVTZXNzaW9uIiwiYXV0b1NhdmVNYW5hZ2VyIiwibWVtb3J5TW9uaXRvciIsInVuc3Vic2NyaWJlTmV0d29yayIsImFkZExpc3RlbmVyIiwiaXNJbml0aWFsIiwib2ZmbGluZU5hbWUiLCJiYWNrdXBTZXNzaW9uSWQiLCJ1bnN1YnNjcmliZU1lbW9yeSIsIm1lbW9yeSIsInVzZWRNQiIsInVzZWRKU0hlYXBTaXplIiwic2F2ZUZ1bmN0aW9uIiwiZW5hYmxlIiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwiZXZlbnQiLCJyZXR1cm5WYWx1ZSIsImRpc2FibGUiLCJzYXZlTm93IiwiaXNEZWZhdWx0QmFsYW5jZXMiLCJzYXZlQ3VycmVudFNlc3Npb24iLCJjb250ZXh0VmFsdWUiLCJpc0JvdEFjdGl2ZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VUcmFkaW5nQ29udGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});