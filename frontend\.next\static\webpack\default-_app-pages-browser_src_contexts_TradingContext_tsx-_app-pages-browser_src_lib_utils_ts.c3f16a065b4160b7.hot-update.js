"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("default-_app-pages-browser_src_contexts_TradingContext_tsx-_app-pages-browser_src_lib_utils_ts",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For StablecoinSwap mode, we need to calculate the ratio via stablecoin prices\n        if (config.tradingMode === \"StablecoinSwap\" && config.preferredStablecoin) {\n            try {\n                // Get both crypto prices in terms of the preferred stablecoin\n                const crypto1StablecoinPrice = await getStablecoinExchangeRate(config.crypto1, config.preferredStablecoin);\n                const crypto2StablecoinPrice = await getStablecoinExchangeRate(config.crypto2, config.preferredStablecoin);\n                if (crypto1StablecoinPrice > 0 && crypto2StablecoinPrice > 0) {\n                    // Calculate Crypto1/Crypto2 ratio via stablecoin\n                    const ratio = crypto1StablecoinPrice / crypto2StablecoinPrice;\n                    console.log(\"✅ StablecoinSwap price via \".concat(config.preferredStablecoin, \": \").concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio.toFixed(6)));\n                    console.log(\"   \".concat(config.crypto1, \"/\").concat(config.preferredStablecoin, \" = \").concat(crypto1StablecoinPrice));\n                    console.log(\"   \".concat(config.crypto2, \"/\").concat(config.preferredStablecoin, \" = \").concat(crypto2StablecoinPrice));\n                    return ratio;\n                }\n            } catch (stablecoinError) {\n                console.warn('Stablecoin price calculation failed, falling back to direct pair...', stablecoinError);\n            }\n        }\n        // For SimpleSpot mode or fallback: try direct pair fetching\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            // For both modes, we want Crypto1/Crypto2 ratio\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (crypto.toUpperCase() === stablecoin.toUpperCase()) return 1.0;\n        // First try Binance API for direct pair\n        const binanceSymbol = \"\".concat(crypto.toUpperCase()).concat(stablecoin.toUpperCase());\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(binanceSymbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Stablecoin rate from Binance: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn(\"Binance API failed for \".concat(binanceSymbol, \", trying CoinGecko...\"));\n        }\n        // Fallback to CoinGecko API\n        const cryptoId = getCoinGeckoId(crypto);\n        const stablecoinId = getCoinGeckoId(stablecoin);\n        if (cryptoId && stablecoinId) {\n            // Try direct conversion\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId];\n                if (rate > 0) {\n                    console.log(\"✅ Stablecoin rate from CoinGecko: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Final fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"⚠️ Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 108000,\n        'ETH': 2100,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    let basePrice;\n    if (config.tradingMode === \"StablecoinSwap\") {\n        // For stablecoin swap mode: Crypto1/Crypto2 = Current market Price\n        // This shows how many units of Crypto2 equals 1 unit of Crypto1\n        basePrice = crypto1USDPrice / crypto2USDPrice;\n        console.log(\"\\uD83D\\uDCCA StablecoinSwap price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    } else {\n        // Simple Spot mode: Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n        basePrice = crypto1USDPrice / crypto2USDPrice;\n        console.log(\"\\uD83D\\uDCCA SimpleSpot price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(basePrice.toFixed(6)));\n    }\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    return finalPrice;\n};\n// Duplicate TradingAction type removed - using the one defined above\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\n// Initial state with default balances (will be updated by global balances later)\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst GLOBAL_BALANCE_KEY = 'pluto_global_balances';\nconst BOT_STATUS_KEY = 'pluto_bot_status';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\n// Global balance persistence functions\nconst saveGlobalBalances = (crypto1Balance, crypto2Balance, stablecoinBalance)=>{\n    try {\n        if (true) {\n            const balances = {\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(GLOBAL_BALANCE_KEY, JSON.stringify(balances));\n            console.log('💰 Global balances saved:', balances);\n        }\n    } catch (error) {\n        console.error('Failed to save global balances:', error);\n    }\n};\nconst loadGlobalBalances = ()=>{\n    try {\n        if (true) {\n            const savedBalances = localStorage.getItem(GLOBAL_BALANCE_KEY);\n            if (savedBalances) {\n                const parsed = JSON.parse(savedBalances);\n                console.log('💰 Global balances loaded:', parsed);\n                return {\n                    crypto1Balance: parsed.crypto1Balance || 10,\n                    crypto2Balance: parsed.crypto2Balance || 100000,\n                    stablecoinBalance: parsed.stablecoinBalance || 0\n                };\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load global balances:', error);\n    }\n    return {\n        crypto1Balance: 10,\n        crypto2Balance: 100000,\n        stablecoinBalance: 0\n    };\n};\nconst saveBotStatus = (status)=>{\n    if (true) {\n        try {\n            localStorage.setItem(BOT_STATUS_KEY, status);\n        } catch (error) {\n            console.error('Failed to save bot status to localStorage:', error);\n        }\n    }\n};\nconst loadBotStatus = ()=>{\n    if (true) {\n        try {\n            const saved = localStorage.getItem(BOT_STATUS_KEY);\n            if (saved && (saved === 'Stopped' || saved === 'WarmingUp' || saved === 'Running')) {\n                return saved;\n            }\n        } catch (error) {\n            console.error('Failed to load bot status from localStorage:', error);\n        }\n    }\n    // Return default status if loading fails\n    return 'Stopped';\n};\n// Create initial state with global balances - called after loadGlobalBalances is defined\nconst createInitialTradingState = ()=>{\n    const globalBalances = loadGlobalBalances();\n    return {\n        config: initialBaseConfig,\n        targetPriceRows: [],\n        orderHistory: [],\n        appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n        currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n        botSystemStatus: 'Stopped',\n        crypto1Balance: globalBalances.crypto1Balance,\n        crypto2Balance: globalBalances.crypto2Balance,\n        stablecoinBalance: globalBalances.stablecoinBalance,\n        backendStatus: 'unknown'\n    };\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2\n            };\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            saveBotStatus('WarmingUp');\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            saveBotStatus('Running');\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            saveBotStatus('Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            saveBotStatus('Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            saveBotStatus('Stopped');\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Toast hook for notifications - temporarily disabled\n    // const { toast } = useToast();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        console.log('🔄 TradingContext: Initializing state...');\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with fresh state but keeping global balances');\n                // Clear the URL parameter to avoid confusion - use setTimeout to avoid render issues\n                setTimeout(()=>{\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }, 0);\n                return createInitialTradingState(); // Use fresh state with current global balances\n            }\n        }\n        // Try to get session manager, but handle case where it might not be ready yet\n        let sessionManager;\n        let currentSessionId;\n        try {\n            sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            currentSessionId = sessionManager.getCurrentSessionId();\n            console.log('🔄 TradingContext: SessionManager available, current session:', currentSessionId);\n        } catch (error) {\n            console.log('🔄 TradingContext: SessionManager not ready yet, falling back to localStorage');\n            sessionManager = null;\n            currentSessionId = null;\n        }\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 No current session - starting with fresh state but keeping global balances');\n            return createInitialTradingState(); // Use fresh state with current global balances\n        }\n        // If we have a session and session manager is available, try to load it\n        if (sessionManager && currentSessionId) {\n            try {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession) {\n                    const savedBotStatus = loadBotStatus();\n                    console.log('🔄 Restoring session from session manager:', currentSession.name);\n                    console.log('🔄 Bot status restored to:', savedBotStatus);\n                    return {\n                        ...initialTradingState,\n                        config: currentSession.config,\n                        targetPriceRows: currentSession.targetPriceRows,\n                        orderHistory: currentSession.orderHistory,\n                        currentMarketPrice: currentSession.currentMarketPrice,\n                        crypto1Balance: currentSession.crypto1Balance,\n                        crypto2Balance: currentSession.crypto2Balance,\n                        stablecoinBalance: currentSession.stablecoinBalance,\n                        botSystemStatus: savedBotStatus // Restore previous bot status to maintain continuity\n                    };\n                }\n            } catch (error) {\n                console.error('🔄 Error loading session from session manager:', error);\n            }\n        }\n        // Fallback to localStorage if session manager fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            const savedBotStatus = loadBotStatus();\n            console.log('🔄 Restoring from localStorage backup');\n            console.log('🔄 Bot status restored to:', savedBotStatus);\n            const globalBalances = loadGlobalBalances();\n            return {\n                ...createInitialTradingState(),\n                ...savedState,\n                // Always use current global balances\n                crypto1Balance: globalBalances.crypto1Balance,\n                crypto2Balance: globalBalances.crypto2Balance,\n                stablecoinBalance: globalBalances.stablecoinBalance,\n                // Restore previous bot status to maintain continuity\n                botSystemStatus: savedBotStatus\n            };\n        }\n        console.log('⚠️ No session data found, starting fresh with global balances');\n        return createInitialTradingState();\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Telegram error notification function - defined early to avoid initialization issues\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage, context)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping error notification');\n                    return;\n                }\n                // Format error message with emoji and structure\n                let message = \"⚠️ <b>Error Alert</b>\\n\\n\";\n                message += \"<b>Type:</b> \".concat(errorType, \"\\n\");\n                message += \"<b>Error:</b> \".concat(errorMessage, \"\\n\");\n                if (context) {\n                    message += \"<b>Context:</b> \".concat(context, \"\\n\");\n                }\n                if (state.config.crypto1 && state.config.crypto2) {\n                    message += \"<b>Trading Pair:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"\\n\");\n                }\n                message += \"<b>Time:</b> \".concat(new Date().toLocaleString(), \"\\n\");\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram error notification:', response.statusText);\n                } else {\n                    console.log('✅ Telegram error notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                // Send error notification for price fetching failures\n                sendTelegramErrorNotification('Price Fetch Error', \"Failed to fetch market price: \".concat(error instanceof Error ? error.message : 'Unknown error'), \"Trading pair: \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2));\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch,\n        sendTelegramErrorNotification\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    // Audio queue system for handling multiple simultaneous sound requests\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const isPlayingAudioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingAudioRef.current || audioQueueRef.current.length === 0 || !audioRef.current) return;\n            isPlayingAudioRef.current = true;\n            const { soundKey, sessionId } = audioQueueRef.current.shift();\n            try {\n                // Get session-specific alarm settings\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                const targetSessionId = sessionId || sessionManager.getCurrentSessionId();\n                const targetSession = targetSessionId ? sessionManager.loadSession(targetSessionId) : null;\n                const alarmSettings = (targetSession === null || targetSession === void 0 ? void 0 : targetSession.alarmSettings) || state.appSettings;\n                if (!alarmSettings.soundAlertsEnabled) {\n                    return; // Skip if sound is disabled\n                }\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    // Validate and fix sound path if needed\n                    let validSoundPath = soundPath;\n                    // Fix old /sounds/ paths to /ringtones/\n                    if (soundPath.startsWith('/sounds/')) {\n                        validSoundPath = soundPath.replace('/sounds/', '/ringtones/');\n                        console.warn(\"Fixed deprecated sound path: \".concat(soundPath, \" -> \").concat(validSoundPath));\n                    }\n                    // Fallback to default sound if path doesn't exist in ringtones\n                    if (validSoundPath.startsWith('/ringtones/') && !validSoundPath.includes('data:audio')) {\n                        const knownRingtones = [\n                            'cheer.wav',\n                            'chest1.wav',\n                            'chime2.wav',\n                            'bells.wav',\n                            'bird1.wav',\n                            'bird7.wav',\n                            'sparrow1.wav',\n                            'space_bells4a.wav',\n                            'sanctuary1.wav',\n                            'marble1.wav',\n                            'foundry2.wav',\n                            'G_hades_curse.wav',\n                            'G_hades_demat.wav',\n                            'G_hades_sanctify.wav',\n                            'dark2.wav',\n                            'Satyr_atk4.wav',\n                            'S_mon1.mp3',\n                            'S_mon2.mp3',\n                            'wolf4.wav',\n                            'goatherd1.wav',\n                            'tax3.wav',\n                            'G_hades_mat.wav'\n                        ];\n                        const filename = validSoundPath.split('/').pop();\n                        if (filename && !knownRingtones.includes(filename)) {\n                            validSoundPath = '/ringtones/cheer.wav'; // Default fallback\n                            console.warn(\"Unknown ringtone file: \".concat(soundPath, \", using default: \").concat(validSoundPath));\n                        }\n                    }\n                    // Stop any currently playing audio to prevent conflicts\n                    audioRef.current.pause();\n                    audioRef.current.currentTime = 0;\n                    audioRef.current.src = validSoundPath;\n                    // Wait for audio to load before playing\n                    await new Promise({\n                        \"TradingProvider.useCallback[processAudioQueue]\": (resolve, reject)=>{\n                            var _audioRef_current, _audioRef_current1, _audioRef_current2;\n                            const onCanPlay = {\n                                \"TradingProvider.useCallback[processAudioQueue].onCanPlay\": ()=>{\n                                    var _audioRef_current, _audioRef_current1;\n                                    (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.removeEventListener('canplaythrough', onCanPlay);\n                                    (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.removeEventListener('error', onError);\n                                    resolve();\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue].onCanPlay\"];\n                            const onError = {\n                                \"TradingProvider.useCallback[processAudioQueue].onError\": (e)=>{\n                                    var _audioRef_current, _audioRef_current1;\n                                    (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.removeEventListener('canplaythrough', onCanPlay);\n                                    (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.removeEventListener('error', onError);\n                                    reject(e);\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue].onError\"];\n                            (_audioRef_current = audioRef.current) === null || _audioRef_current === void 0 ? void 0 : _audioRef_current.addEventListener('canplaythrough', onCanPlay, {\n                                once: true\n                            });\n                            (_audioRef_current1 = audioRef.current) === null || _audioRef_current1 === void 0 ? void 0 : _audioRef_current1.addEventListener('error', onError, {\n                                once: true\n                            });\n                            (_audioRef_current2 = audioRef.current) === null || _audioRef_current2 === void 0 ? void 0 : _audioRef_current2.load();\n                        }\n                    }[\"TradingProvider.useCallback[processAudioQueue]\"]);\n                    // Play the sound with timeout\n                    await audioRef.current.play();\n                    // Set timeout to stop audio after 2 seconds\n                    setTimeout({\n                        \"TradingProvider.useCallback[processAudioQueue]\": ()=>{\n                            if (audioRef.current) {\n                                audioRef.current.pause();\n                                audioRef.current.currentTime = 0;\n                            }\n                        }\n                    }[\"TradingProvider.useCallback[processAudioQueue]\"], 2000);\n                }\n            } catch (error) {\n                // Handle audio errors gracefully\n                if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('interrupted') || error.message.includes('play() request'))) {\n                    console.debug('Audio play interrupted (non-critical):', error.message);\n                } else {\n                    console.error(\"Error playing sound:\", error);\n                    // Try fallback sound for critical errors\n                    if (audioRef.current && audioRef.current.src !== '/ringtones/cheer.wav') {\n                        try {\n                            audioRef.current.src = '/ringtones/cheer.wav';\n                            await audioRef.current.play();\n                            setTimeout({\n                                \"TradingProvider.useCallback[processAudioQueue]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0;\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[processAudioQueue]\"], 2000);\n                        } catch (fallbackError) {\n                            console.error(\"Fallback sound also failed:\", fallbackError);\n                        }\n                    }\n                }\n            } finally{\n                isPlayingAudioRef.current = false;\n                // Process next item in queue after a short delay\n                setTimeout({\n                    \"TradingProvider.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"TradingProvider.useCallback[processAudioQueue]\"], 150);\n            }\n        }\n    }[\"TradingProvider.useCallback[processAudioQueue]\"], [\n        state.appSettings\n    ]);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey, sessionId)=>{\n            // Add to queue instead of playing immediately\n            audioQueueRef.current.push({\n                soundKey,\n                sessionId\n            });\n            processAudioQueue();\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        processAudioQueue\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                playSound('soundOrderExecution');\n                                // Show toast notification for BUY - temporarily disabled\n                                /*toast({\n                type: 'success',\n                title: '🟢 BUY EXECUTED',\n                description: `Bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`,\n                duration: 2000\n              });*/ // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Send error notification for insufficient balance\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for BUY order\"));\n                                sendTelegramErrorNotification('Insufficient Balance', \"Cannot execute BUY order - insufficient \".concat(config.crypto2, \" balance\"), \"Required: \".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \", Available: \").concat(currentCrypto2Balance.toFixed(2), \" \").concat(config.crypto2));\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss - convert USDT profit to BTC equivalent\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"\\uD83D\\uDCB0 SELL Trade P/L: Crypto2=\".concat(realizedProfit.toFixed(6), \", Crypto1=\").concat(realizedProfitCrypto1.toFixed(6)));\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            playSound('soundOrderExecution');\n                            // Show toast notification for SELL - temporarily disabled\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            /*toast({\n              type: realizedProfit > 0 ? 'success' : realizedProfit < 0 ? 'warning' : 'info',\n              title: '🔴 SELL EXECUTED',\n              description: `Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} | ${profitEmoji} Profit: $${realizedProfit.toFixed(2)}`,\n              duration: 2000\n            });*/ // Send Telegram notification for SELL\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Calculate cost basis for this BUY operation in terms of crypto2\n                                const costBasisCrypto2 = amountCrypto2ToUse;\n                                const costBasisStablecoin = stablecoinObtained;\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        // For SELL step of BUY operation: show cost basis as negative (money spent)\n                                        realizedProfitLossCrypto2: -costBasisCrypto2,\n                                        realizedProfitLossCrypto1: crypto1StablecoinPrice > 0 ? -costBasisCrypto2 / crypto1StablecoinPrice : 0\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        // For BUY step: show the value acquired (positive)\n                                        realizedProfitLossCrypto2: costBasisCrypto2,\n                                        realizedProfitLossCrypto1: crypto1Bought // Amount of crypto1 acquired\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                playSound('soundOrderExecution');\n                                // Show toast notification for Stablecoin BUY - temporarily disabled\n                                /*toast({\n                type: 'success',\n                title: '🟢 BUY EXECUTED (Stablecoin)',\n                description: `Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} via ${config.preferredStablecoin}`,\n                duration: 2000\n              });*/ // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss - convert Crypto2 profit to Crypto1 equivalent\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    // For SELL step: show the stablecoin value received (positive)\n                                    realizedProfitLossCrypto2: stablecoinFromC1Sell / crypto2StablecoinPrice,\n                                    realizedProfitLossCrypto1: amountCrypto1ToSell // Amount of crypto1 sold (negative impact on holdings)\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    // For BUY step: show the net profit/loss from the complete operation\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"\\uD83D\\uDCB0 STABLECOIN SWAP P/L: Crypto2=\".concat(realizedProfitInCrypto2.toFixed(6), \", Crypto1=\").concat(realizedProfitCrypto1.toFixed(6)));\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            playSound('soundOrderExecution');\n                            // Show toast notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            toast({\n                                type: realizedProfitInCrypto2 > 0 ? 'success' : realizedProfitInCrypto2 < 0 ? 'warning' : 'info',\n                                title: '🔴 SELL EXECUTED (Stablecoin)',\n                                description: \"Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" | \").concat(profitEmoji, \" Profit: $\").concat(realizedProfitInCrypto2.toFixed(2)),\n                                duration: 2000\n                            });\n                            // Send Telegram notification for Stablecoin SELL\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        // Calculate unrealized profit/loss based on current market price vs original cost\n                        if (state.config.tradingMode === \"StablecoinSwap\") {\n                            // For stablecoin swap: Calculate profit based on current market price vs target price\n                            // If current price > target price = profit (positive income)\n                            // If current price < target price = loss (negative income)\n                            const targetPrice = row.targetPrice || 0;\n                            const priceDifference = currentPrice - targetPrice;\n                            const profitPercentage = targetPrice > 0 ? priceDifference / targetPrice : 0;\n                            // Calculate profit/loss based on the amount held and price difference\n                            const totalUnrealizedProfitInCrypto2 = row.crypto1AmountHeld * priceDifference;\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;\n                            }\n                        } else {\n                            // Simple spot mode logic\n                            const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;\n                            }\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], []);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], []);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config, undefined, {\n                        crypto1: state.crypto1Balance,\n                        crypto2: state.crypto2Balance,\n                        stablecoin: state.stablecoinBalance\n                    }).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Note: No need to stop backend bot during crypto pair change\n                        // Frontend bot system is separate from backend bot system\n                        // The frontend bot status will be reset with the new crypto pair\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config, {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        }).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]).catch({\n                            \"TradingProvider.useEffect\": (error)=>{\n                                console.error('❌ Failed to auto-save session during crypto pair change:', error);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Don't auto-create new session for crypto pair change\n                    // User should manually create session after setting target prices\n                    console.log('🔄 Crypto pair changed, session cleared. Set target prices and start bot to create new session.');\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config, {\n                                        crypto1: state.crypto1Balance,\n                                        crypto2: state.crypto2Balance,\n                                        stablecoin: state.stablecoinBalance\n                                    }).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]).catch({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (error)=>{\n                                            console.error('❌ Failed to create offline backup session:', error);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        console.log(\"Network Disconnected: Bot stopped and session saved. Trading paused until connection restored.\");\n                    } else if (isOnline && !isInitial) {\n                        console.log(\"Network Reconnected: Connection restored. You can resume trading.\");\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Session is active if it's the current session\n                            );\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_5__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Save global balances whenever they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveGlobalBalances(state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Initialize with global balances on mount (only if using default balances)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const isDefaultBalances = state.crypto1Balance === 10 && state.crypto2Balance === 100000 && state.stablecoinBalance === 0;\n            if (isDefaultBalances) {\n                const globalBalances = loadGlobalBalances();\n                if (globalBalances.crypto1Balance !== 10 || globalBalances.crypto2Balance !== 100000 || globalBalances.stablecoinBalance !== 0) {\n                    dispatch({\n                        type: 'UPDATE_BALANCES',\n                        payload: {\n                            crypto1: globalBalances.crypto1Balance,\n                            crypto2: globalBalances.crypto2Balance,\n                            stablecoin: globalBalances.stablecoinBalance\n                        }\n                    });\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Only run once on mount\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_4__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        }).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // New session is active when created\n                                );\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]).catch({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (error)=>{\n                                console.error('Failed to create new session:', error);\n                                sendTelegramErrorNotification('Session Creation Error', 'Failed to create new trading session', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    sendTelegramErrorNotification('Session Save Error', 'Cannot save session - no trading pair selected', 'Please select both crypto1 and crypto2 before saving');\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Current session is active\n                );\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                sendTelegramErrorNotification('Session Save Error', 'Unexpected error while saving session', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state,\n        sendTelegramErrorNotification\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1990,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"6DQ7CJRNGUw3aB37LeA29hbbLMc=\");\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});