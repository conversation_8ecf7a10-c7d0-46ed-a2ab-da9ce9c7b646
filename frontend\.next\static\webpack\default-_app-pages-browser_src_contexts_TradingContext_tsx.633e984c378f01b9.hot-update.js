"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("default-_app-pages-browser_src_contexts_TradingContext_tsx",{

/***/ "(app-pages-browser)/./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useToast,toast auto */ \nlet toastId = 0;\nconst generateId = ()=>\"toast-\".concat(++toastId);\nconst listeners = [];\nlet state = {\n    toasts: []\n};\nfunction emitChange() {\n    listeners.forEach((listener)=>listener(state));\n}\nfunction addToast(toast) {\n    const newToast = {\n        ...toast,\n        id: generateId()\n    };\n    state = {\n        toasts: [\n            newToast,\n            ...state.toasts\n        ].slice(0, 5)\n    };\n    emitChange();\n    // Auto remove after 5 seconds\n    setTimeout(()=>{\n        removeToast(newToast.id);\n    }, 5000);\n    return newToast.id;\n}\nfunction removeToast(id) {\n    state = {\n        toasts: state.toasts.filter((toast)=>toast.id !== id)\n    };\n    emitChange();\n}\nfunction useToast() {\n    const [toastState, setToastState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(state);\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setToastState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setToastState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], []);\n    const toast = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback({\n        \"useToast.useCallback[toast]\": (options)=>{\n            return addToast(options);\n        }\n    }[\"useToast.useCallback[toast]\"], []);\n    const dismiss = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback({\n        \"useToast.useCallback[dismiss]\": (toastId)=>{\n            if (toastId) {\n                removeToast(toastId);\n            } else {\n                // Remove all toasts\n                state = {\n                    toasts: []\n                };\n                emitChange();\n            }\n        }\n    }[\"useToast.useCallback[dismiss]\"], []);\n    return {\n        toasts: toastState.toasts,\n        toast,\n        dismiss\n    };\n}\n// Export simple toast function for direct use\nconst toast = (options)=>addToast(options);\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-toast.ts\n"));

/***/ })

});