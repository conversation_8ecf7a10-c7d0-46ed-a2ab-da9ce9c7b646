"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("default-_app-pages-browser_src_contexts_TradingContext_tsx",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiApi: () => (/* binding */ aiApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   sessionApi: () => (/* binding */ sessionApi),\n/* harmony export */   tradingApi: () => (/* binding */ tradingApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n// API utility functions for backend communication\n// Configure base URL - can be overridden if needed for different environments\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\nconsole.log('API Base URL:', API_BASE_URL); // Log the URL to help with debugging\n// Generic fetch wrapper with error handling\nasync function fetchWithAuth(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Add auth token if available\n    const token = localStorage.getItem('plutoAuthToken');\n    const headers = {\n        'Content-Type': 'application/json',\n        ...token ? {\n            'Authorization': \"Bearer \".concat(token)\n        } : {},\n        ...options.headers\n    };\n    try {\n        // Add timeout to fetch request\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout\n        const response = await fetch(url, {\n            ...options,\n            headers,\n            signal: controller.signal\n        }).finally(()=>clearTimeout(timeoutId));\n        // Handle unauthorized responses\n        if (response.status === 401) {\n            localStorage.removeItem('plutoAuth');\n            localStorage.removeItem('plutoAuthToken');\n            localStorage.removeItem('plutoUser');\n            if (true) {\n                window.location.href = '/login';\n            }\n            throw new Error('Authentication expired. Please login again.');\n        }\n        // Parse JSON response (safely)\n        let data;\n        const contentType = response.headers.get('content-type');\n        if (contentType && contentType.includes('application/json')) {\n            data = await response.json();\n        } else {\n            // Handle non-JSON responses\n            const text = await response.text();\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                // If it's not parseable JSON, use the text as is\n                data = {\n                    message: text\n                };\n            }\n        }\n        // Handle API errors\n        if (!response.ok) {\n            // Provide more detailed error information\n            const errorMessage = (data === null || data === void 0 ? void 0 : data.error) || (data === null || data === void 0 ? void 0 : data.message) || \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            // Handle authentication errors gracefully - return null instead of throwing\n            if (response.status === 401 || response.status === 422) {\n                console.log('🔐 API authentication required - returning null:', {\n                    status: response.status,\n                    url: url.replace(API_BASE_URL, '')\n                });\n                return null; // Return null instead of throwing for auth errors\n            } else {\n                console.error('API error response:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    data: data || 'No response data',\n                    url: url\n                });\n                throw new Error(errorMessage);\n            }\n        }\n        return data;\n    } catch (error) {\n        // Handle network errors specifically\n        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n            console.error('Network error - Is the backend server running?:', error);\n            throw new Error('Cannot connect to server. Please check if the backend is running.');\n        }\n        // Handle timeout\n        if (error.name === 'AbortError') {\n            console.error('Request timeout:', error);\n            throw new Error('Request timed out. Server may be unavailable.');\n        }\n        console.error('API request failed:', error);\n        throw error;\n    }\n}\n// Auth API functions\nconst authApi = {\n    login: async (username, password)=>{\n        try {\n            // Use retry mechanism for login attempts\n            const data = await retryWithBackoff(async ()=>{\n                return await fetchWithAuth('/auth/login', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        username,\n                        password\n                    })\n                });\n            });\n            // The backend returns access_token in the response\n            if (data && data.access_token) {\n                localStorage.setItem('plutoAuthToken', data.access_token);\n                localStorage.setItem('plutoAuth', 'true');\n                // Also store user data if available\n                if (data.user) {\n                    localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                }\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login API error:', error);\n            return false;\n        }\n    },\n    register: async (username, password, email)=>{\n        // Use retry mechanism for registration attempts\n        return retryWithBackoff(async ()=>{\n            return await fetchWithAuth('/auth/register', {\n                method: 'POST',\n                body: JSON.stringify({\n                    username,\n                    password,\n                    email\n                })\n            });\n        });\n    },\n    logout: async ()=>{\n        localStorage.removeItem('plutoAuth');\n        localStorage.removeItem('plutoAuthToken');\n        localStorage.removeItem('plutoUser');\n        return true;\n    }\n};\n// Trading API functions\nconst tradingApi = {\n    getConfig: async (configId)=>{\n        return fetchWithAuth(configId ? \"/trading/config/\".concat(configId) : '/trading/config');\n    },\n    saveConfig: async (config)=>{\n        return fetchWithAuth('/trading/config', {\n            method: 'POST',\n            body: JSON.stringify(config)\n        });\n    },\n    updateConfig: async (configId, config)=>{\n        return fetchWithAuth(\"/trading/config/\".concat(configId), {\n            method: 'PUT',\n            body: JSON.stringify(config)\n        });\n    },\n    startBot: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/start/\".concat(configId), {\n            method: 'POST'\n        });\n    },\n    stopBot: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/stop/\".concat(configId), {\n            method: 'POST'\n        });\n    },\n    getBotStatus: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/status/\".concat(configId));\n    },\n    getTradeHistory: async (configId)=>{\n        const params = configId ? \"?configId=\".concat(configId) : '';\n        return fetchWithAuth(\"/trading/history\".concat(params));\n    },\n    getBalances: async ()=>{\n        return fetchWithAuth('/trading/balances');\n    },\n    getMarketPrice: async (symbol)=>{\n        return fetchWithAuth(\"/trading/market-data/\".concat(symbol));\n    },\n    getTradingPairs: async function() {\n        let exchange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'binance';\n        return fetchWithAuth(\"/trading/exchange/trading-pairs?exchange=\".concat(exchange));\n    },\n    getCryptocurrencies: async function() {\n        let exchange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'binance';\n        return fetchWithAuth(\"/trading/exchange/cryptocurrencies?exchange=\".concat(exchange));\n    }\n};\n// User API functions\nconst userApi = {\n    getProfile: async ()=>{\n        return fetchWithAuth('/user/profile');\n    },\n    updateProfile: async (profileData)=>{\n        return fetchWithAuth('/user/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    },\n    saveApiKeys: async (exchangeData)=>{\n        return fetchWithAuth('/user/apikeys', {\n            method: 'POST',\n            body: JSON.stringify(exchangeData)\n        });\n    },\n    getApiKeys: async ()=>{\n        return fetchWithAuth('/user/apikeys');\n    }\n};\n// AI API functions\nconst aiApi = {\n    getTradingSuggestion: async (data)=>{\n        return fetchWithAuth('/ai/trading-suggestion', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n};\n// Session API functions\nconst sessionApi = {\n    getAllSessions: async function() {\n        let includeInactive = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return fetchWithAuth(\"/sessions/?include_inactive=\".concat(includeInactive));\n    },\n    createSession: async (sessionData)=>{\n        return fetchWithAuth('/sessions/', {\n            method: 'POST',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    getSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId));\n    },\n    updateSession: async (sessionId, sessionData)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId), {\n            method: 'PUT',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    deleteSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId), {\n            method: 'DELETE'\n        });\n    },\n    activateSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId, \"/activate\"), {\n            method: 'POST'\n        });\n    },\n    getSessionHistory: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId, \"/history\"));\n    },\n    getActiveSession: async ()=>{\n        return fetchWithAuth('/sessions/active');\n    }\n};\n// Add a retry mechanism for transient connection issues\nconst retryWithBackoff = async function(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    let retries = 0;\n    const execute = async ()=>{\n        try {\n            return await fn();\n        } catch (error) {\n            // Only retry on network errors, not on 4xx/5xx responses\n            if (error instanceof TypeError && error.message.includes('Failed to fetch') || error.name === 'AbortError') {\n                if (retries < maxRetries) {\n                    const delay = Math.pow(2, retries) * 500; // Exponential backoff\n                    console.log(\"Retrying after \".concat(delay, \"ms (attempt \").concat(retries + 1, \"/\").concat(maxRetries, \")...\"));\n                    retries++;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return execute();\n                }\n            }\n            throw error;\n        }\n    };\n    return execute();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});