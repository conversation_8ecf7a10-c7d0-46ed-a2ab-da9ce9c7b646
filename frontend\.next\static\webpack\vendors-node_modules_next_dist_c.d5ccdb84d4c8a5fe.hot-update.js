"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("vendors-node_modules_next_dist_c",{

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom.development.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react-dom/cjs/react-dom.development.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * @license React\n * react-dom.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function noop() {}\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function createPortal$1(children, containerInfo, implementation) {\n      var key =\n        3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : null;\n      try {\n        testStringCoercion(key);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      JSCompiler_inline_result &&\n        (console.error(\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            key[Symbol.toStringTag]) ||\n            key.constructor.name ||\n            \"Object\"\n        ),\n        testStringCoercion(key));\n      return {\n        $$typeof: REACT_PORTAL_TYPE,\n        key: null == key ? null : \"\" + key,\n        children: children,\n        containerInfo: containerInfo,\n        implementation: implementation\n      };\n    }\n    function getCrossOriginStringAs(as, input) {\n      if (\"font\" === as) return \"\";\n      if (\"string\" === typeof input)\n        return \"use-credentials\" === input ? input : \"\";\n    }\n    function getValueDescriptorExpectingObjectForWarning(thing) {\n      return null === thing\n        ? \"`null`\"\n        : void 0 === thing\n          ? \"`undefined`\"\n          : \"\" === thing\n            ? \"an empty string\"\n            : 'something with type \"' + typeof thing + '\"';\n    }\n    function getValueDescriptorExpectingEnumForWarning(thing) {\n      return null === thing\n        ? \"`null`\"\n        : void 0 === thing\n          ? \"`undefined`\"\n          : \"\" === thing\n            ? \"an empty string\"\n            : \"string\" === typeof thing\n              ? JSON.stringify(thing)\n              : \"number\" === typeof thing\n                ? \"`\" + thing + \"`\"\n                : 'something with type \"' + typeof thing + '\"';\n    }\n    function resolveDispatcher() {\n      var dispatcher = ReactSharedInternals.H;\n      null === dispatcher &&\n        console.error(\n          \"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)\\n2. You might be breaking the Rules of Hooks\\n3. You might have more than one copy of React in the same app\\nSee https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.\"\n        );\n      return dispatcher;\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      Internals = {\n        d: {\n          f: noop,\n          r: function () {\n            throw Error(\n              \"Invalid form element. requestFormReset must be passed a form that was rendered by React.\"\n            );\n          },\n          D: noop,\n          C: noop,\n          L: noop,\n          m: noop,\n          X: noop,\n          S: noop,\n          M: noop\n        },\n        p: 0,\n        findDOMNode: null\n      },\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n    (\"function\" === typeof Map &&\n      null != Map.prototype &&\n      \"function\" === typeof Map.prototype.forEach &&\n      \"function\" === typeof Set &&\n      null != Set.prototype &&\n      \"function\" === typeof Set.prototype.clear &&\n      \"function\" === typeof Set.prototype.forEach) ||\n      console.error(\n        \"React depends on Map and Set built-in types. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills\"\n      );\n    exports.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE =\n      Internals;\n    exports.createPortal = function (children, container) {\n      var key =\n        2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : null;\n      if (\n        !container ||\n        (1 !== container.nodeType &&\n          9 !== container.nodeType &&\n          11 !== container.nodeType)\n      )\n        throw Error(\"Target container is not a DOM element.\");\n      return createPortal$1(children, container, null, key);\n    };\n    exports.flushSync = function (fn) {\n      var previousTransition = ReactSharedInternals.T,\n        previousUpdatePriority = Internals.p;\n      try {\n        if (((ReactSharedInternals.T = null), (Internals.p = 2), fn))\n          return fn();\n      } finally {\n        (ReactSharedInternals.T = previousTransition),\n          (Internals.p = previousUpdatePriority),\n          Internals.d.f() &&\n            console.error(\n              \"flushSync was called from inside a lifecycle method. React cannot flush when React is already rendering. Consider moving this call to a scheduler task or micro task.\"\n            );\n      }\n    };\n    exports.preconnect = function (href, options) {\n      \"string\" === typeof href && href\n        ? null != options && \"object\" !== typeof options\n          ? console.error(\n              \"ReactDOM.preconnect(): Expected the `options` argument (second) to be an object but encountered %s instead. The only supported option at this time is `crossOrigin` which accepts a string.\",\n              getValueDescriptorExpectingEnumForWarning(options)\n            )\n          : null != options &&\n            \"string\" !== typeof options.crossOrigin &&\n            console.error(\n              \"ReactDOM.preconnect(): Expected the `crossOrigin` option (second argument) to be a string but encountered %s instead. Try removing this option or passing a string value instead.\",\n              getValueDescriptorExpectingObjectForWarning(options.crossOrigin)\n            )\n        : console.error(\n            \"ReactDOM.preconnect(): Expected the `href` argument (first) to be a non-empty string but encountered %s instead.\",\n            getValueDescriptorExpectingObjectForWarning(href)\n          );\n      \"string\" === typeof href &&\n        (options\n          ? ((options = options.crossOrigin),\n            (options =\n              \"string\" === typeof options\n                ? \"use-credentials\" === options\n                  ? options\n                  : \"\"\n                : void 0))\n          : (options = null),\n        Internals.d.C(href, options));\n    };\n    exports.prefetchDNS = function (href) {\n      if (\"string\" !== typeof href || !href)\n        console.error(\n          \"ReactDOM.prefetchDNS(): Expected the `href` argument (first) to be a non-empty string but encountered %s instead.\",\n          getValueDescriptorExpectingObjectForWarning(href)\n        );\n      else if (1 < arguments.length) {\n        var options = arguments[1];\n        \"object\" === typeof options && options.hasOwnProperty(\"crossOrigin\")\n          ? console.error(\n              \"ReactDOM.prefetchDNS(): Expected only one argument, `href`, but encountered %s as a second argument instead. This argument is reserved for future options and is currently disallowed. It looks like the you are attempting to set a crossOrigin property for this DNS lookup hint. Browsers do not perform DNS queries using CORS and setting this attribute on the resource hint has no effect. Try calling ReactDOM.prefetchDNS() with just a single string argument, `href`.\",\n              getValueDescriptorExpectingEnumForWarning(options)\n            )\n          : console.error(\n              \"ReactDOM.prefetchDNS(): Expected only one argument, `href`, but encountered %s as a second argument instead. This argument is reserved for future options and is currently disallowed. Try calling ReactDOM.prefetchDNS() with just a single string argument, `href`.\",\n              getValueDescriptorExpectingEnumForWarning(options)\n            );\n      }\n      \"string\" === typeof href && Internals.d.D(href);\n    };\n    exports.preinit = function (href, options) {\n      \"string\" === typeof href && href\n        ? null == options || \"object\" !== typeof options\n          ? console.error(\n              \"ReactDOM.preinit(): Expected the `options` argument (second) to be an object with an `as` property describing the type of resource to be preinitialized but encountered %s instead.\",\n              getValueDescriptorExpectingEnumForWarning(options)\n            )\n          : \"style\" !== options.as &&\n            \"script\" !== options.as &&\n            console.error(\n              'ReactDOM.preinit(): Expected the `as` property in the `options` argument (second) to contain a valid value describing the type of resource to be preinitialized but encountered %s instead. Valid values for `as` are \"style\" and \"script\".',\n              getValueDescriptorExpectingEnumForWarning(options.as)\n            )\n        : console.error(\n            \"ReactDOM.preinit(): Expected the `href` argument (first) to be a non-empty string but encountered %s instead.\",\n            getValueDescriptorExpectingObjectForWarning(href)\n          );\n      if (\n        \"string\" === typeof href &&\n        options &&\n        \"string\" === typeof options.as\n      ) {\n        var as = options.as,\n          crossOrigin = getCrossOriginStringAs(as, options.crossOrigin),\n          integrity =\n            \"string\" === typeof options.integrity ? options.integrity : void 0,\n          fetchPriority =\n            \"string\" === typeof options.fetchPriority\n              ? options.fetchPriority\n              : void 0;\n        \"style\" === as\n          ? Internals.d.S(\n              href,\n              \"string\" === typeof options.precedence\n                ? options.precedence\n                : void 0,\n              {\n                crossOrigin: crossOrigin,\n                integrity: integrity,\n                fetchPriority: fetchPriority\n              }\n            )\n          : \"script\" === as &&\n            Internals.d.X(href, {\n              crossOrigin: crossOrigin,\n              integrity: integrity,\n              fetchPriority: fetchPriority,\n              nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n            });\n      }\n    };\n    exports.preinitModule = function (href, options) {\n      var encountered = \"\";\n      (\"string\" === typeof href && href) ||\n        (encountered +=\n          \" The `href` argument encountered was \" +\n          getValueDescriptorExpectingObjectForWarning(href) +\n          \".\");\n      void 0 !== options && \"object\" !== typeof options\n        ? (encountered +=\n            \" The `options` argument encountered was \" +\n            getValueDescriptorExpectingObjectForWarning(options) +\n            \".\")\n        : options &&\n          \"as\" in options &&\n          \"script\" !== options.as &&\n          (encountered +=\n            \" The `as` option encountered was \" +\n            getValueDescriptorExpectingEnumForWarning(options.as) +\n            \".\");\n      if (encountered)\n        console.error(\n          \"ReactDOM.preinitModule(): Expected up to two arguments, a non-empty `href` string and, optionally, an `options` object with a valid `as` property.%s\",\n          encountered\n        );\n      else\n        switch (\n          ((encountered =\n            options && \"string\" === typeof options.as ? options.as : \"script\"),\n          encountered)\n        ) {\n          case \"script\":\n            break;\n          default:\n            (encountered =\n              getValueDescriptorExpectingEnumForWarning(encountered)),\n              console.error(\n                'ReactDOM.preinitModule(): Currently the only supported \"as\" type for this function is \"script\" but received \"%s\" instead. This warning was generated for `href` \"%s\". In the future other module types will be supported, aligning with the import-attributes proposal. Learn more here: (https://github.com/tc39/proposal-import-attributes)',\n                encountered,\n                href\n              );\n        }\n      if (\"string\" === typeof href)\n        if (\"object\" === typeof options && null !== options) {\n          if (null == options.as || \"script\" === options.as)\n            (encountered = getCrossOriginStringAs(\n              options.as,\n              options.crossOrigin\n            )),\n              Internals.d.M(href, {\n                crossOrigin: encountered,\n                integrity:\n                  \"string\" === typeof options.integrity\n                    ? options.integrity\n                    : void 0,\n                nonce:\n                  \"string\" === typeof options.nonce ? options.nonce : void 0\n              });\n        } else null == options && Internals.d.M(href);\n    };\n    exports.preload = function (href, options) {\n      var encountered = \"\";\n      (\"string\" === typeof href && href) ||\n        (encountered +=\n          \" The `href` argument encountered was \" +\n          getValueDescriptorExpectingObjectForWarning(href) +\n          \".\");\n      null == options || \"object\" !== typeof options\n        ? (encountered +=\n            \" The `options` argument encountered was \" +\n            getValueDescriptorExpectingObjectForWarning(options) +\n            \".\")\n        : (\"string\" === typeof options.as && options.as) ||\n          (encountered +=\n            \" The `as` option encountered was \" +\n            getValueDescriptorExpectingObjectForWarning(options.as) +\n            \".\");\n      encountered &&\n        console.error(\n          'ReactDOM.preload(): Expected two arguments, a non-empty `href` string and an `options` object with an `as` property valid for a `<link rel=\"preload\" as=\"...\" />` tag.%s',\n          encountered\n        );\n      if (\n        \"string\" === typeof href &&\n        \"object\" === typeof options &&\n        null !== options &&\n        \"string\" === typeof options.as\n      ) {\n        encountered = options.as;\n        var crossOrigin = getCrossOriginStringAs(\n          encountered,\n          options.crossOrigin\n        );\n        Internals.d.L(href, encountered, {\n          crossOrigin: crossOrigin,\n          integrity:\n            \"string\" === typeof options.integrity ? options.integrity : void 0,\n          nonce: \"string\" === typeof options.nonce ? options.nonce : void 0,\n          type: \"string\" === typeof options.type ? options.type : void 0,\n          fetchPriority:\n            \"string\" === typeof options.fetchPriority\n              ? options.fetchPriority\n              : void 0,\n          referrerPolicy:\n            \"string\" === typeof options.referrerPolicy\n              ? options.referrerPolicy\n              : void 0,\n          imageSrcSet:\n            \"string\" === typeof options.imageSrcSet\n              ? options.imageSrcSet\n              : void 0,\n          imageSizes:\n            \"string\" === typeof options.imageSizes\n              ? options.imageSizes\n              : void 0,\n          media: \"string\" === typeof options.media ? options.media : void 0\n        });\n      }\n    };\n    exports.preloadModule = function (href, options) {\n      var encountered = \"\";\n      (\"string\" === typeof href && href) ||\n        (encountered +=\n          \" The `href` argument encountered was \" +\n          getValueDescriptorExpectingObjectForWarning(href) +\n          \".\");\n      void 0 !== options && \"object\" !== typeof options\n        ? (encountered +=\n            \" The `options` argument encountered was \" +\n            getValueDescriptorExpectingObjectForWarning(options) +\n            \".\")\n        : options &&\n          \"as\" in options &&\n          \"string\" !== typeof options.as &&\n          (encountered +=\n            \" The `as` option encountered was \" +\n            getValueDescriptorExpectingObjectForWarning(options.as) +\n            \".\");\n      encountered &&\n        console.error(\n          'ReactDOM.preloadModule(): Expected two arguments, a non-empty `href` string and, optionally, an `options` object with an `as` property valid for a `<link rel=\"modulepreload\" as=\"...\" />` tag.%s',\n          encountered\n        );\n      \"string\" === typeof href &&\n        (options\n          ? ((encountered = getCrossOriginStringAs(\n              options.as,\n              options.crossOrigin\n            )),\n            Internals.d.m(href, {\n              as:\n                \"string\" === typeof options.as && \"script\" !== options.as\n                  ? options.as\n                  : void 0,\n              crossOrigin: encountered,\n              integrity:\n                \"string\" === typeof options.integrity\n                  ? options.integrity\n                  : void 0\n            }))\n          : Internals.d.m(href));\n    };\n    exports.requestFormReset = function (form) {\n      Internals.d.r(form);\n    };\n    exports.unstable_batchedUpdates = function (fn, a) {\n      return fn(a);\n    };\n    exports.useFormState = function (action, initialState, permalink) {\n      return resolveDispatcher().useFormState(action, initialState, permalink);\n    };\n    exports.useFormStatus = function () {\n      return resolveDispatcher().useHostTransitionStatus();\n    };\n    exports.version = \"19.1.0-canary-029e8bd6-20250306\";\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react-dom/client.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (true) {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-dom-client.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react-dom/index.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (true) {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-dom.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\n"));

/***/ })

});