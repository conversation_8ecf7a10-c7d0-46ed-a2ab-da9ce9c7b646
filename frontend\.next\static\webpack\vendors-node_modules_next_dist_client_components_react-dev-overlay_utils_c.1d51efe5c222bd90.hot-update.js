"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    useSendMessage: function() {\n        return useSendMessage;\n    },\n    useTurbopack: function() {\n        return useTurbopack;\n    },\n    useWebsocket: function() {\n        return useWebsocket;\n    },\n    useWebsocketPing: function() {\n        return useWebsocketPing;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _getsocketurl = __webpack_require__(/*! ./get-socket-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\");\nfunction useWebsocket(assetPrefix) {\n    const webSocketRef = (0, _react.useRef)(undefined);\n    (0, _react.useEffect)(()=>{\n        if (webSocketRef.current) {\n            return;\n        }\n        const url = (0, _getsocketurl.getSocketUrl)(assetPrefix);\n        webSocketRef.current = new window.WebSocket(\"\" + url + \"/_next/webpack-hmr\");\n    }, [\n        assetPrefix\n    ]);\n    return webSocketRef;\n}\nfunction useSendMessage(webSocketRef) {\n    const sendMessage = (0, _react.useCallback)((data)=>{\n        const socket = webSocketRef.current;\n        if (!socket || socket.readyState !== socket.OPEN) {\n            return;\n        }\n        return socket.send(data);\n    }, [\n        webSocketRef\n    ]);\n    return sendMessage;\n}\nfunction useTurbopack(sendMessage, onUpdateError) {\n    const turbopackState = (0, _react.useRef)({\n        init: false,\n        // Until the dynamic import resolves, queue any turbopack messages which will be replayed.\n        queue: [],\n        callback: undefined\n    });\n    const processTurbopackMessage = (0, _react.useCallback)((msg)=>{\n        const { callback, queue } = turbopackState.current;\n        if (callback) {\n            callback(msg);\n        } else {\n            queue.push(msg);\n        }\n    }, []);\n    (0, _react.useEffect)(()=>{\n        const { current: initCurrent } = turbopackState;\n        // TODO(WEB-1589): only install if `process.turbopack` set.\n        if (initCurrent.init) {\n            return;\n        }\n        initCurrent.init = true;\n        Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_un\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_l\"), __webpack_require__.e(\"vendors-node_modules_n\"), __webpack_require__.e(\"vendors-node_modules_next_font_local_target_css-1d2c50c7\"), __webpack_require__.e(\"vendors-node_modules_t\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js\", 23)).then((param)=>{\n            let { connect } = param;\n            const { current } = turbopackState;\n            connect({\n                addMessageListener (cb) {\n                    current.callback = cb;\n                    // Replay all Turbopack messages before we were able to establish the HMR client.\n                    for (const msg of current.queue){\n                        cb(msg);\n                    }\n                    current.queue = undefined;\n                },\n                sendMessage,\n                onUpdateError\n            });\n        });\n    }, [\n        sendMessage,\n        onUpdateError\n    ]);\n    return processTurbopackMessage;\n}\nfunction useWebsocketPing(websocketRef) {\n    _s();\n    const sendMessage = useSendMessage(websocketRef);\n    const { tree } = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    (0, _react.useEffect)(()=>{\n        // Never send pings when using Turbopack as it's not used.\n        // Pings were originally used to keep track of active routes in on-demand-entries with webpack.\n        if (false) {}\n        // Taken from on-demand-entries-client.js\n        const interval = setInterval(()=>{\n            sendMessage(JSON.stringify({\n                event: 'ping',\n                tree,\n                appDirRoute: true\n            }));\n        }, 2500);\n        return ()=>clearInterval(interval);\n    }, [\n        tree,\n        sendMessage\n    ]);\n}\n_s(useWebsocketPing, \"wUse5NG7XMV1uhKK1kY0LLDje8k=\", false, function() {\n    return [\n        useSendMessage\n    ];\n});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js\n"));

/***/ })

});