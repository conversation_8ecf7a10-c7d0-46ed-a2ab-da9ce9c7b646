/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer, dev }) => {
    // Exclude AI-related modules from webpack resolution to prevent loading errors
    config.resolve.alias = {
      ...config.resolve.alias,
      // Exclude any AI-related imports that might still be cached
      '@/ai': false,
      '@/src/ai': false,
    };

    // Add fallbacks for problematic modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    // Ignore handlebars warnings
    config.module.rules.push({
      test: /\.handlebars$/,
      use: 'null-loader'
    });

    // Ignore specific source map warnings
    config.ignoreWarnings = [
      { module: /node_modules\/handlebars/ },
      { file: /node_modules\/handlebars/ },
      // Ignore AI-related module warnings
      { module: /src\/ai/ },
      { file: /src\/ai/ },
      { module: /@genkit-ai/ },
      { file: /@genkit-ai/ },
    ];

    // Optimize bundle splitting and chunk loading
    if (!isServer) {
      // Increase chunk loading timeout to prevent timeout errors
      config.output.chunkLoadTimeout = 120000; // 2 minutes

      // Enable cross-origin loading for better chunk loading
      config.output.crossOriginLoading = 'anonymous';

      // Optimize chunk splitting
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: 25,
        maxAsyncRequests: 25,
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
            enforce: true,
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 30,
            enforce: true,
          },

          firebase: {
            test: /[\\/]node_modules[\\/]firebase[\\/]/,
            name: 'firebase',
            chunks: 'all',
            priority: 25,
            enforce: true,
          },
          recharts: {
            test: /[\\/]node_modules[\\/]recharts[\\/]/,
            name: 'recharts',
            chunks: 'all',
            priority: 25,
            enforce: true,
          },
        },
      };

      // Add retry mechanism for failed chunk loads
      config.output.globalObject = 'self';
    }

    // Optimize development build performance
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: /node_modules/,
      };
    }

    return config;
  },
  // Performance optimizations
  reactStrictMode: true,
  compress: true,
  poweredByHeader: false,

  // Increase timeout for static generation
  staticPageGenerationTimeout: 300,

  // Experimental features for better performance
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'recharts', 'date-fns'],
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'i.imgur.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // Headers for better chunk loading and caching
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'unsafe-none',
          },
        ],
      },
      {
        source: '/ringtones/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // Output configuration for better caching
  output: 'standalone',
};

export default nextConfig;
