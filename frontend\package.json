{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 9004", "build": "next build", "postinstall": "patch-package", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.7.0", "geist": "^1.3.0", "lucide-react": "^0.475.0", "next": "15.2.3", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-transition-group": "4.4.5", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "@types/webpack": "^5.28.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}