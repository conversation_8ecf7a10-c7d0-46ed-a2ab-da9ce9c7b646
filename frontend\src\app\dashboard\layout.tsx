"use client";
import React, { useEffect } from 'react';
import AppHeader from '@/components/layout/AppHeader';
import TradingConfigSidebar from '@/components/layout/TradingConfigSidebar';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  // Show loading during hydration to prevent mismatch
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-xl">Loading...</p>
      </div>
    );
  }

  // Redirect to login if not authenticated (only after hydration)
  if (!isAuthenticated) {
    useEffect(() => {
      router.replace('/login');
    }, [router]);

    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-xl">Redirecting...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background text-foreground">
      <AppHeader />
      <div className="flex flex-1 overflow-hidden">
        <TradingConfigSidebar />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
