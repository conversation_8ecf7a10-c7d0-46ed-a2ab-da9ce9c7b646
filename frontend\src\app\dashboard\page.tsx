"use client";
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useTradingContext } from '@/contexts/TradingContext';
import { SessionManager } from '@/lib/session-manager';
// SessionManager will be imported dynamically to avoid SSR issues

import OrdersTable from '@/components/dashboard/OrdersTable';
import DashboardTabs from '@/components/dashboard/DashboardTabs';
import BalancesDisplay from '@/components/dashboard/BalancesDisplay';
import MarketPriceDisplay from '@/components/dashboard/MarketPriceDisplay';

export default function DashboardOrdersPage() {
  const { config, saveCurrentSession, targetPriceRows, orderHistory } = useTradingContext();

  const [currentSessionName, setCurrentSessionName] = useState<string>('');
  const [sessionManager, setSessionManager] = useState<any>(null);

  // Initialize SessionManager on client side only
  useEffect(() => {
    setSessionManager(SessionManager.getInstance());
  }, []);

  useEffect(() => {
    if (!sessionManager) return;

    const updateSessionName = () => {
      const currentSessionId = sessionManager.getCurrentSessionId();
      if (currentSessionId) {
        const session = sessionManager.loadSession(currentSessionId);
        if (session) {
          setCurrentSessionName(session.name);
          return;
        }
      }

      // No session or session not found, generate default name
      if (config.crypto1 && config.crypto2) {
        const defaultName = `${config.crypto1}/${config.crypto2} ${config.tradingMode || 'SimpleSpot'}`;
        setCurrentSessionName(defaultName);
      } else {
        setCurrentSessionName('Crypto 1/Crypto 2 = 0');
      }
    };

    updateSessionName();
  }, [config.crypto1, config.crypto2, config.tradingMode, sessionManager, targetPriceRows.length, orderHistory.length]);

  // Show placeholder text when cryptos are not selected
  const displayTitle = currentSessionName || ((config.crypto1 && config.crypto2)
    ? `${config.crypto1}/${config.crypto2} ${config.tradingMode || 'SimpleSpot'}`
    : "Crypto 1/Crypto 2 = 0");



  return (
    <div className="space-y-6">
      <DashboardTabs />
      <BalancesDisplay />
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-primary">Active Orders ({displayTitle})</CardTitle>
          <CardDescription>Current state of your target price levels. Prices update in real-time.</CardDescription>
        </CardHeader>
        <CardContent>
          <MarketPriceDisplay />
          <OrdersTable />
        </CardContent>
      </Card>
    </div>
  );
}
