import type { Metadata } from 'next';
import { GeistSans } from 'geist/font/sans'; // Correct import for Geist Sans
import './globals.css';
import { ClientProviders } from '@/components/ClientProviders';

// GeistSans from 'geist/font/sans' directly provides .variable and .className
// No need to call it as a function like with next/font/google.
// The variable it sets is typically --font-geist-sans.

export const metadata: Metadata = {
  title: 'Pluto Trading Bot',
  description: 'Simulated cryptocurrency trading bot with Neo Brutalist UI.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={GeistSans.variable} suppressHydrationWarning>
      <head>
        {/* Preload critical resources */}
        <link rel="preload" href="/ringtones/cheer.wav" as="audio" />
        <link rel="preconnect" href="https://api.coingecko.com" />
        <link rel="preconnect" href="https://i.imgur.com" />
        <link rel="preconnect" href="http://localhost:5000" />
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//api.coingecko.com" />
        <link rel="dns-prefetch" href="//i.imgur.com" />
        <link rel="dns-prefetch" href="//localhost" />
        {/* Preload critical dashboard routes */}
        <link rel="prefetch" href="/dashboard" />
        <link rel="prefetch" href="/login" />
        {/* Chunk loading retry mechanism */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                if (typeof window === 'undefined') return;

                // Chunk loading retry mechanism
                const originalOnError = window.onerror;
                window.onerror = function(message, source, lineno, colno, error) {
                  if (
                    error?.name === 'ChunkLoadError' ||
                    (typeof message === 'string' && message.includes('Loading chunk'))
                  ) {
                    console.warn('Chunk loading error detected, reloading page...');
                    setTimeout(() => window.location.reload(), 1000);
                    return true;
                  }
                  if (originalOnError) return originalOnError.call(window, message, source, lineno, colno, error);
                  return false;
                };

                // Handle unhandled promise rejections
                window.addEventListener('unhandledrejection', function(event) {
                  const error = event.reason;
                  if (
                    error?.name === 'ChunkLoadError' ||
                    (error?.message && error.message.includes('Loading chunk'))
                  ) {
                    console.warn('Chunk loading promise rejection, reloading page...');
                    event.preventDefault();
                    setTimeout(() => window.location.reload(), 1000);
                  }
                });
              })();
            `,
          }}
        />
      </head>
      <body className="font-sans antialiased" suppressHydrationWarning> {/* Tailwind's font-sans will pick up the CSS variable */}
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
