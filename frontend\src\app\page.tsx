"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Only redirect after authentication is determined (not loading)
    if (!isLoading) {
      const targetPath = isAuthenticated ? '/dashboard' : '/login';
      router.replace(targetPath);
    }
  }, [isAuthenticated, isLoading, router]);

  // This should rarely be seen due to immediate redirect
  return (
    <div className="flex items-center justify-center h-screen bg-background">
      <Loader2 className="h-12 w-12 animate-spin text-primary" />
      <p className="ml-4 text-xl">Redirecting...</p>
    </div>
  );
}
