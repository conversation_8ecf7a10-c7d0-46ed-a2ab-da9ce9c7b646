'use client';

import { AuthProvider } from '@/contexts/AuthContext';
import { TradingProvider } from '@/contexts/TradingContext';
// Temporarily disable toaster to isolate the issue
// import { Toaster } from '@/components/ui/toaster';

interface ClientProvidersProps {
  children: React.ReactNode;
}

export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <AuthProvider>
      <TradingProvider>
        {children}
        {/* Temporarily disabled: <Toaster /> */}
      </TradingProvider>
    </AuthProvider>
  );
}
