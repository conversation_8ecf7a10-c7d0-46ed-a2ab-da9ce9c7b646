'use client';

import { AuthProvider } from '@/contexts/AuthContext';
import { TradingProvider } from '@/contexts/TradingContext';
import { ToastProvider } from '@/components/ui/toast';

interface ClientProvidersProps {
  children: React.ReactNode;
}

export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <AuthProvider>
      <TradingProvider>
        <ToastProvider>
          {children}
        </ToastProvider>
      </TradingProvider>
    </AuthProvider>
  );
}
