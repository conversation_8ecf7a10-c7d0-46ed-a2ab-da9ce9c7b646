"use client";

import React from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { TrendingUp } from 'lucide-react';

export default function MarketPriceDisplay() {
  const { config, currentMarketPrice } = useTradingContext();

  const formatPrice = (price: number) => price.toFixed(config.numDigits);

  // Check if both cryptos are selected
  const hasBothCryptos = config.crypto1 && config.crypto2;

  // Calculate display pair and price based on trading mode
  let displayPair = "Crypto 1/Crypto 2";
  let displayPrice = "0";
  let currencySymbol = "$";

  if (hasBothCryptos && currentMarketPrice > 0) {
    if (config.tradingMode === "StablecoinSwap") {
      // For stablecoin swap mode: Crypto1/Crypto2 = Current Market Price (no currency symbol)
      displayPair = `${config.crypto1}/${config.crypto2}`;
      displayPrice = formatPrice(currentMarketPrice);
      currencySymbol = ''; // No currency symbol for crypto-to-crypto ratio
    } else {
      // Simple Spot mode: Crypto1/Crypto2 = Current Market Price
      displayPair = `${config.crypto1}/${config.crypto2}`;
      displayPrice = formatPrice(currentMarketPrice);
      currencySymbol = '$';
    }
  }

  return (
    <div className="mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md">
      <div className="flex items-center justify-center gap-3">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-500" />
          <span className="text-sm font-medium text-muted-foreground">
            Current Market Price
            <span className="ml-1 text-xs">
              ({config.tradingMode === "StablecoinSwap" ? "Stablecoin Swap" : "Simple Spot"})
            </span>
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-lg font-semibold text-foreground">
            {displayPair}:
          </span>
          <span className="text-2xl font-bold text-primary">
            {currencySymbol}{displayPrice}
          </span>
        </div>
      </div>
    </div>
  );
}
