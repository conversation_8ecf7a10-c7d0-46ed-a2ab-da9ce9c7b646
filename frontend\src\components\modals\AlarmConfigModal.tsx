
"use client";

import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTradingContext } from '@/contexts/TradingContext';
import type { AppSettings } from '@/lib/types';
import { DEFAULT_APP_SETTINGS } from '@/lib/types'; // Import DEFAULT_APP_SETTINGS
import { Volume2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface AlarmConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CUSTOM_SOUND_EXECUTION_VALUE = "custom_sound_execution";
const CUSTOM_SOUND_ERROR_VALUE = "custom_sound_error";

interface LocalAlarmSettings extends Partial<AppSettings> {
  customSoundOrderExecutionDataUri?: string;
  customSoundErrorDataUri?: string;
}

export function AlarmConfigModal({ isOpen, onClose }: AlarmConfigModalProps) {
  const { appSettings, dispatch } = useTradingContext();
  const [localSettings, setLocalSettings] = useState<LocalAlarmSettings>(appSettings);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    setLocalSettings({
      ...appSettings,
      customSoundOrderExecutionDataUri: appSettings.soundOrderExecution?.startsWith('data:audio') ? appSettings.soundOrderExecution : undefined,
      customSoundErrorDataUri: appSettings.soundError?.startsWith('data:audio') ? appSettings.soundError : undefined,
    });
  }, [appSettings, isOpen]);
  
  useEffect(() => {
    if (typeof window !== "undefined") {
        audioRef.current = new Audio();
    }
  }, []);

  const handleSwitchChange = (id: keyof AppSettings, checked: boolean) => {
    setLocalSettings(prev => ({ ...prev, [id]: checked }));
  };

  const handleSelectChange = (id: keyof AppSettings, value: string) => {
     setLocalSettings(prev => ({ ...prev, [id]: value }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, type: 'orderExecution' | 'error') => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUri = e.target?.result as string;
        if (type === 'orderExecution') {
          setLocalSettings(prev => ({ ...prev, customSoundOrderExecutionDataUri: dataUri, soundOrderExecution: CUSTOM_SOUND_EXECUTION_VALUE }));
        } else {
          setLocalSettings(prev => ({ ...prev, customSoundErrorDataUri: dataUri, soundError: CUSTOM_SOUND_ERROR_VALUE }));
        }
        toast({ title: "File Uploaded", description: `${file.name} ready to be used.` });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    const finalSettings: Partial<AppSettings> = { ...localSettings };
    
    if (localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && localSettings.customSoundOrderExecutionDataUri) {
      finalSettings.soundOrderExecution = localSettings.customSoundOrderExecutionDataUri;
    } else if (localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && !localSettings.customSoundOrderExecutionDataUri) {
      finalSettings.soundOrderExecution = DEFAULT_APP_SETTINGS.soundOrderExecution; 
       toast({ title: "Notice", description: "No custom execution sound uploaded, default used.", variant: "default" });
    }

    if (localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && localSettings.customSoundErrorDataUri) {
      finalSettings.soundError = localSettings.customSoundErrorDataUri;
    } else if (localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && !localSettings.customSoundErrorDataUri) {
      finalSettings.soundError = DEFAULT_APP_SETTINGS.soundError;
      toast({ title: "Notice", description: "No custom error sound uploaded, default used.", variant: "default" });
    }

    delete (finalSettings as LocalAlarmSettings).customSoundOrderExecutionDataUri;
    delete (finalSettings as LocalAlarmSettings).customSoundErrorDataUri;

    dispatch({ type: 'SET_APP_SETTINGS', payload: finalSettings });
    toast({ title: "Alarm Settings Saved", description: "Your sound alert preferences have been updated." });
    onClose();
  };

  const playTestSound = (soundSettingKey: 'soundOrderExecution' | 'soundError') => {
    let soundToPlay = localSettings[soundSettingKey];

    if (soundSettingKey === 'soundOrderExecution' && localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE) {
        soundToPlay = localSettings.customSoundOrderExecutionDataUri;
    } else if (soundSettingKey === 'soundError' && localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE) {
        soundToPlay = localSettings.customSoundErrorDataUri;
    }

    if (audioRef.current && soundToPlay) {
      // Validate and fix sound path if needed
      let validSoundPath = soundToPlay;

      // Fix old /sounds/ paths to /ringtones/
      if (soundToPlay.startsWith('/sounds/')) {
        validSoundPath = soundToPlay.replace('/sounds/', '/ringtones/');
        console.warn(`Fixed deprecated sound path: ${soundToPlay} -> ${validSoundPath}`);
      }

      audioRef.current.src = validSoundPath;
      audioRef.current.currentTime = 0; // Reset to beginning

      // Play the sound and limit duration to 2 seconds
      audioRef.current.play().then(() => {
        // Set a timeout to pause the audio after 2 seconds
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0; // Reset for next play
          }
        }, 2000); // 2 seconds
      }).catch(error => {
        console.error("Error playing sound:", error);
        toast({ title: "Sound Error", description: "Could not play test sound. Ensure file is valid or browser permissions are set.", variant: "destructive" });
      });
    } else {
        toast({ title: "No Sound", description: "No sound selected or custom sound not uploaded.", variant: "default" });
    }
  };
  
  const soundOptions = [
    { value: "/ringtones/cheer.wav", label: "Cheer" },
    { value: "/ringtones/chest1.wav", label: "Chest" },
    { value: "/ringtones/chime2.wav", label: "Chime" },
    { value: "/ringtones/bells.wav", label: "Bells" },
    { value: "/ringtones/bird1.wav", label: "Bird 1" },
    { value: "/ringtones/bird7.wav", label: "Bird 2" },
    { value: "/ringtones/sparrow1.wav", label: "Sparrow" },
    { value: "/ringtones/space_bells4a.wav", label: "Space Bells" },
    { value: "/ringtones/sanctuary1.wav", label: "Sanctuary" },
    { value: "/ringtones/marble1.wav", label: "Marble" },
    { value: "/ringtones/foundry2.wav", label: "Foundry" },
    { value: CUSTOM_SOUND_EXECUTION_VALUE, label: "Upload Custom..." }
  ];

  const errorSoundOptions = [
    { value: "/ringtones/G_hades_curse.wav", label: "Hades Curse" },
    { value: "/ringtones/G_hades_demat.wav", label: "Hades Demat" },
    { value: "/ringtones/G_hades_sanctify.wav", label: "Hades Sanctify" },
    { value: "/ringtones/dark2.wav", label: "Dark" },
    { value: "/ringtones/Satyr_atk4.wav", label: "Satyr Attack" },
    { value: "/ringtones/S_mon1.mp3", label: "Monster 1" },
    { value: "/ringtones/S_mon2.mp3", label: "Monster 2" },
    { value: "/ringtones/wolf4.wav", label: "Wolf" },
    { value: "/ringtones/goatherd1.wav", label: "Goatherd" },
    { value: "/ringtones/tax3.wav", label: "Tax Alert" },
    { value: "/ringtones/G_hades_mat.wav", label: "Hades Mat" },
    { value: CUSTOM_SOUND_ERROR_VALUE, label: "Upload Custom..." }
  ];


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-card border-2 border-border">
        <DialogHeader>
          <DialogTitle className="text-primary">Alarm Configuration</DialogTitle>
          <DialogDescription>
            Configure sound alerts for trading events. Ensure browser has audio permissions. Uploaded files are stored as data URIs.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="soundAlertsEnabled" className="text-base">Enable Sound Alerts</Label>
            <Switch
              id="soundAlertsEnabled"
              checked={!!localSettings.soundAlertsEnabled}
              onCheckedChange={(checked) => handleSwitchChange('soundAlertsEnabled', checked)}
            />
          </div>

          {localSettings.soundAlertsEnabled && (
            <>
              <div className="space-y-3 p-4 border-2 border-border rounded-sm">
                <div className="flex items-center justify-between">
                  <Label htmlFor="alertOnOrderExecution">Alert on Successful Order Execution</Label>
                  <Switch
                    id="alertOnOrderExecution"
                    checked={!!localSettings.alertOnOrderExecution}
                    onCheckedChange={(checked) => handleSwitchChange('alertOnOrderExecution', checked)}
                  />
                </div>
                {localSettings.alertOnOrderExecution && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Select
                        value={localSettings.soundOrderExecution}
                        onValueChange={(value) => handleSelectChange('soundOrderExecution', value)}
                      >
                        <SelectTrigger className="flex-grow"><SelectValue placeholder="Select sound" /></SelectTrigger>
                        <SelectContent>
                          {soundOptions.map(s => <SelectItem key={s.value} value={s.value}>{s.label}</SelectItem>)}
                        </SelectContent>
                      </Select>
                      <Button variant="outline" size="icon" onClick={() => playTestSound('soundOrderExecution')} className="btn-outline-neo p-2">
                        <Volume2 className="h-4 w-4" />
                      </Button>
                    </div>
                    {localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && (
                      <div>
                        <Label htmlFor="customSoundOrderFile" className="text-xs">Upload Execution Sound (.mp3, .wav, etc.)</Label>
                        <Input 
                          id="customSoundOrderFile" 
                          type="file" 
                          accept="audio/*" 
                          onChange={(e) => handleFileChange(e, 'orderExecution')} 
                          className={cn("text-xs mt-1", "focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}
                        />
                        {localSettings.customSoundOrderExecutionDataUri && <p className="text-xs text-muted-foreground truncate mt-1">Current: Custom sound uploaded</p>}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-3 p-4 border-2 border-border rounded-sm">
                <div className="flex items-center justify-between">
                  <Label htmlFor="alertOnError">Alert on Errors/Failures</Label>
                  <Switch
                    id="alertOnError"
                    checked={!!localSettings.alertOnError}
                    onCheckedChange={(checked) => handleSwitchChange('alertOnError', checked)}
                  />
                </div>
                {localSettings.alertOnError && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Select
                        value={localSettings.soundError}
                        onValueChange={(value) => handleSelectChange('soundError', value)}
                      >
                        <SelectTrigger className="flex-grow"><SelectValue placeholder="Select sound" /></SelectTrigger>
                        <SelectContent>
                          {errorSoundOptions.map(s => <SelectItem key={s.value} value={s.value}>{s.label}</SelectItem>)}
                        </SelectContent>
                      </Select>
                     <Button variant="outline" size="icon" onClick={() => playTestSound('soundError')} className="btn-outline-neo p-2">
                        <Volume2 className="h-4 w-4" />
                      </Button>
                    </div>
                     {localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && (
                      <div>
                        <Label htmlFor="customSoundErrorFile" className="text-xs">Upload Error Sound (.mp3, .wav, etc.)</Label>
                        <Input 
                          id="customSoundErrorFile" 
                          type="file" 
                          accept="audio/*" 
                          onChange={(e) => handleFileChange(e, 'error')} 
                          className={cn("text-xs mt-1", "focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}
                        />
                        {localSettings.customSoundErrorDataUri && <p className="text-xs text-muted-foreground truncate mt-1">Current: Custom sound uploaded</p>}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" className="btn-outline-neo">Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={handleSave} className="btn-neo">Save Settings</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
