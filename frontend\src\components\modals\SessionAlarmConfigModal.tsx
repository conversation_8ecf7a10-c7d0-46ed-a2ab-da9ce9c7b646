"use client";

import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SessionManager } from '@/lib/session-manager';
import { Volume2 } from 'lucide-react';

// Define default session alarm settings locally to avoid import issues
const DEFAULT_SESSION_ALARM_SETTINGS = {
  soundAlertsEnabled: true,
  alertOnOrderExecution: true,
  alertOnError: true,
  soundOrderExecution: "/ringtones/cheer.wav",
  soundError: "/ringtones/G_hades_curse.wav",
};
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface SessionAlarmConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
  sessionName: string;
}

const CUSTOM_SOUND_EXECUTION_VALUE = "custom_sound_execution";
const CUSTOM_SOUND_ERROR_VALUE = "custom_sound_error";

interface LocalAlarmSettings {
  soundAlertsEnabled: boolean;
  alertOnOrderExecution: boolean;
  alertOnError: boolean;
  soundOrderExecution: string;
  soundError: string;
  customSoundOrderExecutionDataUri?: string;
  customSoundErrorDataUri?: string;
}

const executionSoundOptions = [
  { value: "/ringtones/cheer.wav", label: "Cheer" },
  { value: "/ringtones/chest1.wav", label: "Chest" },
  { value: "/ringtones/chime2.wav", label: "Chime" },
  { value: "/ringtones/bells.wav", label: "Bells" },
  { value: "/ringtones/bird1.wav", label: "Bird 1" },
  { value: "/ringtones/bird7.wav", label: "Bird 2" },
  { value: "/ringtones/sparrow1.wav", label: "Sparrow" },
  { value: "/ringtones/space_bells4a.wav", label: "Space Bells" },
  { value: "/ringtones/sanctuary1.wav", label: "Sanctuary" },
  { value: "/ringtones/marble1.wav", label: "Marble" },
  { value: "/ringtones/foundry2.wav", label: "Foundry" },
  { value: CUSTOM_SOUND_EXECUTION_VALUE, label: "Upload Custom..." }
];

const errorSoundOptions = [
  { value: "/ringtones/G_hades_curse.wav", label: "Hades Curse" },
  { value: "/ringtones/G_hades_demat.wav", label: "Hades Demat" },
  { value: "/ringtones/G_hades_sanctify.wav", label: "Hades Sanctify" },
  { value: "/ringtones/dark2.wav", label: "Dark" },
  { value: "/ringtones/Satyr_atk4.wav", label: "Satyr Attack" },
  { value: "/ringtones/S_mon1.mp3", label: "Monster 1" },
  { value: "/ringtones/S_mon2.mp3", label: "Monster 2" },
  { value: "/ringtones/wolf4.wav", label: "Wolf" },
  { value: "/ringtones/goatherd1.wav", label: "Goatherd" },
  { value: "/ringtones/tax3.wav", label: "Tax Alert" },
  { value: "/ringtones/G_hades_mat.wav", label: "Hades Mat" },
  { value: CUSTOM_SOUND_ERROR_VALUE, label: "Upload Custom..." }
];

export function SessionAlarmConfigModal({ isOpen, onClose, sessionId, sessionName }: SessionAlarmConfigModalProps) {
  const [localSettings, setLocalSettings] = useState<LocalAlarmSettings>({
    ...DEFAULT_SESSION_ALARM_SETTINGS,
    customSoundOrderExecutionDataUri: undefined,
    customSoundErrorDataUri: undefined,
  });
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && sessionId) {
      const sessionManager = SessionManager.getInstance();
      const session = sessionManager.loadSession(sessionId);
      const alarmSettings = session?.alarmSettings || DEFAULT_SESSION_ALARM_SETTINGS;
      
      setLocalSettings({
        ...alarmSettings,
        customSoundOrderExecutionDataUri: alarmSettings.soundOrderExecution?.startsWith('data:audio') ? alarmSettings.soundOrderExecution : undefined,
        customSoundErrorDataUri: alarmSettings.soundError?.startsWith('data:audio') ? alarmSettings.soundError : undefined,
      });
    }
  }, [isOpen, sessionId]);
  
  useEffect(() => {
    if (typeof window !== "undefined") {
        audioRef.current = new Audio();
    }
  }, []);

  const handleSwitchChange = (id: keyof LocalAlarmSettings, checked: boolean) => {
    setLocalSettings(prev => ({ ...prev, [id]: checked }));
  };

  const handleSelectChange = (id: keyof LocalAlarmSettings, value: string) => {
     setLocalSettings(prev => ({ ...prev, [id]: value }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, type: 'orderExecution' | 'error') => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUri = e.target?.result as string;
        if (type === 'orderExecution') {
          setLocalSettings(prev => ({ ...prev, customSoundOrderExecutionDataUri: dataUri, soundOrderExecution: CUSTOM_SOUND_EXECUTION_VALUE }));
        } else {
          setLocalSettings(prev => ({ ...prev, customSoundErrorDataUri: dataUri, soundError: CUSTOM_SOUND_ERROR_VALUE }));
        }
        toast({ title: "File Uploaded", description: `${file.name} ready to be used.` });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    const finalSettings = { ...localSettings };
    
    if (localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && localSettings.customSoundOrderExecutionDataUri) {
      finalSettings.soundOrderExecution = localSettings.customSoundOrderExecutionDataUri;
    } else if (localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && !localSettings.customSoundOrderExecutionDataUri) {
      finalSettings.soundOrderExecution = DEFAULT_SESSION_ALARM_SETTINGS.soundOrderExecution; 
       toast({ title: "Notice", description: "No custom execution sound uploaded, default used.", variant: "default" });
    }

    if (localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && localSettings.customSoundErrorDataUri) {
      finalSettings.soundError = localSettings.customSoundErrorDataUri;
    } else if (localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && !localSettings.customSoundErrorDataUri) {
      finalSettings.soundError = DEFAULT_SESSION_ALARM_SETTINGS.soundError;
      toast({ title: "Notice", description: "No custom error sound uploaded, default used.", variant: "default" });
    }

    // Remove custom data URI properties before saving
    const { customSoundOrderExecutionDataUri, customSoundErrorDataUri, ...settingsToSave } = finalSettings;

    const sessionManager = SessionManager.getInstance();
    const success = sessionManager.updateSessionAlarmSettings(sessionId, settingsToSave);
    
    if (success) {
      toast({ title: "Session Alarm Settings Saved", description: `Sound alerts updated for "${sessionName}".` });
      onClose();
    } else {
      toast({ title: "Error", description: "Failed to save alarm settings.", variant: "destructive" });
    }
  };

  const playTestSound = (soundSettingKey: 'soundOrderExecution' | 'soundError') => {
    let soundToPlay = localSettings[soundSettingKey];

    if (soundSettingKey === 'soundOrderExecution' && localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE) {
        soundToPlay = localSettings.customSoundOrderExecutionDataUri || '';
    } else if (soundSettingKey === 'soundError' && localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE) {
        soundToPlay = localSettings.customSoundErrorDataUri || '';
    }

    if (audioRef.current && soundToPlay) {
      // Validate and fix sound path if needed
      let validSoundPath = soundToPlay;

      // Fix old /sounds/ paths to /ringtones/
      if (soundToPlay.startsWith('/sounds/')) {
        validSoundPath = soundToPlay.replace('/sounds/', '/ringtones/');
        console.warn(`Fixed deprecated sound path: ${soundToPlay} -> ${validSoundPath}`);
      }

      audioRef.current.src = validSoundPath;
      audioRef.current.currentTime = 0; // Reset to beginning

      // Play the sound and limit duration to 2 seconds
      audioRef.current.play().then(() => {
        // Set a timeout to pause the audio after 2 seconds
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0; // Reset for next play
          }
        }, 2000); // 2 seconds
      }).catch(error => {
        console.error("Error playing sound:", error);
        toast({ title: "Sound Error", description: "Could not play test sound. Ensure file is valid or browser permissions are set.", variant: "destructive" });
      });
    } else {
      toast({ title: "No Sound", description: "No sound file selected or uploaded.", variant: "default" });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-card border-2 border-border">
        <DialogHeader>
          <DialogTitle className="text-primary">Session Alarm Configuration</DialogTitle>
          <DialogDescription>
            Configure sound alerts for "{sessionName}". These settings apply only to this session.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="soundAlertsEnabled" className="text-base">Enable Sound Alerts</Label>
            <Switch
              id="soundAlertsEnabled"
              checked={!!localSettings.soundAlertsEnabled}
              onCheckedChange={(checked) => handleSwitchChange('soundAlertsEnabled', checked)}
            />
          </div>

          {localSettings.soundAlertsEnabled && (
            <>
              <div className="space-y-3 p-4 border-2 border-border rounded-sm">
                <div className="flex items-center justify-between">
                  <Label htmlFor="alertOnOrderExecution">Alert on Successful Order Execution</Label>
                  <Switch
                    id="alertOnOrderExecution"
                    checked={!!localSettings.alertOnOrderExecution}
                    onCheckedChange={(checked) => handleSwitchChange('alertOnOrderExecution', checked)}
                  />
                </div>
                {localSettings.alertOnOrderExecution && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Select
                        value={localSettings.soundOrderExecution}
                        onValueChange={(value) => handleSelectChange('soundOrderExecution', value)}
                      >
                        <SelectTrigger className="flex-grow"><SelectValue placeholder="Select sound" /></SelectTrigger>
                        <SelectContent>
                          {executionSoundOptions.map(s => <SelectItem key={s.value} value={s.value}>{s.label}</SelectItem>)}
                        </SelectContent>
                      </Select>
                      <Button variant="outline" size="icon" onClick={() => playTestSound('soundOrderExecution')} className="btn-outline-neo p-2">
                        <Volume2 className="h-4 w-4" />
                      </Button>
                    </div>
                    {localSettings.soundOrderExecution === CUSTOM_SOUND_EXECUTION_VALUE && (
                      <div>
                        <Label htmlFor="customSoundExecutionFile" className="text-xs">Upload Execution Sound (.mp3, .wav, etc.)</Label>
                        <Input 
                          id="customSoundExecutionFile" 
                          type="file" 
                          accept="audio/*" 
                          onChange={(e) => handleFileChange(e, 'orderExecution')} 
                          className={cn("text-xs mt-1", "focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}
                        />
                        {localSettings.customSoundOrderExecutionDataUri && <p className="text-xs text-muted-foreground truncate mt-1">Current: Custom sound uploaded</p>}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-3 p-4 border-2 border-border rounded-sm">
                <div className="flex items-center justify-between">
                  <Label htmlFor="alertOnError">Alert on Errors/Failures</Label>
                  <Switch
                    id="alertOnError"
                    checked={!!localSettings.alertOnError}
                    onCheckedChange={(checked) => handleSwitchChange('alertOnError', checked)}
                  />
                </div>
                {localSettings.alertOnError && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Select
                        value={localSettings.soundError}
                        onValueChange={(value) => handleSelectChange('soundError', value)}
                      >
                        <SelectTrigger className="flex-grow"><SelectValue placeholder="Select sound" /></SelectTrigger>
                        <SelectContent>
                          {errorSoundOptions.map(s => <SelectItem key={s.value} value={s.value}>{s.label}</SelectItem>)}
                        </SelectContent>
                      </Select>
                     <Button variant="outline" size="icon" onClick={() => playTestSound('soundError')} className="btn-outline-neo p-2">
                        <Volume2 className="h-4 w-4" />
                      </Button>
                    </div>
                     {localSettings.soundError === CUSTOM_SOUND_ERROR_VALUE && (
                      <div>
                        <Label htmlFor="customSoundErrorFile" className="text-xs">Upload Error Sound (.mp3, .wav, etc.)</Label>
                        <Input 
                          id="customSoundErrorFile" 
                          type="file" 
                          accept="audio/*" 
                          onChange={(e) => handleFileChange(e, 'error')} 
                          className={cn("text-xs mt-1", "focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}
                        />
                        {localSettings.customSoundErrorDataUri && <p className="text-xs text-muted-foreground truncate mt-1">Current: Custom sound uploaded</p>}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" className="btn-outline-neo">Cancel</Button>
          </DialogClose>
          <Button onClick={handleSave} className="btn-primary-neo">Save Settings</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
