
import React from 'react';
import Image from 'next/image';

interface LogoProps {
  className?: string;
  useFullName?: boolean; // Added prop to control text
}

const Logo: React.FC<LogoProps> = ({ className, useFullName = true }) => {
  return (
    <div className={`flex items-center text-2xl font-bold text-primary ${className}`}>
      <Image
        src="https://i.imgur.com/Q0HDcMH.png"
        alt="Pluto Trading Bot Logo"
        width={28}
        height={28}
        className="mr-2 rounded-sm"
      />
      <span>Pluto{useFullName ? " Trading Bot" : ""}</span>
    </div>
  );
};

export default Logo;
