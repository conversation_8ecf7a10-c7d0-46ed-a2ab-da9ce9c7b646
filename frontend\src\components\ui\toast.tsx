"use client"

import React from "react"

// Simple toast system without external dependencies
interface ToastProps {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  onClose?: () => void;
}

interface ToastProviderProps {
  children: React.ReactNode;
}

interface ToastViewportProps {
  className?: string;
}

interface ToastActionProps {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
}

interface ToastCloseProps {
  className?: string;
  onClick?: () => void;
}

interface ToastTitleProps {
  className?: string;
  children: React.ReactNode;
}

interface ToastDescriptionProps {
  className?: string;
  children: React.ReactNode;
}

// Simple class name utility
function cn(...classes: (string | undefined)[]): string {
  return classes.filter(Boolean).join(' ');
}

const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  return <>{children}</>;
};

const ToastViewport: React.FC<ToastViewportProps> = ({ className }) => (
  <div
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
      className
    )}
  />
);

const Toast: React.FC<ToastProps & { children: React.ReactNode }> = ({
  className,
  variant = "default",
  children,
  ...props
}) => {
  const variantClass = variant === 'destructive'
    ? "border-destructive bg-destructive text-destructive-foreground"
    : "border bg-background text-foreground";

  return (
    <div
      className={cn(
        "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all",
        variantClass,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const ToastAction: React.FC<ToastActionProps> = ({ className, children, onClick }) => (
  <button
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
      className
    )}
    onClick={onClick}
  >
    {children}
  </button>
);

const ToastClose: React.FC<ToastCloseProps> = ({ className, onClick }) => (
  <button
    className={cn(
      "absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",
      className
    )}
    onClick={onClick}
  >
    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
  </button>
);

const ToastTitle: React.FC<ToastTitleProps> = ({ className, children }) => (
  <h3
    className={cn("text-sm font-semibold", className)}
  >
    {children}
  </h3>
);

const ToastDescription: React.FC<ToastDescriptionProps> = ({ className, children }) => (
  <p
    className={cn("text-sm opacity-90", className)}
  >
    {children}
  </p>
);

type ToastActionElement = React.ReactElement<typeof ToastAction>;

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
};
