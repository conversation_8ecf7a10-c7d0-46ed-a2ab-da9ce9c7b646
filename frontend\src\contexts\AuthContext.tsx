
"use client";
import type { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { authApi } from '@/lib/api';
import { SessionManager } from '@/lib/session-manager';

interface AuthContextType {
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // Start with false to ensure server-client consistency
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true); // Start with true to prevent hydration mismatch
  const [isHydrated, setIsHydrated] = useState(false); // Track hydration state
  const router = useRouter();
  const pathname = usePathname();
  
  // Hydration effect - runs only on client after mount
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🔐 Initializing authentication...');

      // Check existing authentication from localStorage
      const storedAuthStatus = localStorage.getItem('plutoAuth');
      const authToken = localStorage.getItem('plutoAuthToken');

      if (storedAuthStatus === 'true' && authToken) {
        console.log('🔐 Found existing auth token - authenticating user');
        setIsAuthenticated(true);
        setIsLoading(false);
        setIsHydrated(true);

        // Now that authentication is confirmed, check backend connection
        try {
          const sessionManager = SessionManager.getInstance();
          sessionManager.checkBackendConnectionWhenReady().catch(() => {
            // Silent fail - session manager will handle fallback
          });
        } catch (error) {
          console.error('🔐 Error loading SessionManager:', error);
        }
        return;
      }

      // If not authenticated, try auto-login for development
      console.log('🔐 No existing auth found - attempting auto-login');
      try {
        // Small delay to ensure backend is ready
        await new Promise(resolve => setTimeout(resolve, 1000));

        const response = await fetch('http://localhost:5000/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: 'testuser',
            password: 'password123'
          })
        });

        if (response.ok) {
          const data = await response.json();
          localStorage.setItem('plutoAuth', 'true');
          localStorage.setItem('plutoAuthToken', data.access_token);
          localStorage.setItem('plutoRefreshToken', data.refresh_token);
          localStorage.setItem('plutoUser', JSON.stringify(data.user));
          setIsAuthenticated(true);
          console.log('🔐 Auto-logged in with test user for development');

          // Now that authentication is established, check backend connection
          try {
            const { SessionManager } = await import('@/lib/session-manager');
            const sessionManager = SessionManager.getInstance();
            sessionManager.checkBackendConnectionWhenReady().catch(() => {
              // Silent fail - session manager will handle fallback
            });
          } catch (error) {
            console.error('🔐 Error loading SessionManager after auto-login:', error);
          }
        } else {
          console.log('🔐 Auto-login failed - server response not ok:', response.status);
        }
      } catch (error) {
        console.log('🔐 Auto-login failed - network error:', error);
      }

      console.log('🔐 Authentication initialization complete');
      setIsLoading(false);
      setIsHydrated(true);
    };

    // Initialize authentication
    initializeAuth();

    // Failsafe timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('🔐 Authentication initialization timeout - forcing completion');
      setIsLoading(false);
      setIsHydrated(true);
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeoutId);
  }, []); // Only run once on mount
  
  useEffect(() => {
    // Route based on authentication status
    if (isAuthenticated && (pathname === '/login' || pathname === '/')) {
      router.replace('/dashboard');
    } else if (!isAuthenticated && pathname !== '/login' && pathname !== '/') {
      router.replace('/login');
    }
  }, [isAuthenticated, pathname, router]);
  
  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const success = await authApi.login(username, password);
      if (success) {
        setIsAuthenticated(true);
        router.push('/dashboard');
        return true;
      }
      setIsAuthenticated(false);
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const logout = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsAuthenticated(false);
      router.push('/login');
    }
  };

  // No loading screen needed since we initialize immediately from localStorage

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
