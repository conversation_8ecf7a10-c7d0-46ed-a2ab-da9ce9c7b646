"use client";
import type { ReactNode } from 'react';
import React, { createContext, useContext, useReducer, useEffect, useCallback, useRef } from 'react';
import type { TradingConfig, TargetPriceRow, OrderHistoryEntry, AppSettings, OrderStatus, DisplayOrderRow } from '@/lib/types';
import { DEFAULT_APP_SETTINGS, AVAILABLE_CRYPTOS, AVAILABLE_QUOTES_SIMPLE, AVAILABLE_STABLECOINS } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';

// Define locally to avoid import issues
const DEFAULT_QUOTE_CURRENCIES = ["USDT", "USDC", "BTC"];
import { tradingApi } from '@/lib/api';
import { SessionManager } from '@/lib/session-manager';
import { NetworkMonitor, AutoSaveManager, MemoryMonitor } from '@/lib/network-monitor';
import { useToast } from '@/components/ui/toast';

// Define Bot System Status
export type BotSystemStatus = 'Stopped' | 'WarmingUp' | 'Running';

// Update TargetPriceRow type
// (Assuming TargetPriceRow is defined in @/lib/types, this is conceptual)
// export interface TargetPriceRow {
//   // ... existing fields
//   lastActionTimestamp?: number;
// }

// Define action types
type TradingAction =
  | { type: 'SET_CONFIG'; payload: Partial<TradingConfig> }
  | { type: 'SET_TARGET_PRICE_ROWS'; payload: TargetPriceRow[] }
  | { type: 'ADD_TARGET_PRICE_ROW'; payload: TargetPriceRow }
  | { type: 'UPDATE_TARGET_PRICE_ROW'; payload: TargetPriceRow }
  | { type: 'REMOVE_TARGET_PRICE_ROW'; payload: string }
  | { type: 'ADD_ORDER_HISTORY_ENTRY'; payload: OrderHistoryEntry }
  | { type: 'UPDATE_ORDER_HISTORY_ENTRY'; payload: OrderHistoryEntry }
  | { type: 'REMOVE_ORDER_HISTORY_ENTRY'; payload: string }
  | { type: 'SET_APP_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'SET_BACKEND_STATUS'; payload: 'online' | 'offline' | 'unknown' }
  | { type: 'SET_MARKET_PRICE'; payload: number }
  | { type: 'FLUCTUATE_MARKET_PRICE' }
  | { type: 'SET_BALANCES'; payload: { crypto1: number; crypto2: number } }
  | { type: 'UPDATE_BALANCES'; payload: { crypto1?: number; crypto2?: number; stablecoin?: number } }
  | { type: 'UPDATE_STABLECOIN_BALANCE'; payload: number }
  | { type: 'CLEAR_ORDER_HISTORY' }
  | { type: 'SYSTEM_START_BOT_INITIATE' }
  | { type: 'SYSTEM_COMPLETE_WARMUP' }
  | { type: 'SYSTEM_STOP_BOT' }
  | { type: 'SYSTEM_RESET_BOT' }
  | { type: 'RESET_SESSION' }
  | { type: 'RESET_FOR_NEW_CRYPTO' };

// Define state type
interface TradingState {
  config: TradingConfig;
  targetPriceRows: TargetPriceRow[];
  orderHistory: OrderHistoryEntry[];
  appSettings: AppSettings;
  backendStatus: 'online' | 'offline' | 'unknown';
  currentMarketPrice: number;
  crypto1Balance: number;
  crypto2Balance: number;
  // isBotActive: boolean; // To be replaced by botSystemStatus
  botSystemStatus: BotSystemStatus; // New state for bot lifecycle
  stablecoinBalance: number; // For Stablecoin Swap mode
}

// Add this function to calculate the initial market price
const calculateInitialMarketPrice = (config: TradingConfig): number => {
  // Return 0 if either crypto is not selected
  if (!config.crypto1 || !config.crypto2) {
    return 0;
  }
  // Default fallback value for valid pairs
  return calculateFallbackMarketPrice(config);
};

// Enhanced API function to get market price for any trading pair
const getMarketPriceFromAPI = async (config: TradingConfig): Promise<number> => {
  try {
    // Return 0 if either crypto is not selected
    if (!config.crypto1 || !config.crypto2) {
      return 0;
    }

    // For StablecoinSwap mode, we need to calculate the ratio via stablecoin prices
    if (config.tradingMode === "StablecoinSwap" && config.preferredStablecoin) {
      try {
        // Get both crypto prices in terms of the preferred stablecoin
        const crypto1StablecoinPrice = await getStablecoinExchangeRate(config.crypto1, config.preferredStablecoin);
        const crypto2StablecoinPrice = await getStablecoinExchangeRate(config.crypto2, config.preferredStablecoin);

        if (crypto1StablecoinPrice > 0 && crypto2StablecoinPrice > 0) {
          // Calculate Crypto1/Crypto2 ratio via stablecoin
          const ratio = crypto1StablecoinPrice / crypto2StablecoinPrice;
          console.log(`✅ StablecoinSwap price via ${config.preferredStablecoin}: ${config.crypto1}/${config.crypto2} = ${ratio.toFixed(6)}`);
          console.log(`   ${config.crypto1}/${config.preferredStablecoin} = ${crypto1StablecoinPrice}`);
          console.log(`   ${config.crypto2}/${config.preferredStablecoin} = ${crypto2StablecoinPrice}`);
          return ratio;
        }
      } catch (stablecoinError) {
        console.warn('Stablecoin price calculation failed, falling back to direct pair...', stablecoinError);
      }
    }

    // For SimpleSpot mode or fallback: try direct pair fetching
    const symbol = `${config.crypto1}${config.crypto2}`.toUpperCase();

    // First try Binance API
    try {
      const response = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`);
      if (response.ok) {
        const data = await response.json();
        const price = parseFloat(data.price);
        if (price > 0) {
          console.log(`✅ Price fetched from Binance: ${config.crypto1}/${config.crypto2} = ${price}`);
          return price;
        }
      }
    } catch (binanceError) {
      console.warn('Binance API failed, trying alternative...', binanceError);
    }

    // Fallback to CoinGecko API for broader pair support
    try {
      const crypto1Id = getCoinGeckoId(config.crypto1);
      const crypto2Id = getCoinGeckoId(config.crypto2);

      // For both modes, we want Crypto1/Crypto2 ratio
      if (crypto1Id && crypto2Id) {
        const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${crypto1Id}&vs_currencies=${crypto2Id}`);
        if (response.ok) {
          const data = await response.json();
          const price = data[crypto1Id]?.[crypto2Id];
          if (price > 0) {
            console.log(`✅ Price fetched from CoinGecko: ${config.crypto1}/${config.crypto2} = ${price}`);
            return price;
          }
        }
      }
    } catch (geckoError) {
      console.warn('CoinGecko API failed, using mock price...', geckoError);
    }

    // Final fallback to mock price
    const mockPrice = calculateFallbackMarketPrice(config);
    console.log(`⚠️ Using mock price: ${config.crypto1}/${config.crypto2} = ${mockPrice}`);
    return mockPrice;

  } catch (error) {
    console.error('Error fetching market price:', error);
    return calculateFallbackMarketPrice(config);
  }
};

// Helper function to map crypto symbols to CoinGecko IDs
const getCoinGeckoId = (symbol: string): string | null => {
  const mapping: { [key: string]: string } = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum',
    'SOL': 'solana',
    'ADA': 'cardano',
    'DOT': 'polkadot',
    'MATIC': 'matic-network',
    'AVAX': 'avalanche-2',
    'LINK': 'chainlink',
    'UNI': 'uniswap',
    'USDT': 'tether',
    'USDC': 'usd-coin',
    'BUSD': 'binance-usd',
    'DAI': 'dai'
  };
  return mapping[symbol.toUpperCase()] || null;
};

// Helper function to get stablecoin exchange rates for real market data
const getStablecoinExchangeRate = async (crypto: string, stablecoin: string): Promise<number> => {
  try {
    // For stablecoin-to-stablecoin, assume 1:1 rate
    if (crypto.toUpperCase() === stablecoin.toUpperCase()) return 1.0;

    // First try Binance API for direct pair
    const binanceSymbol = `${crypto.toUpperCase()}${stablecoin.toUpperCase()}`;
    try {
      const response = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${binanceSymbol}`);
      if (response.ok) {
        const data = await response.json();
        const price = parseFloat(data.price);
        if (price > 0) {
          console.log(`✅ Stablecoin rate from Binance: ${crypto}/${stablecoin} = ${price}`);
          return price;
        }
      }
    } catch (binanceError) {
      console.warn(`Binance API failed for ${binanceSymbol}, trying CoinGecko...`);
    }

    // Fallback to CoinGecko API
    const cryptoId = getCoinGeckoId(crypto);
    const stablecoinId = getCoinGeckoId(stablecoin);

    if (cryptoId && stablecoinId) {
      // Try direct conversion
      const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${cryptoId}&vs_currencies=${stablecoinId}`);
      if (response.ok) {
        const data = await response.json();
        const rate = data[cryptoId]?.[stablecoinId];
        if (rate > 0) {
          console.log(`✅ Stablecoin rate from CoinGecko: ${crypto}/${stablecoin} = ${rate}`);
          return rate;
        }
      }
    }

    // Final fallback: calculate via USD prices
    const cryptoUSDPrice = getUSDPrice(crypto);
    const stablecoinUSDPrice = getUSDPrice(stablecoin);
    const rate = cryptoUSDPrice / stablecoinUSDPrice;

    console.log(`⚠️ Fallback stablecoin rate: ${crypto}/${stablecoin} = ${rate} (via USD)`);
    return rate;

  } catch (error) {
    console.error('Error fetching stablecoin exchange rate:', error);
    // Final fallback
    const cryptoUSDPrice = getUSDPrice(crypto);
    const stablecoinUSDPrice = getUSDPrice(stablecoin);
    return cryptoUSDPrice / stablecoinUSDPrice;
  }
};

// Helper function to get USD price (extracted from calculateFallbackMarketPrice)
const getUSDPrice = (crypto: string): number => {
  const usdPrices: { [key: string]: number } = {
    // Major cryptocurrencies
    'BTC': 108000,
    'ETH': 2100,
    'SOL': 240,
    'ADA': 1.2,
    'DOGE': 0.4,
    'LINK': 25,
    'MATIC': 0.5,
    'DOT': 8,
    'AVAX': 45,
    'SHIB': 0.000030,
    'XRP': 2.5,
    'LTC': 110,
    'BCH': 500,

    // DeFi tokens
    'UNI': 15,
    'AAVE': 180,
    'MKR': 1800,
    'SNX': 3.5,
    'COMP': 85,
    'YFI': 8500,
    'SUSHI': 2.1,
    '1INCH': 0.65,
    'CRV': 0.85,
    'UMA': 3.2,

    // Layer 1 blockchains
    'ATOM': 12,
    'NEAR': 6.5,
    'ALGO': 0.35,
    'ICP': 14,
    'HBAR': 0.28,
    'APT': 12.5,
    'TON': 5.8,
    'FTM': 0.95,
    'ONE': 0.025,

    // Other popular tokens
    'FIL': 8.5,
    'TRX': 0.25,
    'ETC': 35,
    'VET': 0.055,
    'QNT': 125,
    'LDO': 2.8,
    'CRO': 0.18,
    'LUNC': 0.00015,

    // Gaming & Metaverse
    'MANA': 0.85,
    'SAND': 0.75,
    'AXS': 8.5,
    'ENJ': 0.45,
    'CHZ': 0.12,

    // Infrastructure & Utility
    'THETA': 2.1,
    'FLOW': 1.2,
    'XTZ': 1.8,
    'EOS': 1.1,
    'GRT': 0.28,
    'BAT': 0.35,

    // Privacy coins
    'ZEC': 45,
    'DASH': 35,

    // DEX tokens
    'LRC': 0.45,
    'ZRX': 0.65,
    'KNC': 0.85,

    // Other tokens
    'REN': 0.15,
    'BAND': 2.5,
    'STORJ': 0.85,
    'NMR': 25,
    'ANT': 8.5,
    'BNT': 0.95,
    'MLN': 35,
    'REP': 15,

    // Smaller cap tokens
    'IOTX': 0.065,
    'ZIL': 0.045,
    'ICX': 0.35,
    'QTUM': 4.5,
    'ONT': 0.45,
    'WAVES': 3.2,
    'LSK': 1.8,
    'NANO': 1.5,
    'SC': 0.008,
    'DGB': 0.025,
    'RVN': 0.035,
    'BTT': 0.0000015,
    'WIN': 0.00015,
    'HOT': 0.0035,
    'DENT': 0.0018,
    'NPXS': 0.00085,
    'FUN': 0.0085,
    'CELR': 0.025,

    // Stablecoins
    'USDT': 1.0,
    'USDC': 1.0,
    'FDUSD': 1.0,
    'BUSD': 1.0,
    'DAI': 1.0
  };
  return usdPrices[crypto.toUpperCase()] || 100;
};

// Enhanced fallback function for market price calculation supporting all trading pairs
const calculateFallbackMarketPrice = (config: TradingConfig): number => {
  const crypto1USDPrice = getUSDPrice(config.crypto1);
  const crypto2USDPrice = getUSDPrice(config.crypto2);

  let basePrice: number;

  if (config.tradingMode === "StablecoinSwap") {
    // For stablecoin swap mode: Crypto1/Crypto2 = Current market Price
    // This shows how many units of Crypto2 equals 1 unit of Crypto1
    basePrice = crypto1USDPrice / crypto2USDPrice;

    console.log(`📊 StablecoinSwap price calculation: ${config.crypto1} ($${crypto1USDPrice}) / ${config.crypto2} ($${crypto2USDPrice}) = ${basePrice.toFixed(6)}`);
  } else {
    // Simple Spot mode: Calculate the ratio: how many units of crypto2 = 1 unit of crypto1
    basePrice = crypto1USDPrice / crypto2USDPrice;

    console.log(`📊 SimpleSpot price calculation: ${config.crypto1} ($${crypto1USDPrice}) / ${config.crypto2} ($${crypto2USDPrice}) = ${basePrice.toFixed(6)}`);
  }

  // Add small random fluctuation
  const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%
  const finalPrice = basePrice * (1 + fluctuation);

  return finalPrice;
};

// Duplicate TradingAction type removed - using the one defined above

const initialBaseConfig: TradingConfig = {
  tradingMode: "SimpleSpot",
  crypto1: "", // Start with empty crypto1
  crypto2: "", // Start with empty crypto2
  baseBid: 100,
  multiplier: 1.005,
  numDigits: 4,
  slippagePercent: 0.2, // Default 0.2% slippage range
  incomeSplitCrypto1Percent: 50,
  incomeSplitCrypto2Percent: 50,
  preferredStablecoin: AVAILABLE_STABLECOINS[0],
};

// Initial state with default balances (will be updated by global balances later)
const initialTradingState: TradingState = {
  config: initialBaseConfig,
  targetPriceRows: [],
  orderHistory: [],
  appSettings: DEFAULT_APP_SETTINGS,
  currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),
  botSystemStatus: 'Stopped',
  crypto1Balance: 10, // Default balance
  crypto2Balance: 100000, // Default balance
  stablecoinBalance: 0, // Default balance
  backendStatus: 'unknown',
};

const lastActionTimestampPerCounter = new Map<number, number>();

// LocalStorage persistence functions
const STORAGE_KEY = 'pluto_trading_state';
const GLOBAL_BALANCE_KEY = 'pluto_global_balances';
const BOT_STATUS_KEY = 'pluto_bot_status';

const saveStateToLocalStorage = (state: TradingState) => {
  try {
    if (typeof window !== 'undefined') {
      const stateToSave = {
        config: state.config,
        targetPriceRows: state.targetPriceRows,
        orderHistory: state.orderHistory,
        appSettings: state.appSettings,
        currentMarketPrice: state.currentMarketPrice,
        crypto1Balance: state.crypto1Balance,
        crypto2Balance: state.crypto2Balance,
        stablecoinBalance: state.stablecoinBalance,
        botSystemStatus: state.botSystemStatus,
        timestamp: Date.now()
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
    }
  } catch (error) {
    console.error('Failed to save state to localStorage:', error);
  }
};

const loadStateFromLocalStorage = (): Partial<TradingState> | null => {
  try {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        // Check if state is not too old (24 hours)
        if (parsed.timestamp && (Date.now() - parsed.timestamp) < 24 * 60 * 60 * 1000) {
          return parsed;
        }
      }
    }
  } catch (error) {
    console.error('Failed to load state from localStorage:', error);
  }
  return null;
};

// Global balance persistence functions
const saveGlobalBalances = (crypto1Balance: number, crypto2Balance: number, stablecoinBalance: number) => {
  try {
    if (typeof window !== 'undefined') {
      const balances = {
        crypto1Balance,
        crypto2Balance,
        stablecoinBalance,
        timestamp: Date.now()
      };
      localStorage.setItem(GLOBAL_BALANCE_KEY, JSON.stringify(balances));
      console.log('💰 Global balances saved:', balances);
    }
  } catch (error) {
    console.error('Failed to save global balances:', error);
  }
};

const loadGlobalBalances = () => {
  try {
    if (typeof window !== 'undefined') {
      const savedBalances = localStorage.getItem(GLOBAL_BALANCE_KEY);
      if (savedBalances) {
        const parsed = JSON.parse(savedBalances);
        console.log('💰 Global balances loaded:', parsed);
        return {
          crypto1Balance: parsed.crypto1Balance || 10,
          crypto2Balance: parsed.crypto2Balance || 100000,
          stablecoinBalance: parsed.stablecoinBalance || 0
        };
      }
    }
  } catch (error) {
    console.error('Failed to load global balances:', error);
  }
  return {
    crypto1Balance: 10,
    crypto2Balance: 100000,
    stablecoinBalance: 0
  };
};

const saveBotStatus = (status: BotSystemStatus) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(BOT_STATUS_KEY, status);
    } catch (error) {
      console.error('Failed to save bot status to localStorage:', error);
    }
  }
};

const loadBotStatus = (): BotSystemStatus => {
  if (typeof window !== 'undefined') {
    try {
      const saved = localStorage.getItem(BOT_STATUS_KEY);
      if (saved && (saved === 'Stopped' || saved === 'WarmingUp' || saved === 'Running')) {
        return saved as BotSystemStatus;
      }
    } catch (error) {
      console.error('Failed to load bot status from localStorage:', error);
    }
  }

  // Return default status if loading fails
  return 'Stopped';
};

// Create initial state with global balances - called after loadGlobalBalances is defined
const createInitialTradingState = (): TradingState => {
  const globalBalances = loadGlobalBalances();
  return {
    config: initialBaseConfig,
    targetPriceRows: [],
    orderHistory: [],
    appSettings: DEFAULT_APP_SETTINGS,
    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),
    botSystemStatus: 'Stopped',
    crypto1Balance: globalBalances.crypto1Balance,
    crypto2Balance: globalBalances.crypto2Balance,
    stablecoinBalance: globalBalances.stablecoinBalance,
    backendStatus: 'unknown',
  };
};

const tradingReducer = (state: TradingState, action: TradingAction): TradingState => {
  switch (action.type) {
    case 'SET_CONFIG':
      const newConfig = { ...state.config, ...action.payload };
      // If trading pair changes, reset market price (it will be re-calculated by effect)
      if (action.payload.crypto1 || action.payload.crypto2) {
        return { ...state, config: newConfig, currentMarketPrice: calculateInitialMarketPrice(newConfig) };
      }
      return { ...state, config: newConfig };
    case 'SET_TARGET_PRICE_ROWS':
      return { ...state, targetPriceRows: action.payload.sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1})) };
    case 'ADD_TARGET_PRICE_ROW': {
      const newRows = [...state.targetPriceRows, action.payload].sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1}));
      return { ...state, targetPriceRows: newRows };
    }
    case 'UPDATE_TARGET_PRICE_ROW': {
      const updatedRows = state.targetPriceRows.map((row: TargetPriceRow) =>
        row.id === action.payload.id ? action.payload : row
      ).sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1}));
      return { ...state, targetPriceRows: updatedRows };
    }
    case 'REMOVE_TARGET_PRICE_ROW': {
      const filteredRows = state.targetPriceRows.filter((row: TargetPriceRow) => row.id !== action.payload).sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice).map((row: TargetPriceRow, index: number) => ({...row, counter: index + 1}));
      return { ...state, targetPriceRows: filteredRows };
    }
    case 'ADD_ORDER_HISTORY_ENTRY':
      return { ...state, orderHistory: [action.payload, ...state.orderHistory] };
    case 'CLEAR_ORDER_HISTORY':
      return { ...state, orderHistory: [] };
    case 'SET_APP_SETTINGS':
      return { ...state, appSettings: { ...state.appSettings, ...action.payload } };
    case 'SET_MARKET_PRICE':
      return { ...state, currentMarketPrice: action.payload };
    case 'FLUCTUATE_MARKET_PRICE': {
      if (state.currentMarketPrice <= 0) return state;
      // More realistic fluctuation: smaller, more frequent changes
      const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update
      const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);
      return { ...state, currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice };
    }
    // case 'SET_BOT_STATUS': // Removed
    case 'SET_BALANCES':
      return {
        ...state,
        crypto1Balance: action.payload.crypto1,
        crypto2Balance: action.payload.crypto2,
      };
    case 'UPDATE_BALANCES':
      return {
        ...state,
        crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,
        crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,
        stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance,
      };
    case 'UPDATE_STABLECOIN_BALANCE':
      return {
        ...state,
        stablecoinBalance: action.payload,
      };
    case 'RESET_SESSION':
      const configForReset = { ...state.config };
      return {
        ...initialTradingState,
        config: configForReset,
        appSettings: { ...state.appSettings },
        currentMarketPrice: calculateInitialMarketPrice(configForReset),
      };
    case 'SET_BACKEND_STATUS':
      return { ...state, backendStatus: action.payload };
    case 'SYSTEM_START_BOT_INITIATE':
      // Continue from previous state instead of resetting
      saveBotStatus('WarmingUp');
      return {
        ...state,
        botSystemStatus: 'WarmingUp',
        // Keep existing targetPriceRows and orderHistory - don't reset
      };
    case 'SYSTEM_COMPLETE_WARMUP':
      saveBotStatus('Running');
      return { ...state, botSystemStatus: 'Running' };
    case 'SYSTEM_STOP_BOT':
      saveBotStatus('Stopped');
      return { ...state, botSystemStatus: 'Stopped' };
    case 'SYSTEM_RESET_BOT':
      // Fresh Start: Clear all target price rows and order history completely
      lastActionTimestampPerCounter.clear();
      // Clear current session to force new session name generation
      const sessionManager = SessionManager.getInstance();
      sessionManager.clearCurrentSession();
      saveBotStatus('Stopped');
      return {
        ...state,
        botSystemStatus: 'Stopped',
        targetPriceRows: [], // Completely clear all target price rows
        orderHistory: [],
      };
    case 'SET_TARGET_PRICE_ROWS':
      return { ...state, targetPriceRows: action.payload };
    case 'RESET_FOR_NEW_CRYPTO':
      saveBotStatus('Stopped');
      return {
        ...initialTradingState,
        config: state.config, // Keep the new crypto configuration
        backendStatus: state.backendStatus, // Preserve backend status
        botSystemStatus: 'Stopped', // Ensure bot is stopped
        currentMarketPrice: 0, // Reset market price for new crypto
      };
    default:
      return state;
  }
};

interface TradingContextType extends Omit<TradingState, 'backendStatus' | 'botSystemStatus'> {
  dispatch: React.Dispatch<TradingAction>;
  setTargetPrices: (prices: number[]) => void;
  getDisplayOrders: () => DisplayOrderRow[];
  backendStatus: 'online' | 'offline' | 'unknown';
  fetchMarketPrice: () => Promise<void>;
  checkBackendStatus: () => Promise<void>;
  botSystemStatus: BotSystemStatus;
  isBotActive: boolean; // Derived from botSystemStatus
  startBackendBot: (configId: string) => Promise<boolean>;
  stopBackendBot: (configId: string) => Promise<boolean>;
  saveConfigToBackend: (config: TradingConfig) => Promise<string | null>;
  saveCurrentSession: () => boolean;
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS

export const TradingProvider = ({ children }: { children: ReactNode }) => {
  // Toast hook for notifications
  const { toast } = useToast();

  // Initialize state with localStorage data if available
  const initializeState = () => {
    console.log('🔄 TradingContext: Initializing state...');

    // Check if this is a new session from URL parameter
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const isNewSession = urlParams.get('newSession') === 'true';

      if (isNewSession) {
        console.log('🆕 New session requested - starting with fresh state but keeping global balances');
        // Clear the URL parameter to avoid confusion - use setTimeout to avoid render issues
        setTimeout(() => {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, '', newUrl);
        }, 0);
        return createInitialTradingState(); // Use fresh state with current global balances
      }
    }

    // Try to get session manager, but handle case where it might not be ready yet
    let sessionManager;
    let currentSessionId;

    try {
      sessionManager = SessionManager.getInstance();
      currentSessionId = sessionManager.getCurrentSessionId();
      console.log('🔄 TradingContext: SessionManager available, current session:', currentSessionId);
    } catch (error) {
      console.log('🔄 TradingContext: SessionManager not ready yet, falling back to localStorage');
      sessionManager = null;
      currentSessionId = null;
    }

    // If no current session exists for this window, start fresh
    if (!currentSessionId) {
      console.log('🆕 No current session - starting with fresh state but keeping global balances');
      return createInitialTradingState(); // Use fresh state with current global balances
    }

    // If we have a session and session manager is available, try to load it
    if (sessionManager && currentSessionId) {
      try {
        const currentSession = sessionManager.loadSession(currentSessionId);
        if (currentSession) {
          const savedBotStatus = loadBotStatus();
          console.log('🔄 Restoring session from session manager:', currentSession.name);
          console.log('🔄 Bot status restored to:', savedBotStatus);
          return {
            ...initialTradingState,
            config: currentSession.config,
            targetPriceRows: currentSession.targetPriceRows,
            orderHistory: currentSession.orderHistory,
            currentMarketPrice: currentSession.currentMarketPrice,
            crypto1Balance: currentSession.crypto1Balance,
            crypto2Balance: currentSession.crypto2Balance,
            stablecoinBalance: currentSession.stablecoinBalance,
            botSystemStatus: savedBotStatus // Restore previous bot status to maintain continuity
          };
        }
      } catch (error) {
        console.error('🔄 Error loading session from session manager:', error);
      }
    }

    // Fallback to localStorage if session manager fails
    const savedState = loadStateFromLocalStorage();
    if (savedState) {
      const savedBotStatus = loadBotStatus();
      console.log('🔄 Restoring from localStorage backup');
      console.log('🔄 Bot status restored to:', savedBotStatus);
      const globalBalances = loadGlobalBalances();
      return {
        ...createInitialTradingState(),
        ...savedState,
        // Always use current global balances
        crypto1Balance: globalBalances.crypto1Balance,
        crypto2Balance: globalBalances.crypto2Balance,
        stablecoinBalance: globalBalances.stablecoinBalance,
        // Restore previous bot status to maintain continuity
        botSystemStatus: savedBotStatus
      };
    }

    console.log('⚠️ No session data found, starting fresh with global balances');
    return createInitialTradingState();
  };

  const [state, dispatch] = useReducer(tradingReducer, initializeState() as TradingState);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  // Removed processing locks and cooldowns for continuous trading

  // Telegram error notification function - defined early to avoid initialization issues
  const sendTelegramErrorNotification = useCallback(async (errorType: string, errorMessage: string, context?: string) => {
    try {
      const telegramToken = localStorage.getItem('telegram_bot_token');
      const telegramChatId = localStorage.getItem('telegram_chat_id');

      if (!telegramToken || !telegramChatId) {
        console.log('Telegram not configured - skipping error notification');
        return;
      }

      // Format error message with emoji and structure
      let message = `⚠️ <b>Error Alert</b>\n\n`;
      message += `<b>Type:</b> ${errorType}\n`;
      message += `<b>Error:</b> ${errorMessage}\n`;

      if (context) {
        message += `<b>Context:</b> ${context}\n`;
      }

      if (state.config.crypto1 && state.config.crypto2) {
        message += `<b>Trading Pair:</b> ${state.config.crypto1}/${state.config.crypto2}\n`;
      }

      message += `<b>Time:</b> ${new Date().toLocaleString()}\n`;

      const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: telegramChatId,
          text: message,
          parse_mode: 'HTML'
        })
      });

      if (!response.ok) {
        console.error('Failed to send Telegram error notification:', response.statusText);
      } else {
        console.log('✅ Telegram error notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending Telegram notification:', error);
    }
  }, [state.config.crypto1, state.config.crypto2]);

  // Initialize fetchMarketPrice first
  const fetchMarketPrice = useCallback(async (): Promise<void> => {
    try {
      // Don't fetch price if either crypto is not selected
      if (!state.config.crypto1 || !state.config.crypto2) {
        dispatch({ type: 'SET_MARKET_PRICE', payload: 0 });
        return;
      }
      const price = await getMarketPriceFromAPI(state.config);
      dispatch({ type: 'SET_MARKET_PRICE', payload: price });
    } catch (error) {
      console.error('Failed to fetch market price:', error);
      // Send error notification for price fetching failures
      sendTelegramErrorNotification(
        'Price Fetch Error',
        `Failed to fetch market price: ${error instanceof Error ? error.message : 'Unknown error'}`,
        `Trading pair: ${state.config.crypto1}/${state.config.crypto2}`
      );
    }
  }, [state.config, dispatch, sendTelegramErrorNotification]);

  // Market price fluctuation effect - simulates real-time price changes
  useEffect(() => {
    // Initial fetch
    fetchMarketPrice();

    // Set up price fluctuation interval for simulation
    const priceFluctuationInterval = setInterval(() => {
      // Only fluctuate price if online
      const networkMonitor = NetworkMonitor.getInstance();
      if (networkMonitor.getStatus().isOnline) {
        dispatch({ type: 'FLUCTUATE_MARKET_PRICE' });
      }
    }, 2000); // Update every 2 seconds for realistic simulation

    return () => {
      clearInterval(priceFluctuationInterval);
    };
  }, [fetchMarketPrice, dispatch]);

  // Other effects
  useEffect(() => {
    if (typeof window !== "undefined") {
        audioRef.current = new Audio();
    }
  }, []);

  // Audio queue system for handling multiple simultaneous sound requests
  const audioQueueRef = useRef<Array<{ soundKey: 'soundOrderExecution' | 'soundError'; sessionId?: string }>>([]);
  const isPlayingAudioRef = useRef(false);

  const processAudioQueue = useCallback(async () => {
    if (isPlayingAudioRef.current || audioQueueRef.current.length === 0 || !audioRef.current) return;

    isPlayingAudioRef.current = true;
    const { soundKey, sessionId } = audioQueueRef.current.shift()!;

    try {
      // Get session-specific alarm settings
      const sessionManager = SessionManager.getInstance();
      const targetSessionId = sessionId || sessionManager.getCurrentSessionId();
      const targetSession = targetSessionId ? sessionManager.loadSession(targetSessionId) : null;
      const alarmSettings = targetSession?.alarmSettings || state.appSettings;

      if (!alarmSettings.soundAlertsEnabled) {
        return; // Skip if sound is disabled
      }

      let soundPath: string | undefined;
      if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {
        soundPath = alarmSettings.soundOrderExecution;
      } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {
        soundPath = alarmSettings.soundError;
      }

      if (soundPath) {
        // Validate and fix sound path if needed
        let validSoundPath = soundPath;

        // Fix old /sounds/ paths to /ringtones/
        if (soundPath.startsWith('/sounds/')) {
          validSoundPath = soundPath.replace('/sounds/', '/ringtones/');
          console.warn(`Fixed deprecated sound path: ${soundPath} -> ${validSoundPath}`);
        }

        // Fallback to default sound if path doesn't exist in ringtones
        if (validSoundPath.startsWith('/ringtones/') && !validSoundPath.includes('data:audio')) {
          const knownRingtones = [
            'cheer.wav', 'chest1.wav', 'chime2.wav', 'bells.wav', 'bird1.wav', 'bird7.wav',
            'sparrow1.wav', 'space_bells4a.wav', 'sanctuary1.wav', 'marble1.wav', 'foundry2.wav',
            'G_hades_curse.wav', 'G_hades_demat.wav', 'G_hades_sanctify.wav', 'dark2.wav',
            'Satyr_atk4.wav', 'S_mon1.mp3', 'S_mon2.mp3', 'wolf4.wav', 'goatherd1.wav',
            'tax3.wav', 'G_hades_mat.wav'
          ];

          const filename = validSoundPath.split('/').pop();
          if (filename && !knownRingtones.includes(filename)) {
            validSoundPath = '/ringtones/cheer.wav'; // Default fallback
            console.warn(`Unknown ringtone file: ${soundPath}, using default: ${validSoundPath}`);
          }
        }

        // Stop any currently playing audio to prevent conflicts
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        audioRef.current.src = validSoundPath;

        // Wait for audio to load before playing
        await new Promise<void>((resolve, reject) => {
          const onCanPlay = () => {
            audioRef.current?.removeEventListener('canplaythrough', onCanPlay);
            audioRef.current?.removeEventListener('error', onError);
            resolve();
          };

          const onError = (e: Event) => {
            audioRef.current?.removeEventListener('canplaythrough', onCanPlay);
            audioRef.current?.removeEventListener('error', onError);
            reject(e);
          };

          audioRef.current?.addEventListener('canplaythrough', onCanPlay, { once: true });
          audioRef.current?.addEventListener('error', onError, { once: true });
          audioRef.current?.load();
        });

        // Play the sound with timeout
        await audioRef.current.play();

        // Set timeout to stop audio after 2 seconds
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
          }
        }, 2000);
      }
    } catch (error) {
      // Handle audio errors gracefully
      if (error instanceof Error && (
        error.name === 'AbortError' ||
        error.message.includes('interrupted') ||
        error.message.includes('play() request')
      )) {
        console.debug('Audio play interrupted (non-critical):', error.message);
      } else {
        console.error("Error playing sound:", error);
        // Try fallback sound for critical errors
        if (audioRef.current && audioRef.current.src !== '/ringtones/cheer.wav') {
          try {
            audioRef.current.src = '/ringtones/cheer.wav';
            await audioRef.current.play();
            setTimeout(() => {
              if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current.currentTime = 0;
              }
            }, 2000);
          } catch (fallbackError) {
            console.error("Fallback sound also failed:", fallbackError);
          }
        }
      }
    } finally {
      isPlayingAudioRef.current = false;
      // Process next item in queue after a short delay
      setTimeout(() => processAudioQueue(), 150);
    }
  }, [state.appSettings]);

  const playSound = useCallback((soundKey: 'soundOrderExecution' | 'soundError', sessionId?: string) => {
    // Add to queue instead of playing immediately
    audioQueueRef.current.push({ soundKey, sessionId });
    processAudioQueue();
  }, [processAudioQueue]);

  // Telegram notification function
  const sendTelegramNotification = useCallback(async (message: string) => {
    try {
      const telegramToken = localStorage.getItem('telegram_bot_token');
      const telegramChatId = localStorage.getItem('telegram_chat_id');

      if (!telegramToken || !telegramChatId) {
        console.log('Telegram not configured - skipping notification');
        return;
      }

      const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: telegramChatId,
          text: message,
          parse_mode: 'HTML'
        })
      });

      if (!response.ok) {
        console.error('Failed to send Telegram notification:', response.statusText);
      }
    } catch (error) {
      console.error('Error sending Telegram notification:', error);
    }
  }, []);



  // Effect to update market price when trading pair changes
  useEffect(() => {
    // When crypto1 or crypto2 (parts of state.config) change,
    // the fetchMarketPrice useCallback gets a new reference.
    // The useEffect above (which depends on fetchMarketPrice)
    // will re-run, clear the old interval, make an initial fetch with the new config,
    // and set up a new interval.
    // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.
    // Thus, no explicit dispatch is needed here.
  }, [state.config.crypto1, state.config.crypto2]); // Dependencies ensure this reacts to pair changes

  const setTargetPrices = useCallback((prices: number[]) => {
    if (!prices || !Array.isArray(prices)) return;

    // Sort prices from lowest to highest for proper counter assignment
    const sortedPrices = [...prices].filter((price: number) => !isNaN(price) && price > 0).sort((a, b) => a - b);

    const newRows: TargetPriceRow[] = sortedPrices.map((price: number, index: number) => {
        const existingRow = state.targetPriceRows.find((r: TargetPriceRow) => r.targetPrice === price);
        if (existingRow) {
          // Update counter for existing row based on sorted position
          return { ...existingRow, counter: index + 1 };
        }

        return {
          id: uuidv4(),
          counter: index + 1, // Counter starts from 1, lowest price gets counter 1
          status: 'Free' as OrderStatus,
          orderLevel: 0,
          valueLevel: state.config.baseBid, // Use baseBid from config
          targetPrice: price,
        };
      });
    dispatch({ type: 'SET_TARGET_PRICE_ROWS', payload: newRows });
  }, [state.targetPriceRows, state.config.baseBid, dispatch]);

  // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION
  useEffect(() => {
    // Check network status first
    const networkMonitor = NetworkMonitor.getInstance();
    const isOnline = networkMonitor.getStatus().isOnline;

    // Only check essential conditions AND network status
    if (state.botSystemStatus !== 'Running' ||
        state.targetPriceRows.length === 0 ||
        state.currentMarketPrice <= 0 ||
        !isOnline) {

      if (!isOnline && state.botSystemStatus === 'Running') {
        console.log('🔴 Trading paused - network offline');
      }
      return;
    }

    // Execute trading logic immediately - no locks, no cooldowns, no delays
    const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;
    const sortedRowsForLogic = [...targetPriceRows].sort((a: TargetPriceRow, b: TargetPriceRow) => a.targetPrice - b.targetPrice);

    // Use mutable variables for balance tracking within this cycle
    let currentCrypto1Balance = crypto1Balance;
    let currentCrypto2Balance = crypto2Balance;
    let actionsTaken = 0;

    console.log(`🚀 CONTINUOUS TRADING: Price $${currentMarketPrice.toFixed(2)} | Targets: ${sortedRowsForLogic.length} | Balance: $${currentCrypto2Balance} ${config.crypto2}`);

    // Show which targets are in range
    const targetsInRange = sortedRowsForLogic.filter(row => {
      const diffPercent = (Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice) * 100;
      return diffPercent <= config.slippagePercent;
    });

    if (targetsInRange.length > 0) {
      console.log(`🎯 TARGETS IN RANGE (±${config.slippagePercent}%):`, targetsInRange.map(row => `Counter ${row.counter} (${row.status})`));
    }

    // CONTINUOUS TRADING LOGIC: Process all targets immediately
    for (let i = 0; i < sortedRowsForLogic.length; i++) {
      const activeRow = sortedRowsForLogic[i];
      const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;

      // STEP 1: Check if TargetRowN is triggered (within slippage range)
      if (priceDiffPercent <= config.slippagePercent) {

        if (config.tradingMode === "SimpleSpot") {
          // STEP 2: Evaluate Triggered TargetRowN's Status
          if (activeRow.status === "Free") {
            // STEP 2a: Execute BUY on TargetRowN
            const costCrypto2 = activeRow.valueLevel;

            if (currentCrypto2Balance >= costCrypto2) {
              const amountCrypto1Bought = costCrypto2 / currentMarketPrice;
              const updatedRow: TargetPriceRow = {
                ...activeRow,
                status: "Full",
                orderLevel: activeRow.orderLevel + 1,
                valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),
                crypto1AmountHeld: amountCrypto1Bought,
                originalCostCrypto2: costCrypto2,
                crypto1Var: amountCrypto1Bought,
                crypto2Var: -costCrypto2,
              };
              dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedRow });
              dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance + amountCrypto1Bought, crypto2: currentCrypto2Balance - costCrypto2 } });
              dispatch({
                type: 'ADD_ORDER_HISTORY_ENTRY',
                payload: {
                  id: uuidv4(),
                  timestamp: Date.now(),
                  pair: `${config.crypto1}/${config.crypto2}`,
                  crypto1: config.crypto1,
                  orderType: "BUY",
                  amountCrypto1: amountCrypto1Bought,
                  avgPrice: currentMarketPrice,
                  valueCrypto2: costCrypto2,
                  price1: currentMarketPrice,
                  crypto1Symbol: config.crypto1 || '',
                  crypto2Symbol: config.crypto2 || ''
                }
              });
              console.log(`✅ BUY: Counter ${activeRow.counter} bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`);
              playSound('soundOrderExecution');

              // Show toast notification for BUY
              toast({
                type: 'success',
                title: '🟢 BUY EXECUTED',
                description: `Bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`,
                duration: 2000
              });

              // Send Telegram notification for BUY
              sendTelegramNotification(
                `🟢 <b>BUY EXECUTED</b>\n` +
                `📊 Counter: ${activeRow.counter}\n` +
                `💰 Amount: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}\n` +
                `💵 Price: $${currentMarketPrice.toFixed(2)}\n` +
                `💸 Cost: $${costCrypto2.toFixed(2)} ${config.crypto2}\n` +
                `📈 Mode: Simple Spot`
              );

              actionsTaken++;

              // Update local balance for subsequent checks in this cycle
              currentCrypto2Balance -= costCrypto2;
              currentCrypto1Balance += amountCrypto1Bought;
            } else {
              // Send error notification for insufficient balance
              console.log(`❌ Insufficient ${config.crypto2} balance for BUY order`);
              sendTelegramErrorNotification(
                'Insufficient Balance',
                `Cannot execute BUY order - insufficient ${config.crypto2} balance`,
                `Required: ${costCrypto2.toFixed(2)} ${config.crypto2}, Available: ${currentCrypto2Balance.toFixed(2)} ${config.crypto2}`
              );
            }
          }

          // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY
          const currentCounter = activeRow.counter;
          const inferiorRow = sortedRowsForLogic.find(row => row.counter === currentCounter - 1);

          if (inferiorRow && inferiorRow.status === "Full" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {
            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;
            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;
            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;

            // Calculate Crypto1 profit/loss - convert USDT profit to BTC equivalent
            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit / currentMarketPrice : 0;

            const updatedInferiorRow: TargetPriceRow = {
              ...inferiorRow,
              status: "Free",
              crypto1AmountHeld: undefined,
              originalCostCrypto2: undefined,
              valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),
              crypto1Var: -amountCrypto1ToSell,
              crypto2Var: crypto2Received,
            };
            dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedInferiorRow });
            dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance - amountCrypto1ToSell, crypto2: currentCrypto2Balance + crypto2Received } });
            dispatch({
              type: 'ADD_ORDER_HISTORY_ENTRY',
              payload: {
                id: uuidv4(),
                timestamp: Date.now(),
                pair: `${config.crypto1}/${config.crypto2}`,
                crypto1: config.crypto1,
                orderType: "SELL",
                amountCrypto1: amountCrypto1ToSell,
                avgPrice: currentMarketPrice,
                valueCrypto2: crypto2Received,
                price1: currentMarketPrice,
                crypto1Symbol: config.crypto1 || '',
                crypto2Symbol: config.crypto2 || '',
                realizedProfitLossCrypto2: realizedProfit,
                realizedProfitLossCrypto1: realizedProfitCrypto1
              }
            });

            console.log(`💰 SELL Trade P/L: Crypto2=${realizedProfit.toFixed(6)}, Crypto1=${realizedProfitCrypto1.toFixed(6)}`);
            console.log(`✅ SELL: Counter ${currentCounter - 1} sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}. Profit: $${realizedProfit.toFixed(2)}`);
            playSound('soundOrderExecution');

            // Show toast notification for SELL
            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';
            toast({
              type: realizedProfit > 0 ? 'success' : realizedProfit < 0 ? 'warning' : 'info',
              title: '🔴 SELL EXECUTED',
              description: `Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} | ${profitEmoji} Profit: $${realizedProfit.toFixed(2)}`,
              duration: 2000
            });

            // Send Telegram notification for SELL
            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';
            sendTelegramNotification(
              `🔴 <b>SELL EXECUTED</b>\n` +
              `📊 Counter: ${currentCounter - 1}\n` +
              `💰 Amount: ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}\n` +
              `💵 Price: $${currentMarketPrice.toFixed(2)}\n` +
              `💸 Received: $${crypto2Received.toFixed(2)} ${config.crypto2}\n` +
              `${profitEmoji} Profit: $${realizedProfit.toFixed(2)} ${config.crypto2}\n` +
              `📈 Mode: Simple Spot`
            );

            actionsTaken++;

            // Update local balance for subsequent checks in this cycle
            currentCrypto1Balance -= amountCrypto1ToSell;
            currentCrypto2Balance += crypto2Received;
          }
        } else if (config.tradingMode === "StablecoinSwap") {
          // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)
          if (activeRow.status === "Free") {
            // STEP 2a: Execute Two-Step "Buy Crypto1 via Stablecoin" on TargetRowN
            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)

            if (currentCrypto2Balance >= amountCrypto2ToUse) {
              // Step 1: Sell Crypto2 for PreferredStablecoin
              // Get real market price for Crypto2/Stablecoin pair (synchronous for now)
              const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');
              const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;

              // Step 2: Buy Crypto1 with Stablecoin
              // Get real market price for Crypto1/Stablecoin pair
              const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');
              const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;

              // Update Row N: Free → Full, Level++, Value recalculated
              const newLevel = activeRow.orderLevel + 1;
              const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);

              const updatedRow: TargetPriceRow = {
                ...activeRow,
                status: "Full", // Free → Full
                orderLevel: newLevel, // Level = Level + 1
                valueLevel: newValue, // Value = BaseBid * (Multiplier ^ Level)
                crypto1AmountHeld: crypto1Bought, // Store amount held for N-1 logic
                originalCostCrypto2: amountCrypto2ToUse, // Store original cost for profit calculation
                crypto1Var: crypto1Bought, // Positive amount of Crypto1 acquired
                crypto2Var: -amountCrypto2ToUse, // Negative amount of Crypto2 sold
              };

              dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedRow });
              dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance + crypto1Bought, crypto2: currentCrypto2Balance - amountCrypto2ToUse } });

              // Calculate cost basis for this BUY operation in terms of crypto2
              const costBasisCrypto2 = amountCrypto2ToUse;
              const costBasisStablecoin = stablecoinObtained;

              // Add history entries for both steps of the stablecoin swap
              dispatch({
                type: 'ADD_ORDER_HISTORY_ENTRY',
                payload: {
                  id: uuidv4(),
                  timestamp: Date.now(),
                  pair: `${config.crypto2}/${config.preferredStablecoin}`,
                  crypto1: config.crypto2,
                  orderType: "SELL",
                  amountCrypto1: amountCrypto2ToUse,
                  avgPrice: crypto2StablecoinPrice,
                  valueCrypto2: stablecoinObtained,
                  price1: crypto2StablecoinPrice,
                  crypto1Symbol: config.crypto2 || '',
                  crypto2Symbol: config.preferredStablecoin || '',
                  // For SELL step of BUY operation: show cost basis as negative (money spent)
                  realizedProfitLossCrypto2: -costBasisCrypto2, // Cost of this operation
                  realizedProfitLossCrypto1: crypto1StablecoinPrice > 0 ? -costBasisCrypto2 / crypto1StablecoinPrice : 0
                }
              });

              dispatch({
                type: 'ADD_ORDER_HISTORY_ENTRY',
                payload: {
                  id: uuidv4(),
                  timestamp: Date.now(),
                  pair: `${config.crypto1}/${config.preferredStablecoin}`,
                  crypto1: config.crypto1,
                  orderType: "BUY",
                  amountCrypto1: crypto1Bought,
                  avgPrice: crypto1StablecoinPrice,
                  valueCrypto2: stablecoinObtained,
                  price1: crypto1StablecoinPrice,
                  crypto1Symbol: config.crypto1 || '',
                  crypto2Symbol: config.preferredStablecoin || '',
                  // For BUY step: show the value acquired (positive)
                  realizedProfitLossCrypto2: costBasisCrypto2, // Value acquired in crypto2 terms
                  realizedProfitLossCrypto1: crypto1Bought // Amount of crypto1 acquired
                }
              });

              console.log(`✅ STABLECOIN BUY: Counter ${activeRow.counter} | Step 1: Sold ${amountCrypto2ToUse} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin} | Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} | Level: ${activeRow.orderLevel} → ${newLevel}`);
              playSound('soundOrderExecution');

              // Show toast notification for Stablecoin BUY
              toast({
                type: 'success',
                title: '🟢 BUY EXECUTED (Stablecoin)',
                description: `Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} via ${config.preferredStablecoin}`,
                duration: 2000
              });

              // Send Telegram notification for Stablecoin BUY
              sendTelegramNotification(
                `🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>\n` +
                `📊 Counter: ${activeRow.counter}\n` +
                `🔄 Step 1: Sold ${amountCrypto2ToUse.toFixed(2)} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin}\n` +
                `🔄 Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1}\n` +
                `📊 Level: ${activeRow.orderLevel} → ${newLevel}\n` +
                `📈 Mode: Stablecoin Swap`
              );

              actionsTaken++;

              // Update local balance for subsequent checks in this cycle
              currentCrypto2Balance -= amountCrypto2ToUse;
              currentCrypto1Balance += crypto1Bought;
            }
          }

          // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY
          const currentCounter = activeRow.counter;
          const inferiorRow = sortedRowsForLogic.find(row => row.counter === currentCounter - 1);

          if (inferiorRow && inferiorRow.status === "Full" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {
            // Execute Two-Step "Sell Crypto1 & Reacquire Crypto2 via Stablecoin" for TargetRowN_minus_1
            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;

            // Step A: Sell Crypto1 for PreferredStablecoin
            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');
            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;

            // Step B: Buy Crypto2 with Stablecoin
            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');
            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;

            // Calculate realized profit in Crypto2
            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;

            // Calculate Crypto1 profit/loss - convert Crypto2 profit to Crypto1 equivalent
            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;

            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared
            const updatedInferiorRow: TargetPriceRow = {
              ...inferiorRow,
              status: "Free", // Full → Free
              // orderLevel: REMAINS UNCHANGED per specification
              crypto1AmountHeld: undefined, // Clear held amount
              originalCostCrypto2: undefined, // Clear original cost
              valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel), // Recalculate value based on current level
              crypto1Var: 0, // Clear to 0.00 (Free status)
              crypto2Var: 0, // Clear to 0.00 (Free status)
            };

            dispatch({ type: 'UPDATE_TARGET_PRICE_ROW', payload: updatedInferiorRow });
            dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: currentCrypto1Balance - amountCrypto1ToSell, crypto2: currentCrypto2Balance + crypto2Reacquired } });

            // Add history entries for both steps of the N-1 stablecoin swap
            dispatch({
              type: 'ADD_ORDER_HISTORY_ENTRY',
              payload: {
                id: uuidv4(),
                timestamp: Date.now(),
                pair: `${config.crypto1}/${config.preferredStablecoin}`,
                crypto1: config.crypto1,
                orderType: "SELL",
                amountCrypto1: amountCrypto1ToSell,
                avgPrice: crypto1StablecoinPrice,
                valueCrypto2: stablecoinFromC1Sell,
                price1: crypto1StablecoinPrice,
                crypto1Symbol: config.crypto1 || '',
                crypto2Symbol: config.preferredStablecoin || '',
                // For SELL step: show the stablecoin value received (positive)
                realizedProfitLossCrypto2: stablecoinFromC1Sell / crypto2StablecoinPrice, // Convert stablecoin to crypto2 equivalent
                realizedProfitLossCrypto1: amountCrypto1ToSell // Amount of crypto1 sold (negative impact on holdings)
              }
            });

            dispatch({
              type: 'ADD_ORDER_HISTORY_ENTRY',
              payload: {
                id: uuidv4(),
                timestamp: Date.now(),
                pair: `${config.crypto2}/${config.preferredStablecoin}`,
                crypto1: config.crypto2,
                orderType: "BUY",
                amountCrypto1: crypto2Reacquired,
                avgPrice: crypto2StablecoinPrice,
                valueCrypto2: stablecoinFromC1Sell,
                price1: crypto2StablecoinPrice,
                crypto1Symbol: config.crypto2 || '',
                crypto2Symbol: config.preferredStablecoin || '',
                // For BUY step: show the net profit/loss from the complete operation
                realizedProfitLossCrypto2: realizedProfitInCrypto2,
                realizedProfitLossCrypto1: realizedProfitCrypto1
              }
            });

            console.log(`💰 STABLECOIN SWAP P/L: Crypto2=${realizedProfitInCrypto2.toFixed(6)}, Crypto1=${realizedProfitCrypto1.toFixed(6)}`);

            console.log(`✅ STABLECOIN SELL: Counter ${currentCounter - 1} | Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin} | Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2} | Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} | Level: ${inferiorRow.orderLevel} (unchanged)`);
            playSound('soundOrderExecution');

            // Show toast notification for Stablecoin SELL
            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';
            toast({
              type: realizedProfitInCrypto2 > 0 ? 'success' : realizedProfitInCrypto2 < 0 ? 'warning' : 'info',
              title: '🔴 SELL EXECUTED (Stablecoin)',
              description: `Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} | ${profitEmoji} Profit: $${realizedProfitInCrypto2.toFixed(2)}`,
              duration: 2000
            });

            // Send Telegram notification for Stablecoin SELL
            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';
            sendTelegramNotification(
              `🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>\n` +
              `📊 Counter: ${currentCounter - 1}\n` +
              `🔄 Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin}\n` +
              `🔄 Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2}\n` +
              `${profitEmoji} Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2}\n` +
              `📊 Level: ${inferiorRow.orderLevel} (unchanged)\n` +
              `📈 Mode: Stablecoin Swap`
            );

            actionsTaken++;

            // Update local balance for subsequent checks in this cycle
            currentCrypto1Balance -= amountCrypto1ToSell;
            currentCrypto2Balance += crypto2Reacquired;
          }
        }
      }
    }

    if (actionsTaken > 0) {
      console.log(`🎯 CYCLE COMPLETE: ${actionsTaken} actions taken at price $${currentMarketPrice.toFixed(2)}`);
    }

  }, [state.botSystemStatus, state.currentMarketPrice, state.targetPriceRows, state.config, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, dispatch, playSound, sendTelegramNotification]);


  const getDisplayOrders = useCallback((): DisplayOrderRow[] => {
    if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {
      return [];
    }

    return state.targetPriceRows.map((row: TargetPriceRow) => {
      const currentPrice = state.currentMarketPrice || 0;
      const targetPrice = row.targetPrice || 0;
      const percentFromActualPrice = currentPrice && targetPrice
        ? ((currentPrice / targetPrice) - 1) * 100
        : 0;

      let incomeCrypto1: number | undefined;
      let incomeCrypto2: number | undefined;

      if (row.status === "Full" && row.crypto1AmountHeld && row.originalCostCrypto2) {
        // Calculate unrealized profit/loss based on current market price vs original cost
        if (state.config.tradingMode === "StablecoinSwap") {
          // For stablecoin swap: Calculate profit based on current market price vs target price
          // If current price > target price = profit (positive income)
          // If current price < target price = loss (negative income)
          const targetPrice = row.targetPrice || 0;
          const priceDifference = currentPrice - targetPrice;
          const profitPercentage = targetPrice > 0 ? (priceDifference / targetPrice) : 0;

          // Calculate profit/loss based on the amount held and price difference
          const totalUnrealizedProfitInCrypto2 = row.crypto1AmountHeld * priceDifference;
          incomeCrypto2 = totalUnrealizedProfitInCrypto2;
          if (currentPrice > 0) {
            incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;
          }
        } else {
          // Simple spot mode logic
          const totalUnrealizedProfitInCrypto2 = (currentPrice * row.crypto1AmountHeld) - row.originalCostCrypto2;
          incomeCrypto2 = totalUnrealizedProfitInCrypto2;
          if (currentPrice > 0) {
            incomeCrypto1 = totalUnrealizedProfitInCrypto2 / currentPrice;
          }
        }
      }

      return {
        ...row,
        currentPrice,
        priceDifference: targetPrice - currentPrice,
        priceDifferencePercent: currentPrice > 0 ? ((targetPrice - currentPrice) / currentPrice) * 100 : 0,
        potentialProfitCrypto1: (state.config.incomeSplitCrypto1Percent / 100) * row.valueLevel / (targetPrice || 1),
        potentialProfitCrypto2: (state.config.incomeSplitCrypto2Percent / 100) * row.valueLevel,
        percentFromActualPrice,
        incomeCrypto1,
        incomeCrypto2,
      };
    }).sort((a: DisplayOrderRow, b: DisplayOrderRow) => b.targetPrice - a.targetPrice);
  }, [state.targetPriceRows, state.currentMarketPrice, state.config.incomeSplitCrypto1Percent, state.config.incomeSplitCrypto2Percent, state.config.baseBid, state.config.multiplier]);


  // Backend Integration Functions
  const saveConfigToBackend = useCallback(async (config: TradingConfig): Promise<string | null> => {
    try {
      const configData = {
        name: `${config.crypto1}/${config.crypto2} ${config.tradingMode}`,
        tradingMode: config.tradingMode,
        crypto1: config.crypto1,
        crypto2: config.crypto2,
        baseBid: config.baseBid,
        multiplier: config.multiplier,
        numDigits: config.numDigits,
        slippagePercent: config.slippagePercent,
        incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,
        incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,
        preferredStablecoin: config.preferredStablecoin,
        targetPrices: state.targetPriceRows.map(row => row.targetPrice)
      };

      const response = await tradingApi.saveConfig(configData);
      console.log('✅ Config saved to backend:', response);
      return response.config?.id || null;
    } catch (error) {
      console.error('❌ Failed to save config to backend:', error);
      return null;
    }
  }, [state.targetPriceRows]);

  const startBackendBot = useCallback(async (configId: string): Promise<boolean> => {
    try {
      const response = await tradingApi.startBot(configId);
      console.log('✅ Bot started on backend:', response);
      return true;
    } catch (error) {
      console.error('❌ Failed to start bot on backend:', error);
      return false;
    }
  }, []);

  const stopBackendBot = useCallback(async (configId: string): Promise<boolean> => {
    try {
      const response = await tradingApi.stopBot(configId);
      console.log('✅ Bot stopped on backend:', response);
      return true;
    } catch (error) {
      console.error('❌ Failed to stop bot on backend:', error);
      return false;
    }
  }, []);

  const checkBackendStatus = useCallback(async (): Promise<void> => {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');
      dispatch({ type: 'SET_BACKEND_STATUS', payload: 'offline' });
      return;
    }
    try {
      const healthResponse = await fetch(`${apiUrl}/health/`);
      if (!healthResponse.ok) {
        // Log more details if the response was received but not OK
        console.error(`Backend health check failed with status: ${healthResponse.status} ${healthResponse.statusText}`);
        const responseText = await healthResponse.text().catch(() => 'Could not read response text.');
        console.error('Backend health check response body:', responseText);
      }
      dispatch({ type: 'SET_BACKEND_STATUS', payload: healthResponse.ok ? 'online' : 'offline' });
    } catch (error: any) {
      dispatch({ type: 'SET_BACKEND_STATUS', payload: 'offline' });
      console.error('Backend connectivity check failed. Error details:', error);
      if (error.cause) {
        console.error('Fetch error cause:', error.cause);
      }
      // It's also useful to log the apiUrl to ensure it's what we expect
      console.error('Attempted to fetch API URL:', `${apiUrl}/health/`);
    }
  }, [dispatch]);

  // Initialize backend status check (one-time only)
  useEffect(() => {
    // Initial check for backend status only
    checkBackendStatus();
  }, [checkBackendStatus]);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    saveStateToLocalStorage(state);
  }, [state]);


  // Effect to handle bot warm-up period (immediate execution)
  useEffect(() => {
    if (state.botSystemStatus === 'WarmingUp') {
      console.log("Bot is Warming Up... Immediate execution enabled.");
      // Immediate transition to Running state - no delays
      dispatch({ type: 'SYSTEM_COMPLETE_WARMUP' });
      console.log("Bot is now Running immediately.");
    }
  }, [state.botSystemStatus, dispatch]);

  // Effect to handle session runtime tracking
  useEffect(() => {
    const sessionManager = SessionManager.getInstance();
    const currentSessionId = sessionManager.getCurrentSessionId();

    if (currentSessionId) {
      if (state.botSystemStatus === 'Running') {
        // Bot started running, start runtime tracking
        sessionManager.startSessionRuntime(currentSessionId);
        console.log('✅ Started runtime tracking for session:', currentSessionId);
      } else if (state.botSystemStatus === 'Stopped') {
        // Bot stopped, stop runtime tracking
        sessionManager.stopSessionRuntime(currentSessionId);
        console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);
      }
    }
  }, [state.botSystemStatus]);

  // Auto-create session when bot starts if none exists and handle runtime tracking
  useEffect(() => {
    const sessionManager = SessionManager.getInstance();

    // Check if we need to create a session when bot starts
    if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {
      // Only create if we have valid crypto configuration AND target prices set
      // This prevents auto-creation for fresh windows
      if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {
        sessionManager.createNewSessionWithAutoName(
          state.config,
          undefined,
          { crypto1: state.crypto1Balance, crypto2: state.crypto2Balance, stablecoin: state.stablecoinBalance }
        )
          .then((sessionId) => {
            sessionManager.setCurrentSession(sessionId);
            console.log('✅ Auto-created session:', sessionId);
          })
          .catch((error) => {
            console.error('❌ Failed to auto-create session:', error);
          });
      }
    }

    // Handle runtime tracking based on bot status
    const currentSessionId = sessionManager.getCurrentSessionId();
    if (currentSessionId) {
      if (state.botSystemStatus === 'Running') {
        // Start runtime tracking when bot becomes active
        sessionManager.startSessionRuntime(currentSessionId);
        console.log('⏱️ Started runtime tracking for session:', currentSessionId);
      } else if (state.botSystemStatus === 'Stopped') {
        // Stop runtime tracking when bot stops
        sessionManager.stopSessionRuntime(currentSessionId);
        console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);
      }
    }
  }, [state.botSystemStatus, state.config.crypto1, state.config.crypto2]);

  // Handle crypto pair changes during active trading
  useEffect(() => {
    const sessionManager = SessionManager.getInstance();
    const currentSessionId = sessionManager.getCurrentSessionId();

    if (currentSessionId) {
      const currentSession = sessionManager.loadSession(currentSessionId);

      // Check if crypto pair has changed from the current session
      if (currentSession &&
          (currentSession.config.crypto1 !== state.config.crypto1 ||
           currentSession.config.crypto2 !== state.config.crypto2)) {

        console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');

        // Auto-save current session if bot was running or has data
        if (state.botSystemStatus === 'Running' ||
            state.targetPriceRows.length > 0 ||
            state.orderHistory.length > 0) {

          // Note: No need to stop backend bot during crypto pair change
          // Frontend bot system is separate from backend bot system
          // The frontend bot status will be reset with the new crypto pair

          // Save current session with timestamp
          const timestamp = new Date().toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          });
          const savedName = `${currentSession.name} (AutoSaved ${timestamp})`;

          sessionManager.createNewSession(
            savedName,
            currentSession.config,
            { crypto1: state.crypto1Balance, crypto2: state.crypto2Balance, stablecoin: state.stablecoinBalance }
          )
            .then((savedSessionId) => {
              sessionManager.saveSession(
                savedSessionId,
                currentSession.config,
                state.targetPriceRows,
                state.orderHistory,
                state.currentMarketPrice,
                state.crypto1Balance,
                state.crypto2Balance,
                state.stablecoinBalance,
                false
              );
              console.log('💾 AutoSaved session:', savedName);
            })
            .catch((error) => {
              console.error('❌ Failed to auto-save session during crypto pair change:', error);
            });
        }

        // Reset for new crypto pair
        dispatch({ type: 'RESET_FOR_NEW_CRYPTO' });

        // Don't auto-create new session for crypto pair change
        // User should manually create session after setting target prices
        console.log('🔄 Crypto pair changed, session cleared. Set target prices and start bot to create new session.');
      }
    }
  }, [state.config.crypto1, state.config.crypto2]);

  // Initialize network monitoring and auto-save
  useEffect(() => {
    const networkMonitor = NetworkMonitor.getInstance();
    const autoSaveManager = AutoSaveManager.getInstance();
    const memoryMonitor = MemoryMonitor.getInstance();
    const sessionManager = SessionManager.getInstance();

    // Set up network status listener
    const unsubscribeNetwork = networkMonitor.addListener((isOnline, isInitial) => {
      console.log(`🌐 Network status changed: ${isOnline ? 'Online' : 'Offline'}`);

      // Handle offline state - stop bot and save session
      if (!isOnline && !isInitial) {
        // Stop the bot if it's running
        if (state.botSystemStatus === 'Running') {
          console.log('🔴 Internet lost - stopping bot and saving session');
          dispatch({ type: 'SYSTEM_STOP_BOT' });

          // Auto-save current session
          const sessionManager = SessionManager.getInstance();
          const currentSessionId = sessionManager.getCurrentSessionId();
          if (currentSessionId) {
            const currentSession = sessionManager.loadSession(currentSessionId);
            if (currentSession) {
              // Create offline backup session
              const timestamp = new Date().toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              });
              const offlineName = `${currentSession.name} (Offline Backup ${timestamp})`;

              sessionManager.createNewSession(
                offlineName,
                currentSession.config,
                { crypto1: state.crypto1Balance, crypto2: state.crypto2Balance, stablecoin: state.stablecoinBalance }
              )
                .then((backupSessionId) => {
                  sessionManager.saveSession(
                    backupSessionId,
                    state.config,
                    state.targetPriceRows,
                    state.orderHistory,
                    state.currentMarketPrice,
                    state.crypto1Balance,
                    state.crypto2Balance,
                    state.stablecoinBalance,
                    false
                  );
                  console.log('💾 Created offline backup session:', offlineName);
                })
                .catch((error) => {
                  console.error('❌ Failed to create offline backup session:', error);
                });
            }
          }
        }

        console.log("Network Disconnected: Bot stopped and session saved. Trading paused until connection restored.");
      } else if (isOnline && !isInitial) {
        console.log("Network Reconnected: Connection restored. You can resume trading.");
      }
    });

    // Set up memory monitoring
    const unsubscribeMemory = memoryMonitor.addListener((memory) => {
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      if (usedMB > 150) { // 150MB threshold
        console.warn(`🧠 High memory usage: ${usedMB.toFixed(2)}MB`);
      }
    });

    // Set up auto-save
    const saveFunction = () => {
      try {
        // Save to session manager if we have a current session
        const currentSessionId = sessionManager.getCurrentSessionId();
        if (currentSessionId) {
          sessionManager.saveSession(
            currentSessionId,
            state.config,
            state.targetPriceRows,
            state.orderHistory,
            state.currentMarketPrice,
            state.crypto1Balance,
            state.crypto2Balance,
            state.stablecoinBalance,
            true // Session is active if it's the current session
          );
        }

        // Also save to localStorage as backup
        saveStateToLocalStorage(state);
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    };

    autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds

    // Add beforeunload listener to save session on browser close/refresh
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Save immediately before unload
      saveFunction();

      // If bot is running, show warning
      if (state.botSystemStatus === 'Running') {
        const message = 'Trading bot is currently running. Are you sure you want to leave?';
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function
    return () => {
      unsubscribeNetwork();
      unsubscribeMemory();
      autoSaveManager.disable();
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [state]);

  // Force save when bot status changes
  useEffect(() => {
    const autoSaveManager = AutoSaveManager.getInstance();
    autoSaveManager.saveNow();
  }, [state.botSystemStatus]);

  // Save global balances whenever they change
  useEffect(() => {
    saveGlobalBalances(state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance);
  }, [state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance]);

  // Initialize with global balances on mount (only if using default balances)
  useEffect(() => {
    const isDefaultBalances = state.crypto1Balance === 10 && state.crypto2Balance === 100000 && state.stablecoinBalance === 0;
    if (isDefaultBalances) {
      const globalBalances = loadGlobalBalances();
      if (globalBalances.crypto1Balance !== 10 || globalBalances.crypto2Balance !== 100000 || globalBalances.stablecoinBalance !== 0) {
        dispatch({
          type: 'UPDATE_BALANCES',
          payload: {
            crypto1: globalBalances.crypto1Balance,
            crypto2: globalBalances.crypto2Balance,
            stablecoin: globalBalances.stablecoinBalance
          }
        });
      }
    }
  }, []); // Only run once on mount

  // Manual save session function
  const saveCurrentSession = useCallback((): boolean => {
    try {
      const sessionManager = SessionManager.getInstance();
      const currentSessionId = sessionManager.getCurrentSessionId();

      if (!currentSessionId) {
        // No current session, create one first
        if (state.config.crypto1 && state.config.crypto2) {
          sessionManager.createNewSessionWithAutoName(
            state.config,
            undefined,
            { crypto1: state.crypto1Balance, crypto2: state.crypto2Balance, stablecoin: state.stablecoinBalance }
          )
            .then((sessionId) => {
              sessionManager.setCurrentSession(sessionId);
              sessionManager.saveSession(
                sessionId,
                state.config,
                state.targetPriceRows,
                state.orderHistory,
                state.currentMarketPrice,
                state.crypto1Balance,
                state.crypto2Balance,
                state.stablecoinBalance,
                true // New session is active when created
              );
            })
            .catch((error) => {
              console.error('Failed to create new session:', error);
              sendTelegramErrorNotification(
                'Session Creation Error',
                'Failed to create new trading session',
                `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
              );
            });
          return true;
        }
        sendTelegramErrorNotification(
          'Session Save Error',
          'Cannot save session - no trading pair selected',
          'Please select both crypto1 and crypto2 before saving'
        );
        return false;
      }

      // Save existing session
      return sessionManager.saveSession(
        currentSessionId,
        state.config,
        state.targetPriceRows,
        state.orderHistory,
        state.currentMarketPrice,
        state.crypto1Balance,
        state.crypto2Balance,
        state.stablecoinBalance,
        true // Current session is active
      );
    } catch (error) {
      console.error('Failed to save current session:', error);
      sendTelegramErrorNotification(
        'Session Save Error',
        'Unexpected error while saving session',
        `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      return false;
    }
  }, [state, sendTelegramErrorNotification]);

  // Context value
  const contextValue: TradingContextType = {
    ...state,
    dispatch,
    setTargetPrices,
    getDisplayOrders,
    checkBackendStatus,
    fetchMarketPrice,
    startBackendBot,
    stopBackendBot,
    saveConfigToBackend,
    saveCurrentSession,
    backendStatus: state.backendStatus as 'online' | 'offline' | 'unknown',
    botSystemStatus: state.botSystemStatus,
    isBotActive: state.botSystemStatus === 'Running',
  };

  return (
    <TradingContext.Provider value={contextValue}>
      {children}
    </TradingContext.Provider>
  );
};

// Custom hook to use the trading context
export const useTradingContext = (): TradingContextType => {
  const context = useContext(TradingContext);
  if (context === undefined) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};

