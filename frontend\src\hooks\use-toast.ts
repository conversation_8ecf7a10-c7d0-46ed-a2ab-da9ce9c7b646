"use client"

import React from "react"

// Simple toast system
interface ToastItem {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
}

interface ToastState {
  toasts: ToastItem[];
}

let toastId = 0;
const generateId = () => `toast-${++toastId}`;

const listeners: Array<(state: ToastState) => void> = [];
let state: ToastState = { toasts: [] };

function emitChange() {
  listeners.forEach(listener => listener(state));
}

function addToast(toast: Omit<ToastItem, 'id'>) {
  const newToast: ToastItem = {
    ...toast,
    id: generateId(),
  };

  state = {
    toasts: [newToast, ...state.toasts].slice(0, 5), // Keep max 5 toasts
  };

  emitChange();

  // Auto remove after 5 seconds
  setTimeout(() => {
    removeToast(newToast.id);
  }, 5000);

  return newToast.id;
}

function removeToast(id: string) {
  state = {
    toasts: state.toasts.filter(toast => toast.id !== id),
  };
  emitChange();
}

function useToast() {
  const [toastState, setToastState] = React.useState<ToastState>(state);

  React.useEffect(() => {
    listeners.push(setToastState);
    return () => {
      const index = listeners.indexOf(setToastState);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }, []);

  const toast = React.useCallback((options: Omit<ToastItem, 'id'>) => {
    return addToast(options);
  }, []);

  const dismiss = React.useCallback((toastId?: string) => {
    if (toastId) {
      removeToast(toastId);
    } else {
      // Remove all toasts
      state = { toasts: [] };
      emitChange();
    }
  }, []);

  return {
    toasts: toastState.toasts,
    toast,
    dismiss,
  };
}

// Export simple toast function for direct use
const toast = (options: Omit<ToastItem, 'id'>) => addToast(options);

export { useToast, toast };
